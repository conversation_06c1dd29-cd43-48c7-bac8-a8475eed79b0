// LoginForm.vue
<script setup lang="ts">
import { computed, reactive, ref } from 'vue'

// Form state
const formData = reactive({
  email: '',
  password: '',
})

// UI state
const isEmailFocused = ref(false)
const isPasswordFocused = ref(false)
const showPassword = ref(false)

// Computed properties
const faceAnimation = computed(() => ({
  'transform transition-transform duration-300': true,
  'translate-y-1': isEmailFocused.value,
}))

// Event handlers
function handleEmailFocus() {
  isEmailFocused.value = true
  isPasswordFocused.value = false
}

function handleEmailBlur() {
  isEmailFocused.value = false
}

function handlePasswordFocus() {
  isPasswordFocused.value = true
  isEmailFocused.value = false
}

function handlePasswordBlur() {
  isPasswordFocused.value = false
}

function togglePassword() {
  showPassword.value = !showPassword.value
}

function handleSubmit() {
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="bg-white p-8 rounded-xl shadow-lg w-full max-w-sm">
      <!-- Bear Avatar -->
      <div class="w-32 h-32 mx-auto mb-8">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 200 200"
          class="w-full h-full"
        >
          <!-- Circle Background -->
          <circle cx="100" cy="100" r="100" fill="#90CDF4" />

          <!-- Face Container -->
          <g :class="faceAnimation">
            <!-- Ears -->
            <g>
              <!-- Left Ear -->
              <circle cx="50" cy="60" r="20" fill="#805AD5" />
              <circle cx="50" cy="60" r="15" fill="#D6BCFA" />

              <!-- Right Ear -->
              <circle cx="150" cy="60" r="20" fill="#805AD5" />
              <circle cx="150" cy="60" r="15" fill="#D6BCFA" />
            </g>

            <!-- Face -->
            <circle cx="100" cy="100" r="65" fill="#805AD5" />

            <!-- White Face Patch -->
            <!-- <path
              d="M55 85 Q100 140 145 85 Q145 120 100 140 Q55 120 55 85"
              fill="#fff"
            /> -->

            <!-- Eyes -->
            <g v-if="!isPasswordFocused">
              <!-- Open Eyes -->
              <g :class="{ 'transform scale-110': isEmailFocused }">
                <!-- Left Eye -->
                <circle cx="75" cy="95" r="10" fill="#2D3748" />
                <circle cx="72" cy="92" r="3" fill="#fff" />

                <!-- Right Eye -->
                <circle cx="125" cy="95" r="10" fill="#2D3748" />
                <circle cx="122" cy="92" r="3" fill="#fff" />
              </g>
            </g>
            <g v-else>
              <!-- Closed Eyes -->
              <path d="M65 95 Q75 90 85 95" stroke="#2D3748" stroke-width="3" fill="none" />
              <path d="M115 95 Q125 90 135 95" stroke="#2D3748" stroke-width="3" fill="none" />
            </g>

            <!-- Nose -->
            <g>
              <path
                d="M95 105 Q100 110 105 105 Q100 115 95 105"
                fill="#2D3748"
              />
            </g>

            <!-- Mouth -->
            <path
              :d="isEmailFocused
                ? 'M75 120 Q100 140 125 120'
                : 'M75 120 Q100 130 125 120'"
              stroke="#2D3748"
              stroke-width="3"
              fill="none"
              :class="{ 'transform translate-y-1': isEmailFocused }"
            />

            <!-- Blush -->
            <g opacity="0.5">
              <circle cx="65" cy="115" r="10" fill="#FC8181" />
              <circle cx="135" cy="115" r="10" fill="#FC8181" />
            </g>
          </g>
        </svg>
      </div>

      <!-- Login Form -->
      <form class="space-y-6" @submit.prevent="handleSubmit">
        <div class="space-y-2">
          <label class="block text-gray-700 font-medium">
            Email
          </label>
          <input
            v-model="formData.email"
            type="email"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200"
            placeholder="<EMAIL>"
            required
            @focus="handleEmailFocus"
            @blur="handleEmailBlur"
          >
        </div>

        <div class="space-y-2">
          <label class="block text-gray-700 font-medium">
            Password
          </label>
          <div class="relative">
            <input
              v-model="formData.password"
              :type="showPassword ? 'text' : 'password'"
              class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200"
              required
              @focus="handlePasswordFocus"
              @blur="handlePasswordBlur"
            >
            <button
              type="button"
              class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
              @click="togglePassword"
            >
              {{ showPassword ? 'Hide' : 'Show' }}
            </button>
          </div>
        </div>

        <button
          type="submit"
          class="w-full bg-purple-500 text-white font-bold py-3 px-4 rounded-lg hover:bg-purple-600 transition-colors duration-200"
        >
          Log in
        </button>
      </form>
    </div>
  </div>
</template>

<style scoped>
/* Optional: Add any additional custom styles here */
</style>
