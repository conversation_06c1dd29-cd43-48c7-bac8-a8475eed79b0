/* .worksiteName {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  max-width: 100px;
}

.red {
}

.blue {
  background-color: #3b82f6;
}

.green {
  background-color: #22c55e;
}

.card {
  height: 90%;
  width: 100%;
  border-radius: 20px;
  background-color: white;
}

.scroll-snap-card {
  height: 85%;
  width: 100%;
  scroll-snap-type: y;
  overflow: auto;
}

.card .card-title {
  display: flex;
  align-items: center;
  justify-content: left;
  width: 100%;
  height: 5vw;
}

.card .card-title p {
  margin: 0 0 0 20px;
  font-size: calc(1vw + 10px);
  font-weight: 600;
  color: #1570ef;
}

.scroll-snap-card .slide {
  width: 100%;
  height: 'fit-content';
  scroll-snap-align: start;
  display: flex;
  align-items: flex-start;
  justify-content: left;
  flex-direction: column;
}

.scroll-snap-card .attendance-text p {
  font-size: 1.2vw;
}

.attendance-container {
  background-color: #dcf6e0;
  width: 100%;
  display: flex;
  height: 2vw;
}

.attendance-left-block {
  background-color: #106b1e;
  width: 10%;
}

.attendance-right-block {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;

}

.attendance-right-block .attendance-text {
  color: green;
  margin: auto 8px;
  font-size: 1.2vw;
}

.attendance-content p {
  margin-top: 8px;
  margin-left: 2vw;
  font-size: calc(0.6vw + 5px);
}



.ant-picker-content thead th {
    text-align: center; 

.ant-picker-content thead th {
    text-align: center; 
    font-size: 1.2vw;
}

.custom-cell-size {
  border-radius: 10px;
} */