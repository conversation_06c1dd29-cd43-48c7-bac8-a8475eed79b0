import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { AvatarItem } from '~@/api/employee/employee'
import { getEmployeeAvatar } from '~@/api/employee/employee'
import { useApiRequest } from '~@/composables/useApiRequest'
import logger from '~@/utils/logger'

const apiRequest = useApiRequest(getEmployeeAvatar, { immediate: false, showNotify: false })

export const useAvatarStore = defineStore('avatar', () => {
  // state
  const avatars = ref<AvatarItem[]>([])

  // getters
  function getAvatarBase64ByEmployeeId(employeeId: string) {
    const avatarItem: AvatarItem | undefined = avatars.value.find(avatar => avatar.employeeId === employeeId)
    if (!avatarItem)
      return null
    return avatarItem.avatar.avatarBase64
  }

  function getImageSrcByEmployeeId(employeeId: string) {
    const avatarBase64 = getAvatarBase64ByEmployeeId(employeeId)
    if (!avatarBase64)
      return null
    return `data:image/jpeg;base64,${avatarBase64}`
  }

  // actions
  async function fetchAvatar() {
    try {
      await apiRequest.execute()
      avatars.value = apiRequest.data.value?.items ?? []
    }
    catch (error) {
      logger.error(error)
    }
  }

  return {
    avatars,
    getAvatarBase64ByEmployeeId,
    getImageSrcByEmployeeId,
    fetchAvatar,
  }
})
