import type { ExtractPropTypes, VNodeChild } from 'vue'
import { arrayType, booleanType, eventType, numberType, stringType } from '@v-c/utils'

export type CheckedType = boolean | string | number
export type MenuData = MenuDataItem[]

export type Key = string | number

export interface MenuDataItem {
  // Tiêu đề của mục, có thể là string hoặc hàm trả về VNodeChild.
  title: string | (() => VNodeChild)
  // Biểu tượng của mục, có thể là string hoặc hàm trả về VNodeChild.
  icon?: string | (() => VNodeChild)
  // Đường dẫn của mục.
  path: string
  // Tên của component được liên kết với mục.
  component?: string
  // Mảng các mục con.
  children?: MenuDataItem[]
  // Đường dẫn redirect.
  redirect?: string
  // Đánh dấu mục là page tabs cố định.
  affix?: boolean
  // ID của mục cha.
  parentName?: string | null
  // Tên của mục (sử dụng trong route).
  name?: string
  // Ẩn mục trong menu.
  hideInMenu?: boolean
  // Mảng chứa các key của mục cha.
  parentKeys?: string[]
  // Đánh dấu mục sử dụng iframe.
  isIframe?: boolean
  // URL cho mục iframe.
  url?: string
  // Ẩn mục trong breadcrumb.
  hideInBreadcrumb?: boolean
  // Ẩn tất cả các mục con trong menu.
  hideChildrenInMenu?: boolean
  // Đánh dấu mục để giữ cho nó sống.
  keepAlive?: boolean
  // Mảng các mục cha.
  matched?: MenuDataItem[]
  // Chế độ mở liên kết ngoài.
  target?: '_blank' | '_self' | '_parent'
  // Cấu hình đa ngôn ngữ.
  locale?: string

  isLeaf?: boolean

  displayOrder?: number
}

export type LayoutType = 'mix' | 'side' | 'top'

export type ThemeType = 'light' | 'dark' | 'inverted'

export type ContentWidth = 'Fluid' | 'Fixed'

export interface MenuSelectEvent {
  item: any
  key: string
  selectedKeys: string[]
}

const proLayoutEvents = {
  'onUpdate:openKeys': eventType<(val: string[]) => void>(),
  'onUpdate:selectedKeys': eventType<(val: string[]) => void>(),
  'onMenuSelect': eventType<(data: MenuSelectEvent) => void>(),
}

export const proLayoutProps = {
  layout: stringType<LayoutType>('mix'),
  logo: stringType(),
  title: stringType(),
  collapsedWidth: numberType(64),
  siderWidth: numberType(280),
  headerHeight: numberType<number>(48),
  menuData: arrayType<MenuData>(),
  fixedHeader: booleanType<boolean>(false),
  fixedSider: booleanType<boolean>(true),
  splitMenus: booleanType(),
  collapsed: booleanType<boolean>(false),
  leftCollapsed: booleanType<boolean>(false),
  theme: stringType<ThemeType>('light'),
  onCollapsed: eventType<(collapsed: boolean) => void>(),
  isMobile: booleanType(),
  contentWidth: stringType<ContentWidth>(),
  header: booleanType<boolean>(true),
  footer: booleanType<boolean>(true),
  menu: booleanType<boolean>(true),
  menuHeader: booleanType<boolean>(true),
  // 展开菜单
  openKeys: arrayType<string[]>(),
  // 选中菜单
  selectedKeys: arrayType<string[]>(),
  copyright: stringType(),
  ...proLayoutEvents,
}

export type ProLayoutProps = Partial<ExtractPropTypes<typeof proLayoutProps>>
