import type { CostCategoryItem, GetCostCategoryParams } from '~@/api/company/cost-category'
import { getCostCategory } from '~@/api/company/cost-category'

export function useCategory() {
  const messageNotification = useMessage()
  const categoryData = ref<CostCategoryItem[]>([])
  const isLoading = ref(false)

  const fetchCategory = async (params?: GetCostCategoryParams) => {
    isLoading.value = true
    try {
      const { data, status, message } = await getCostCategory(params)
      if (status === 200) {
        categoryData.value = data?.items ?? []
        return data?.items ?? []
      }
      else {
        messageNotification.error(message ?? 'Fetch category failed!')
        return []
      }
    }
    catch (error) {
      messageNotification.error('Failed to fetch category data')
      return []
    }
    finally {
      isLoading.value = false
    }
  }

  return {
    categoryData,
    isLoading,
    fetchCategory,
  }
}
