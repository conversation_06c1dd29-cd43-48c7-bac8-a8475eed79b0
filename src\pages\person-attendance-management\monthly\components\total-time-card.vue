<script setup lang="ts">
const props = defineProps({
  totalWorkTime: {
    type: Number,
  },
  avatarSrc: {
    type: String,
  },
  avatarBackgroundColor: {
    type: String,
  },
  avatarColor: {
    type: String,
  },
  label: {
    type: String,
  },
})
</script>

<template>
  <div style="display: flex; align-items: center;">
    <a-avatar
      class="time-card-avatar"
      shape="circle"
      :style="{ backgroundColor: props.avatarBackgroundColor,
                color: props.avatarColor }"
    >
      <template #icon>
        <img :src="props.avatarSrc">
      </template>
    </a-avatar>
    <div class="time-card-label">
      <span
        style="font-weight: bold;
        font-size: 20px"
      >
        {{ props.totalWorkTime }}
      </span>
      <span style="font-weight: 600; color: #74797a">{{ props.label }}</span>
    </div>
  </div>
</template>

<style scoped>
.time-card-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 5px;
}

.time-card-avatar {
  padding: calc(5px + 0.2vw);
  width: 52px;
  height: 52px;
}
</style>
