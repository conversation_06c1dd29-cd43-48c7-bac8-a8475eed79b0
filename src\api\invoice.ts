import type { FileType } from 'ant-design-vue/es/upload/interface'

export interface InputCostItem {
  createTime?: string
  updateTime?: string
  inputCostItemId: string
  transactionDate: string
  itemId: string
  itemName: string
  unit: string
  vendorId: string
  vendorName: string
  quantity: number
  price: number
  taxRate: number
  totalNonTaxed: number
  totalTaxed: number
  description: string
  inputCostId: string
  originalInputCostNumber: string
}

export interface InputCostItemPostParams {
  transactionDate?: string
  inputCostId?: string
  item?: Item
  itemName?: string
  unit?: string
  vendorId?: string
  quantity?: number
  price?: number
  taxRate?: number
  totalNonTaxed?: number
  totalTaxed?: number
  description?: string
}

export interface Item {
  itemId: string
  itemName?: string
  categoryId?: string
}

export interface InputCostItemPutParams {
  inputCostItemId?: string
  transactionDate?: string
  item: Item
  unit?: string
  quantity?: number
  price?: number
  taxRate?: number
  totalNonTaxed?: number
  totalTaxed?: number
  description?: string
  isDeleted: boolean
}

export interface InputCostPutParams {
  inputCostItems?: any
  title?: string
  issueDate?: string
  paymentDate?: string
  originalNumber?: string
  projectId?: string
  constructionId?: string
  entryTypeId?: string
  vendorId?: string
  paymentTypeId?: string
  description?: string
  totalAmount?: number
  images?: FileType[]
}

interface Vendor {
  vendorId: string
  vendorName?: string
}

export interface InputCostPostParams {
  inputCostItems?: InputCostItemPostParams[]
  title?: string
  issueDate?: string
  paymentDate?: string
  originalNumber?: string
  projectId?: string
  constructionId?: string
  entryTypeId?: string
  vendor: Vendor
  paymentTypeId?: string
  description?: string
  totalAmount?: number
  images?: FileType[]
}

export interface InputCost {
  createTime: string
  updateTime: string
  inputCostId: string
  title: string
  issueDate: string
  paymentDate: string
  originalNumber: string
  projectId: string
  projectName: string
  constructionId: string
  constructionName: string
  isMainConstruction: boolean
  entryTypeId: string
  entryTypeCode: string
  entryTypeName: string
  vendorId: string
  vendorCode: string
  vendorName: string
  vendorPresentativeName: string
  vendorAddress: string
  vendorPhoneNumber: string
  vendorEmail: string
  totalAmount: number
  paymentTypeId: string
  paymentTypeCode: string
  paymentTypeName: string
  description: string
  imageUrls: string[]
  itemId: string
}

export interface PagedResponse<T> {
  items: T[]
  pageNum: number
  pageSize: number
  totalRecords: number
}

interface InputCostListResponse extends PagedResponse<InputCost> {}

export interface InputCostFilterRequest {
  projectId?: string
  constructionId?: string
  vendorId?: string
  title?: string
  issueDateFrom?: string
  issueDateTo?: string
  paymentDateFrom?: string
  paymentDateTo?: string
  paymentTypeId?: string
  totalAmountMin?: number
  totalAmountMax?: number
  entryTypeId?: string
  pageNum?: number // Default is 1
  pageSize?: number // Default is 10
}

export async function getInputCostListApi(params: InputCostFilterRequest) {
  return useGet<InputCostListResponse>('v1/cost/inputcost', params)
}

export async function getInputCostByIdApi(inputCostId: string) {
  return useGet<InputCost>(`v1/cost/inputcost/${inputCostId}`)
}

export async function createInputCostApi(formData: FormData) {
  return usePost<InputCost>('v1/cost/inputcost', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  })
}

export async function updateInputCostApi(inputCostId: string, formData: FormData) {
  return usePut<InputCost>(`v1/cost/inputcost/${inputCostId}`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  })
}

export async function updateInputCostItemApi(inputCostId: string, params: InputCostPutParams) {
  return usePut<InputCost>(`v1/cost/inputcost/${inputCostId}`, params)
}

export async function deleteInputCostApi(inputCostId: string) {
  return useDelete<InputCost>(`v1/cost/inputcost/${inputCostId}`)
}

export function fetchInputCostImage(url: string, orgId: string) {
  const host = import.meta.env.VITE_APP_BASE_API ?? ''
  return `${host}/v1/cost/inputcost/images?imageUrl=${url}&orgId=${orgId}`
}

export function deleteInputCostImageApi(inputCostId: string, imageUrl: string) {
  return useDelete<InputCost>(`v1/cost/inputcost/${inputCostId}/image?imageUrl=${imageUrl}`)
}
