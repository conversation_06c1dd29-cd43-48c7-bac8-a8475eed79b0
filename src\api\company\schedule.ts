export const SCHEDULE_ROLES = [
  'supervisor',
  'foreman',
  'worker',
  'heavyMachineryOperator',
  'formworkWorker',
  'rebarWorker',
  'engineer',
  'contractType',
  'unassigned',
  'officeOperation',
] as const

export type ScheduleRole = (typeof SCHEDULE_ROLES)[number]

export interface DownloadScheduleParams {
  fromDate: string
  toDate: string
}

export interface ScheduleShiftParams {
  employeeId: string
  startTime?: string
  endTime?: string
  assignedRole?: string
  totalScheduledWorkTime?: number
  workshiftId?: string
}

export interface UpdateScheduleShiftParams {
  startTime: string
  endTime: string
  assignedRole?: string
  assignedWorkload?: number // For only outsource shift
  totalScheduledWorkTime?: number // endTime - startTime
}

export interface ScheduleParams {
  projectId?: string
  workingDate?: string // ISO format "YYYY-MM-DD"
  plannedWorkload?: number
  estimatedWorkload?: number
  description?: string
  shifts?: ScheduleShiftParams[]
  outsourceShifts?: OutsourceShiftParams[]
}

export interface UpdateScheduleParams {
  plannedWorkload?: number
  estimatedWorkload?: number
}

export interface ScheduleItem {
  scheduleId: string
  projectId: string
  workingDate: string // ISO format "YYYY-MM-DD"
  totalActualWorkload: number
  totalEstimatedWorkload: number
  totalPlannedWorkload: number
  shifts?: EmployeeShift[]
  outsourceShifts?: OutsourceShift[]
}

export interface EmployeeShift {
  employeeShiftId: string
  employeeId?: string
  employeeName: string
  employeeCode: string
  workingDate: string // ISO format "YYYY-MM-DD"
  scheduledStartTime: string
  scheduledEndTime: string
  assignedWorkload: number
  workingRole: string
  projectScheduleId?: string
  totalScheduledWorkTime: number
}

export interface OutsourceShift {
  outSourceShiftId: string
  projectScheduleId: string
  outSourceName: string
  outSourceCode: string
  workingDate: string
  scheduledStartTime: string
  scheduledEndTime: string
  assignedWorkload: number
  workingRole: string
  totalScheduledWorkTime: number
}
export type ShiftParams = Partial<EmployeeShift>

export interface MergedShift {
  workingDate: string
  employeeShifts: EmployeeShift[]
}

export interface MergedOutsourceShift {
  workingDate: string
  outsourceShifts: OutsourceShift[]
}

export interface ProjectScheduleProcessedItem {
  projectId: string
  projectCode: string
  projectName: string
  shiftCount: number
  scheduleCount: number
  shifts: MergedShift[]
  schedules: ScheduleItem[]
}
export interface ProjectScheduleItem {
  projectId: string
  projectCode: string
  projectName: string
  shifts: EmployeeShift[]
  outsourceShifts: OutsourceShift[]
  schedules: ScheduleItem[]
}

export interface ProjectScheduleParams {
  fromDate: string
  toDate: string
  pageNum?: number
  pageSize?: number
  searchKeyword?: string // search by project name or project code
}

export interface ProjectScheduleResponse {
  items: ProjectScheduleItem[]
  pageNum: number
  pageSize: number
  totalRecords: number
}

export interface TargetInfo {
  projectId?: string
  workingDate?: string
}

export interface OutsourceShiftParams {
  outSourceId: string
  startTime: string
  endTime: string
  assignedWorkload: number
  assignedRole: string
  workshiftId?: string
}

export interface DuplicateScheduleParams {
  projectScheduleIds: string[]
  targetDateFrom: string
  targetDateTo: string
}

export async function getProjectScheduleApi(params: ProjectScheduleParams) {
  return useGet<ProjectScheduleResponse>('v1/schedule', params)
}

export async function createNewScheduleApi(params: ScheduleParams) {
  return usePost<ScheduleItem>('v1/schedule', params)
}

export async function createScheduleShiftApi(scheduleId: string, params: ScheduleShiftParams) {
  return usePost<EmployeeShift>(`v1/schedule/${scheduleId}/shift`, params)
}

export async function updateScheduleShiftApi(shiftId: string, params: UpdateScheduleShiftParams) {
  return usePut<EmployeeShift>(`v1/schedule/shift/${shiftId}`, params)
}

export async function deleteProjectScheduleApi(projectScheduleId: string) {
  return useDelete<any>(`v1/schedule/${projectScheduleId}`)
}

export async function updateScheduleApi(projectScheduleId: string, params: UpdateScheduleParams) {
  return usePut<ScheduleItem>(`v1/schedule/${projectScheduleId}`, params)
}

export async function duplicateAnEmployeeShiftApi(sourceEmployeeShiftId: string, targetInfo: TargetInfo) {
  return usePost<EmployeeShift>(`v1/schedule/shift/${sourceEmployeeShiftId}/duplicate`, targetInfo)
}

export async function createOutsourceShiftApi(scheduleId: string, params: OutsourceShiftParams) {
  return usePost<OutsourceShift>(`v1/schedule/${scheduleId}/outsource`, params)
}

export async function updateOutsourceShiftApi(outSourceShiftId: string, params: UpdateScheduleShiftParams) {
  return usePut<OutsourceShift>(`v1/schedule/outsource/${outSourceShiftId}`, params)
}

export async function duplicateAnOutsourceShiftApi(sourceOutsourceShiftId: string, targetInfo: TargetInfo) {
  return usePost<OutsourceShift>(`v1/schedule/outsource/${sourceOutsourceShiftId}/duplicate`, targetInfo)
}

export async function deleteScheduleApi(scheduleId: string) {
  return useDelete<any>(`v1/schedule/${scheduleId}`)
}

export async function deleteEmployeeShiftApi(employeeShiftId: string) {
  return useDelete<any>(`v1/schedule/shift/${employeeShiftId}`)
}

export async function deleteOutsourceShiftApi(outsourceShiftId: string) {
  return useDelete<any>(`v1/schedule/outsource/${outsourceShiftId}`)
}

export async function duplicateScheduleApi(params: DuplicateScheduleParams) {
  return usePost<any>('v1/schedule/duplicates', params)
}
