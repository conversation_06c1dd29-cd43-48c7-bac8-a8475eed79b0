import { createPinia } from 'pinia'
import { createApp } from 'vue'
import type { App } from 'vue'
import Root from './App.vue'
import { setupI18n } from './locales'
import { setupAccessDirective, setupLoadingDirective } from './directive'
import router from '~/router'
import '~@/router/router-guard.ts'
import 'ant-design-vue/dist/reset.css'
import '~/assets/styles/reset.css'
import '~/assets/styles/global.css'
import 'uno.css'
import '~/assets/styles/index.css'

const pinia = createPinia()

async function start() {
  const app: App = createApp(Root)
  app.use(pinia)
  await setupI18n(app)
  setupDirective(app)
  app.use(router)
  app.mount('#app')
  app.config.performance = true
  if (import.meta.env.PROD)
    console.error = () => {} // Tắt lỗi trong console
}

function setupDirective(app: App) {
  setupLoadingDirective(app)
  setupAccessDirective(app)
}
start()
