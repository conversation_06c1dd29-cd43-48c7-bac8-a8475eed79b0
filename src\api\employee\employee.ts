import type { RoleResponse } from '../company/role'
import type { UserInfoItem } from '~@/types/company/user-info'

// Interface for employee role
export interface EmployeeRole {
  employeeRoleId: string
  employeeRoleName: string
}
export interface RoleCombo {
  roleId: string
  roleName: string
}

export interface EmployeeMail {
  email: string
  isPrimary: boolean
}

export interface EmployeePhone {
  phone: string
  isPrimary: boolean
}

export interface EmployeeItem {
  employeeId: string
  employeeCode: string
  employeeName: string
  employeeMails: EmployeeMail[]
  employeePhones: EmployeePhone[]
  employeeAddress: string
  gender: boolean
  birthday: string
  workingStatus: string
  workingStatusName: string
  workingFromDate: string
  workingToDate: string
  structureId: string
  structureName: string
  positionId: string
  positionName: string
  rankingId: string
  rankingName: string
  salaryInDay: number
  salaryInHour: number
  salaryInMonth: number
  standardWorkingHours: number
  employeeType: boolean
  roles: RoleCombo[]
}

// src/types/Employee.ts
export interface EmployeeItemParam {
  accountId: string
  structureId: string
  positionId: string
  employeeType: boolean
  rankingId: string
  rankingStartDate: string
  rankingEndDate: string
  workingStatus: string
  workingFromDate: string
  roleIds: string[]
  baseLeave: number
  baseLeaveExpire: string
  employeeMails: EmployeeMail[]
  employeePhones: EmployeePhone[]
  employeeAddress: string
  employeeCode: string
  workingToDate: string
  salaryInDay: number
  salaryInHour: number
}

export interface EmployeeDetailsParams {
  userInfo?: UserInfoItem
  employeeMails?: EmployeeMail[]
  employeePhones?: EmployeePhone[]
  employeeAddress?: string
  structureId?: string
  positionId?: string
  employeeType?: boolean
  workingStatus?: string
  rankingId?: string
  workingFromDate?: string
  workingToDate?: string
  salaryInDay?: number
  salaryInHour?: number
  salaryInMonth?: number
  standardWorkingHours?: number
  roleIds?: string[]
}

export interface EmployeeDataResponse {
  items: EmployeeItem[]
  pageIndex: number
  pageSize: number
  totalRecords: number
}

export interface AssignEmployeeRole {
  employeeId: string
  roleId: string
  assignRoles: string
}

export interface EmployeeAssignOrg {
  employeeId: string
  orgId: string
  employeeRoleIds: string
  structureId: string
  positionId: string
  rankingId: string
}

export interface EmployeeCombo {
  employeeId: string
  employeeName: string
  employeeCode: string
}

export interface EmployeeComboResponse {
  items: EmployeeCombo[]
  totalCount: number
  pageSize: number
  pageIndex: number
}

export interface EmployeeQueryParams {
  pageNum: number
  pageSize: number
  name?: string
  dob?: string
  structureId?: string
  rankingId?: string
  gender?: boolean
  workingStatus?: string
  positionId?: string
  employeeType?: boolean // true: employee, false: outsource
}

export interface InviteEmployeeParams {
  email: string
  employeeCode: string
  roleIds: string[]
  invitationDescription?: string
}

export interface EmployeeQueryParams {
  gender?: boolean
  name?: string
  code?: string
  dob?: string
  structureId?: string
  positionId?: string
  workingStatus?: string
  salary?: number
  salaryMinValue?: number
  salaryInHour?: number
  salaryInHourMinValue?: number
  pageNum: number
  pageSize: number
}

export interface EmployeePreference {
  employeeUid: string;
  employeeCode: string;
  employeeName: string;
  enableAutoCheckOut: boolean;
  updateBy: string;
  updateAt: string;
}

export interface AvatarResponse {
  items: AvatarItem[]
}

export interface AvatarItem {
  employeeId: string
  avatar: {
    avatarUrl: string
    avatarBase64: string
    avatarByteArr: string
    metadata: {
      contentType: string
      originalfilename: string
      uploadedat: string
    }
  }
}
export interface EmployeePreferenceResponse {
  items: EmployeePreference[];
}

export async function getEmployeeDataApi(params?: EmployeeQueryParams) {
  return useGet<EmployeeDataResponse>('v1/employee', params)
}
export async function getEmployeeById(id: number) {
  return useGet<EmployeeItem>(`v1/employee/${id}`)
}

export async function getEmployeePreference() {
  return useGet<EmployeePreferenceResponse>('v1/employee/preference');
}

export async function updateEmployeePreference(
  employeeUid: string,
  enableAutoCheckOut: boolean,
) {
  return usePut<EmployeePreference>(`v1/employee/${employeeUid}/preference`, {
    enableAutoCheckOut,
  });
}

export async function getEmployeeInfo() {
  return useGet<EmployeeItem>('v1/employee/employee-info')
}

export async function getSimpleEmployeeInfoApi() {
  return useGet<EmployeeComboResponse>('v1/employee/simple')
}

export async function getCurrentEmployeeInfo() {
  return useGet<EmployeeItem>('v1/employee/current/info')
}

export async function createEmployeeApi(employeeData: EmployeeItemParam) {
  return usePost<EmployeeItem>('v1/employee', employeeData)
}

export async function createAdmin(employeeData: EmployeeItemParam) {
  return usePost<EmployeeItem>('v1/employee/create-admin', employeeData)
}

export async function updateEmployeeApi(
  employeeId: string,
  params: EmployeeDetailsParams,
) {
  return usePut<EmployeeItem>(`v1/employee/${employeeId}`, params)
}

export async function deleteEmployeeApi(id: string) {
  return useDelete<any>(`v1/employee/${id}`)
}

export async function assignEmployeeRole(assignRoles: AssignEmployeeRole) {
  return usePut<AssignEmployeeRole>('v1/employee/assign-role', assignRoles)
}

export async function assignEmployeeOrg(employeeAssignOrg: EmployeeAssignOrg) {
  return useGet<EmployeeAssignOrg>('v1/employee/assign-org', employeeAssignOrg)
}

export async function inviteEmployeeApi(params: InviteEmployeeParams) {
  return usePost<boolean>('v1/employee/invitation', params)
}

export async function getCurrentRolesApi() {
  return useGet<RoleResponse>('v1/employee/current/role')
}

export async function getEmployeeApprovalAuthority() {
  return useGet<EmployeeDataResponse>('v1/employee/approval-authority')
}

export async function getEmployeeAvatar() {
  return useGet<AvatarResponse>('v1/employee/avatar')
}
