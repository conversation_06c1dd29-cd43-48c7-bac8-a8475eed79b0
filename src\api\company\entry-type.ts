export interface EntryTypeResponse {
  entryTypes: EntryTypeItem[];
  pageIndex: number;
  pageSize: number;
}

export interface EntryTypeItem {
  description: string;
  entryTypeId: string;
  entryTypeName: string;
}

interface EntryTypeLogsResponse {
  entityChanges: EntryTypeLogItem[];
  pageNum: number;
  pageSize: number;
  totalRecords: number;
}

export interface EntryTypeLogItem {
  action: string;
  auditLogId: string;
  changedList: EntryTypeChangedListItem[];
  entityId: string;
  description: string;
  modifiedTime: string;
  modifiedUserId: string;
  modifiedUserName: string;
}

export interface EntryTypeChangedListItem {
  fieldName: string;
  valueAfter: string | number | boolean | number[] | string[];
  valueBefore: string | number | boolean | number[] | string[];
}

export async function getEntryType(params?: any) {
  return useGet<EntryTypeResponse>('v1/cost/entrytype', params);
}

export async function getOneEntryType(id: string, params?: any) {
  return useGet<EntryTypeItem>(`v1/cost/entrytype/${id}`, params);
}

export async function getEntryTypeLogs(id: string, params?: any) {
  return useGet<EntryTypeLogsResponse>(`v1/cost/entrytype/${id}/logs`, params);
}

export async function deleteEntryType(id: string) {
  return useDelete(`v1/cost/entrytype/${id}`);
}

export function createEntryType(data: EntryTypeItem) {
  return usePost('v1/cost/entrytype', data);
}

export function updateEntryType(id: string, data: EntryTypeItem) {
  return usePut(`v1/cost/entrytype/${id}`, data);
}
