import { UploadFile } from 'ant-design-vue';
import { useOrg } from '~@/composables/org';

export interface ContractorItemResponse {
  items: ContractorItem[];
  pageIndex: number;
  pageSize: number;
}
interface PaymentRequestItem {
  constructionId: string;
  requestAmount: number;
  retentionAmount: number;
  releasedAmount: number;
  totalClaimedAmount: number;
  profitByRequestedAmount: number;
  profitByClaimedAmount: number;
  projectName: string;
  constructionName: string;
  requestPaymentFrom: string;
  requestPaymentTo: string;
  paymentDate: string;
}

interface ProjectItem {
  projectId: string;
  projectCode: string;
  projectName: string;
  address: string;
}

export interface ContractorItem {
  logo?: UploadFile;
  logoUrl?: string;
  contractorId: string;
  contractorCode: string;
  contractorName: string;
  contractorSubName?: string;
  description?: string;
  corporateNumber?: string;
  address?: string;
  phoneNumber?: string;
  email?: string;
  contactPerson: {
    name?: string;
    phoneNumber?: string;
    email?: string;
  };
  paymentRequests?: PaymentRequestItem[];
  projects?: ProjectItem[];
}

interface ContractorLogsResponse {
  entityChanges: ContractorLogItem[];
  pageNum: number;
  pageSize: number;
  totalRecords: number;
}

export interface ContractorChangedListItem {
  fieldName: string;
  valueAfter: string | number | boolean | number[] | string[];
  valueBefore: string | number | boolean | number[] | string[];
}

export interface ContractorLogItem {
  action: string;
  auditLogId: string;
  changedList: ContractorChangedListItem[];
  entityId: string;
  description: string;
  modifiedTime: string;
  modifiedUserId: string;
  modifiedUserName: string;
}

export interface GetContractorParams {
  keyword?: string;
  pageNum?: number;
  pageSize?: number;
}

interface GetContractorLogsParams {
  dateFrom?: string;
  dateTo?: string;
  action?: string;
  pageNum?: number;
  pageSize?: number;
}

export async function getContractor(params?: GetContractorParams) {
  return useGet<ContractorItemResponse>('v1/contractor', params);
}

export async function getContractorItem(params?: GetContractorParams) {
  return useGet<ContractorItemResponse>('v1/contractor', params);
}

export function getContractorLogo(id: string): string {
  const host = import.meta.env.VITE_APP_BASE_API ?? '';
  return `${host}/v1/contractor/${id}/logo?orgId=${useOrg().value}`;
}

export async function getOneContractor(
  id: string,
  params?: GetContractorParams
) {
  return useGet<ContractorItem>(`v1/contractor/${id}`, params);
}

export async function createContractor(data: Partial<ContractorItem>) {
  return usePost('v1/contractor', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

export async function updateContractor(
  id: string,
  data: Partial<ContractorItem>
) {
  return usePut(`v1/contractor/${id}`, data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

export async function deleteContractor(id: string) {
  return useDelete(`v1/contractor/${id}`);
}

export async function getContractorLogs(
  id: string,
  params?: GetContractorLogsParams
) {
  return useGet<ContractorLogsResponse>(`v1/contractor/${id}/logs`, params);
}
