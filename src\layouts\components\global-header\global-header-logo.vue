<script setup lang="ts">
import { useLayoutState } from '../../basic-layout/context'

const { logo, layout, isMobile } = useLayoutState()
const cls = computed(() => ({
  'ant-pro-global-header-logo': layout.value === 'mix' || isMobile.value,
  'ant-pro-top-nav-header-logo': layout.value === 'top' && !isMobile.value,
}))
</script>

<template>
  <div :class="cls">
    <a c-primary href="#" target="_blank">
      <img :src="logo" class="d-none d-sm-flex">
    </a>
  </div>
</template>

<style lang="less">
.ant-pro-global-header-logo a{
  padding-top: 10px;
}
</style>
