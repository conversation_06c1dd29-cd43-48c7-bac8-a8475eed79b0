<script setup lang="ts">
import { useI18n } from 'vue-i18n'

defineProps<{
  totalWorktimeOfDay: number
  totalOvertimeOfDay: number
}>()
const { t } = useI18n()
</script>

<template>
  <a-row>
    <a-col align="center" :span="24">
      <span class="text-[1.5rem] font-medium">{{ t('TotalTitle') }}</span>
    </a-col>
  </a-row>

  <!-- Total time stats -->
  <a-row :gutter="0" justify="center" class="mt-0 p-[18px]">
    <!-- Work time -->
    <a-col :xs="12" :sm="12" class="text-center">
      <div class="flex flex-col items-center gap-y-4">
        <a-avatar class="icon-circle-time" src="/worktime.svg" />
        <span class="total-timer text-base sm:text-lg">
          {{ Math.round(totalWorktimeOfDay * 100) / 100 }}
        </span>
        <span class="total-text text-sm sm:text-base">
          {{ t("dashboard.totalTime") }}
        </span>
      </div>
    </a-col>

    <!-- Overtime -->
    <a-col :xs="12" :sm="12" class="text-center">
      <div class="flex flex-col items-center gap-y-4">
        <a-avatar class="icon-circle-over" src="/overtime.svg" />
        <span class="total-timer text-base sm:text-lg">
          {{ Math.round(totalOvertimeOfDay * 100) / 100 }}
        </span>
        <span class="total-text text-sm sm:text-base">
          {{ t("dashboard.overtime") }}
        </span>
      </div>
    </a-col>
  </a-row>
</template>

<style scoped>
.icon-circle-time{
  background-color: #CEE1F1;
  padding: 1.25rem;
  width: 4.5rem;
  height: 4.5rem;
}

.total-timer{
  font-size: 2rem;
  font-weight: 500;
}

.total-text{
  color: #74797A;
  font-size: 1rem;
  font-weight: 500;
}

.icon-circle-over {
  background-color: #FCBF91;
  padding: 1rem;
  width: 4.5rem;
  height: 4.5rem;
}
</style>
