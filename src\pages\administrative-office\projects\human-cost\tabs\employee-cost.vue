<script lang="ts" setup>
import {
  LeftOutlined,
  RightOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';
import type {
  ColumnGroupType,
  ColumnType,
  TablePaginationConfig,
} from 'ant-design-vue/es/table';
import type { FilterValue } from 'ant-design-vue/es/table/interface';
import dayjs from 'dayjs';
import { usePagination } from 'vue-request';
import type {
  RankingDataResponse,
  RankingItem,
} from '~@/api/company/ranking';
import {
  getRankingEmployeeListApi,
} from '~@/api/company/ranking';

type ColumnItemType<T> = ColumnGroupType<T> | ColumnType<T>;

interface Params {
  pageNum?: number;
  pageSize?: number;
  keyword?: string;
  dateFrom?: string;
  dateTo?: string;
}

const { t } = useI18n();
const searchForm = ref<Params>({
  pageSize: 10,
  pageNum: 1,
  dateFrom: dayjs().startOf('month').format('YYYY-MM-DD'),
  dateTo: dayjs().endOf('month').format('YYYY-MM-DD'),
});
const searchDate = ref<dayjs.Dayjs>(dayjs());

// Add disabledRightArrow computed property
const disabledRightArrow = computed(() => {
  // Disable right arrow if current date is in the future or current month
  const currentDate = dayjs();
  const selectedDate = searchDate.value;
  return selectedDate.isSame(currentDate, 'month') || selectedDate.isAfter(currentDate, 'month');
});

async function queryData(params?: Params) {
  const { data } = await getRankingEmployeeListApi(params);
  if (!data)
    return ref<RankingDataResponse>({ items: [] }).value;

  return ref(data).value;
}

const {
  data: dataSource,
  loading,
  total,
  current,
  run,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [searchForm.value],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
});

const pagination = computed(() => ({
  total: total.value ?? 10,
  current: current.value,
  pageSize: pageSize.value,
  showSizeChanger: true,
}));

const columns = computed<ColumnItemType<RankingItem>[]>(() => [
  { title: t('form.employee-name'), dataIndex: 'employeeName', width: 200 },
  { title: t('form.rank-name'), dataIndex: 'rankName', width: 200 },
  { title: t('form.cost-amount'), dataIndex: 'costAmount', width: 200 },
  {
    title: t('form.avarage-cost-amount'),
    dataIndex: 'averageCostAmount',
    width: 200,
  },
]);

function handleTableChange(pagination: TablePaginationConfig, filters: Record<string, FilterValue>) {
  searchForm.value.pageSize = pagination.pageSize;
  searchForm.value.pageNum = pagination.current;
  run({ ...searchForm.value, ...filters });
}

function handlePaginationChange(page: number, pageSize: number) {
  searchForm.value.pageSize = pageSize;
  searchForm.value.pageNum = page;
  run(searchForm.value);
}

function onSearch() {
  searchForm.value.dateFrom = dayjs(searchDate.value)
    .startOf('month')
    .format('YYYY-MM-DD');
  searchForm.value.dateTo = dayjs(searchDate.value)
    .endOf('month')
    .format('YYYY-MM-DD');
  handleTableChange(
    { pageSize: searchForm.value.pageSize ?? 10, current: 1 },
    {},
  );
}

function handleLeftArrowClick() {
  searchDate.value = dayjs(searchDate.value).subtract(1, 'month');
  onSearch();
}

function handleRightArrowClick() {
  searchDate.value = dayjs(searchDate.value).add(1, 'month');
  onSearch();
}

onMounted(async () => {
  // Removed getRankingMapMethodApi call
});

onUnmounted(() => {
  // Removed progressInterval clearing
});
</script>

<template>
  <page-container>
    <a-row :wrap="false" :gutter="[12, 12]" class="h-[calc(100vh-160px)] flex-col">
      <a-col flex="none" span="24">
        <a-row :gutter="[12, 12]">
          <a-col span="24">
            <a-row :gutter="[12, 12]">
              <a-col flex="none">
                <a-input
                  v-model:value="searchForm.keyword" :placeholder="t('search')" style="width: 25rem" allow-clear
                  @press-enter="onSearch"
                >
                  <template #prefix>
                    <SearchOutlined class="text-gray-500" />
                  </template>
                </a-input>
              </a-col>
              <a-col flex="auto">
                <div class="flex gap-8 justify-end">
                  <div class="flex items-center">
                    <LeftOutlined
                      class="flex justify-center w-6 h-6 bg-white rounded-full"
                      @click="handleLeftArrowClick"
                    />
                    <a-date-picker
                      v-model:value="searchDate" picker="month" :allow-clear="false" :format="(value: dayjs.Dayjs) =>
                        `${value
                          .startOf('month')
                          .format('DD/MM/YYYY')} - ${value
                          .endOf('month')
                          .format('DD/MM/YYYY')}`
                      "
                      class="search-date" @change="onSearch"
                    />
                    <RightOutlined
                      class="flex justify-center w-6 h-6 bg-white rounded-full"
                      :style="{
                        opacity: disabledRightArrow ? 0.5 : 1,
                        cursor: disabledRightArrow ? 'not-allowed' : 'pointer',
                      }" @click="handleRightArrowClick"
                    />
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>
          <a-col span="24">
            <a-table
              class="tableEmployeeCost" :scroll="{ x: 'max-content', y: 'calc(100vh - 320px)' }"
              :columns="columns" :data-source="dataSource?.items" :loading="loading" :pagination="false"
              row-key="employeeRankId" @change="handleTableChange"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'costAmount'">
                  {{ record.costAmount != null ? t('currency.unit') + record.costAmount : t('form.empty') }}
                </template>
                <template v-if="column.dataIndex === 'averageCostAmount'">
                  {{ record.averageCostAmount != null ? t('currency.unit') + record.averageCostAmount : t('form.empty') }}
                </template>
              </template>
            </a-table>
          </a-col>
        </a-row>
      </a-col>
      <a-col flex="auto" span="24">
        <div class="h-full flex items-end">
          <a-row justify="space-between" class="mt-4 w-full">
            <a-col>
              <a-pagination
                class="pagination" :total="pagination.total" :current="pagination.current"
                :page-size="pagination.pageSize" @change="handlePaginationChange"
              />
            </a-col>
            <a-col>
              <a-row :gutter="[12, 12]" justify="center" align="middle">
                <a-col>{{ t('show') }}</a-col>
                <a-col>
                  <a-pagination
                    class="pagination pagination-right" :total="pagination.total"
                    :current="pagination.current" :page-size="pagination.pageSize" show-size-changer
                    :build-option-text="(props: any) => props.value" @change="handlePaginationChange"
                  />
                </a-col>
                <a-col>{{ t('entries') }}</a-col>
              </a-row>
            </a-col>
          </a-row>
        </div>
      </a-col>
    </a-row>
  </page-container>
</template>

<style lang="less" scoped>
.pagination {
  :deep(.ant-pagination-item-active) {
    background: #f99649;
    border-color: #f99649;

    a {
      color: #fff;
    }
  }

  :deep(.ant-select-selector) {
    background: #f99649;
    border-color: #f99649 !important;

    .ant-select-selection-item {
      color: #fff;
    }
  }

  :deep(.ant-select-arrow) {
    color: #fff;
  }
}

.pagination-right {
  :deep(.ant-pagination-prev) {
    display: none;
  }

  :deep(.ant-pagination-next) {
    display: none;
  }

  :deep(.ant-pagination-item) {
    display: none;
  }

  :deep(.ant-pagination-options) {
    margin: 0;
  }

  :deep(.ant-pagination-jump-next) {
    display: none;
  }

  :deep(.ant-pagination-jump-prev) {
    display: none;
  }
}

.search-date {
  border: none;
  background: none;
  box-shadow: none;

  :deep(.ant-picker-suffix) {
    display: none;
  }

  :deep(input) {
    cursor: pointer;
    width: 165px;
  }
}
</style>
