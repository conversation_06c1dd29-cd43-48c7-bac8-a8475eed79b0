<script setup lang="ts">
import { pick } from '@v-c/utils'
import { notification } from 'ant-design-vue'
import { UAParser } from 'ua-parser-js'
import BasicLayout from './basic-layout/index.vue'
import SettingDrawer from './components/setting-drawer/index.vue'
import MultiTab from './multi-tab/index.vue'
import { animationNameList } from '~@/config/default-setting'
import { onMessageListener } from '~@/config/firebase'
import { createNotificationDevice } from '~@/api/company/notification'
import { getDeviceId } from '~@/utils/fingerprinting'
import { useFcmToken } from '~@/composables/org'

defineOptions({
  name: 'ProLayout',
})
const appStore = useAppStore()
const { layoutSetting } = storeToRefs(appStore)
const userStore = useUserStore()
const layoutMenu = useLayoutMenu()
const { t } = useI18nLocale()
const { selectedKeys, openKeys } = storeToRefs(layoutMenu)
const { isMobile, isPad } = useQueryBreakpoints()

watch(isPad, (val) => {
  if (val)
    appStore.toggleCollapsed(true)
  else appStore.toggleCollapsed(false)
})

watch(isMobile, (val) => {
  if (val)
    appStore.toggleCollapsed(true)
  else appStore.toggleCollapsed(false)
})

const layoutProps = computed(() =>
  pick(appStore.layoutSetting, [
    'fixedHeader',
    'fixedSider',
    'splitMenus',
    'menuHeader',
    'header',
    'menu',
    'layout',
    'footer',
    'contentWidth',
    'compactAlgorithm',
  ]),
)


function handleMessages() {
  onMessageListener()
    .then((payload) => {
      notification.info({
        message: payload.notification?.title,
        description: payload.notification?.body,
        duration: 5,
      })

      handleMessages()
    })
    .catch(() => {
      setTimeout(handleMessages, 5000)
    })
}

onMounted(async () => {
  const parser = new UAParser()
  const result = parser.getResult()
  const token = useFcmToken().value

  if (token) {
    await createNotificationDevice({
      firebaseToken: token,
      deviceId: await getDeviceId(),
      appVersion: import.meta.env.VITE_APP_VERSION,
      osVersion: result.os.version,
      platform: result.os.name,
    })
  }

  handleMessages()
})
</script>

<template>
  <BasicLayout
    :collapsed="layoutSetting.collapsed"
    :theme="layoutSetting.theme"
    :menu-data="userStore.menuData"
    v-bind="layoutProps"
    :selected-keys="selectedKeys"
    :open-keys="layoutSetting.layout === 'top' ? [] : openKeys"
    :copyright="layoutSetting.copyright"
    :is-mobile="isMobile"
    :logo="layoutSetting.logo"
    :title="layoutSetting.title"
    :accordion-mode="layoutSetting.accordionMode"
    :left-collapsed="layoutSetting.leftCollapsed"
    :header-height="layoutSetting.headerHeight"
    @update:open-keys="layoutMenu.handleOpenKeys"
    @update:selected-keys="layoutMenu.handleSelectedKeys"
    @update:collapsed="appStore.toggleCollapsed"
  >
    <template #headerActions>
      <SelectLang />
      <NotificateAlert />
      <UserAvatar />
      <!-- <GithubLink />
      <template v-if="!isMobile">
        <GiteeLink />
        <DocLink />
      </template> -->
    </template>
    <template #contentPrefix>
      <MultiTab v-if="layoutSetting.multiTab" />
    </template>

    <template #renderFooterLinks />

    <!-- <a-watermark
      h-full
      flex
      flex-col
      flex-1
      :content="
        !layoutSetting.watermark ? '' : layoutSetting.title ?? 'Kantoku'
      "
    > -->
    <RouterView>
      <template #default="{ Component }">
        <component :is="Component" />
      </template>
    </RouterView>
    <!-- </a-watermark> -->
  </BasicLayout>
  <SettingDrawer
    v-model:open="layoutSetting.drawerVisible"
    :t="t"
    :theme="layoutSetting.theme"
    :color-primary="layoutSetting.colorPrimary"
    :color-weak="layoutSetting.colorWeak"
    :multi-tab="layoutSetting.multiTab"
    :multi-tab-fixed="layoutSetting.multiTabFixed"
    :animation-name-list="animationNameList"
    :animation-name="layoutSetting.animationName"
    :keep-alive="layoutSetting.keepAlive"
    :accordion-mode="layoutSetting.accordionMode"
    :left-collapsed="layoutSetting.leftCollapsed"
    :watermark="layoutSetting.watermark"
    v-bind="layoutProps"
    :layout-setting="layoutSetting"
    @setting-change="appStore.changeSettingLayout"
  />
</template>

<style lang="less">
.header-actions {
  .ant-space-item {
    height: 100%;
  }
  .ant-space-item:not(:last-child) {
    display: flex;
    align-items: center;
  }
}
</style>
