<!-- eslint-disable curly -->
<!-- eslint-disable antfu/top-level-function -->
<script setup lang="ts">
import { LockOutlined } from '@ant-design/icons-vue'
import { AxiosError } from 'axios'
import type { Rule } from 'ant-design-vue/es/form'
import { type FormInstance, message } from 'ant-design-vue'
import { changePasswordApi } from '~/api/common/login'
import type { ChangePasswordParams } from '~@/api/common/login'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'
import router from '~@/router'
import { getQueryParam } from '~@/utils/tools'
import logger from '~@/utils/logger'

// const message = useMessage();
const notification = useNotification()
const { t } = useI18n()
const formRef = ref<FormInstance>()
const submitLoading = shallowRef(false)
const errorAlert = shallowRef(false)
const isFormValid = ref(false)

const formState = reactive<ChangePasswordParams>({
  confirmNewPassword: '',
  newPassword: '',
  oldPassword: '',
})

async function updatePassword(params: ChangePasswordParams) {
  try {
    const { data, status, code } = await changePasswordApi(params)
    if (status === ResponseStatusEnum.SUCCESS) {
      logger.log('data', data)
      notification.success({
        message: '成功でした',
        duration: 3,
      })
      return true
    }
    else {
      logger.error(t(code))
      message.error(t(code))
      return false
    }
  }
  catch (e) {
    if (e instanceof AxiosError) {
      errorAlert.value = true
    }
    return false
  }
}

async function submit() {
  submitLoading.value = true
  try {
    await formRef.value?.validate()
    const params = {
      oldPassword: formState.oldPassword,
      newPassword: formState.newPassword,
      confirmNewPassword: formState.confirmNewPassword,
    } as unknown as ChangePasswordParams

    const userStore = useUserStore()
    await userStore.getEmployeeInfo()
    // if (isChangedPass.value === undefined || isChangedPass.value === true) {
    //   if (token.value !== null) {
    //     const tokenForgotPass = token.value
    //     token.value = null
    //     result.value = await forgotPasswordApi(tokenForgotPass, params)
    //   }
    //   else {
    //     // logger.log("Token is not assigned for forgotPassword api")
    //   }
    // }
    // else {
    //   result.value = await changePasswordApi(params)
    // }
    const ressult = await updatePassword(params)
    if (ressult) {
      const redirect = getQueryParam('redirect', '/login')
      logger.log('router', router)
      router.push({
        path: redirect,
        replace: true,
      })
    }
    submitLoading.value = false

  //   if (result.value.Success) {
  //     // token.value = Data?.AccessToken;
  //     notification.success({
  //       message: '成功でした',
  //       duration: 3,
  //     })
  //     const redirect = getQueryParam('redirect', '/login')
  //     router.push({
  //       path: redirect,
  //       replace: true,
  //     })
  //   }
  //   else {
  //     errorAlert.value = true
  //     submitLoading.value = false
  //   }
  }
  catch (e) {
    if (e instanceof AxiosError)
      errorAlert.value = true
    submitLoading.value = false
  }
}

const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{6,}$/

const validatePass = async (_rule: Rule, value: string) => {
  if (value === '') {
    isFormValid.value = false
    return Promise.reject(new Error('パスワードを入力してください'))
  }
  else if (!regex.test(formState.newPassword)) {
    isFormValid.value = false
    return Promise.reject(new Error('パスワードには、大文字のアルファベット、小文字のアルファベット、数字が少なくとも1つ含まれている必要があります。そして、パスワードは少なくとも6文字でなければなりません'))
  }
  else {
    if (formState.confirmNewPassword !== '') {
      formRef.value?.validateFields('confirmNewPassword')
    }
    return Promise.resolve()
  }
}

const validateConfirmPassword = async (_rule: Rule, value: string) => {
  if (value === '') {
    isFormValid.value = false
    return Promise.reject(new Error('パスワードを再度入力してください'))
  }
  else if (value !== formState.newPassword) {
    isFormValid.value = false
    return Promise.reject(new Error('二つの入力が一致しません！'))
  }
  else {
    isFormValid.value = true
    return Promise.resolve()
  }
}
const rules: Record<string, Rule[]> = {
  newPassword: [{ required: true, validator: validatePass, trigger: 'blur' }],
  confirmNewPassword: [{ validator: validateConfirmPassword, trigger: 'blur' }],
}
</script>

<template>
  <div class="login-container">
    <div class="login-content flex-center">
      <div class="box-border flex min-h-[520px]">
        <div class="ant-pro-form-login-main-right px-5 w-[400px] flex-center flex-col relative z-11">
          <div class="text-center py-6 text-2xl">{{ t("pages.login.change.password.placeholder") }}</div>
          <a-form ref="formRef" :rules="rules" class="w-full" :model="formState">
            <a-form-item has-feedback name="oldPassword">
              <a-input
                v-model:value="formState.oldPassword" allow-clear
                :placeholder="t('oldPassword')" size="large"/>
            </a-form-item>
            <a-form-item has-feedback name="newPassword">
              <a-input-password
                v-model:value="formState.newPassword" allow-clear
                :placeholder="t('newPassword')" size="large">
                <template #prefix>
                  <LockOutlined />
                </template>
              </a-input-password>
            </a-form-item>
            <a-form-item has-feedback name="confirmNewPassword">
              <a-input-password
                v-model:value="formState.confirmNewPassword" allow-clear size="large"
                :placeholder="t('confirmNewPassword')">
                <template #prefix>
                  <LockOutlined />
                </template>
              </a-input-password>
            </a-form-item>
            <a-form-item>
              <a-button :disabled="!isFormValid" type="primary" block size="large" :loading="submitLoading" @click="submit">
                {{ t("button.change") }}
              </a-button>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped></style>
