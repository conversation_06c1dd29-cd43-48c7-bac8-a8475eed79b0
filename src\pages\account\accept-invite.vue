<script setup lang="ts">
import { onMounted, ref } from 'vue'
import {
  CheckOutlined,
  ClockCircleOutlined,
  CloseOutlined,
} from '@ant-design/icons-vue'
import type { AcceptInvitationParams, InvitationItem } from '~@/types/account'
import { acceptInvitationApi, getInvitationsApi } from '~@/api/account/account'
import { useAuth } from '~@/composables/account/useAuth'
import type { SignOnParams } from '~@/api/common/login'
import { signOnApi } from '~@/api/common/login'
import { getQueryParam } from '~@/utils/tools'
import { useOrg } from '~@/composables/org'

const { clearToken } = useAuth()

const { t } = useI18n()
const router = useRouter()
// State
const invitations = ref<InvitationItem[]>([])
const isLoading = ref(true)
const loadingStates = ref<Record<string, { accept: boolean; reject: boolean }>>({})
const isInvitationAccept = ref<Record<string, { isAccept: boolean; isReject: boolean }>>({})
const messageNotification = useNotification()

async function handleAccept(invitationId: string, isAccept: boolean) {
  try {
    loadingStates.value[invitationId] = { ...loadingStates.value[invitationId], accept: true }
    const params: AcceptInvitationParams = {
      invitationId,
      isAccept,
    }
    const { status, message } = await acceptInvitationApi(params)
    if (status === 200) {
      isInvitationAccept.value[invitationId] = { isAccept, isReject: !isAccept }
      messageNotification.success({
        message: isAccept ? t('message.accept-invitation') : t('message.reject-invitation'),
      })
    }
    else {
      messageNotification.error({
        message: message || t('message.error-accept-invitation'),
      })
    }
  }
  catch (error) {
    messageNotification.error({
      message: t('message.error-accept-invitation'),
    })
  }
  finally {
    loadingStates.value[invitationId] = { ...loadingStates.value[invitationId], accept: false }
  }
}

function formatDate(date: string) {
  return new Date(date).toLocaleDateString('ja-JP', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

function getStatusColor(isAccept: boolean) {
  return isAccept ? 'success' : 'error'
}

function getStatusText(isAccept: boolean) {
  return isAccept ? 'Đã chấp nhận' : 'Đã từ chối'
}

async function getInvitations() {
  const { data, status, message } = await getInvitationsApi()
  if (status === 200) {
    invitations.value = data?.items || []
    isLoading.value = false
  }
  else {
    messageNotification.error({ message })
    isLoading.value = false
  }
}

function handleClose() {
  clearToken()
  router.push('/login')
}

async function signOn(orgId: string) {
  const signOnParams: SignOnParams = {
    orgId,
  }
  const { data, status, message } = await signOnApi(signOnParams)
  if (status === 200) {
    const token = useAuthorization()
    token.value = `Bearer ${data?.accessToken}`
    const org = useOrg();
    org.value = orgId;
    const redirect = getQueryParam('redirect', '/')
    router.push({
      path: redirect,
      replace: true,
    })
  }
  else {
    messageNotification.error({ message })
  }
}

// Lifecycle
onMounted(async () => {
  await getInvitations()
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50">
    <div class="p-6 max-w-7xl mx-auto">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">
          {{ t('title.invite-to-organization') }}
        </h1>
        <p class="text-gray-600">
          {{ t('description.invite-to-organization') }}
        </p>
      </div>

      <a-card
        :bordered="false"
        class="overflow-hidden bg-white/80 backdrop-blur-sm shadow-xl rounded-xl"
      >
        <div v-if="isLoading" class="flex justify-center py-20">
          <a-spin size="large" />
        </div>
        <template v-else>
          <div v-if="invitations.length === 0" class="text-center py-20">
            <a-empty :description="t('message.no-invitations')" />
          </div>

          <div v-else class="space-y-4">
            <a-card
              v-for="org in invitations"
              :key="org.invitationId"
              class="transition-all duration-300 hover:shadow-md border border-gray-100"
            >
              <div class="flex items-center justify-between flex-wrap md:flex-nowrap gap-4">
                <div class="flex items-center space-x-4">
                  <div class="w-16 h-16 rounded-lg bg-gradient-to-br from-blue-100 to-indigo-100 flex items-center justify-center">
                    <span class="text-2xl font-bold text-indigo-600">
                      {{ org.orgName.charAt(0) }}
                    </span>
                  </div>
                  <div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-1">
                      {{ org.orgName }}
                    </h3>
                    <p class="text-gray-600">
                      {{ org.description }}
                    </p>
                    <div class="flex items-center space-x-2 mt-2">
                      <ClockCircleOutlined class="text-gray-400" />
                      <span class="text-sm text-gray-500">
                        {{ t('message.expire-time') }}
                      </span>
                      <span class="text-sm text-gray-500">
                        {{ formatDate(org.expireTime) }}
                      </span>
                    </div>
                  </div>
                </div>

                <div

                  class="flex items-center space-x-3 ml-auto"
                >
                  <div
                    v-if="!isInvitationAccept[org.invitationId]?.isReject
                      && !isInvitationAccept[org.invitationId]?.isAccept"
                    class="flex items-center space-x-3"
                  >
                    <a-button
                      type="default"
                      danger
                      :loading="loadingStates[org.invitationId]?.reject"
                      class="hover:scale-105 transition-transform"
                      @click="handleAccept(org.invitationId, false)"
                    >
                      <template #icon>
                        <CloseOutlined />
                      </template>
                      {{ t('button.reject') }}
                    </a-button>

                    <a-button
                      type="primary"
                      :loading="loadingStates[org.invitationId]?.accept"
                      class="hover:scale-105 transition-transform"
                      @click="handleAccept(org.invitationId, true)"
                    >
                      <template #icon>
                        <CheckOutlined />
                      </template>
                      {{ t('button.accept') }}
                    </a-button>
                  </div>
                  <div v-else>
                    <a-tag
                      :color="getStatusColor(isInvitationAccept[org.invitationId]?.isAccept)"
                      class="px-4 py-1"
                    >
                      {{ getStatusText(isInvitationAccept[org.invitationId]?.isAccept) }}
                    </a-tag>
                    <a-button v-if="isInvitationAccept[org.invitationId]?.isAccept" type="primary" @click="signOn(org.orgId)">
                      {{ t('button.access') }}
                    </a-button>
                  </div>
                </div>
              </div>
            </a-card>
          </div>
        </template>
        <div class="flex justify-end mt-4">
          <a-button type="primary" @click="handleClose">
            {{ t('button.back-to-login-page') }}
          </a-button>
        </div>
      </a-card>
    </div>
  </div>
</template>

<style scoped>
.custom-pagination :deep(.ant-pagination-item-active) {
  background-color: #4f46e5;
  border-color: #4f46e5;
}

.custom-pagination :deep(.ant-pagination-item-active a) {
  color: white;
}

.custom-message {
  border-radius: 8px;
  padding: 12px 24px;
}

/* Hover effects */
.ant-card {
  transition: all 0.3s ease;
}

.ant-card:hover {
  transform: translateY(-2px);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c7c7c7;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
