<script lang="ts" setup>
import type { TablePaginationConfig } from 'ant-design-vue'
import { message } from 'ant-design-vue'
import {
  ArrowRightOutlined,
  CloseOutlined,
  DownOutlined,
  LeftOutlined,
  RightOutlined,
} from '@ant-design/icons-vue'
import type {
  FilterValue,
} from 'ant-design-vue/es/table/interface'
import { usePagination } from 'vue-request'
import dayjs from 'dayjs'
import _, { cloneDeep } from 'lodash'
import {
  RequestStatus,
  RequestStatusEnum,
} from '~@/enums/system-status-enum'
import type {
  SystemStatusEnumKey,
} from '~@/enums/system-status-enum'
import {
  approveRequestApi,
  getApproverRequestApi,
  rejectRequestApi,
} from '~@/api/dashboard/date-off-request'
import type {
  RequestDataResponse,
} from '~@/api/dashboard/date-off-request'
import type {
  LeaveTypeItem,
  RequestTypeItem,
} from '~@/api/common/common'
import {
  getLeaveTypeList,
  getRequestTypeList,
} from '~@/api/common/common'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'
import logger from '~@/utils/logger'

interface Params {
  pageNum?: number
  pageSize?: number
  keyword?: string
  statusCode?: string[]
  fromDate?: string
  toDate?: string
  date?: string
  requestTypeCode?: string
}

const initSearchForm: Params = {
  pageSize: 10,
  pageNum: 1,
  fromDate: dayjs().startOf('month').format('YYYY-MM-DD'),
  toDate: dayjs().endOf('month').format('YYYY-MM-DD'),
  requestTypeCode: undefined,
  statusCode: undefined,
}

const visibleFilter = ref(false)
const requestTypes = ref<RequestTypeItem[]>([])
const leaveTypes = ref<LeaveTypeItem[]>([])
const { t } = useI18n()
const searchForm = ref<Params>({ ...cloneDeep(initSearchForm) })
const searchDate = ref<dayjs.Dayjs>(dayjs())

async function queryData(params?: Params) {
  const { data } = await getApproverRequestApi(params)
  if (!data)
    return ref<RequestDataResponse>({ items: [] }).value

  return ref(data).value
}

const {
  data: dataSource,
  loading,
  total,
  current,
  run,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [searchForm.value],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const pagination = computed(() => ({
  total: total.value ?? 10,
  current: current.value,
  pageSize: pageSize.value,
  showSizeChanger: true,
}))

function resetFilter() {
  Object.assign(searchForm.value, cloneDeep(initSearchForm))
}

function handleTableChange(pagination: TablePaginationConfig, filters: Record<string, FilterValue>) {
  searchForm.value.pageSize = pagination.pageSize
  searchForm.value.pageNum = pagination.current
  run({ ...searchForm.value, ...filters })
}

function handlePaginationChange(page: number, pageSize: number) {
  searchForm.value.pageSize = pageSize
  searchForm.value.pageNum = page
  run(searchForm.value)
}

function onSearch() {
  searchForm.value.fromDate = dayjs(searchDate.value)
    .startOf('month')
    .format('YYYY-MM-DD')
  searchForm.value.toDate = dayjs(searchDate.value)
    .endOf('month')
    .format('YYYY-MM-DD')
  handleTableChange(
    { pageSize: searchForm.value.pageSize ?? 10, current: 1 },
    {},
  )
}

const calculateDate = computed(() => {
  return (requestFrom?: string, requestTo?: string) => {
    if (!requestFrom || !requestTo) {
      return {
        days: 0,
        hours: 0,
        minutes: 0,
      }
    }

    const days = dayjs(requestTo).diff(dayjs(requestFrom), 'day')
    const hours = dayjs(requestTo).diff(dayjs(requestFrom), 'hour')
    const minutes = dayjs(requestTo).diff(dayjs(requestFrom), 'minute')

    return {
      days,
      hours: hours - days * 24,
      minutes: minutes - hours * 60,
    }
  }
})

async function handleApprove(id: string) {
  try {
    const approve = await approveRequestApi(id)
    if (approve.status !== ResponseStatusEnum.SUCCESS)
      return

    if (dataSource.value) {
      Object.assign(
        dataSource.value.items.filter(item => item.requestId === id)[0],
        approve.data,
      )
    }
    message.success(approve.message)
  }
  catch (error) {
    logger.error(error)
  }
}

async function handleReject(id: string) {
  try {
    const reject = await rejectRequestApi(id)
    if (reject.status !== ResponseStatusEnum.SUCCESS)
      return

    if (dataSource.value) {
      Object.assign(
        dataSource.value.items.filter(item => item.requestId === id)[0],
        reject.data,
      )
    }

    message.success(reject.message)
  }
  catch (error) {
    logger.error(error)
  }
}

onMounted(async () => {
  const res = await Promise.all([
    getRequestTypeList(),
    getLeaveTypeList(),
  ])
  requestTypes.value = res[0].data?.items ?? []
  leaveTypes.value = res[1].data?.items ?? []
})

watch(
  () => t('locale'),
  () => onSearch(),
)
</script>

<template>
  <page-container>
    <div class="h-[calc(100vh-100px)] flex flex-col gap-3">
      <!-- Header Section -->
      <div class="flex-none">
        <div class="space-y-3">
          <!-- Title and Controls -->
          <div class="flex items-center justify-between">
            <div class="font-bold text-xl">
              {{ t('request-management') }}
            </div>
            <div class="flex gap-4 items-center">
              <!-- Filters -->
              <div class="flex gap-2 items-center">
                <a-popover
                  v-model:open="visibleFilter"
                  trigger="click"
                  placement="bottomRight"
                  :arrow="false"
                  :overlay-style="{ width: '300px' }"
                >
                  <template #title>
                    <div class="flex items-center justify-between p-2">
                      <span class="flex items-center gap-1">
                        <CarbonFilterNew size="14" />
                        {{ t('button.filters') }}
                      </span>
                      <a-button
                        type="text"
                        size="small"
                        class="flex items-center justify-center"
                        @click="visibleFilter = false"
                      >
                        <CloseOutlined />
                      </a-button>
                    </div>
                    <a-divider class="m-0" />
                  </template>
                  <template #content>
                    <a-form
                      layout="vertical"
                      class="mx-auto p-2"
                      :model="searchForm"
                    >
                      <div class="space-y-3">
                        <a-form-item
                          class="mb-2"
                          :label="$t('form.request-type')"
                        >
                          <a-select
                            v-model:value="searchForm.requestTypeCode"
                            allow-clear
                            :placeholder="$t('form.request-type')"
                            :options="requestTypes"
                            :field-names="{
                              label: 'requestTypeName',
                              value: 'requestTypeCode',
                            }"
                          />
                        </a-form-item>
                        <a-divider class="mb-0 mt-0" />
                        <a-form-item
                          class="mb-2"
                          :label="$t('form.approval-status')"
                        >
                          <a-select
                            v-model:value="searchForm.statusCode"
                            allow-clear
                            :placeholder="$t('form.approval-status')"
                          >
                            <a-select-option
                              v-for="item in RequestStatus"
                              :key="item.value"
                              :value="item.value"
                            >
                              {{ t(item.value) }}
                            </a-select-option>
                          </a-select>
                        </a-form-item>
                        <a-divider class="mb-0 mt-0" />
                        <div class="p-2 text-right">
                          <div class="flex justify-between">
                            <a-button @click="resetFilter">
                              {{ $t('button.reset') }}
                            </a-button>
                            <a-button
                              type="primary"
                              @click=" {
                                visibleFilter = false;
                                onSearch();
                              }
                              "
                            >
                              {{ $t('button.apply') }}
                            </a-button>
                          </div>
                        </div>
                      </div>
                    </a-form>
                  </template>
                  <a-button class="flex flex-items-center">
                    <CarbonFilterNew size="16" class="mr-1" />
                    {{ t('button.filter') }} <DownOutlined />
                  </a-button>
                </a-popover>
                <a-button
                  @click="
                    () => {
                      resetFilter();
                      onSearch();
                    }
                  "
                >
                  {{ t('button.resetFilter') }}
                </a-button>
              </div>
              <!-- Date Navigation -->
              <div class="flex items-center">
                <LeftOutlined
                  class="flex justify-center w-6 h-6 bg-white rounded-full"
                  @click="
                    searchDate = dayjs(searchDate).subtract(1, 'month');
                    onSearch();
                  "
                />
                <a-date-picker
                  v-model:value="searchDate"
                  picker="month"
                  :allow-clear="false"
                  :format="
                    (value: dayjs.Dayjs) =>
                      `${value
                        .startOf('month')
                        .format('YYYY/MM/DD')} - ${value
                        .endOf('month')
                        .format('YYYY/MM/DD')}`
                  "
                  class="search-date"
                  @change="onSearch"
                />
                <RightOutlined
                  class="flex justify-center w-6 h-6 bg-white rounded-full"
                  @click="
                    searchDate = dayjs(searchDate).add(1, 'month');
                    onSearch();
                  "
                />
              </div>
              <carbon-calendar-filter />
            </div>
          </div>
        </div>
      </div>

      <!-- Content Section -->
      <div class="flex-1 overflow-hidden">
        <div v-if="loading" class="flex justify-center items-center h-64">
          <a-spin size="large" />
        </div>
        <div v-else-if="!dataSource?.items?.length" class="flex justify-center items-center h-64 text-gray-500">
          <a-empty :image-style="{ height: '100px' }" />
        </div>
        <div v-else class="h-full overflow-y-auto space-y-4 pr-2">
          <!-- Request Cards -->
          <div
            v-for="record in dataSource?.items"
            :key="record.requestId"
            class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
          >
            <div class="flex items-start justify-between gap-4">
              <!-- Status Badge -->
              <div class="flex-shrink-0">
                <a-tag
                  class="px-3 py-2 text-sm font-bold min-w-[100px] text-center"
                  :color="RequestStatus[record.statusCode as SystemStatusEnumKey]?.color"
                >
                  {{ record.statusName }}
                </a-tag>
              </div>

              <!-- Main Content -->
              <div class="flex-1 space-y-4">
                <!-- Request Header -->

                <!-- Request Details -->
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                  <div class="space-y-3">
                    <div class="flex flex-wrap items-center gap-4">
                      <div class="flex flex-wrap items-center gap-4">
                        <h3 class="text-lg font-bold text-gray-800">
                          {{ record.requestTypeName }}
                        </h3>
                        <div
                          v-if="record.leaveTypeName"
                          class="flex items-center gap-2"
                        >
                          <span class="text-gray-500 text-sm">
                            {{ t('form.leave-type') }}:
                          </span>
                          <span class="font-medium text-sm">
                            {{ record.leaveTypeName }}
                          </span>
                        </div>
                      </div>

                      <!-- Created Time -->
                      <div class="flex items-center gap-2 text-sm text-gray-500">
                        <img src="/icon/clock.svg" class="w-4 h-4">
                        <span v-if="record.createTime">
                          {{ dayjs().diff(dayjs(record.createTime), 'day') }}
                          {{ t('form.days').toLowerCase() }}
                          {{ t('ago') }}
                        </span>
                      </div>
                    </div>
                  </div>
                  <!-- Left Side - Date/Time Info -->
                  <div class="space-y-3">
                    <div class="flex items-center gap-2">
                      <CarbonCalendar class="text-gray-500" />
                      <span class="font-semibold text-gray-700">{{ t('time') }}:</span>
                      <div class="bg-blue-50 text-blue-800 px-3 py-1 rounded-md text-sm font-medium">
                        {{
                          calculateDate(
                            record.requestFrom,
                            record.requestTo,
                          ).days
                        }}
                        {{ t('form.days').toLowerCase() }}
                        {{
                          calculateDate(
                            record.requestFrom,
                            record.requestTo,
                          ).hours
                        }}
                        {{ t('form.hours').toLowerCase() }}
                        {{
                          calculateDate(
                            record.requestFrom,
                            record.requestTo,
                          ).minutes
                        }}
                        {{ t('form.minutes').toLowerCase() }}
                      </div>
                    </div>
                    <div class="text-sm text-gray-600 font-medium pl-6">
                      {{ record.requestFrom }}
                      <ArrowRightOutlined class="mx-2" />
                      {{ record.requestTo }}
                    </div>
                  </div>

                  <!-- Right Side - People Info -->
                  <div class="space-y-3">
                    <!-- Author -->
                    <div class="flex flex-wrap items-center gap-2">
                      <div class="flex items-center gap-2">
                        <CarbonUser class="text-gray-500" />
                        <span class="font-semibold text-gray-700">
                          {{ t('form.author') }}:
                        </span>
                      </div>
                      <a-tag color="blue" class="font-medium">
                        {{ record.createUserName }}
                      </a-tag>
                    </div>

                    <!-- Approver -->
                    <div class="flex flex-wrap items-center gap-2">
                      <div class="flex items-center gap-2">
                        <CarbonUser class="text-gray-500" />
                        <span
                          v-if="record.status === RequestStatusEnum.REJECTED"
                          class="font-semibold text-gray-700"
                        >
                          {{ t('form.rejected-by') }}:
                        </span>
                        <span v-else class="font-semibold text-gray-700">
                          {{ t('form.approved-by') }}:
                        </span>
                      </div>
                      <div class="flex flex-wrap gap-2">
                        <a-tag
                          v-if="record.approver1Name"
                          :color="RequestStatus[record.statusCode as SystemStatusEnumKey]?.color"
                          class="font-medium"
                        >
                          {{ record.approver1Name }}
                          ({{
                            dayjs(record.approver1Time).format(
                              'DD.MM.YYYY HH:mm',
                            )
                          }})
                        </a-tag>
                        <a-tag
                          v-if="record.approver2Name"
                          :color="RequestStatus[record.statusCode as SystemStatusEnumKey]?.color"
                          class="font-medium"
                        >
                          {{ record.approver2Name }}
                          ({{
                            dayjs(record.approver2Time).format(
                              'DD.MM.YYYY HH:mm',
                            )
                          }})
                        </a-tag>
                        <a-tag
                          v-if="!record.approver1Name && !record.approver2Name"
                          color="volcano"
                          class="font-medium"
                        >
                          {{ t('NOT_APPROVED') }}
                        </a-tag>
                      </div>
                    </div>
                  </div>

                  <div class="space-y-3">
                    <div class="flex gap-2">
                      <span class="font-semibold text-gray-700 flex-shrink-0">
                        {{ t('form.reason') }}:
                      </span>
                      <span class="text-gray-600">
                        {{ record.description }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div
                v-if="record.statusCode === RequestStatusEnum.PENDING"
                class="flex-shrink-0 flex flex-col gap-2"
              >
                <a-popconfirm
                  :title="t('message.approve-confirmation')"
                  @confirm="handleApprove(record.requestId)"
                >
                  <a-button type="primary" size="small" class="min-w-[80px]">
                    {{ t('button.approve') }}
                  </a-button>
                </a-popconfirm>
                <a-popconfirm
                  :title="t('message.reject-confirmation')"
                  @confirm="handleReject(record.requestId)"
                >
                  <a-button danger type="primary" size="small" class="min-w-[80px]">
                    {{ t('button.reject') }}
                  </a-button>
                </a-popconfirm>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div class="flex-none">
        <div class="flex items-center justify-between pt-4 border-t border-gray-200">
          <a-pagination
            class="pagination"
            :total="pagination.total"
            :current="pagination.current"
            :page-size="pagination.pageSize"
            @change="handlePaginationChange"
          />
          <div class="flex items-center gap-3 text-sm text-gray-600">
            <span>{{ t('show') }}</span>
            <a-pagination
              class="pagination pagination-right"
              :total="pagination.total"
              :current="pagination.current"
              :page-size="pagination.pageSize"
              show-size-changer
              :build-option-text="(props: any) => props.value"
              @change="handlePaginationChange"
            />
            <span>{{ t('entries') }}</span>
          </div>
        </div>
      </div>
    </div>
  </page-container>
</template>

<style lang="less" scoped>
.pagination {
  :deep(.ant-pagination-item-active) {
    background: #f99649;
    border-color: #f99649;
    a {
      color: #fff;
    }
  }
  :deep(.ant-select-selector) {
    background: #f99649;
    border-color: #f99649 !important;
    .ant-select-selection-item {
      color: #fff;
    }
  }
  :deep(.ant-select-arrow) {
    color: #fff;
  }
}
.pagination-right {
  :deep(.ant-pagination-prev) {
    display: none;
  }
  :deep(.ant-pagination-next) {
    display: none;
  }
  :deep(.ant-pagination-item) {
    display: none;
  }
  :deep(.ant-pagination-options) {
    margin: 0;
  }
  :deep(.ant-pagination-jump-next) {
    display: none;
  }
  :deep(.ant-pagination-jump-prev) {
    display: none;
  }
}
.search-date {
  border: none;
  background: none;
  box-shadow: none;

  :deep(.ant-picker-suffix) {
    display: none;
  }
  :deep(input) {
    cursor: pointer;
    width: 165px;
  }
}
</style>
