<script setup lang="ts">
import { BookFilled } from '@ant-design/icons-vue'
import { NTooltip } from 'naive-ui'

defineProps({
  title: {
    type: String,
    required: true,
  },
  content: {
    type: String,
    required: true,
  },
  value: {
    type: String,
    required: true,
  },
})
</script>

<template>
  <NTooltip :style="{ backgroundColor: '#91CAFF' }">
    <div class="text-xs space-y-2">
      <p class="text-blue-100 flex items-center">
        <span class="text-blue-800 rounded mr-2">
          <BookFilled class="text-blue-200" />
        </span>
        <b class="text-white">{{ title }}</b>
      </p>
      <div class="bg-white border-2 border-blue-300 p-3 rounded-md shadow-md flex">
        <div class="text-sm font-mono text-blue-700 font-medium">
          <div class="text-center mx-1">
            {{ content }}
          </div>
        </div>
      </div>
    </div>
    <template #trigger>
      <span class="text-red-600 font-bold">
        {{ value }}
      </span>
    </template>
  </NTooltip>
</template>
