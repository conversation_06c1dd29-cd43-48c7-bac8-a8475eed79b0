import type { RouteRecordRaw } from 'vue-router'

const Layout = () => import('~/layouts/index.vue')

export default [
  {
    path: '/login',
    component: () => import('~/pages/account/login.vue'),
    meta: {
      title: 'ログイン',
    },
  },
  {
    path: '/test',
    component: () => import('~/pages/administrative-office/projects/schedule/components/ScheduleModal.vue'),
    meta: {
      title: 'テスト',
    },
  },
  {
    path: '/invitation/accept',
    component: () => import('~/pages/account/accept-invite.vue'),
    meta: {
      title: '招待を承認する',
    },
  },
  {
    path: '/account/sign-up',
    component: () => import('~/pages/account/sign-up.vue'),
    meta: {
      title: '登録',
    },
  },
  {
    path: '/change-password',
    component: () => import('~/pages/common/change-password.vue'),
    meta: {
      title: '暗証番号変更',
    },
  },
  // {
  //   path: '/profile/basic',
  //   component: () => import('~/pages/profile/basic/index.vue'),
  //   id: 34,
  //   title: 'ProfileBasic',
  //   icon: null,
  //   redirect: null,
  //   parentId: null,
  //   name: null,
  //   hideInMenu: false,
  //   hideInBreadcrumb: null,
  //   hideChidrenInMenu: null,
  //   locale: 'page.profile.basic',
  //   meta: {
  //     title: '基本情報',
  //   },
  // },

  {
    path: '/forgot-account',
    component: () => import('~/pages/common/forgot-account.vue'),
    meta: {
      title: 'アカウントの忘れ',
    },
  },
  {
    path: '/401',
    name: 'Error401',
    component: () => import('~/pages/exception/401.vue'),
    meta: {
      title: '認証の有効期限が切れました',
    },
  },
  {
    path: '/common',
    name: 'LayoutBasicRedirect',
    component: Layout,
    redirect: '/common/redirect',
    children: [
      {
        path: '/common/redirect',
        component: () => import('~/pages/common/route-view.vue'),
        name: 'CommonRedirect',
        redirect: '/redirect',
        children: [
          {
            path: '/redirect/:path(.*)',
            name: 'RedirectPath',
            component: () => import('~/pages/common/redirect.vue'),
          },
        ],
      },
    ],
  },
  {
    path: '/:pathMatch(.*)',
    meta: {
      title: '画面が見つかりません',
    },
    component: () => import('~/pages/exception/error.vue'),
  },
] as RouteRecordRaw[]
