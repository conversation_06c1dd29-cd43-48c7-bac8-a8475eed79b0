import type { BreakTimeItem } from '../attendance'

export interface WorkshiftItem {
  workShiftId: string
  workShiftCode: string
  workShiftName: string
  checkInTime: string
  checkOutTime: string
  description: string
  workShiftBreaks?: BreakTimeItem[]
  totalRequiredTime: number
  isDefault?: boolean
}

export type WorkshiftParam = Partial<WorkshiftItem>

export interface WorkshiftResponse {
  items: WorkshiftItem[]
  pageNum: number
  pageSize: number
  totalRecords: number
}

export interface QueryWorkshiftParams {
  keyword?: string
  projectId?: string
  pageNum?: number
  pageSize?: number
}

export async function getWorkshiftApi(param?: any) {
  return useGet<WorkshiftResponse>('v1/workshift', param)
}

export async function createWorkshiftApi(param?: WorkshiftParam) {
  return usePost<any[]>('v1/workshift', param)
}

export async function getWorkshiftDetailApi(id: number) {
  return useGet<any[]>(`v1/workshift/${id}`)
}

export async function updateWorkshiftApi(workshiftUid: string, param?: WorkshiftParam) {
  return usePut<any[]>(`v1/workshift/${workshiftUid}`, param)
}

export async function deleteWorkshiftApi(workshiftId: string) {
  return useDelete<any[]>(`v1/workshift/${workshiftId}`)
}

// export async function onDeleteDetail(id: number) {
//   return useDelete<any[]>(`v1/ShiftWork/DeleteDetail/${id}`)
// }
