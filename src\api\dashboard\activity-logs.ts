export interface AttendanceLogParams {
  dateFrom?: string
  dateTo?: string
  action?: string
  pageNum?: number
  pageSize?: number
}
export interface EmployeeLogParams {
  employeeId: string
}

export interface Change {
  fieldName: string
  valueBefore: any
  valueAfter: any
}
export interface EntityChange {
  entityId: string
  changedList: Change[]
  modifiedTime: string
  modifiedEmployeeId: string
  modifiedEmployeeName: string
}

export async function getAttendanceLogApi(employeeShiftId: string, params: AttendanceLogParams) {
  return useGet<EntityChange[]>(`v1/attendance/${employeeShiftId}/logs`, params)
}

export async function getEmployeeLogApi(params: EmployeeLogParams) {
  return useGet<EntityChange[]>(`v1/employee/${params.employeeId}/logs`)
}
