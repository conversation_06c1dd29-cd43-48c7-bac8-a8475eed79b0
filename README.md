## Create `.env` files

```
VITE_APP_NAME = 'Kantoku'
VITE_APP_BASE = /
VITE_APP_BASE_API = *replace with your own api*
VITE_APP_MAP_KEY = *replace with your own map key*
VITE_APP_LOAD_ROUTE_WAY = BACKEND
```

## Run project 
```
npm install
npm run dev

````
## Build project
```
npm run build

```

## ✨ Core Principles

1. **Code Sạch & Chuẩn**  
   - Sử dụng ESLint + Prettier để tự động format code  
   - Cấu hình nghiêm ngặt: `@typescript-eslint/no-explicit-any: error`

2. **An Toàn Kiểu Dữ Liệu**  
   - 100% TypeScript với strict mode  
   - Không sử dụng `any` trong codebase

3. **Cập Nhật Thông Minh**  
   - Tối ưu reactivity: chỉ re-render phần thay đổi  
   - Không refresh toàn bộ dữ liệu sau CRUD

4. **Kiến Tr<PERSON><PERSON>**  
   - Tách component theo Single Responsibility Principle  
   - Logic nghiệp vụ trong composables