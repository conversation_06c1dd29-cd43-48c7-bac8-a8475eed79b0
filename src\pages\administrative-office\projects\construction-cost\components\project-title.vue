<script lang='ts' setup>
import type { ProjectItem } from '~@/api/company/project'

defineProps({
  projectInfo: {
    type: Object as () => ProjectItem,
    required: false,
  },
})

const { t } = useI18n()
// const constructionData = ref({
//   constructionId: 'CONST-140-2022',
//   constructionName: '国道140号（新山梨環状道路東部区間2期）落合5号橋（仮称）外下部工事（一部保守）',
//   description: 'Bridge construction and maintenance project',
//   isPrimary: true,
// })
</script>

<template>
  <a-card class="mb-6 shadow-sm">
    <a-row>
      <a-col :span="24">
        <a-descriptions :title="t('projectInfo')" bordered>
          <a-descriptions-item :label="t('projectName')" :span="3">
            {{ projectInfo?.projectCode }} -
            {{ projectInfo?.projectName }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('customer')" :span="2">
            {{ projectInfo?.customerName }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('contractor')">
            {{ projectInfo?.contractorName }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('contract-construction-period')" :span="2">
            {{ projectInfo?.expectedStartDate }} ~ {{ projectInfo?.expectedEndDate }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('managersInfo')">
            <div class="flex flex-wrap">
              <div v-for="(item, index) in projectInfo?.managersInfo" :key="index">
                <a-tag
                  :color="item?.isPrimaryManager ? 'blue' : ''"
                >
                  {{ item?.managerName }}
                </a-tag>
              </div>
            </div>
          </a-descriptions-item>
          <a-descriptions-item :label="t('description')" :span="2">
            {{ projectInfo?.description }}
          </a-descriptions-item>
        </a-descriptions>
      </a-col>
    </a-row>
  </a-card>
</template>
