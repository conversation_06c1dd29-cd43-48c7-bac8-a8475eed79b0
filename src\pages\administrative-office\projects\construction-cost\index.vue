<script lang="ts" setup>
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import type { SelectValue } from 'ant-design-vue/es/select'
import { FileExcelOutlined } from '@ant-design/icons-vue'
import ConstructionCost from './construction-cost.vue'
import ProjectConstruction from './components/project-construction.vue'
import SummaryDetail from './summary-detail.vue'
import ProjectTitle from './components/project-title.vue'
import TableDataList from '././components/table-data-list.vue'
import type { ConstructionCostItem, ConstructionCostQueryParams, CostAmountItem, EstimateBudgetItem } from '~@/api/construction-cost'
import { getConstructionCostByProjectApi } from '~@/api/construction-cost'
import type { ConstructionItem } from '~@/api/construction'
import { getConstructionByProjectIdApi } from '~@/api/construction'
import type { ProjectComboItem, ProjectItem } from '~@/api/company/project'
import { getProjectByIdApi, getProjectComboApi } from '~@/api/company/project'
import { ConstructionType } from '~@/utils/constant'
import type { CostCategoryItem, GetCostCategoryParams } from '~@/api/company/cost-category'
import { getCostCategory } from '~@/api/company/cost-category'
import { exportSimulationToExcel } from '~@/utils/excel/simulationExporter'

const { t } = useI18n()
const loading = ref<boolean>(false)
const messageNotify = useMessage()
const currentMonth = ref<Dayjs>(dayjs())
const queryParams: ConstructionCostQueryParams = reactive ({
  startDate: '2025-02-02',
  endDate: '2025-03-02',
})
const constructionCosts = ref<ConstructionCostItem[]>([])
const drawerVisible = ref<boolean>(false)
const projectInfo = ref<ProjectItem | undefined>()
const currentProjectId = ref<string>('')
const projectOptions = ref<ProjectComboItem[]>([])
const categoryData = ref<CostCategoryItem[]>([])

// Fetch data function

// Map API data to table format
function mapCategorizedCosts() {
  // This would map the API's categorizedCosts to our table format
  // For now, we're using the mock data above
}

const mainCostAmount = ref<CostAmountItem | undefined>(undefined)
const subCostAmount = ref<CostAmountItem | undefined>(undefined)
const overallCostAmount = ref<CostAmountItem | undefined>(undefined)

const mainConstructionCost = ref<ConstructionCostItem | undefined>(undefined)
const subConstructionCost = ref<ConstructionCostItem | undefined>(undefined)

const mainEstimateBudget = ref<EstimateBudgetItem | undefined>(undefined)
const subEstimateBudget = ref<EstimateBudgetItem | undefined>(undefined)
const overallEstimateBudget = ref<EstimateBudgetItem | undefined>(undefined)

const projectConstructions = ref<ConstructionItem[]>([])
const mainConstruction = ref<ConstructionItem | undefined>(undefined)
const subConstruction = ref<ConstructionItem | undefined>(undefined)
// const overallConstruction = ref<ConstructionItem | undefined>(undefined)

const mainContractTotalModifiedCost = ref(0)
const subContractTotalModifiedCost = ref(0)
const overallContractTotalModifiedCost = ref(0)

const mainTotalContractCost = ref(0)
const subTotalContractCost = ref(0)
const overallTotalContractCost = ref(0)

const mainTotalEstimateCost = ref(0)
const subTotalEstimateCost = ref(0)
const overallTotalEstimateCost = ref(0)
const mainEstimateProfitMargin = ref(0)
const subEstimateProjectMargin = ref(0)
const overallEstimateProjectMargin = ref(0)

function initCostAmountData() {
  // Init cost amount
  mainConstructionCost.value = constructionCosts.value.find((item: ConstructionCostItem) => item.isPrimary)
  subConstructionCost.value = constructionCosts.value.find((item: ConstructionCostItem) => !item.isPrimary)

  mainConstruction.value = projectConstructions.value.find((item: ConstructionItem) => item.isPrimary)
  subConstruction.value = projectConstructions.value.find((item: ConstructionItem) => !item.isPrimary)

  mainContractTotalModifiedCost.value = mainConstruction.value?.contractualCosts?.totalModifiedCost ?? 0
  subContractTotalModifiedCost.value = subConstruction.value?.contractualCosts?.totalModifiedCost ?? 0
  overallContractTotalModifiedCost.value = mainContractTotalModifiedCost.value + subContractTotalModifiedCost.value

  mainTotalContractCost.value = mainConstruction.value?.contractualCosts?.totalInitialCost ?? 0
  subTotalContractCost.value = subConstruction.value?.contractualCosts?.totalInitialCost ?? 0
  overallTotalContractCost.value = mainTotalContractCost.value + subTotalContractCost.value

  mainCostAmount.value = {
    type: ConstructionType.MAIN,
    previousAmount: mainConstructionCost.value?.lastAccumulateCost?.requestAmount ?? 0,
    previousProgressPercentage: 0,
    currentAmount: mainConstructionCost.value?.currentCost?.requestAmount ?? 0,
    currentProgressPercentage: 0,
    accumulatedAmount: mainConstructionCost.value?.currentAccumulateCost?.requestAmount ?? 0,
    accumulatedProgressPercentage: 0,
    remainingBalance: 0,
  }
  if (mainContractTotalModifiedCost.value) {
    mainCostAmount.value.previousProgressPercentage = (mainCostAmount.value.previousAmount / mainContractTotalModifiedCost.value) * 100
    mainCostAmount.value.currentProgressPercentage = (mainCostAmount.value.currentAmount / mainContractTotalModifiedCost.value) * 100
    mainCostAmount.value.accumulatedProgressPercentage = (mainCostAmount.value.accumulatedAmount / mainContractTotalModifiedCost.value) * 100
  }
  mainCostAmount.value.remainingBalance = mainContractTotalModifiedCost.value - mainCostAmount.value.accumulatedAmount

  subCostAmount.value = {
    type: ConstructionType.SUB,
    previousAmount: subConstructionCost.value?.lastAccumulateCost?.requestAmount ?? 0,
    previousProgressPercentage: 0,
    currentAmount: subConstructionCost.value?.currentCost?.requestAmount ?? 0,
    currentProgressPercentage: 0,
    accumulatedAmount: subConstructionCost.value?.currentAccumulateCost?.requestAmount ?? 0,
    accumulatedProgressPercentage: 0,
    remainingBalance: 0,
  }
  if (subContractTotalModifiedCost.value) {
    subCostAmount.value.previousProgressPercentage = (subCostAmount.value.previousAmount / subContractTotalModifiedCost.value) * 100
    subCostAmount.value.currentProgressPercentage = (subCostAmount.value.currentAmount / subContractTotalModifiedCost.value) * 100
    subCostAmount.value.accumulatedProgressPercentage = (subCostAmount.value.accumulatedAmount / subContractTotalModifiedCost.value) * 100
  }
  subCostAmount.value.remainingBalance = subContractTotalModifiedCost.value - subCostAmount.value.accumulatedAmount

  overallCostAmount.value = {
    type: ConstructionType.OVERALL,
    previousAmount: mainCostAmount.value.previousAmount + subCostAmount.value.previousAmount,
    previousProgressPercentage: 0,
    currentAmount: mainCostAmount.value.currentAmount + subCostAmount.value.currentAmount,
    currentProgressPercentage: 0,
    accumulatedAmount: mainCostAmount.value.accumulatedAmount + subCostAmount.value.accumulatedAmount,
    accumulatedProgressPercentage: 0,
    remainingBalance: mainCostAmount.value.remainingBalance + subCostAmount.value.remainingBalance,
  }
  // Init total progress percentage
  if (overallContractTotalModifiedCost.value) {
    overallCostAmount.value.previousProgressPercentage = (overallCostAmount.value.previousAmount / overallContractTotalModifiedCost.value) * 100
    overallCostAmount.value.currentProgressPercentage = (overallCostAmount.value.currentAmount / overallContractTotalModifiedCost.value) * 100
    overallCostAmount.value.accumulatedProgressPercentage = (overallCostAmount.value.accumulatedAmount / overallContractTotalModifiedCost.value) * 100
  }
}

function initEstimateBudget() {
  mainTotalEstimateCost.value = mainConstruction.value?.estimatedCosts?.totalEstimateCost ?? 0
  subTotalEstimateCost.value = subConstruction.value?.estimatedCosts?.totalEstimateCost ?? 0
  overallTotalEstimateCost.value = mainTotalEstimateCost.value + subTotalEstimateCost.value

  if (mainTotalContractCost.value !== 0)
    mainEstimateProfitMargin.value = (mainTotalContractCost.value - mainTotalEstimateCost.value) / mainTotalContractCost.value * 100
  if (subTotalContractCost.value !== 0)
    subEstimateProjectMargin.value = (subTotalContractCost.value - subTotalEstimateCost.value) / subTotalContractCost.value * 100
  if (overallTotalContractCost.value !== 0)
    overallEstimateProjectMargin.value = (overallTotalContractCost.value - overallTotalEstimateCost.value) / overallTotalContractCost.value * 100

  mainEstimateBudget.value = {
    type: ConstructionType.MAIN,
    budgetAccordingToProgress: 0,
    againstBudget: 0,
    actualProfitMargin: 0,
    estimatedProfitMargin: 0,
  }

  const mainAccumulatedTotalCost = mainConstructionCost.value?.currentAccumulateCost?.totalCost ?? 0
  if (mainCostAmount.value) {
    mainEstimateBudget.value.budgetAccordingToProgress = (mainTotalEstimateCost.value * (mainCostAmount.value?.accumulatedProgressPercentage ?? 0)) / 100
    mainEstimateBudget.value.againstBudget = mainCostAmount.value?.accumulatedAmount - mainEstimateBudget.value.budgetAccordingToProgress
    if (mainCostAmount.value.accumulatedAmount)
      mainEstimateBudget.value.actualProfitMargin = (mainCostAmount.value.accumulatedAmount - mainAccumulatedTotalCost) / mainCostAmount.value.accumulatedAmount * 100
    mainEstimateBudget.value.estimatedProfitMargin = mainEstimateBudget.value.actualProfitMargin - mainEstimateProfitMargin.value
  }

  subEstimateBudget.value = {
    type: ConstructionType.SUB,
    budgetAccordingToProgress: 0,
    againstBudget: 0,
    actualProfitMargin: 0,
    estimatedProfitMargin: 0,
  }

  const subAccumulatedTotalCost = subConstructionCost.value?.currentAccumulateCost?.totalCost ?? 0
  if (subCostAmount.value) {
    subEstimateBudget.value.budgetAccordingToProgress = (subTotalEstimateCost.value * (subCostAmount.value?.accumulatedProgressPercentage ?? 0)) / 100
    subEstimateBudget.value.againstBudget = subCostAmount.value?.accumulatedAmount - subEstimateBudget.value.budgetAccordingToProgress
    if (subCostAmount.value.accumulatedAmount !== 0)
      subEstimateBudget.value.actualProfitMargin = (subCostAmount.value.accumulatedAmount - subAccumulatedTotalCost) / subCostAmount.value.accumulatedAmount * 100
    subEstimateBudget.value.estimatedProfitMargin = subEstimateBudget.value.actualProfitMargin - subEstimateProjectMargin.value
  }

  overallEstimateBudget.value = {
    type: ConstructionType.OVERALL,
    budgetAccordingToProgress: 0,
    againstBudget: 0,
    actualProfitMargin: 0,
    estimatedProfitMargin: 0,
  }

  const overallAccumulatedTotalCost = mainConstructionCost.value?.currentAccumulateCost?.totalCost ?? 0 + (subConstructionCost.value?.currentAccumulateCost?.totalCost ?? 0)
  if (overallCostAmount.value) {
    overallEstimateBudget.value.budgetAccordingToProgress = (overallTotalEstimateCost.value * (overallCostAmount.value?.accumulatedProgressPercentage ?? 0)) / 100
    overallEstimateBudget.value.againstBudget = overallCostAmount.value?.accumulatedAmount - overallEstimateBudget.value.budgetAccordingToProgress
    if (overallCostAmount.value.accumulatedAmount !== 0)
      overallEstimateBudget.value.actualProfitMargin = (overallCostAmount.value.accumulatedAmount - overallAccumulatedTotalCost) / overallCostAmount.value.accumulatedAmount * 100
    overallEstimateBudget.value.estimatedProfitMargin = overallEstimateBudget.value.actualProfitMargin - overallEstimateProjectMargin.value
  }
}

async function fetchConstructionCostList() {
  try {
    loading.value = true
    const { data, status, message } = await getConstructionCostByProjectApi(currentProjectId.value, queryParams)
    constructionCosts.value = data?.items ?? []

    if (status === 200) {
      constructionCosts.value = data?.items ?? []

      // Map categorized costs to table data
      mapCategorizedCosts()
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (error) {
    messageNotify.error('データの取得に失敗しました')
  }
  finally {
    loading.value = false
  }
}

// Fake data
// const projectConstructions = ref([
//   {
//     constructionId: 'CONST001',
//     constructionName: 'Main Building',
//     description: 'Primary construction for office complex',
//     isPrimary: true,
//     contractualCosts: {
//       initialCostItems: [
//         { sequenceNumber: 1, amount: 5000000 },
//         { sequenceNumber: 2, amount: 3000000 },
//       ],
//       totalInitialCost: 8000000,
//       modifiedCostItems: [
//         { sequenceNumber: 1, amount: 1200000 },
//       ],
//       totalModifiedCost: 1200000,
//       constructionId: 'CONST001',
//       isPrimary: true,
//     },
//     estimatedCosts: {
//       estimateCostItems: [
//         { sequenceNumber: 1, amount: 8500000 },
//       ],
//       totalEstimateCost: 8500000,
//       constructionId: 'CONST001',
//       isPrimary: true,
//     },
//     accumulatedCosts: {
//       categorizedCosts: [
//         {
//           categoryId: 'CAT001',
//           categoryName: 'Foundation Work',
//           totalAmount: 4000000,
//           quantity: 10,
//           subCategories: ['Excavation', 'Concrete'],
//         },
//         {
//           categoryId: 'CAT002',
//           categoryName: 'Structural Work',
//           totalAmount: 3000000,
//           quantity: 5,
//           subCategories: ['Steel Frame', 'Reinforcement'],
//         },
//       ],
//       riskAmount: 500000,
//       totalAccumulatedCost: 7500000,
//       constructionId: 'CONST001',
//       isPrimary: true,
//     },
//   },
// ])

async function fetchConstructionList() {
  try {
    loading.value = true
    const { data, status, message } = await getConstructionByProjectIdApi(currentProjectId.value)
    if (status === 200)
      projectConstructions.value = data?.constructions ?? []

    else
      messageNotify.error(message)
  }
  catch (error) {
    messageNotify.error('データの取得に失敗しました')
  }
  finally {
    loading.value = false
  }
}

async function fetchProjectInfo() {
  try {
    const { data, status, message } = await getProjectByIdApi(currentProjectId.value)
    if (status === 200)
      projectInfo.value = data ?? undefined
    else
      messageNotify.error(message)
  }
  catch (error) {
    messageNotify.error('データの取得に失敗しました')
  }
}

async function fetchProjectList() {
  try {
    const { data, status, message } = await getProjectComboApi()
    if (status === 200) {
      projectOptions.value = data?.items ?? []
      currentProjectId.value = projectOptions.value.length > 0 ? projectOptions.value[0].id : ''
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (error) {
    messageNotify.error('データの取得に失敗しました')
  }
}

async function fetchCategoryData() {
  try {
    const params: GetCostCategoryParams = {
      keyword: undefined,
      parentId: undefined,
      pageNum: 1,
      pageSize: 100,
    }
    const { data, status, message } = await getCostCategory(params)
    if (status === 200)
      categoryData.value = data?.items ?? []

    else
      messageNotify.error(message)
  }
  catch (error) {
    messageNotify.error('データの取得に失敗しました')
  }
}

function handleSelectProjectChange(value: string) {
  currentProjectId.value = value
  fetchConstructionCostList()
  fetchConstructionList()
  fetchProjectInfo()
  initCostAmountData()
  initEstimateBudget()
}

function handleExport() {
  const mainConstructionCost: ConstructionCostItem = constructionCosts.value.find((item: ConstructionCostItem) => item.isPrimary) as ConstructionCostItem
  const subConstructionCost: ConstructionCostItem = constructionCosts.value.find((item: ConstructionCostItem) => !item.isPrimary) as ConstructionCostItem
  exportSimulationToExcel({
    mainCostAmount: mainCostAmount.value as CostAmountItem,
    subCostAmount: subCostAmount.value as CostAmountItem,
    overallCostAmount: overallCostAmount.value as CostAmountItem,
    mainEstimateBudget: mainEstimateBudget.value as EstimateBudgetItem,
    subEstimateBudget: subEstimateBudget.value as EstimateBudgetItem,
    overallEstimateBudget: overallEstimateBudget.value as EstimateBudgetItem,
    mainConstructionCost,
    subConstructionCost,
  })
}

onMounted(async () => {
  await fetchProjectList()
  await fetchCategoryData()
  const promises = [fetchProjectInfo(), fetchConstructionCostList(), fetchConstructionList()]
  await Promise.all(promises)
  initCostAmountData()
  initEstimateBudget()
})
</script>

<template>
  <page-container>
    <!-- <FinancialData
      :construction-costs="constructionCosts"
    /> -->
    <div class="flex items-center justify-center gap-2 mb-2">
      <div class="flex items-center gap-2">
        <span class="font-bold text-[25px]">
          {{ t('simulationReport') }}
        </span>
      </div>
    </div>
    <div class="flex items-center justify-end gap-2 mb-2">
      <div class="flex items-center gap-2">
        <a-date-picker
          v-model:value="currentMonth"
          picker="month"
        />
        <a-select
          v-model:value="currentProjectId"
          :options="projectOptions"
          :field-names="{ label: 'name', value: 'id' }"
          @change="(value: SelectValue) => handleSelectProjectChange(String(value))"
        />
        <a-button
          type="primary"
          class="flex items-center gap-x-2"
          @click="handleExport"
        >
          <template #icon>
            <FileExcelOutlined />
          </template>
          {{ t('export') }}
        </a-button>
        <a-button type="primary" @click="drawerVisible = true">
          {{ t('button.detail') }}
        </a-button>
      </div>
    </div>
    <ProjectTitle
      :project-info="projectInfo"
    />
    <TableDataList
      :main-cost-amount="mainCostAmount"
      :sub-cost-amount="subCostAmount"
      :overall-cost-amount="overallCostAmount"
      :main-estimate-budget="mainEstimateBudget"
      :sub-estimate-budget="subEstimateBudget"
      :overall-estimate-budget="overallEstimateBudget"
    />
    <div v-for="constructionCost in constructionCosts" :key="constructionCost.constructionId">
      <ConstructionCost
        :construction-cost="constructionCost"
        :last-accumulate-cost="constructionCost.lastAccumulateCost"
        :current-cost="constructionCost.currentCost"
        :current-accumulate-cost="constructionCost.currentAccumulateCost"
      />
      <SummaryDetail
        :construction-id="constructionCost.constructionId"
        :is-primary="constructionCost.isPrimary"
      />
    </div>
    <a-drawer
      v-model:open="drawerVisible"
      :title="t('constructionInfoDetail')"
      placement="right"
      width="40%"
    >
      <div class="grid grid-cols-1 gap-4">
        <a-card v-for="construction in projectConstructions" :key="construction.constructionId" class="rounded-xl">
          <ProjectConstruction :construction="construction" />
        </a-card>
      </div>
    </a-drawer>
  </page-container>
</template>
