/// <reference types="vitest" />
import { resolve } from 'node:path'
import { fileURLToPath } from 'node:url'
import * as process from 'node:process'
import { loadEnv } from 'vite'
import type { ConfigEnv, UserConfig } from 'vite'
import { createVitePlugins } from './plugins'
import { OUTPUT_DIR } from './plugins/constants'

const baseSrc = fileURLToPath(new URL('./src', import.meta.url))
// https://vitejs.dev/config/
export default ({ mode }: ConfigEnv): UserConfig => {
  const env = loadEnv(mode, process.cwd())
  const proxyObj = {}

  // Thêm cờ tính năng vào define
  // Khi bật (true): Vue sẽ cung cấp chi tiết về các sự không khớp trong quá trình hydration.
  // Điều này giúp bạn dễ dàng debug vấn đề khi có sự khác biệt giữa những gì được render ở máy chủ và client.

  // Khi tắt (false): Vue sẽ không hiển thị chi tiết về sự không khớp.
  // Điều này có thể giúp giảm kích thước bundle trong production, nhưng sẽ làm mất đi thông tin chi tiết nếu bạn cần debug vấn đề hydration.
  const defineConfig = {
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false, // Hoặc true tùy theo nhu cầu của bạn
  }
  if (
    mode === 'development'
    && env.VITE_APP_BASE_API_DEV
    && env.VITE_APP_BASE_URL_DEV
  ) {
    proxyObj[env.VITE_APP_BASE_API_DEV] = {
      target: env.VITE_APP_BASE_URL_DEV,
      changeOrigin: true,
      rewrite: path =>
        path.replace(new RegExp(`^${env.VITE_APP_BASE_API_DEV}`), ''),
    }
  }
  return {
    plugins: createVitePlugins(env),
    resolve: {
      alias: [
        {
          find: 'dayjs',
          replacement: 'dayjs/esm',
        },
        {
          find: /^dayjs\/locale/,
          replacement: 'dayjs/esm/locale',
        },
        {
          find: /^dayjs\/plugin/,
          replacement: 'dayjs/esm/plugin',
        },
        {
          find: 'vue-i18n',
          replacement:
            mode === 'development'
              ? 'vue-i18n/dist/vue-i18n.esm-browser.js'
              : 'vue-i18n/dist/vue-i18n.esm-bundler.js',
        },
        {
          find: /^ant-design-vue\/es$/,
          replacement: 'ant-design-vue/es',
        },
        {
          find: /^ant-design-vue\/dist$/,
          replacement: 'ant-design-vue/dist',
        },
        {
          find: /^ant-design-vue\/lib$/,
          replacement: 'ant-design-vue/es',
        },
        {
          find: /^ant-design-vue$/,
          replacement: 'ant-design-vue/es',
        },
        {
          find: 'lodash',
          replacement: 'lodash-es',
        },
        {
          find: '~@',
          replacement: baseSrc,
        },
        {
          find: '~',
          replacement: baseSrc,
        },
        {
          find: '@',
          replacement: baseSrc,
        },
        {
          find: '~#',
          replacement: resolve(baseSrc, './enums'),
        },
      ],
    },
    build: {
      chunkSizeWarningLimit: 4096,
      outDir: OUTPUT_DIR,
      sourcemap: false,
      rollupOptions: {
        output: {
          manualChunks: {
            vue: ['vue', 'vue-router', 'pinia', 'vue-i18n', '@vueuse/core'],
            antd: ['ant-design-vue', '@ant-design/icons-vue', 'dayjs'],
            // lodash: ['loadsh-es'],
          },
        },
      },
    },
    server: {
      port: 6678,
      host: '0.0.0.0',
      proxy: {
        ...proxyObj,
        [env.VITE_APP_BASE_API]: {
          target: env.VITE_APP_BASE_URL,
          //   如果你是https接口，需要配置这个参数
          //   secure: false,
          changeOrigin: true,
          rewrite: path =>
            path.replace(new RegExp(`^${env.VITE_APP_BASE_API}`), ''),
        },
      },
    },
    test: {
      globals: true,
      environment: 'jsdom',
    },
    define: defineConfig,
  }
}
