<script lang="ts" setup>
import {
  SearchOutlined,
  PlusOutlined,
  DownCircleOutlined,
  UpCircleOutlined,
} from '@ant-design/icons-vue';
import type {
  ColumnGroupType,
  ColumnType,
  TablePaginationConfig,
} from 'ant-design-vue/es/table';
import { usePagination } from 'vue-request';
import type {
  CostCategoryItem,
  CostCategoryLogItem,
  CostCategoryResponse,
} from '~@/api/company/cost-category';
import {
  getCostCategory,
  getCostCategoryLogs,
} from '~@/api/company/cost-category';
import { FilterValue, Key } from 'ant-design-vue/es/table/interface';
import ItemDetail from './item-detail.vue';

type ColumnItemType<T> = ColumnGroupType<T> | ColumnType<T>;

type CategoryItemType = CostCategoryItem & {
  isAdd?: boolean;
  isEdit?: boolean;
};

interface Params {
  pageNum?: number;
  pageSize?: number;
  keyword?: string;
}

interface LogState {
  logId: string;
  pageSize: number;
  pageNum: number;
  hasMore: boolean;
}

const logRef = ref();
const { arrivedState } = useScroll(logRef);
const expandedRowKeys = ref<Key[]>([]);
const { t } = useI18n();
const searchForm = ref<Params>({
  pageSize: 10,
  pageNum: 1,
});
const logState = reactive<LogState>({
  logId: '',
  pageSize: 20,
  pageNum: 1,
  hasMore: true,
});
const logData = ref<CostCategoryLogItem[]>([]);

const queryData = async (params?: Params) => {
  const { data } = await getCostCategory(params);
  if (!data) {
    return ref<CostCategoryResponse>({ items: [] }).value;
  }
  return ref(data).value;
};

const {
  data: dataSource,
  loading,
  total,
  current,
  run,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [searchForm.value],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
});

const pagination = computed(() => ({
  total: total.value ?? 10,
  current: current.value,
  pageSize: pageSize.value,
}));

const columns = computed<ColumnItemType<CategoryItemType>[]>(() => [
  { title: t('form.code'), dataIndex: 'code', width: 150 },
  { title: t('form.category'), dataIndex: 'name', width: 150 },
  { title: t('form.products'), dataIndex: 'products', width: 150 },
  { title: t('note'), dataIndex: 'note', width: 150 },
  {
    title: t('action'),
    dataIndex: 'action',
    width: 150,
    align: 'center',
    fixed: 'right',
  },
]);

const handlePaginationChange = (page: number, pageSize: number) => {
  searchForm.value.pageSize = pageSize;
  searchForm.value.pageNum = page;
  run(searchForm.value);
};

const handleTableChange = (
  pagination: TablePaginationConfig,
  filters: Record<string, FilterValue>
) => {
  searchForm.value.pageSize = pagination.pageSize;
  searchForm.value.pageNum = pagination.current;
  run({ ...searchForm.value, ...filters });
};

const onSearch = () => {
  handleTableChange(
    { pageSize: searchForm.value.pageSize || 10, current: 1 },
    {}
  );
};

const handleAdd = (parentId: string, index: number) => {
  const newData: CategoryItemType = {
    categoryId: `${Date.now()}`,
    categoryCode: '',
    categoryName: '',
    description: '',
    parentId,
    isAdd: true,
  };

  if (dataSource.value) {
    const item = dataSource.value.items[index];

    if (item.categories) item.categories.unshift(newData);
    else item.categories = [newData];

    if (item.editableData) item.editableData[newData.categoryId] = newData;
    else item.editableData = { [newData.categoryId]: newData };

    if (expandedRowKeys.value.indexOf(parentId) === -1) {
      expandedRowKeys.value.push(parentId);
    }
  }
};

const handleScroll = async () => {
  if (!logState.hasMore) return;
  logState.pageNum += 1;

  const logs = await getCostCategoryLogs(logState.logId, {
    pageSize: logState.pageSize,
    pageNum: logState.pageNum,
  });

  const entityChanges = logs.data?.entityChanges ?? [];
  logData.value = [...logData.value, ...entityChanges];
  logState.hasMore = entityChanges.length === logState.pageSize;
};

watch(
  () => arrivedState.bottom,
  async (value: boolean) => {
    if (value) await handleScroll();
  }
);
</script>

<template>
  <page-container>
    <a-row
      :wrap="false"
      :gutter="[12, 12]"
      class="h-[calc(100vh-100px)] flex-col"
    >
      <a-col flex="none" span="24">
        <a-row>
          <a-col span="24">
            <div class="bg-white px-4 pt-4 pb-2 rounded-t-lg">
              <a-row :gutter="[12, 12]">
                <a-col flex="auto">
                  <a-typography-title :level="4">
                    {{ t('categories-management') }}
                  </a-typography-title>
                </a-col>
                <a-col flex="none">
                  <a-input
                    v-model:value="searchForm.keyword"
                    :placeholder="t('search')"
                    style="width: 270px"
                    allow-clear
                    @press-enter="onSearch"
                  >
                    <template #prefix>
                      <SearchOutlined class="text-gray-500" />
                    </template>
                  </a-input>
                </a-col>
              </a-row>
            </div>
          </a-col>
          <a-col span="24">
            <a-table
              class="tableCategory"
              bordered
              :scroll="{ x: 'max-content', y: 'calc(100vh - 260px)' }"
              :columns="columns"
              :data-source="dataSource?.items"
              :loading="loading"
              :pagination="false"
              row-key="categoryId"
              @change="handleTableChange"
              :expanded-row-keys="expandedRowKeys"
              @expanded-rows-change="expandedRowKeys = $event"
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.dataIndex === 'code'">
                  <div>{{ record.categoryCode }}</div>
                </template>
                <template v-else-if="column.dataIndex === 'name'">
                  <div>{{ record.categoryName }}</div>
                </template>
                <template v-else-if="column.dataIndex === 'products'">
                  <div class="flex gap-2">
                    {{ record.numberOfItems?.toLocaleString() }}
                    {{ t('form.products') }}
                  </div>
                </template>
                <template v-else-if="column.dataIndex === 'note'">
                  <div>{{ record.description }}</div>
                </template>
                <template
                  v-if="
                    column.dataIndex === 'action' && record.isSystemCategory
                  "
                >
                  <div class="flex justify-center items-center">
                    <a-button
                      class="flex items-center"
                      size="small"
                      color="primary"
                      @click="handleAdd(record.categoryId, index)"
                    >
                      <PlusOutlined />
                      {{ t('button.add-more') }}
                    </a-button>
                  </div>
                </template>
              </template>

              <template #expandIcon="props">
                <div class="flex justify-center items-center">
                  <a-button
                    type="link"
                    @click="
                      (event) => {
                        if (!props) return;
                        if (props.expanded) {
                          props.record.categories = [];
                          props.record.editableData = {};
                        }
                        if (!props?.expanded && !props.record.categories) {
                          props.record.categories = [];
                          props.record.editableData = {};
                        }
                        props.onExpand(props.record, event);
                      }
                    "
                  >
                    <UpCircleOutlined v-if="props?.expanded" />
                    <DownCircleOutlined v-else class="text-black" />
                  </a-button>
                </div>
              </template>
              <template #expandedRowRender="{ record, expanded }">
                <item-detail
                  v-if="expanded"
                  :parent-id="record.categoryId"
                  v-model:categories="record.categories"
                  v-model:editableData="record.editableData"
                />
              </template>
              <template #expandColumnTitle>
                <span />
              </template>
            </a-table>
          </a-col>
        </a-row>
      </a-col>
      <a-col flex="auto" span="24">
        <div class="h-full flex items-end">
          <a-row justify="space-between" class="mt-4 w-full">
            <a-col>
              <a-pagination
                class="pagination"
                :total="pagination.total"
                :current="pagination.current"
                :pageSize="pagination.pageSize"
                @change="handlePaginationChange"
              />
            </a-col>
            <a-col>
              <a-row :gutter="[12, 12]" justify="center" align="middle">
                <a-col>{{ t('show') }}</a-col>
                <a-col>
                  <a-pagination
                    class="pagination pagination-right"
                    :total="pagination.total"
                    :current="pagination.current"
                    :pageSize="pagination.pageSize"
                    showSizeChanger
                    :buildOptionText="(props: any) => props.value"
                    @change="handlePaginationChange"
                  />
                </a-col>
                <a-col>{{ t('entries') }}</a-col>
              </a-row>
            </a-col>
          </a-row>
        </div>
      </a-col>
    </a-row>
  </page-container>
</template>

<style lang="less" scoped>
.tableCategory {
  :deep(.ant-table-header) {
    border-radius: 0;
  }
  :deep(.ant-table-header > table) {
    border-radius: 0;
  }
  :deep(.ant-table-cell) {
    border-radius: 0 !important;
  }
  :deep(.ant-table-cell > .ant-table-expanded-row-fixed) {
    margin: -16px !important;
    padding: 0;
    background: #fff;
  }
  :deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
    background: #f2f8fd;
  }
}
.pagination {
  :deep(.ant-pagination-item-active) {
    background: #f99649;
    border-color: #f99649;
    a {
      color: #fff;
    }
  }
  :deep(.ant-select-selector) {
    background: #f99649;
    border-color: #f99649 !important;
    .ant-select-selection-item {
      color: #fff;
    }
  }
  :deep(.ant-select-arrow) {
    color: #fff;
  }
}
.pagination-right {
  :deep(.ant-pagination-prev) {
    display: none;
  }
  :deep(.ant-pagination-next) {
    display: none;
  }
  :deep(.ant-pagination-item) {
    display: none;
  }
  :deep(.ant-pagination-options) {
    margin: 0;
  }
  :deep(.ant-pagination-jump-next) {
    display: none;
  }
  :deep(.ant-pagination-jump-prev) {
    display: none;
  }
}
</style>
