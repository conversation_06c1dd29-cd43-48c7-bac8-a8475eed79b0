export interface InputCostItemResponse {
  items: InputCostItem[]
  pageNum: number
  pageSize: number
  totalRecords: number
}
export interface InputCostItem {
  averagePrice: number // 平均単価
  categoryCode: string | null
  categoryName: string | null
  constructionId: string | null
  createTime: string | null
  description: string | null
  inputCostId: string | null
  inputCostItemId: string | null
  itemId: string | null
  itemName: string // 例: "単価BB"
  originalInputCostNumber: number | null
  price: number // 単価
  quantity: number // 数量
  taxRate: number | null
  totalAverageAmount: number // 平均単価 × 数量
  totalNonTaxed: number // 税抜金額
  totalTaxed: number | null // 税込金額
  transactionDate: string | null
  unit: string | null // 単位
  updateTime: string | null
  vendorId: string | null
  vendorName: string | null
}
export interface InputCostItemQueryParams {
  projectId?: string
  constructionId?: string
  inputCostId?: string
  categoryId?: string
  vendorId?: string
  dateFrom?: string
  dateTo?: string
  pageNum?: number
  pageSize?: number
}

export interface InputCostData {
  constructionId: string
  transactionDate: string
  item: {
    itemId: string
    itemName: string
    categoryId?: string
  }
  unit: string
  vendorId?: string
  quantity?: number
  price?: number
  taxRate?: number
  totalNonTaxed?: number
  totalTaxed?: number
  description?: string
  vendor?: {
    vendorId: string
    vendorName: string
  }
}

interface AddInputCostItem {
  transactionDate: string
  itemId: string
  unit: string
  vendorId: string
  quantity: number
  price: number
  taxRate: number
  totalNonTaxed: number
  totalTaxed: number
  description: string
}

interface UpdateInputCostItem {
  transactionDate: string
  itemId: string
  unit: string
  vendorId: string
  quantity: number
  price: number
  taxRate: number
  totalNonTaxed: number
  totalTaxed: number
  description: string
  inputCostItemId: string
}

interface DeleteInputCostItem {
  inputCostItemId: string
}

export interface InputCostItemUpdateRequest {
  addInputCostItems: AddInputCostItem[]
  updateInputCostItems: UpdateInputCostItem[]
  deleteInputCostItems: DeleteInputCostItem[]
}

interface ActionQueryParams {
  dateFrom?: string
  dateTo?: string
  action?: string
  pageNum?: number
  pageSize?: number
}

interface FieldChange {
  fieldName: string
  valueBefore: string
  valueAfter: string
}

interface EntityChange {
  auditLogId: string
  entityId: string
  action: string
  changedList: FieldChange[]
  modifiedTime: string // ISO 8601 date string
  modifiedUserId: string
  modifiedUserName: string
}

interface AuditLogResponse {
  entityChanges: EntityChange[]
  pageNum: number
  pageSize: number
  totalRecords: number
}

export interface InputCostItemQuery {
  projectId?: string
  constructionId?: string
  inputCostId?: string
  categoryId?: string
  vendorId?: string
  dateFrom?: string
  dateTo?: string
  pageNum?: number
  pageSize?: number
}

export interface InputCostItemParams {
  constructionId?: string
  transactionDate: string
  itemId: string
  unit: string
  vendorId?: string
  quantity: number
  price: number
  taxRate: number
  totalNonTaxed?: number
  totalTaxed?: number
  description: string
}
// export async function getInputCostItemApi(params: InputCostItemQueryParams) {
//   return useGet<InputCostItemResponse>('v1/cost/inputcostitem', params)
// }

export async function getInputCostItemListApi(params?: InputCostItemQuery) {
  return useGet<InputCostItemResponse>('v1/cost/inputcostitem', params)
}

export async function getInputCostItemApi(categoryId: string, params?: InputCostItemQueryParams) {
  return useGet<InputCostItemResponse>(`v1/cost/inputcostitem/category/${categoryId}`, params)
}

export async function getOneInputCostItemApi(id: string) {
  return useGet<InputCostItem>(`v1/cost/inputcostitem/${id}`)
}

export async function createInputCostItem(data: InputCostData) {
  return usePost<InputCostItem>('v1/cost/inputcostitem', data)
}

export async function updateInputCostItemApi(id: string, data: InputCostItemParams) {
  return usePut<InputCostItem>(`v1/cost/inputcostitem/${id}`, data)
}

export async function getInputCostItemLogsApi(id: string, params?: ActionQueryParams) {
  return useGet<AuditLogResponse>(`v1/cost/inputcostitem/${id}/logs`, params)
}

export async function deleteInputCostItemApi(inputCostItemId: string) {
  return useDelete<InputCostItem>(`v1/cost/inputcostitem/${inputCostItemId}`)
}
