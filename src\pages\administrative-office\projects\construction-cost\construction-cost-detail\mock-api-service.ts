// mockApiService.ts
import type { CostsData } from './types'

// Mock data for development
export function getMockCostsData(): CostsData {
  return {
    contractualCosts: [
      {
        categoryId: 'cc-001',
        categoryName: 'Labor',
        totalAmount: 125000,
        subCategories: ['Regular Hours', 'Overtime', 'Specialized Labor'],
        description: 'All labor costs as per contract specifications',
      },
      {
        categoryId: 'cc-002',
        categoryName: 'Materials',
        totalAmount: 78500,
        subCategories: ['Raw Materials', 'Components', 'Supplies'],
      },
      {
        categoryId: 'cc-003',
        categoryName: 'Equipment',
        totalAmount: 45200,
        description: 'Equipment rental and purchases',
      },
    ],
    estimatedCosts: [
      {
        categoryId: 'ec-001',
        categoryName: 'Labor',
        totalAmount: 130000,
        subCategories: ['Regular Hours', 'Overtime', 'Specialized Labor'],
      },
      {
        categoryId: 'ec-002',
        categoryName: 'Materials',
        totalAmount: 82000,
        subCategories: ['Raw Materials', 'Components', 'Supplies'],
        description: 'Includes 5% contingency for price fluctuations',
      },
      {
        categoryId: 'ec-003',
        categoryName: 'Transportation',
        totalAmount: 12500,
      },
    ],
    accumulatedCosts: [
      {
        categoryId: 'ac-001',
        categoryName: 'Labor',
        totalAmount: 95600,
        subCategories: ['Regular Hours', 'Overtime', 'Specialized Labor'],
        description: 'Actual labor costs to date',
      },
      {
        categoryId: 'ac-002',
        categoryName: 'Materials',
        totalAmount: 68300,
        subCategories: ['Raw Materials', 'Components', 'Supplies'],
      },
      {
        categoryId: 'ac-003',
        categoryName: 'Miscellaneous',
        totalAmount: 8700,
        description: 'Unforeseen expenses and adjustments',
      },
    ],
  }
}

// Mock API implementation
export function fetchCostsData(): Promise<CostsData> {
  return new Promise((resolve) => {
    // Simulate network delay
    setTimeout(() => {
      resolve(getMockCostsData())
    }, 1000)
  })
}
