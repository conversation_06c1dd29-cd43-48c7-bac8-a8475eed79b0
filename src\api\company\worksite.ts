export interface WorkplacesResponse {
  items: WorkplaceInfo[]
  pageIndex: number
  pageSize: number
  totalRow: number
  pageCount: number
}

export interface User {
  userId: string
  name: string
}
export interface WorkplaceInfo {
  workplaceId: string
  workplaceCode: string
  workplaceName: string
  orgId: string
  description: string
  address: string
  status: boolean
  isWorkSite: boolean
  createTime: string
  updateTime: string
  primaryManager: User
  subManagers: User[]
}

export type WorkplaceParams = Partial<WorkplaceInfo>

export interface WorkplaceSearchParams {
  status?: any
  type?: any
  pageNum?: number
  pageSize?: number
  [key: string]: any
}

export interface WorksiteManager {
  workplaceId?: number
  primaryManagerUserId?: number
  subManagerUserIds?: string
}

export interface WorksiteBelongToManagerItem {
  id: number
  code: string
  address: string | null
  isOffice: boolean | null
  managers: any[]
  name: string
}

export interface WorksiteBelongToManagerResponse {
  items: WorksiteBelongToManagerItem[]
  pageNum: number
  pageSize: number
}

export interface WorksiteComboItem {
  id: number
  name: string
  code: string
  address: string
}

export interface OfficeComboItem {
  id: number
  name: string
  code: string
  address: string
}

export interface WorksiteComboResponse {
  items: WorksiteComboItem[]
  pageNum: number
  pageSize: number
}

export interface OfficeComboResponse {
  items: OfficeComboItem[]
  pageNum: number
  pageSize: number
}

export interface WorksiteComboParams {
  pageSize: number
  pageNum: number
}

export async function getWorksiteComboApi() {
  return useGet<WorksiteComboResponse>('v1/workplace/simple-worksites')
}

export async function getOfficeComboApi() {
  return useGet<OfficeComboResponse>('v1/workplace/simple-offices')
}

export async function getWorkplacesNonpaging() {
  return useGet<WorkplacesResponse>('v1/workplace/nonpaging')
}
export async function getWorkplacesApi(params: WorkplaceSearchParams) {
  return useGet<WorkplacesResponse>('v1/workplace/paginated', params)
}

export async function createWorkplaceApi(data?: WorkplaceParams) {
  return usePost<WorkplaceInfo>('v1/workplace', data)
}

export async function assignManagerApi(data: WorksiteManager) {
  return usePut<WorkplaceInfo>('v1/workplace/assign-manager', data)
}

export async function updateWorkplaceApi(data?: WorkplaceParams) {
  return usePut<WorkplaceInfo>(`v1/workplace/${data?.workplaceId}`, data)
}

export async function getWorkplacesAllApi() {
  return useGet<WorkplacesResponse>('v1/workplace/nonpaging')
}

export async function deleteWorkplaceApi(id: number) {
  return useDelete<WorkplacesResponse>(`v1/workplace/${id}`)
}
