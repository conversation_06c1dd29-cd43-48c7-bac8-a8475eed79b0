<script lang="ts" setup>
import type { UnwrapRef } from 'vue'
import { reactive, ref } from 'vue'
import { CheckCircleOutlined, CheckOutlined, ClockCircleOutlined, CloseOutlined, DeleteOutlined, EditOutlined, FileTextOutlined, PlusOutlined, SaveOutlined, TeamOutlined, UserOutlined } from '@ant-design/icons-vue'
import type { Dayjs } from 'dayjs'
import { usePagination } from 'vue-request'
import { cloneDeep, uniqueId } from 'lodash'
import type { AttendanceItem } from '~@/api/attendance'
import { createDailyReportApi, updateDailyReportApi } from '~@/api/company/project'
import type { DailyReportItem, DailyReportParams, EmployeeWorkload, ManagedProjectItem, OutSourceWorkload } from '~@/api/company/project'
import type { OutsourceQueryParams } from '~@/api/outsource'
import { fetchOutsourceListApi } from '~@/api/outsource'
import type { EmployeeItem } from '~@/api/employee/employee'
import { getEmployeeDataApi } from '~@/api/employee/employee'
import logger from '~@/utils/logger'

const props = defineProps({
  project: {
    type: Object as () => ManagedProjectItem,
    required: true,
  },
  projectName: {
    type: String,
    required: true,
  },
  address: {
    type: String,
    required: true,
  },
  selectedDate: {
    type: Object as () => Dayjs,
    required: true,
  },
  attendanceList: {
    type: Array as () => AttendanceItem[],
    required: true,
  },
  dailyReportData: {
    type: Object as () => DailyReportItem,
    required: false,
  },
})

const emit = defineEmits<{
  (event: 'updateIsDailyReportCreated'): void
}>()

const { t } = useI18n()
const employeeEditableData: UnwrapRef<Record<string, EmployeeWorkload>> = reactive({})
const outsourceEditableData: UnwrapRef<Record<string, OutSourceWorkload>> = reactive({})
const employeeData = ref<EmployeeItem[]>([])
const workNotes = ref('')

const messageNotify = useMessage()
// Danh sách chấm công
interface AttendanceRecord {
  key: string
  name: string
  workTime: string
  workTimeDetails?: string
  remainingTime: string
  remainingTimeDetails?: string
  endTime?: string
  endTimeDetails?: string
  approvalStatus: string
  remarks?: string
  specialNote?: string
  isWeekday?: boolean
}

// Định nghĩa cột cho bảng
const columns = reactive<any>([
  {
    title: t('employeeName'),
    dataIndex: 'employeeName',
    key: 'employeeName',
    width: 200,
  },
  {
    title: t('totalWorkTime'),
    dataIndex: 'totalWorkTime',
    key: 'worktotalWorkTime',
    width: 100,
  },
  {
    title: t('total-over-time'),
    dataIndex: 'totalOverTime',
    key: 'remainingTime',
    width: 100,
  },
  {
    title: t('coefficient'),
    dataIndex: 'coefficient',
    key: 'coefficient',
    width: 100,
  },
  {
    title: t('attendance-status'),
    dataIndex: 'attendanceStatus',
    key: 'attendanceStatus',
    width: 100,
  },
  {
    title: t('note'),
    dataIndex: 'description',
    key: 'description',
  },
])

const employeeWorkloadColumns = reactive<any>([
  {
    title: t('employeeName'),
    dataIndex: 'employeeName',
    key: 'employeeName',
    align: 'center',
    width: 200,
  },
  {
    title: t('workloadOnMainConstruction'),
    dataIndex: 'workloadOnMainConstruction',
    key: 'workloadOnMainConstruction',
    align: 'center',
  },
  {
    title: t('workloadOnSubConstruction'),
    dataIndex: 'workloadOnSubConstruction',
    key: 'workloadOnSubConstruction',
    align: 'center',
  },
  {
    title: t('totalWorkload'),
    dataIndex: 'totalWorkload',
    key: 'action',
    align: 'center',
  },
  {
    title: t('action'),
    dataIndex: 'action',
    key: 'action',
    align: 'center',
  },
])

const outSourceWorkloadColumns = reactive<any>([
  {
    title: t('outsource-name'),
    dataIndex: 'outSourceName',
    key: 'outSourceName',
    align: 'center',
    width: 200,
  },
  {
    title: t('workloadOnMainConstruction'),
    dataIndex: 'workloadOnMainConstruction',
    key: 'workloadOnMainConstruction',
    align: 'center',
  },
  {
    title: t('workloadOnSubConstruction'),
    dataIndex: 'workloadOnSubConstruction',
    key: 'workloadOnSubConstruction',
    align: 'center',
  },
  {
    title: t('totalWorkload'),
    dataIndex: 'totalWorkload',
    key: 'action',
    align: 'center',
  },
  {
    title: t('action'),
    dataIndex: 'action',
    key: 'action',
    align: 'center',
  },
])

// Xuất báo cáo
const exportModalVisible = ref(false)
const isLoading = ref(false)

const exportFormat = ref('pdf')
const exportRange = ref('day')
const includeNotes = ref(true)
const employeeWorkloads = ref<EmployeeWorkload[]>([])
const outSourceWorkloads = ref<OutSourceWorkload[]>([])

// Computed properties
// const currentDate = computed(() => {
//   return selectedDate.format('M/DD')
// })

// Methods
function getDayType() {
  const day = props.selectedDate.day()
  return day === 0 || day === 6 ? t('weekend') : t('weekday')
}
function getRowClassName(record: AttendanceRecord) {
  return record.isWeekday ? 'bg-blue-50' : ''
}

function handleExportOk() {
  exportModalVisible.value = false
}

async function createReport() {
  if (isLoading.value || !props.project.id)
    return

  isLoading.value = true
  const employeeWorkloadParams = employeeWorkloads.value.map((item: EmployeeWorkload) => {
    return {
      employeeId: item.employeeId,
      workloadOnMainConstruction: item.workloadOnMainConstruction,
      workloadOnSubConstruction: item.workloadOnSubConstruction,
    }
  })
  const outSourceWorkloadParams = outSourceWorkloads.value.map((item: OutSourceWorkload) => {
    return {
      outSourceId: item.outSourceId,
      workloadOnMainConstruction: item.workloadOnMainConstruction,
      workloadOnSubConstruction: item.workloadOnSubConstruction,
    }
  })
  const params: DailyReportParams = {
    description: workNotes.value,
    employeeWorkload: employeeWorkloadParams,
    outSourceWorkload: outSourceWorkloadParams,
    reportDate: props.selectedDate.format('YYYY-MM-DD'),
  }
  try {
    const { status, message } = await createDailyReportApi(props.project.id, params)
    if (status === 200) {
      messageNotify.success(message)
      emit('updateIsDailyReportCreated')
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (e) {
  }
  finally {
    isLoading.value = false
  }
}

function handleUpdateReport() {
  updateDailyReport()
}

async function updateDailyReport() {
  const reportId = props.dailyReportData?.reportId
  if (isLoading.value || !props.project.id || !reportId)
    return

  isLoading.value = true
  const employeeWorkloadParams = employeeWorkloads.value.map((item: EmployeeWorkload) => {
    return {
      employeeId: item.employeeId,
      workloadOnMainConstruction: item.workloadOnMainConstruction,
      workloadOnSubConstruction: item.workloadOnSubConstruction,
    }
  })
  const outSourceWorkloadParams = outSourceWorkloads.value.map((item: OutSourceWorkload) => {
    return {
      outSourceId: item.outSourceId,
      workloadOnMainConstruction: item.workloadOnMainConstruction,
      workloadOnSubConstruction: item.workloadOnSubConstruction,
    }
  })
  const params: DailyReportParams = {
    description: workNotes.value,
    employeeWorkload: employeeWorkloadParams,
    outSourceWorkload: outSourceWorkloadParams,
    reportDate: props.selectedDate.format('YYYY-MM-DD'),
  }

  try {
    const { status, message } = await updateDailyReportApi(reportId, params)
    if (status === 200)
      messageNotify.success(message)

    else
      messageNotify.error(message)
  }
  catch (e) {
  }
  finally {
    isLoading.value = false
  }
}

const employeeOptions = computed(() => {
  return props.attendanceList.map((item: AttendanceItem) => {
    return {
      employeeId: item.employeeId,
      employeeName: item.employeeName,
    }
  })
})

const outSourceParams: OutsourceQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
})

async function queryOutSource(outSourceParams: OutsourceQueryParams) {
  const data = await fetchOutsourceListApi(outSourceParams)
  return data
}

const {
  data: outSourceData,
} = usePagination(queryOutSource, {
  defaultParams: [outSourceParams],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const outSourceOptions = computed(() => {
  return outSourceData.value?.items || []
})

// const fakeData = ref<DailyReportItem>({
//   reportId: 'RPT-20250411-001',
//   projectId: 'PRJ-0001',
//   projectCode: 'PJT-ABC123',
//   projectName: 'Xây dựng chung cư Sakura Tower',
//   address: '123 Đường Hoa Anh Đào, Quận 1, TP.HCM',
//   reportDate: '2025-04-11',
//   description: 'Báo cáo tiến độ thi công tuần thứ 15.',
//   employeeWorkloads: [
//     {
//       employeeId: 'EMP-001',
//       employeeName: 'Nguyễn Văn A',
//       rankingName: 'Rank A',
//       workloadOnMainConstruction: 32,
//       workloadOnSubConstruction: 8,
//     },
//     {
//       employeeId: 'EMP-002',
//       employeeName: 'Trần Thị B',
//       rankingName: 'Rank B',
//       workloadOnMainConstruction: 28,
//       workloadOnSubConstruction: 12,
//     },
//   ],
//   outSourceWorkloads: [
//     {
//       outSourceId: 'OUT-001',
//       outSourceName: 'Công ty TNHH Xây dựng Minh Tâm',
//       workloadOnMainConstruction: 100,
//       workloadOnSubConstruction: 20,
//     },
//   ],
// })

watch(() => props.dailyReportData, (newVal) => {
  if (!newVal)
    return
  refreshDailyReport(newVal)
}, { deep: true, immediate: true })

async function refreshDailyReport(newDailyReport: DailyReportItem) {
  workNotes.value = newDailyReport.description ?? ''
  employeeWorkloads.value = newDailyReport?.employeeWorkloads.map((item: EmployeeWorkload) => {
    return {
      ...item,
      key: uniqueId(),
    }
  }) || []
  outSourceWorkloads.value = newDailyReport?.outSourceWorkloads.map((item: OutSourceWorkload) => {
    return {
      ...item,
      key: uniqueId(),
    }
  }) || []
}

const isDailyReportExits = computed(() => {
  return !!props.dailyReportData?.reportId
})

function addNewEmployeeWorkload() {
  employeeWorkloads.value.push({
    key: uniqueId(),
    employeeId: '',
    employeeName: '',
    rankingName: '',
    workloadOnMainConstruction: 0,
    workloadOnSubConstruction: 0,
  })
}

function addNewOutsourceWorkload() {
  outSourceWorkloads.value.push({
    key: uniqueId(),
    outSourceId: '',
    outSourceName: '',
    workloadOnMainConstruction: 0,
    workloadOnSubConstruction: 0,
  })
}

function editEmployeeWorkload(key: string) {
  employeeEditableData[key] = cloneDeep(employeeWorkloads.value.filter(item => key === item.key)[0])
}

function deleteEmployeeWorkload(key: string) {
  employeeWorkloads.value = employeeWorkloads.value.filter(item => key !== item.key)
}

function saveEmployeeWorkload(key: string) {
  employeeEditableData[key].employeeName = getEmployeeName(employeeEditableData[key].employeeId) ?? ''
  Object.assign(employeeWorkloads.value.filter(item => key === item.key)[0], employeeEditableData[key])
  delete employeeEditableData[key]
}

function cancelEmployeeWorkload(key: string) {
  delete employeeEditableData[key]
}

function editOutsourceWorkload(key: string) {
  outsourceEditableData[key] = cloneDeep(outSourceWorkloads.value.filter(item => key === item.key)[0])
}

function deleteOutsourceWorkload(key: string) {
  outSourceWorkloads.value = outSourceWorkloads.value.filter(item => key !== item.key)
}

function cancelOutsourceWorkload(key: string) {
  delete outsourceEditableData[key]
}

function saveOutsourceWorkload(key: string) {
  outsourceEditableData[key].outSourceName = getOutsourceName(outsourceEditableData[key].outSourceId) ?? ''
  Object.assign(outSourceWorkloads.value.filter(item => key === item.key)[0], outsourceEditableData[key])
  delete outsourceEditableData[key]
}

function getEmployeeName(value: any) {
  const idx = employeeOptions.value.findIndex((item: any) => item.employeeId === value)
  if (idx !== -1)
    return employeeOptions.value[idx].employeeName ?? ''
}

function getOutsourceName(value: any) {
  const idx = outSourceOptions.value.findIndex((item: any) => item.outSourceId === value)
  if (idx !== -1)
    return outSourceOptions.value[idx].outSourceName ?? ''
}

async function getEmployeeData() {
  try {
    const { status, data } = await getEmployeeDataApi()
    if (status === 200)
      employeeData.value = data?.items ?? []
  }
  catch (e) {
    logger.error(e)
  }
}

function findStandardWorkingHour(employeeId?: string) {
  if (!employeeId)
    return 0
  const idx = employeeData.value.findIndex((item: any) => item.employeeId === employeeId)
  if (idx !== -1)
    return employeeData.value[idx].standardWorkingHours ?? 0
  return 0
}

const standardWorkLoad = [4.0, 3.75, 3.5, 3.25, 3.0, 2.75, 2.5, 2.25, 2.0, 1.75, 1.5, 1.25, 1.0, 0.75, 0.5, 0.25, 0.0]
function roundToTwoDecimals(totalWorkTime: number, standardWorkingHour: number): number {
  const workload = Math.round(((totalWorkTime + Number.EPSILON) / standardWorkingHour) * 100) / 100
  let result = 0
  const len = standardWorkLoad.length
  for (let i = 0; i < len; i++) {
    const value = standardWorkLoad[i]
    if (workload >= value) {
      result = value
      break
    }
  }
  return result
}

function initEmployeeWorkloads() {
  const groupedEmployees = props.attendanceList.reduce((acc, curr) => {
    const key = curr.employeeId ?? '0'
    if (!acc[key]) {
      const employeeWorkload: EmployeeWorkload = {
        employeeId: curr.employeeId ?? '',
        employeeName: curr.employeeName ?? '',
        rankingName: '',
        workloadOnMainConstruction: 0,
        workloadOnSubConstruction: 0,
        totalWorkload: 0,
        totalWorkTime: curr.totalWorkTime ?? 0,
      }

      acc[key] = {
        ...employeeWorkload,
      }
    }
    else {
      const totalWorkTime = curr.totalWorkTime ?? 0
      acc[key].totalWorkTime! += totalWorkTime
    }
    return acc
  }, {} as Record<string, EmployeeWorkload>)

  // Clear existing data before adding new data
  employeeWorkloads.value = []

  Object.values(groupedEmployees).forEach((items) => {
    const employeeWorkload: EmployeeWorkload = {
      key: uniqueId(),
      employeeId: items.employeeId,
      employeeName: items.employeeName,
      rankingName: '',
      workloadOnMainConstruction: 0,
      workloadOnSubConstruction: 0,
      totalWorkload: items.totalWorkload,
    }
    const standardWorkingHour = findStandardWorkingHour(items.employeeId)
    const totalWorkTime = items.totalWorkTime ?? 0

    if (standardWorkingHour > 0) {
      employeeWorkload.workloadOnMainConstruction = roundToTwoDecimals(totalWorkTime, standardWorkingHour)
      employeeWorkload.totalWorkload = employeeWorkload.workloadOnMainConstruction
    }
    employeeWorkloads.value.push(employeeWorkload)
  })
}
onMounted(async () => {
  if (props.attendanceList.length > 0) {
    await getEmployeeData()
    initEmployeeWorkloads()
  }
})
</script>

<template>
  <div class="max-w-7xl mx-auto p-6 min-h-screen">
    <!-- Header Section -->
    <div class="bg-blue-50 rounded-xl p-6 mb-6 text-black shadow-md">
      <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 ">
        <div class="flex items-center gap-3">
          <span class="text-3xl font-bold">
            {{ selectedDate.format("MM/DD") }}
          </span>
          <a-tag
            :color="getDayType() === t('weekend') ? 'orange' : 'blue'"
            class="text-sm font-semibold px-3 py-1"
          >
            {{ getDayType() }}
          </a-tag>
        </div>
        <div class="text-right">
          <div class="text-lg font-semibold mb-1">
            {{ projectName }}
          </div>
          <div class="text-sm opacity-90">
            <span class="font-medium">{{ t('address') }}:</span>
            <span class="ml-2">{{ address }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Attendance Summary Card -->
    <a-card class="mb-6 rounded-xl border-1">
      <template #title>
        <div class="flex items-center gap-2 text-lg font-semibold text-gray-700">
          <CheckCircleOutlined class="text-blue-500 text-xl" />
          {{ t('attendance-list') }}
        </div>
      </template>

      <div class="overflow-hidden rounded-lg">
        <a-table
          :data-source="attendanceList"
          :columns="columns"
          :pagination="false"
          :row-class-name="getRowClassName"
          :scroll="{ x: 'max-content' }"
          size="middle"
          class="attendance-table"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'totalOverTime'">
              <a-tag color="green" class="flex items-center gap-1 w-fit">
                <ClockCircleOutlined />
                {{ record.totalOverTime }}
              </a-tag>
            </template>

            <template v-if="column.dataIndex === 'attendanceStatus'">
              <a-tag v-if="!record.checkInTime" color="blue">
                {{ t('status.scheduled') }}
              </a-tag>
              <a-tag v-else-if="record.breakList && !record.breakList.length" color="green">
                {{ t('status.checkIn') }}
              </a-tag>
              <a-tag v-else-if="!record.checkOutTime" color="orange">
                {{ t('status.break') }}
              </a-tag>
              <a-tag v-else color="red">
                {{ t('status.checkOut') }}
              </a-tag>
            </template>

            <template v-if="column.key === 'remarks'">
              <div>
                <div>{{ record.remarks }}</div>
                <div v-if="record.specialNote" class="text-xs text-red-500 italic mt-1">
                  {{ record.specialNote }}
                </div>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- Employee Workload Section -->
    <a-card class="mb-6 rounded-xl border-1">
      <template #title>
        <div class="flex items-center gap-2 text-lg font-semibold text-gray-700">
          <UserOutlined class="text-blue-500 text-xl" />
          {{ t('employeeName') }} {{ t('totalWorkload') }}
        </div>
      </template>
      <template #extra>
        <a-button type="primary" ghost class="flex items-center gap-2" @click="addNewEmployeeWorkload">
          <PlusOutlined />
          {{ t('button.add') }}
        </a-button>
      </template>

      <div class="overflow-hidden rounded-lg">
        <a-table
          :data-source="employeeWorkloads"
          :columns="employeeWorkloadColumns"
          :pagination="false"
          size="middle"
          class="workload-table"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'employeeName'">
              <div v-if="employeeEditableData[record.key]" class="min-w-32">
                <a-select
                  v-model:value="employeeEditableData[record.key].employeeId"
                  :options="employeeOptions"
                  :field-names="{ label: 'employeeName', value: 'employeeId' }"
                  :placeholder="t('message.please-select', { msg: t('employeeName') })"
                  class="w-full"
                />
              </div>
              <div v-else class="flex items-center gap-2 flex-wrap">
                <span class="font-medium text-gray-700">{{ record.employeeName }}</span>
                <a-tag v-if="record?.rankingName" color="blue" class="text-xs">
                  {{ record.rankingName }}
                </a-tag>
                <a-tag v-else color="red" class="text-xs">
                  {{ t('unRanked') }}
                </a-tag>
              </div>
            </template>

            <template v-if="column.dataIndex === 'workloadOnMainConstruction'">
              <div v-if="employeeEditableData[record.key]" class="min-w-28">
                <a-input-number
                  v-model:value="employeeEditableData[record.key].workloadOnMainConstruction"
                  :min="0"
                  :step="0.25"
                  :precision="2"
                  class="w-full"
                />
              </div>
              <div v-else class="text-center font-medium text-gray-600">
                {{ record.workloadOnMainConstruction }}
              </div>
            </template>

            <template v-if="column.dataIndex === 'workloadOnSubConstruction'">
              <div v-if="employeeEditableData[record.key]" class="min-w-28">
                <a-input-number
                  v-model:value="employeeEditableData[record.key].workloadOnSubConstruction"
                  :min="0"
                  :step="0.25"
                  :precision="2"
                  class="w-full"
                />
              </div>
              <div v-else class="text-center font-medium text-gray-600">
                {{ record.workloadOnSubConstruction }}
              </div>
            </template>

            <template v-if="column.dataIndex === 'totalWorkload'">
              <div class="text-center">
                <a-tag color="purple" class="font-semibold">
                  {{ (record.workloadOnMainConstruction || 0) + (record.workloadOnSubConstruction || 0) }}
                </a-tag>
              </div>
            </template>

            <template v-if="column.dataIndex === 'action'">
              <div class="flex justify-center gap-1">
                <div v-if="!employeeEditableData[record.key]" class="flex gap-1">
                  <a-tooltip :title="t('button.edit')">
                    <a-button
                      type="text"
                      size="small"
                      class="text-blue-500 hover:bg-blue-50"
                      @click="editEmployeeWorkload(record.key)"
                    >
                      <EditOutlined />
                    </a-button>
                  </a-tooltip>
                  <a-tooltip :title="t('button.delete')">
                    <a-button
                      type="text"
                      size="small"
                      class="text-red-500 hover:bg-red-50"
                      @click="deleteEmployeeWorkload(record.key)"
                    >
                      <DeleteOutlined />
                    </a-button>
                  </a-tooltip>
                </div>
                <div v-else class="flex gap-1">
                  <a-tooltip :title="t('button.save')">
                    <a-button
                      type="text"
                      size="small"
                      class="text-green-500 hover:bg-green-50"
                      @click="saveEmployeeWorkload(record.key)"
                    >
                      <CheckOutlined />
                    </a-button>
                  </a-tooltip>
                  <a-tooltip :title="t('button.cancel')">
                    <a-button
                      type="text"
                      size="small"
                      class="text-red-500 hover:bg-red-50"
                      @click="cancelEmployeeWorkload(record.key)"
                    >
                      <CloseOutlined />
                    </a-button>
                  </a-tooltip>
                </div>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- Outsource Workload Section -->
    <a-card class="mb-6 rounded-xl border-1">
      <template #title>
        <div class="flex items-center gap-2 text-lg font-semibold text-gray-700">
          <TeamOutlined class="text-blue-500 text-xl" />
          {{ t('outsource-name') }} {{ t('totalWorkload') }}
        </div>
      </template>
      <template #extra>
        <a-button type="primary" ghost class="flex items-center gap-2" @click="addNewOutsourceWorkload">
          <PlusOutlined />
          {{ t('button.add') }}
        </a-button>
      </template>

      <div class="overflow-hidden rounded-lg">
        <a-table
          :data-source="outSourceWorkloads"
          :columns="outSourceWorkloadColumns"
          :pagination="false"
          size="middle"
          class="workload-table"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'outSourceName'">
              <div v-if="outsourceEditableData[record.key]" class="min-w-32">
                <a-select
                  v-model:value="outsourceEditableData[record.key].outSourceId"
                  :options="outSourceOptions"
                  :field-names="{ label: 'outSourceName', value: 'outSourceId' }"
                  placeholder="Chọn nhà thầu phụ"
                  class="w-full"
                />
              </div>
              <div v-else class="font-medium text-gray-700">
                {{ record.outSourceName }}
              </div>
            </template>

            <template v-if="column.dataIndex === 'workloadOnMainConstruction'">
              <div v-if="outsourceEditableData[record.key]" class="min-w-28">
                <a-input-number
                  v-model:value="outsourceEditableData[record.key].workloadOnMainConstruction"
                  :min="0"
                  :step="0.25"
                  :precision="2"
                  class="w-full"
                />
              </div>
              <div v-else class="text-center font-medium text-gray-600">
                {{ record.workloadOnMainConstruction }}
              </div>
            </template>

            <template v-if="column.dataIndex === 'workloadOnSubConstruction'">
              <div v-if="outsourceEditableData[record.key]" class="min-w-28">
                <a-input-number
                  v-model:value="outsourceEditableData[record.key].workloadOnSubConstruction"
                  :min="0"
                  :step="0.25"
                  :precision="2"
                  class="w-full"
                />
              </div>
              <div v-else class="text-center font-medium text-gray-600">
                {{ record.workloadOnSubConstruction }}
              </div>
            </template>

            <template v-if="column.dataIndex === 'totalWorkload'">
              <div class="text-center">
                <a-tag color="purple" class="font-semibold">
                  {{ (record.workloadOnMainConstruction || 0) + (record.workloadOnSubConstruction || 0) }}
                </a-tag>
              </div>
            </template>

            <template v-if="column.dataIndex === 'action'">
              <div class="flex justify-center gap-1">
                <div v-if="!outsourceEditableData[record.key]" class="flex gap-1">
                  <a-tooltip title="Chỉnh sửa">
                    <a-button
                      type="text"
                      size="small"
                      class="text-blue-500 hover:bg-blue-50"
                      @click="editOutsourceWorkload(record.key)"
                    >
                      <EditOutlined />
                    </a-button>
                  </a-tooltip>
                  <a-tooltip title="Xóa">
                    <a-button
                      type="text"
                      size="small"
                      class="text-red-500 hover:bg-red-50"
                      @click="deleteOutsourceWorkload(record.key)"
                    >
                      <DeleteOutlined />
                    </a-button>
                  </a-tooltip>
                </div>
                <div v-else class="flex gap-1">
                  <a-tooltip title="Lưu">
                    <a-button
                      type="text"
                      size="small"
                      class="text-green-500 hover:bg-green-50"
                      @click="saveOutsourceWorkload(record.key)"
                    >
                      <CheckOutlined />
                    </a-button>
                  </a-tooltip>
                  <a-tooltip title="Hủy">
                    <a-button
                      type="text"
                      size="small"
                      class="text-red-500 hover:bg-red-50"
                      @click="cancelOutsourceWorkload(record.key)"
                    >
                      <CloseOutlined />
                    </a-button>
                  </a-tooltip>
                </div>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- Work Notes Section -->
    <a-card class="mb-6 rounded-xl border-1">
      <template #title>
        <div class="flex items-center gap-2 text-lg font-semibold text-gray-700">
          <FileTextOutlined class="text-blue-500 text-xl" />
          {{ t('work-notes') }}
        </div>
      </template>

      <div class="p-2">
        <a-textarea
          v-model:value="workNotes"
          :placeholder="t('placeholder.enter-data', { msg: `${t('work-notes')}` })"
          :rows="4"
          :max-length="500"
          show-count
          class="rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
        />
      </div>
    </a-card>

    <!-- Action Section -->
    <div class="p-3">
      <div class="flex justify-center gap-4">
        <a-button
          v-if="!isDailyReportExits"
          :loading="isLoading"
          type="primary"
          size="large"
          class="min-w-30 h-12 text-base font-semibold rounded-lg flex items-center gap-2"
          @click="createReport"
        >
          <SaveOutlined />
          {{ t('button.create') }}
        </a-button>
        <a-button
          v-else
          :loading="isLoading"
          type="primary"
          size="large"
          class="min-w-30 h-12 text-base font-semibold rounded-lg flex items-center gap-2"
          @click="handleUpdateReport"
        >
          <EditOutlined />
          {{ t('button.update') }}
        </a-button>
      </div>
    </div>
  </div>

  <!-- Export Report Modal -->
  <a-modal
    v-model:visible="exportModalVisible"
    title="Xuất báo cáo"
    ok-text="Xuất báo cáo"
    cancel-text="Hủy"
    @ok="handleExportOk"
  >
    <div class="mb-4">
      <p class="mb-2">
        Chọn định dạng xuất:
      </p>
      <a-radio-group v-model:value="exportFormat">
        <a-radio value="pdf">
          PDF
        </a-radio>
        <a-radio value="excel">
          Excel
        </a-radio>
        <a-radio value="csv">
          CSV
        </a-radio>
      </a-radio-group>
    </div>

    <div class="mb-4">
      <p class="mb-2">
        Chọn phạm vi dữ liệu:
      </p>
      <a-radio-group v-model:value="exportRange">
        <a-radio value="day">
          Ngày hiện tại
        </a-radio>
        <a-radio value="week">
          Tuần này
        </a-radio>
        <a-radio value="month">
          Tháng này
        </a-radio>
      </a-radio-group>
    </div>

    <div>
      <a-checkbox v-model:checked="includeNotes">
        Bao gồm ghi chú
      </a-checkbox>
    </div>
  </a-modal>
</template>

<style scoped>
</style>
