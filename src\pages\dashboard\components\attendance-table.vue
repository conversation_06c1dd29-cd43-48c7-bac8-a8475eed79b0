<!-- eslint-disable curly -->
<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
// import axios from 'axios'
import { Modal, type TableColumnType, type TableProps } from 'ant-design-vue'
import dayjs from 'dayjs'
import type { SortOrder } from 'ant-design-vue/es/table/interface'
import {
  CheckOutlined,
  CloseOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons-vue'
import { type CSSProperties, createVNode } from 'vue'
import { createAttendanceApi, deleteAttendanceApi, updateAttendanceApi } from '~@/api/attendance'
import type { AttendanceItem, AttendanceItemParams, AttendanceUpdateParams } from '~@/api/attendance'
import logger from '~@/utils/logger'
import WorkTimeModal from '~@/components/common/WorkTimeModal.vue'

const props = defineProps({
  dataSource: {
    type: Array as () => AttendanceItem[],
    required: true,
  },
  selectedDate: {
    type: dayjs.Dayjs,
  },
  scroll: {
    type: Object as () => {
      x?: string | number | true
      y?: string | number
    },
  },
  typeOfSort: {
    type: String,
    default: 'descend',
  },
})

const emit = defineEmits<{
  // (event: 'fastCheckIn', shiftId: string, params: FastCheckInParams): void
  (event: 'attendanceDailyRequest', employeeShiftId: string): void
  (event: 'refreshAttendance'): void
  (event: 'update:selectedDate', date: dayjs.Dayjs): void
}>()

const { t } = useI18n()
const messageNotify = useMessage()
// const { isWorkingTimeValid } = useValidateTime()
const idDetail = ref<string>('')
const loading = shallowRef(false)
const isWorkTimeModalOpened = ref(false)
const attendanceItem = ref<AttendanceItem | null>(null)
const sortedData = ref<AttendanceItem[]>([])
const sortedInfo = ref({
  columnKey: 'workingDate',
  order: props.typeOfSort,
})

const columns = computed<TableColumnType[]>(() => {
  const sorted = sortedInfo.value || {}
  return [
    {
      title: t('dashboard.workplace.timeKeepingData.date'),
      dataIndex: 'workingDate',
      fixed: 'left',
      key: 'workingDate',
      align: 'center',
      // sorter: (a: AttendanceItem, b: AttendanceItem) => {
      //   if (sorted.order === 'ascend') {
      //     if (dayjs(a.workingDate).isBefore(dayjs(b.workingDate)))
      //       return -1
      //     if (dayjs(a.workingDate).isAfter(dayjs(b.workingDate)))
      //       return 1
      //     return 0
      //   }
      //   else {
      //     if (dayjs(a.workingDate).isBefore(dayjs(b.workingDate)))
      //       return 1
      //     if (dayjs(a.workingDate).isAfter(dayjs(b.workingDate)))
      //       return -1
      //     return 0
      //   }
      // },
      sortOrder: sorted.columnKey === 'workingDate' ? sorted.order as SortOrder : undefined,
      customCell: (data: any, index: number = 0) => {
        if (sortedData.value.length > 0) {
          if (index === 0 || sortedData.value[index - 1].workingDate !== data.workingDate) {
            let count = 1
            for (let i = index + 1; i < sortedData.value.length; i++) {
              if (sortedData.value[i].workingDate === data.workingDate) {
                count += 1
              }
            }
            return { rowSpan: count }
          }
          else {
            return { rowSpan: 0 }
          }
        }
        else {
          return { rowSpan: 1 }
        }
      },
    },
    {
      title: t('project'),
      dataIndex: 'projectName',
      responsive: ['lg'],
      key: 'projectName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: t('table.checkIn'),
      dataIndex: 'checkInTime',
      align: 'center',
      ellipsis: true,
    },
    {
      title: t('table.breakTime'),
      dataIndex: 'breakList',
      responsive: ['lg'],
      align: 'center',
      ellipsis: true,
    },
    {
      title: t('table.checkOut'),
      dataIndex: 'checkOutTime',
      align: 'center',
      ellipsis: true,
    },
    {
      title: t('totalWorkTime'),
      dataIndex: 'totalWorkTime',
      align: 'center',
      customRender: ({ record }: any) => {
        if (!record.totalWorkTime)
          return '-'
        return Math.round(record.totalWorkTime * 100) / 100
      },
      ellipsis: true,
      responsive: ['lg'],
    },
    {
      title: t('totalOvertime'),
      dataIndex: 'totalOverTime',
      align: 'center',
      customRender: ({ record }: any) => {
        if (!record.totalOverTime)
          return '-'
        return Math.round(record.totalOverTime * 100) / 100
      },
      ellipsis: true,
      responsive: ['lg'],
    },
    {
      title: t('approvalStatus'),
      dataIndex: 'isRequested',
      align: 'center',
      width: 200,
      ellipsis: true,
    },
    {
      title: t('table.comment'),
      dataIndex: 'description',
      align: 'center',
      ellipsis: true,
      customRender: ({ record }: any) => {
        if (!record.description)
          return '-'
        return record.description
      },
      responsive: ['lg'],
    },
    {
      title: t('action'),
      dataIndex: 'action',
      align: 'center',
    },
  ]
})

const sortDataByType = (type: string) => {
  let sortValue = -1
  if (type === 'descend') {
    sortValue = 1
  }
  sortedData.value = sortedData.value.sort((a: AttendanceItem, b: AttendanceItem) => {
    const dateA = dayjs(a.workingDate)
    const dateB = dayjs(b.workingDate)
    if (dateA.isBefore(dateB))
      return sortValue
    if (dateA.isAfter(dateB))
      return sortValue * -1
    return 0
  })
}

const handleChange: TableProps['onChange'] = (_, __, sorter) => {
  sortedInfo.value = sorter as { columnKey: string; order: string }
  sortDataByType(sortedInfo.value.order)
}

// const attendanceDailyRequest = (employeeShift: AttendanceItem) => {
//   if (!isWorkingTimeValid(employeeShift?.breakList ?? [], employeeShift?.checkInTime ?? '', employeeShift?.checkOutTime ?? ''))
//     return
//   emit('attendanceDailyRequest', employeeShift?.employeeShiftId ?? '')
// }

const customizeTable = () => {
  const style: CSSProperties = {
    fontSize: '1rem',
  }
  return style
}

// const fastCheckIn = async (employeeShift: AttendanceItem) => {
//   const shiftId = employeeShift.employeeShiftId
//   if (!shiftId) {
//     return
//   }
//   const params: FastCheckInParams = {
//     latitude: '',
//     longitude: '',
//   }
//   emit('fastCheckIn', shiftId, params)
// }

// const isFastCheckInSatify = (employeeShift: AttendanceItem) => {
//   if (!employeeShift.checkInTime && employeeShift.scheduledStartTime && employeeShift.scheduledEndTime) {
//     return true
//   }
//   return false
// }
function getModifierColor(modifier?: 'SYSTEM' | 'AUTHOR' | 'MANAGER') {
  if (!modifier)
    return 'text-blue-600'
  const colors: Record<string, string> = {
    SYSTEM: 'text-blue-600',
    AUTHOR: 'text-orange',
    MANAGER: 'text-green-600',
    AUTO: 'text-red-600',
  }
  return colors[modifier] || 'text-blue-600'
}

function viewAttendanceDetail(employeeShift: AttendanceItem) {
  isWorkTimeModalOpened.value = true
  attendanceItem.value = employeeShift
}

async function deleteAttendance(employeeShiftId: string) {
  if (loading.value)
    return
  try {
    loading.value = true
    const { status, message } = await deleteAttendanceApi(employeeShiftId)
    if (status === 200) {
      messageNotify.success(message)
      emit('refreshAttendance')
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (e) {
    logger.error(e)
  }
  finally {
    loading.value = false
  }
}

function handleDeleteShift(employeeShift: AttendanceItem) {
  Modal.confirm({
    title: t('confirm.delete'),
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', {}, `${t('alert.confirmDelete', { msg: t('attendance') })}`),
    cancelText: t('button.cancel'),
    okText: t('button.ok'),
    async onOk() {
      if (!employeeShift?.employeeShiftId)
        return
      await deleteAttendance(employeeShift.employeeShiftId)
    },
    onCancel() {},
  })
}

const onUpdateAttendance = async (employeeShiftId: string, params: AttendanceUpdateParams) => {
  if (loading.value)
    return
  loading.value = true
  try {
    const { status, message } = await updateAttendanceApi(employeeShiftId, params)
    if (status === 200) {
      messageNotify.success(message)
    }
  }
  catch (e) {
  }
  finally {
    loading.value = false
  }
}

const onCreatNewAttendance = async (params: AttendanceUpdateParams) => {
  if (loading.value)
    return
  loading.value = true
  try {
    const { status, message } = await createAttendanceApi(params)
    if (status === 200) {
      messageNotify.success(message)
    }
  }
  catch (e) {
  }
  finally {
    loading.value = false
  }
}

async function onSaveAttendance(employeeShiftId: string, params: AttendanceItemParams) {
  if (employeeShiftId !== '') {
    await onUpdateAttendance(employeeShiftId, params)
    emit('refreshAttendance')
  }
  else {
    await onCreatNewAttendance(params)
    emit('refreshAttendance')
  }
}

const getRowSpan = (workingDate?: string) => {
  if (!workingDate || sortedData.value.length === 0)
    return 1

  let count = 0
  for (let i = 0; i < sortedData.value.length; i++) {
    if (sortedData.value[i].workingDate === workingDate) {
      count += 1
    }
  }
  return count
}

const customRow = computed(() => (data: AttendanceItemParams) => {
  const isActive = data.workingDate === idDetail.value
  let classActive = ''
  if (isActive)
    classActive += 'row-active'
  if (isActive && getRowSpan(data.workingDate) === 1)
    classActive += ' row-alone'

  return {
    onClick: () => {
      idDetail.value = data.workingDate ?? ''
      emit('update:selectedDate', dayjs(data.workingDate))
    },
    class: classActive,
  }
})

const showBreakTime = (timeValue: string) => {
  if (!timeValue)
    return '--:--'
  return timeValue.slice(0, 5)
}

watch(() => props.dataSource, (newVal) => {
  sortedData.value = [
    ...newVal,
  ]
}, { deep: true })

onMounted(async () => {
  sortedData.value = props.dataSource
})
</script>

<template>
  <a-table
    class="custom-antd-table"
    :loading="loading"
    :columns="columns"
    :data-source="dataSource"
    bordered
    :pagination="false"
    :style="customizeTable()"
    :scroll="props.scroll"
    row-key="workingDate"
    :custom-row="customRow"
    @change="handleChange"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'checkInTime'">
        <span v-if="record.checkInTime" :class="getModifierColor(record?.modifiedCheckInTimeLastModifierType)">
          {{ record.checkInTime.slice(0, 5) }}
        </span>
        <span v-else-if="record.scheduledStartTime" class="text-gray-400">
          {{ record.scheduledStartTime.slice(0, 5) }}
        </span>
      </template>
      <template v-if="column.dataIndex === 'checkOutTime'">
        <span v-if="record.checkOutTime" :class="getModifierColor(record?.modifiedCheckOutTimeLastModifierType)">
          {{ record.checkOutTime.slice(0, 5) }}
        </span>
        <span v-else-if="record.scheduledEndTime" class="text-gray-400">
          {{ record.scheduledEndTime.slice(0, 5) }}
        </span>
      </template>
      <template v-if="column.dataIndex === 'isRequested'">
        <template v-if="record.isRequested">
          <template v-if="record.isApproved === true">
            <a-tag class="text-[1rem] text-[#278836] bg-[#C7ECD1] rounded-[1rem] border-[#278836] p-1">
              <CheckOutlined />
              {{ t('dashboard.workplace.timeKeepingData.approval') }}
            </a-tag>
          </template>
          <template v-else-if="record.isApproved === false">
            <a-tag class="text-[1rem] text-[#BD3D44] bg-[#FFC0C4] rounded-[1rem] border-[#BD3D44] p-1">
              <CloseOutlined />
              {{ t('dashboard.workplace.timeKeepingData.cancel') }}
            </a-tag>
          </template>
          <template v-else-if="record.isApproved === null">
            <div class="flex justify-center">
              <a-tag class="flex justify-center items-center w-[7rem] text-[1rem] text-[#DC6000] bg-[#FCE9D2] rounded-[1rem] border-[#DC6000] p-1">
                <!-- <a-avatar src="/icon/pending_icon.svg" shape="square" class="w-[1rem] h-[1rem] mr-[3px]" /> -->
                <span>
                  {{ t('dashboard.workplace.timeKeepingData.pending') }}
                </span>
              </a-tag>
            </div>
          </template>
        </template>
      </template>
      <template v-if="column.dataIndex === 'workingDate'">
        <span v-if="record.workingDate" color="#08223B">
          {{ dayjs(record.workingDate, 'YYYY-MM-DD').format("MM/DD (dd)") }}
        </span>
        <!-- <template v-else>
          <router-link
            :to="{
              path: '/dashboard/attendance-detail',
              query: { workingDate: record.workingDate },
            }"
          >
            {{ dayjs(record.workingDate).format("MM/DD (dd)") }}
          </router-link>
        </template> -->
      </template>
      <template v-if="column.dataIndex === 'breakList'">
        <template v-if="record.modifiedBreakList">
          <div class="flex flex-col" :class="getModifierColor(record?.modifiedBreakListLastModifierType)">
            <template v-for="item in record.modifiedBreakList" :key="item">
              <span>
                {{ showBreakTime(item.breakInTime) }} ~ {{ showBreakTime(item.breakOutTime) }}
              </span>
            </template>
          </div>
        </template>
        <template v-else-if="!record.breakList?.length">
          <span>-</span>
        </template>
        <template v-else>
          <div class="flex flex-col" :class="getModifierColor(record?.modifiedBreakListLastModifierType)">
            <template v-for="item in record.breakList" :key="item">
              <span>
                {{ showBreakTime(item.breakInTime) }} ~ {{ showBreakTime(item.breakOutTime) }}
              </span>
            </template>
          </div>
        </template>
      </template>
      <!-- <template v-if="column.dataIndex === 'fastCheckIn'">
        <a-button v-if="isFastCheckInSatify(record as AttendanceItem)" type="primary" @click="fastCheckIn(record as AttendanceItem)">
          {{ t('fastCheckIn') }}
        </a-button>
      </template> -->
      <template v-if="column.dataIndex === 'action'">
        <div v-if="record.employeeShiftId" class="flex gap-x-4 justify-center">
          <CarbonEdit class="cursor-pointer hover:text-blue" @click="viewAttendanceDetail(record as AttendanceItem)" />
          <CarbonDelete class="cursor-pointer" @click="handleDeleteShift(record as AttendanceItem)" />
        </div>
      </template>
    </template>
  </a-table>

  <WorkTimeModal
    :show="isWorkTimeModalOpened"
    :attendance-item="attendanceItem"
    @update:show="isWorkTimeModalOpened = $event"
    @save-attendance="onSaveAttendance"
  />
</template>

