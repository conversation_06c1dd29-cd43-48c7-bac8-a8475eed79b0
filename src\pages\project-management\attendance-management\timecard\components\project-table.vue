<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
import { CheckOutlined, ClockCircleOutlined, CloseOutlined, EditOutlined, HistoryOutlined, MoreOutlined, PlusOutlined } from '@ant-design/icons-vue'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import type { UnwrapRef } from 'vue'
import { Tag } from 'ant-design-vue'
import DailyReport from './DailyReport.vue'
import type { AttendanceLogParams, EntityChange } from '~@/api/dashboard/activity-logs'
import { getAttendanceLogApi } from '~@/api/dashboard/activity-logs'
import type { ApproveRequestByAttendanceItemParams, AttendanceByWorksiteParams, AttendanceItem, AttendanceUpdateParams } from '~@/api/attendance'
import { approveRequestByAttendanceItem, createAttendanceApi, getAttendanceByWorksiteApi, updateAttendanceApi } from '~@/api/attendance'
import logger from '~@/utils/logger'
import { getDailyReportApi } from '~@/api/company/project'
import type { DailyReportItem, ManagedProjectItem } from '~@/api/company/project'
import WorkTimeModal from '~@/components/common/WorkTimeModal.vue'
import { ModalType } from '~@/enums/system-status-enum'
import { useAvatarStore } from '~@/stores/avatar'

// import KantokuTimePicker from '~@/pages/manager/components/kantoku-time-picker.vue'

const props = defineProps({
  project: {
    type: Object as () => ManagedProjectItem,
    required: true,
  },
  projectId: {
    type: String,
    required: true,
  },
  projectName: {
    type: String,
    required: true,
  },
  address: {
    type: String,
    required: true,
  },
  searchDate: {
    type: String,
    required: false,
  },
  isSearchAll: {
    type: Boolean,
    required: false,
  },
})

const { t } = useI18n()
const messageNotify = useMessage()
const loading = shallowRef(false)
const currentDate = ref<Dayjs>(dayjs())
const selectedDate = ref<Dayjs>(dayjs())
const attendanceData = ref<AttendanceItem[]>([])
const rejectModalIsVisible = ref<boolean>(false)
const tableKey = ref<number>(0)
const rejectReason = ref<string>('')
const attendanceLogVisible = ref<boolean>(false)
const currentEmployeeShiftId = ref<string>()
const entityChangeData = ref<EntityChange[]>([])
const modalType = ref<ModalType>(ModalType.ADD)
const isRepresentative = ref<boolean>(false)
const dailyReportVisible = ref<boolean>(false)
const showAttendanceEditModal = ref<boolean>(false)
const attendanceItem = ref<AttendanceItem | null>(null)
const editTableData: UnwrapRef<Record<string, AttendanceItem>> = reactive({})
const isDailyReportExits = ref(false)
const avatarStore = useAvatarStore()


const columns: any = computed(() => {
  return [
    {
      title: t('employeeName'),
      dataIndex: 'employeeName',
      key: 'employeeName',
      fixed: 'left',
      align: 'center',
      width: 350,
    },
    {
      title: t('table.checkIn'),
      dataIndex: 'checkInTime',
      align: 'center',
    },
    {
      title: t('table.breakTime'),
      dataIndex: 'breakList',
      align: 'center',
      width: 150,
    },
    {
      title: t('table.checkOut'),
      dataIndex: 'checkOutTime',
      align: 'center',
    },
    {
      title: t('totalWorkTime'),
      dataIndex: 'totalWorkTime',
      align: 'center',
      width: 100,
    },
    {
      title: t('totalBreakTime'),
      dataIndex: 'totalBreakTime',
      align: 'center',
      width: 100,
    },
    {
      title: t('table.overtime'),
      dataIndex: 'totalOverTime',
      align: 'center',
      width: 100,
    },
    {
      title: t('requestStatus'),
      dataIndex: 'isApproved',
      key: 'isApproved',
      align: 'center',
      width: 150,
    },
    // {
    //   title: t('isRequested'),
    //   dataIndex: 'isRequested',
    //   key: 'isRequested',
    //   align: 'center',
    // },
    {
      title: t('table.comment'),
      dataIndex: 'description',
      align: 'center',
      maxWidth: 300,
    },
    {
      title: t('action'),
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      align: 'center',
    },
  ]
})

const onRefreshTable = () => {
  tableKey.value++
}

const customeAttendanceData = () => {
  attendanceData.value.forEach((item: AttendanceItem) => {
    if (!item.breakList)
      item.breakList = []
  })
}

async function getAttendanceRequestByWorksite() {
  try {
    const params: AttendanceByWorksiteParams = {
      projectId: props.projectId,
      date: selectedDate.value.format('YYYY-MM-DD'),
    }
    const { data, status } = await getAttendanceByWorksiteApi(params)
    if (status === 200) {
      attendanceData.value = data?.items ?? []
      isDailyReportExits.value = data?.hasReport ?? false
      customeAttendanceData()
    }
    else {
      attendanceData.value = []
    }
  }
  catch (e) {
    logger.error(e)
  }
}

const onChange = async () => {
  await getAttendanceRequestByWorksite()
  await queryDailyReport()
}

const onApproveRequest = async (shiftId: string) => {
  const params: ApproveRequestByAttendanceItemParams = {
    employeeShiftId: shiftId,
    approvalStatus: true,
  }
  try {
    const { data, status, code } = await approveRequestByAttendanceItem(shiftId, params)
    if (status === 200) {
      logger.log('onApproveRequest: ', data)
      messageNotify.success('承認に成功しました')
      getAttendanceRequestByWorksite()
      onRefreshTable()
    }
    else {
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

const onOpenRejectModal = (employeeShiftId?: string) => {
  if (!employeeShiftId)
    return

  rejectModalIsVisible.value = true
  currentEmployeeShiftId.value = employeeShiftId
}

const onOpenViewLogModal = async (employeeShiftId: string) => {
  if (!employeeShiftId) {
    messageNotify.error('employeeShiftId is required')
    return
  }
  await getAttendanceLog(employeeShiftId)
  // if (entityChangeData.value.length === 0) {
  //   messageNotify.warning('ログはありません')
  //   return
  // }
  attendanceLogVisible.value = true
}

const confirmReject = async () => {
  if (!currentEmployeeShiftId.value)
    return

  try {
    const params: ApproveRequestByAttendanceItemParams = {
      employeeShiftId: currentEmployeeShiftId.value,
      approvalStatus: false,
      rejectReason: rejectReason.value,
    }
    const { data, status, code } = await approveRequestByAttendanceItem(currentEmployeeShiftId.value, params)
    if (status === 200) {
      logger.log('rejectRequest', data)
      messageNotify.success('成功')
      rejectModalIsVisible.value = false
      rejectReason.value = ''
      getAttendanceRequestByWorksite()
    }
    else {
      logger.error(t(code))
      messageNotify.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
  finally {
    currentEmployeeShiftId.value = undefined
    rejectReason.value = ''
    rejectModalIsVisible.value = false
  }
}

async function getAttendanceLog(employeeShiftId: string) {
  try {
    const params: AttendanceLogParams = {
      dateFrom: selectedDate.value.format('YYYY-MM-DD'),
      dateTo: currentDate.value.format('YYYY-MM-DD'),
    }
    const { data, status, code } = await getAttendanceLogApi(employeeShiftId, params)
    if (status === 200) {
      logger.log('getAttendanceLog', data)
      entityChangeData.value = data ?? []
    }
    else {
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

function openAttendanceEditModal(record: AttendanceItem) {
  attendanceItem.value = {
    ...record,
  }
  showAttendanceEditModal.value = true
  modalType.value = ModalType.EDIT
  isRepresentative.value = false
}

function openAttendanceAddModal() {
  attendanceItem.value = null
  showAttendanceEditModal.value = true
  modalType.value = ModalType.ADD
  isRepresentative.value = true
}

const onSaveChangeAttendance = async (employeeShiftId: string, params: AttendanceUpdateParams) => {
  if (loading.value)
    return
  loading.value = true

  try {
    switch (modalType.value) {
      case ModalType.ADD: {
        const { status, message } = await createAttendanceApi(params)
        if (status === 200) {
          messageNotify.success(message)
          await getAttendanceRequestByWorksite()
        }
        break
      }
      case ModalType.EDIT:{
        const { status, message } = await updateAttendanceApi(employeeShiftId, params)
        if (status === 200) {
          messageNotify.success(message)
          await getAttendanceRequestByWorksite()
        }
        break
      }
    }
  }
  catch (e) {
  }
  finally {
    loading.value = false
  }
}

async function handleOpenDailyReport() {
  await queryDailyReport()
  dailyReportVisible.value = true
}

function getModifierColor(modifier?: 'SYSTEM' | 'AUTHOR' | 'MANAGER') {
  if (!modifier)
    return 'text-blue-600'
  const colors: Record<string, string> = {
    SYSTEM: 'text-blue-600',
    AUTHOR: 'text-orange-600',
    MANAGER: 'text-green-600',
    AUTO: 'text-red-600',
  }
  return colors[modifier] || 'text-blue-600'
}

function updateIsDailyReportCreated() {
  queryDailyReport()
}

const dailyReportData = ref<DailyReportItem | undefined>(undefined)

async function queryDailyReport() {
  const projectId = props.project.id
  const reportDate = selectedDate.value.format('YYYY-MM-DD')
  try {
    const { data, status } = await getDailyReportApi(projectId, reportDate)
    if (status === 200)
      dailyReportData.value = data ?? undefined

    else
      dailyReportData.value = undefined
  }
  catch (e) {
    logger.error(e)
  }
}

const statusTag = (record: AttendanceItem) => {
  // Xác định trạng thái và màu sắc
  if (record.checkOutTime)
    return h(Tag, { color: 'red' }, () => t('status.checkOut'))
  if (!record.checkInTime)
    return h(Tag, { color: 'blue' }, () => t('status.scheduled'))
  else if (record.breakList && !record.breakList.length)
    return h(Tag, { color: 'green' }, () => t('status.checkIn'))
  else if (!record.checkOutTime)
    return h(Tag, { color: 'orange' }, () => t('status.break'))
}

const getStatusAttendanceComponent = (record: AttendanceItem) => {
  if (record.isApproved)
    return h(Tag, { color: 'success' }, () => t('request.isApproved'))

  if (record.isRequested && !record.isApproved) {
    if (record.approvedBy)
      return h(Tag, { color: 'error' }, () => t('request.isRejected'))

    else
      return h(Tag, { color: 'orange' }, () => t('request.isRequested'))
  }
  if (record.isRequested)
    return h(Tag, { color: 'orange' }, () => t('request.isRequested'))

  return h(Tag, { color: 'gray' }, () => t('request.isNotRequested'))
}

watch(() => props.searchDate, async (newDate) => {
  if (newDate) {
    selectedDate.value = dayjs(newDate)
    await getAttendanceRequestByWorksite()
  }
})

onMounted(() => {
  getAttendanceRequestByWorksite()
})
</script>

<template>
  <a-col :span="24">
    <a-card>
      <template #title>
        <a-row>
          <a-col :span="12">
            <div class="flex flex-col">
              <span class="text-lg font-semibold">{{ projectName }}</span>
              <span class="text-sm font-light text-gray-500">{{ address }}</span>
            </div>
          </a-col>
          <a-col :span="12" align="right">
            <a-date-picker v-model:value="selectedDate" @change="onChange" />
          </a-col>
        </a-row>
      </template>
      <a-table
        :columns="columns"
        :data-source="attendanceData"
        :scroll="{ x: 'max-content' }"
        :bordered="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'employeeName'">
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-2 sm:space-y-0 sm:space-x-4 p-4 max-w-lg mx-auto">
              <a-avatar :size="40" :src="avatarStore.getImageSrcByEmployeeId(record.employeeId) ?? ''" />
              <!-- Employee Name -->
              <span class="text-md font-semibold text-[#111827]">
                {{ record.employeeCode }} - {{ record.employeeName }}
              </span>
              <!-- Working Status -->
              <component :is="statusTag(record as AttendanceItem)" />
            </div>
          </template>

          <template v-if="column.dataIndex === 'checkInTime'">
            <div class="flex flex-col items-center space-x-2 p-3 max-w-md mx-auto" :class="getModifierColor(record?.modifiedCheckOutTimeLastModifierType)">
              <!-- Thời gian -->
              <span v-if="record.checkInTime" class="text-md font-semibold">
                {{ record?.checkInTime.slice(0, 5) }}
              </span>
              <!-- Địa chỉ -->
              <div class="flex items-center space-x-1 text-gray-500">
                <span class="text-xs font-light">{{ record?.checkInLocation }}</span>
              </div>
            </div>
          </template>

          <template v-if="column.dataIndex === 'checkOutTime'">
            <div class="flex flex-col items-center space-x-2 p-3 max-w-md mx-auto" :class="getModifierColor(record?.modifiedCheckOutTimeLastModifierType)">
              <!-- Thời gian -->
              <span class="text-md font-semibold">
                {{ record?.checkOutTime?.slice(0, 5) }}
              </span>
              <!-- Địa chỉ -->
              <div class="flex items-center space-x-1 text-gray-500">
                <span class="text-xs font-light">{{ record?.checkOutLocation }}</span>
              </div>
            </div>
          </template>

          <template v-if="column.dataIndex === 'breakList'">
            <div v-for="(item) in record.breakList" :key="item" class="grid grid-cols-1" :class="getModifierColor(record?.modifiedBreakListLastModifierType)">
              <div class="flex gap-x-[10px] text-md font-semibold">
                <span v-if="item.breakInTime">{{ item.breakInTime.slice(0, 5) }}</span>
                <span> ~ </span>
                <span v-if="item.breakOutTime">{{ item.breakOutTime.slice(0, 5) }}</span>
              </div>
            </div>
          </template>

          <template v-if="column.dataIndex === 'description'">
            <a-input
              v-if="editTableData[record.employeeShiftId]"
              v-model:value="editTableData[record.employeeShiftId].description!"
              :placeholder="t('placeholder.note')"
            />
            <div
              v-else
              class="text-sm text-gray-700 break-words whitespace-pre-wrap max-w-xs leading-relaxed"
              style="word-break: break-word; overflow-wrap: break-word;"
            >
              {{ record.description }}
            </div>
          </template>

          <template v-if="column.dataIndex === 'totalWorkTime'">
            <span v-if="!editTableData[record.employeeShiftId]">
              {{ record.totalWorkTime }}
            </span>
            <span>
              {{ editTableData[record.employeeShiftId]?.totalWorkTime }}
            </span>
          </template>

          <template v-if="column.dataIndex === 'attendanceStatus'">
            <a-tag v-if="!record.checkInTime" color="blue">
              {{ t('status.scheduled') }}
            </a-tag>
            <a-tag v-else-if="record.breakList && !record.breakList.length" color="green">
              {{ t('status.checkIn') }}
            </a-tag>
            <a-tag v-else-if="!record.checkOutTime" color="orange">
              {{ t('status.break') }}
            </a-tag>
            <a-tag v-else color="red">
              {{ t('status.checkOut') }}
            </a-tag>
          </template>
          <template v-if="column.dataIndex === 'totalBreakTime'">
            <span v-if="!editTableData[record.employeeShiftId]">
              {{ record.totalBreakTime }}
            </span>
            <span v-else>
              {{ editTableData[record.employeeShiftId].totalBreakTime }}
            </span>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <a-dropdown trigger="click">
              <MoreOutlined />
              <template #overlay>
                <a-menu>
                  <a-menu-item key="approve" @click="onApproveRequest(record.employeeShiftId)">
                    <CheckOutlined />
                    {{ t('button.approve') }}
                  </a-menu-item>
                  <a-menu-item key="reject" @click="onOpenRejectModal(record?.employeeShiftId)">
                    <CloseOutlined />
                    {{ t('button.reject') }}
                  </a-menu-item>
                  <a-menu-item key="edit" @click="openAttendanceEditModal(record as AttendanceItem)">
                    <EditOutlined />
                    {{ t('button.edit') }}
                  </a-menu-item>
                  <a-menu-item key="viewLog" @click="onOpenViewLogModal(record.employeeShiftId)">
                    <HistoryOutlined />
                    {{ t('button.editHistory') }}
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>
          <template v-if="column.dataIndex === 'isApproved' && record.isApproved !== null">
            <component :is="getStatusAttendanceComponent(record as AttendanceItem)" />
          </template>
        </template>
      </a-table>
      <div class="flex justify-between mt-4">
        <div>
          <a-button class="flex items-center" type="primary" @click="openAttendanceAddModal">
            <PlusOutlined />
            {{ t('add-representative') }}
          </a-button>
        </div>
        <div>
          <a-button v-if="!isDailyReportExits" type="primary" @click="handleOpenDailyReport">
            {{ t('button.createDailyReport') }}
          </a-button>
          <a-button v-else color="white" class="bg-orange-500" @click="handleOpenDailyReport">
            {{ t('button.dailyReportCreated') }}
          </a-button>
        </div>
      </div>
    </a-card>
  </a-col>
  <a-modal
    v-model:open="rejectModalIsVisible"
    width="300px"
    :title="t('title.reject')"
    :ok-text="t('button.confirm')"
    :cancel-text="t('button.cancel')"
    @ok="confirmReject"
  >
    <a-textarea
      v-model:value="rejectReason"
      :rows="3"
      :placeholder="t('placeholder.rejectReason')"
    />
  </a-modal>

  <a-modal v-model:open="attendanceLogVisible">
    <template v-if="entityChangeData.length">
      <a-timeline>
        <template v-for="item in entityChangeData" :key="item">
          <a-timeline-item>
            <template #dot>
              <ClockCircleOutlined style="font-size: 16px" />
            </template>
            {{ dayjs(item.modifiedTime).format('YYYY-MM-DD HH:mm:ss') }}
          </a-timeline-item>
          <template v-for="change in item.changedList" :key="change">
            <a-timeline-item>{{ `Update ${change.fieldName} from ${change.valueBefore} -> ${change.valueAfter} by ${item.modifiedEmployeeName}` }}</a-timeline-item>
          </template>
        </template>
      </a-timeline>
    </template>
    <template v-else>
      <a-empty>
        <template #description>
          履歴がありません
        </template>
      </a-empty>
    </template>
  </a-modal>

  <a-modal v-model:open="dailyReportVisible" width="1000px" :destroy-on-close="true">
    <template #footer />
    <DailyReport
      :project="project"
      :attendance-list="attendanceData"
      :project-name="projectName"
      :address="address"
      :selected-date="selectedDate"
      :is-daily-report-created="isDailyReportExits"
      :daily-report-data="dailyReportData"
      @update-is-daily-report-created="updateIsDailyReportCreated"
    />
  </a-modal>

  <template v-if="showAttendanceEditModal">
    <WorkTimeModal
      :show="showAttendanceEditModal"
      :type="modalType"
      :attendance-item="attendanceItem"
      :is-representative="isRepresentative"
      :project-id="projectId"
      @update:show="showAttendanceEditModal = $event"
      @save-attendance="onSaveChangeAttendance"
    />
  </template>
</template>
