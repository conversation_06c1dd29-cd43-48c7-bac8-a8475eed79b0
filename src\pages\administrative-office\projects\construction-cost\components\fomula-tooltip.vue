<script setup lang="ts">
import { BookFilled } from '@ant-design/icons-vue'
import { NTooltip } from 'naive-ui'

defineProps({
  title: {
    type: String,
    required: true,
  },
  numerator: {
    type: String,
    required: true,
  },
  denominator: {
    type: String,
    required: true,
  },
  valuePercentage: {
    type: String,
    required: true,
  },
})
</script>

<template>
  <NTooltip :style="{ backgroundColor: '#91CAFF' }">
    <div class="text-xs space-y-2">
      <p class="text-blue-100 flex items-center">
        <span class="text-blue-800 rounded mr-2">
          <BookFilled class="text-blue-200" />
        </span>
        <b class="text-white">{{ title }}</b>
      </p>
      <div class="bg-white border-b-2 border-t-0 border-l-0 border-r-0 border-blue-300 border-solid p-3 rounded-md shadow-md flex">
        <div class="text-sm font-mono text-blue-700 font-medium flex items-center">
          =
        </div>
        <div class="text-sm font-mono text-blue-700 font-medium">
          <div class="text-center mx-1">
            <div class="border-b-2 border-t-0 border-l-0 border-r-0 border-blue-700 border-solid">
              {{ numerator }}
            </div>
            <div>{{ denominator }}</div>
          </div>
        </div>
        <div class="text-sm font-mono text-blue-700 font-medium flex items-center">
          × 100
        </div>
      </div>
    </div>
    <template #trigger>
      <span class="text-blue-600 hover:text-blue-800 font-medium transition-colors duration-300">
        {{ valuePercentage }}
      </span>
    </template>
  </NTooltip>
</template>
