<script lang="ts" setup>
import { useInfiniteScroll } from '@vueuse/core'
import ProjectTable from './components/project-table.vue'
import type { ManagedProjectItem } from '~@/api/company/project'
import { getManagedProjectsApi } from '~@/api/company/project'
import logger from '~@/utils/logger'
import { useWindowScrollSize } from '~@/composables/use-window-scroll-size'

const { t } = useI18n()
const projectData = ref<ManagedProjectItem[]>([])
const tmpProject = ref<ManagedProjectItem[]>([])
const searchDate = ref<string>()
const isSearchAll = ref<boolean>(false)
const scrollTarget = ref<HTMLElement | null>(null)
const projectCount = ref(2)

const { height } = useWindowScrollSize()

async function getprojectsBelongToManager() {
  try {
    const { data, status, code } = await getManagedProjectsApi()
    if (status === 200) {
      projectData.value = data?.items ?? []
      projectData.value.sort((a, b) => a.code.localeCompare(b.code))
      logger.log('projectData', projectData.value)
      if (projectData.value.length > projectCount.value) {
        for (let i = 0; i < projectCount.value; i++)
          tmpProject.value.push(projectData.value[i])
      }
      else {
        tmpProject.value = projectData.value
      }
    }
    else {
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

function handleDateChange() {
  logger.log('Filter date:', searchDate.value)
  // Add your filtering logic here
}

const infiniteLoading = ref(false)

async function loadMore() {
  if (infiniteLoading.value)
    return // Tránh gọi nhiều lần khi đang loading

  infiniteLoading.value = true

  await new Promise(resolve => setTimeout(resolve, 500))
  if (projectCount.value < projectData.value.length) {
    tmpProject.value.push(projectData.value[projectCount.value])
    projectCount.value += 1
  }
  infiniteLoading.value = false
}

useInfiniteScroll(
  scrollTarget,
  loadMore,
  {
    distance: 100,
  },
)

onMounted(async () => {
  getprojectsBelongToManager()
})
</script>

<template>
  <page-container>
    <div class="bg-white p-6 rounded-xl mb-6 w-full shadow-sm border border-gray-100">
      <div class="flex items-center justify-between gap-4 w-full">
        <div class="flex-shrink-0">
          <h1 class="text-2xl font-semibold text-gray-800">
            {{ t('title.timecard') }}
          </h1>
        </div>

        <div class="flex items-center gap-3">
          <div class="flex flex-col gap-1">
            <a-date-picker
              v-model:value="searchDate"
              class="w-56 shadow-sm"
              value-format="YYYY-MM-DD"
              :placeholder="t('selectDate')"
              :allow-clear="true"
              size="large"
              @change="handleDateChange"
            />
          </div>
        </div>
      </div>
    </div>

    <div>
      <div ref="scrollTarget" :style="{ height: `${height - 220}px` }" class="overflow-y-auto">
        <div v-for="project in tmpProject" :key="project.id" class="mb-4">
          <ProjectTable
            :project="project"
            :project-id="project.id"
            :project-name="`${project.code} - ${project.name}`"
            :address="project?.address ?? ''"
            :search-date="searchDate"
            :is-search-all="isSearchAll"
          />
        </div>
        <div v-if="infiniteLoading" class="flex justify-center items-center h-10">
          <a-spin />
        </div>
      </div>
    </div>
  </page-container>
</template>

<style scoped>
:deep(.ant-picker) {
  border-radius: 8px;
  border: 1.5px solid #e2e8f0;
  transition: all 0.2s ease;
}

:deep(.ant-picker:hover) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

:deep(.ant-picker-focused) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15);
}

:deep(.ant-picker-large) {
  padding: 8px 12px;
  font-size: 14px;
}
</style>
