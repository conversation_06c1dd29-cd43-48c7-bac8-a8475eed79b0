<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
// import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import type { UnwrapRef } from 'vue'
import { CheckOutlined, CloseOutlined, EditOutlined, MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { SquarePen } from 'lucide-vue-next'
import { Button, Tag } from 'ant-design-vue'
import FastCheckInBtn from './fast-check-in-btn.vue'
import type { AttendanceItem, AttendanceUpdateParams, BreakTimeItem, FastCheckInParams } from '~@/api/attendance'
import { getWorkingLocationDisplayText } from '~@/api/attendance'
import { formatTimeToHHMMSS } from '~@/utils/apiTimer'

const props = defineProps({
  employeeShift: {
    type: Object as () => AttendanceItem,
    required: true,
  },
  isEditable: {
    type: Boolean,
    required: true,
    default: true,
  },
  index: {
    type: Number,
    required: true,
  },
  total: {
    type: Number,
    required: true,
  },
})

const emit = defineEmits<{
  (event: 'updateAttendanceItem', employeeShiftId: string, params: AttendanceUpdateParams): void
  (event: 'requestApproval', employeeShiftId: string): void
  (event: 'fastCheckIn', employeeShiftId: string, params: FastCheckInParams): void
}>()

const messageNotify = useMessage()
const { isWorkingTimeValid } = useValidateTime()

const { t } = useI18n()
const checkInTime = ref<string>('09:00:00')
const checkOutTime = ref<string>('18:00:00')
const breakList = ref<BreakTimeItem[]>([])
const description = ref<string>('')
const isEdit: UnwrapRef<Record<string, boolean>> = reactive({})
const isWorkTimeModalOpened = ref(false)
const attendanceItem = ref<AttendanceItem | null>(null)
const editedProject = ref<string>('')

function onEdit(key: string) {
  isEdit[key] = true
}

function isCheckInOutTimeValid(checkInTime?: string, checkOutTime?: string) {
  if (!checkInTime || !checkOutTime)
    return false
  const currentTime = dayjs()
  const checkInTimeTmp = dayjs(checkInTime, 'HH:mm:ss')
  const checkOutTimeTmp = dayjs(checkOutTime, 'HH:mm:ss')
  if (checkInTimeTmp.isAfter(currentTime)) {
    messageNotify.error(t('requireCheckInTimeBeforeCurrentTime'))
    return false
  }
  if (checkOutTimeTmp.isAfter(currentTime)) {
    messageNotify.error(t('requireCheckOutTimeBeforeCurrentTime'))
    return false
  }
  if (checkOutTimeTmp.isBefore(checkInTimeTmp)) {
    messageNotify.error(t('requireCheckOutTimeAfterCheckInTime'))
    return false
  }
  return true
}

const onCloseEditCheckInOutTime = () => {
  isEdit[`${props.employeeShift.employeeShiftId}-CHECKINOUT`] = false
  checkInTime.value = props.employeeShift.checkInTime ?? ''
  checkOutTime.value = props.employeeShift.checkOutTime ?? ''
}

async function updateCheckInOutTime(employeeShiftId?: string, checkInTime?: string, checkOutTime?: string) {
  if (!employeeShiftId || !checkInTime || !checkOutTime)
    return
  const params: AttendanceUpdateParams = {}
  if (props.employeeShift.checkInTime !== checkInTime)
    params.checkInTime = formatTimeToHHMMSS(checkInTime)
  if (props.employeeShift.checkOutTime !== checkOutTime)
    params.checkOutTime = formatTimeToHHMMSS(checkOutTime)

  if (!isCheckInOutTimeValid(checkInTime, checkOutTime))
    return
  emit('updateAttendanceItem', employeeShiftId, params)
  isEdit[`${employeeShiftId}-CHECKINOUT`] = false
}

function onCloseEditBreakTime(employeeShiftId: string, index: number) {
  isEdit[`${employeeShiftId}-BREAK-${index}`] = false
  const len = props.employeeShift.breakList?.length ?? 0
  if (len < index + 1) {
    breakList.value.splice(index, 1)
  }
  else {
    breakList.value[index] = {
      breakInTime: props.employeeShift.breakList?.[index]?.breakInTime ?? '',
      breakOutTime: props.employeeShift.breakList?.[index]?.breakOutTime ?? '',
    }
  }
}

function updateBreakTime(employeeShiftId?: string, breakTime?: BreakTimeItem, index?: number) {
  if (!employeeShiftId || !breakTime)
    return
  breakTime.breakInTime = formatTimeToHHMMSS(breakTime.breakInTime)
  breakTime.breakOutTime = formatTimeToHHMMSS(breakTime.breakOutTime)
  const params: AttendanceUpdateParams = {
    breakList: breakList.value,
  }
  if (!isWorkingTimeValid(breakList.value ?? [], checkInTime.value ?? '', checkOutTime.value ?? ''))
    return
  emit('updateAttendanceItem', employeeShiftId, params)
  isEdit[`${employeeShiftId}-BREAK-${index}`] = false
}

function removeBreakTime(employeeShiftId: string, index: number) {
  if (employeeShiftId === '')
    return
  breakList.value.splice(index, 1)
  const params: AttendanceUpdateParams = {
    breakList: breakList.value,
  }
  emit('updateAttendanceItem', employeeShiftId, params)
}

function updateDescription(employeeShiftId?: string, description?: string) {
  if (!employeeShiftId || !description)
    return
  const params: AttendanceUpdateParams = {
    description,
  }
  emit('updateAttendanceItem', employeeShiftId, params)
}

function handleAddBreakTime() {
  if (!checkInTime.value) {
    messageNotify.error(t('notification.checkInTimeRequired'))
    return
  }
  if (breakList.value.length >= 3) {
    messageNotify.error(t('notification.maxToThreeBreakTime'))
    return
  }
  breakList.value.push({
    breakInTime: '00:00:00',
    breakOutTime: '00:00:00',
  })
  isEdit[`${props.employeeShift.employeeShiftId}-BREAK-${breakList.value.length - 1}`] = true
}

function requestApproval() {
  if (!isWorkingTimeValid(breakList.value ?? [], checkInTime.value ?? '', checkOutTime.value ?? ''))
    return
  emit('requestApproval', props.employeeShift.employeeShiftId ?? '')
}

function fastCheckIn() {
  const params: FastCheckInParams = {
    latitude: '',
    longitude: '',
  }
  emit('fastCheckIn', props.employeeShift.employeeShiftId ?? '', params)
}

function getModifierColor(modifier?: 'SYSTEM' | 'AUTHOR' | 'MANAGER' | 'AUTO' | null) {
  if (!modifier)
    return 'text-blue-600'
  const colors: Record<string, string> = {
    SYSTEM: 'text-blue-600',
    AUTHOR: 'text-orange',
    MANAGER: 'text-green-600',
    AUTO: 'text-red-600',
  }
  return colors[modifier] || 'text-blue-600'
}

function handleEditProject(employeeShift: AttendanceItem) {
  isWorkTimeModalOpened.value = true
  attendanceItem.value = employeeShift
  editedProject.value = employeeShift.projectId ?? ''
}

function onSaveAttendance(employeeShiftId: string, params: AttendanceUpdateParams) {
  emit('updateAttendanceItem', employeeShiftId, params)
  isWorkTimeModalOpened.value = false
}

// function handleSelectTime(time: Dayjs, type: 'checkInTime' | 'checkOutTime') {
//   if (type === 'checkInTime')
//     checkInTime.value = time.format('HH:mm:ss')
//   else
//     checkOutTime.value = time.format('HH:mm:ss')
// }

// function handleSelectBreakTime(time: Dayjs, type: 'breakInTime' | 'breakOutTime', breakTime?: BreakTimeItem) {
//   if (!breakTime)
//     return
//   breakTime[type] = time.format('HH:mm:ss')
// }

// const cancelRequest = () => {
//   emit('cancelRequest', props.employeeShift.employeeShiftId ?? '')
// }

const getStatusAttendanceComponent = (employeeShift: AttendanceItem) => {
  if (employeeShift.isApproved === true) {
    return h(Tag, {
      class: 'flex justify-center items-center text-[1rem] text-[#278836] bg-[#C7ECD1] border-[#278836] p-2',
    }, () => t('request.isApproved'))
  }

  if (!employeeShift.isApproved && employeeShift.approvedBy) {
    return h(Tag, {
      class: 'flex justify-center items-center text-[1rem] text-[#BD3D44] bg-[#FFC0C4] border-[#BD3D44] p-2',
    }, () => t('request.isRejected'))
  }

  // Fast Check-in button
  if (employeeShift.isScheduled && !employeeShift.checkInTime) {
    return h(FastCheckInBtn, {
      content: t('button.check-in'),
      onClick: fastCheckIn,
    })
  }

  // Request Approval button
  if (!employeeShift.isRequested) {
    return h(Button, {
      type: 'primary',
      onClick: () => requestApproval(),
    }, () => t('button.request-approval'))
  }

  // Pending status
  if (employeeShift.isApproved === null) {
    return h(Tag, {
      class: 'flex justify-center items-center text-[1rem] text-[#DC6000] bg-[#FCE9D2] border-[#DC6000] p-2',
    }, () => h('div', { class: 'flex items-center gap-1' }, () => t('dashboard.workplace.timeKeepingData.pending')))
  }

  if (employeeShift.isRequested) {
    return h(Tag, {
      class: 'flex justify-center items-center text-[1rem] text-[#BD3D44] bg-[#FFC0C4] border-[#BD3D44] p-2',
    }, () => t('dashboard.workplace.timeKeepingData.pending'))
  }

  // Rejected/Cancelled status
  // return h('a-tag', {
  //   class: 'flex justify-center items-center text-[1rem] text-[#BD3D44] bg-[#FFC0C4] border-[#BD3D44] p-2',
  // }, () => [
  //   h(CloseOutlined),
  //   t('dashboard.workplace.timeKeepingData.cancel'),
  // ])
}

// Lifecycle hooks
watch(() => props.employeeShift, (newVal) => {
  if (!newVal)
    return

  if (newVal.checkInTime)
    checkInTime.value = newVal.checkInTime
  if (newVal.checkOutTime)
    checkOutTime.value = newVal.checkOutTime
  breakList.value = newVal.breakList?.map(item => ({
    breakInTime: formatTimeToHHMMSS(item.breakInTime),
    breakOutTime: formatTimeToHHMMSS(item.breakOutTime),
  })) ?? []
  description.value = newVal.description ?? ''
}, { deep: true, immediate: true })
</script>

<template>
  <div class="flex flex-col">
    <div class="flex gap-x-2 items-center">
      <span class="text-lg font-medium font-semibold mb-1"> {{ employeeShift?.projectCode }} - {{ employeeShift?.projectName }}</span>
      <div>
        <SquarePen class="cursor-pointer hover:text-blue-500" @click="handleEditProject(employeeShift)" />
      </div>
      <!-- <button class="flex gap-2 items-center justify-center bg-[#ff7070] p-2 rounded-3xl text-white w-[100px] " @click="handleEditProject(employeeShift)">
        <SquarePen class="cursor-pointer" />
        <span class="text-sm font-medium">{{ t('button.edit') }}</span>
      </button> -->
      <!-- <EditOutlined class="cursor-pointer hover:text-blue-500" @click="handleEditProject(employeeShift)" /> -->
    </div>
    <div class="flex gap-x-2">
      <span class="text-sm font-normal text-[#74797A]">{{ getWorkingLocationDisplayText(employeeShift?.workingLocation, t) }}</span>
    </div>
  </div>
  <a-space direction="vertical" class="w-full">
    <!-- Check-in/Check-out times -->
    <div class="flex justify-between">
      <div class="flex items-center gap-x-2">
        <div>
          <span>{{ t('check-time') }}:</span>
        </div>
        <div v-if="!isEdit[`${employeeShift.employeeShiftId}-CHECKINOUT`]" class="flex gap-2">
          <div>
            <span
              v-if="employeeShift.checkInTime"
              :class="getModifierColor(employeeShift.modifiedCheckInTimeLastModifierType)"
            >
              {{ employeeShift.checkInTime.slice(0, 5) }}
            </span>
            <span v-else-if="employeeShift.scheduledStartTime" class="text-gray-400">
              {{ employeeShift.scheduledStartTime.slice(0, 5) }}
            </span>
            <span v-else>--:--</span>
            <span>&nbsp;-&nbsp;</span>
            <span
              v-if="employeeShift.checkOutTime"
              :class="getModifierColor(employeeShift.modifiedCheckOutTimeLastModifierType)"
            >
              {{ checkOutTime?.slice(0, 5) }}
            </span>
            <span v-else-if="employeeShift.scheduledEndTime" class="text-gray-400">
              {{ employeeShift.scheduledEndTime.slice(0, 5) }}
            </span>
            <span v-else>--:--</span>
          </div>
          <EditOutlined v-if="isEditable" class="cursor-pointer hover:text-blue-500" @click="onEdit(`${employeeShift.employeeShiftId}-CHECKINOUT`)" />
        </div>
        <div v-if="isEdit[`${employeeShift.employeeShiftId}-CHECKINOUT`] && isEditable" class="flex gap-2">
          <!-- <a-time-picker
            v-model:value="checkInTime"
            value-format="HH:mm:ss"
            format="HH:mm"
            :minute-step="5"
            class="w-full"
            @select="(time: Dayjs) => handleSelectTime(time, 'checkInTime')"
          />
          <a-time-picker
            v-model:value="checkOutTime"
            value-format="HH:mm:ss"
            format="HH:mm"
            :minute-step="5"
            class="w-full"
            @select="(time: Dayjs) => handleSelectTime(time, 'checkOutTime')"
          /> -->
          <TimePicker 
            v-model:value="checkInTime"
            :minute-step="5"
            class="w-24"
            value-type="string"
          />
          <TimePicker 
            v-model:value="checkOutTime"
            :minute-step="5"
            class="w-24"
            value-type="string"
          />
          <div class="flex flex-col gap-2">
            <CheckOutlined class="cursor-pointer" @click="updateCheckInOutTime(employeeShift.employeeShiftId, checkInTime, checkOutTime)" />
            <CloseOutlined class="cursor-pointer" @click="onCloseEditCheckInOutTime" />
          </div>
        </div>
      </div>

      <!-- Overtime -->
      <div class="flex items-center gap-2">
        <label class="text-sm font-medium">{{ t('overtime') }}:</label>
        <div class="text-lg font-semibold">
          {{ employeeShift.totalOverTime }}
        </div>
      </div>

      <!-- Total hours -->
      <div class="flex items-center gap-2">
        <label class="text-sm font-medium">{{ t('total-hours') }}:</label>
        <div class="text-lg font-semibold">
          {{ employeeShift.totalWorkTime }}
        </div>
      </div>
    </div>

    <!-- Break times -->
    <div class="flex justify-between mb-2 row-span-3 row-start-2  ">
      <div class="flex gap-2">
        <span class="text-sm font-medium">{{ t('break-time') }}:</span>
        <div v-if="breakList.length">
          <div v-for="(breakTime, idx) in breakList" :key="idx" class="mb-2">
            <div v-if="isEdit[`${employeeShift.employeeShiftId}-BREAK-${idx}`]" class="flex gap-2">
              <div class="flex gap-2 items-start justify-center">
                <!-- <a-time-picker
                  v-model:value="breakTime.breakInTime"
                  value-format="HH:mm:ss"
                  format="HH:mm"
                  :minute-step="5"
                  @select="(time: Dayjs) => handleSelectBreakTime(time, 'breakInTime', breakTime)"
                />
                <span class="mt-[2px] text-lg">-</span>
                <a-time-picker
                  v-model:value="breakTime.breakOutTime"
                  value-format="HH:mm:ss"
                  format="HH:mm"
                  :minute-step="5"
                  @select="(time: Dayjs) => handleSelectBreakTime(time, 'breakOutTime', breakTime)"
                /> -->
                <TimePicker 
                  v-model:value="breakTime.breakInTime"
                  :minute-step="5"
                  class="w-24"
                  value-type="string"
                />
                <span class="mt-[2px] text-lg">-</span>
                <TimePicker 
                  v-model:value="breakTime.breakOutTime"
                  :minute-step="5"
                  class="w-24"
                  value-type="string"
                />
              </div>
              <div class="flex flex-col gap-2">
                <CheckOutlined class="cursor-pointer" @click="updateBreakTime(employeeShift?.employeeShiftId, breakTime, idx)" />
                <CloseOutlined class="cursor-pointer" @click="onCloseEditBreakTime(employeeShift?.employeeShiftId ?? '', idx)" />
              </div>
            </div>
            <div v-else class="flex gap-2">
              <div class="flex gap-2 items-start">
                <div class="mt-[2px] h-full">
                  <CarbonBreakOne />
                </div>
                <span :class="getModifierColor(employeeShift.modifiedBreakListLastModifierType)">
                  {{ breakTime?.breakInTime ? dayjs(breakTime.breakInTime, 'HH:mm:ss').format('HH:mm') : '--:--' }} - {{ breakTime?.breakOutTime ? dayjs(breakTime.breakOutTime, 'HH:mm:ss').format('HH:mm') : '--:--' }}
                </span>
              </div>
              <EditOutlined v-if="isEditable" class="cursor-pointer hover:text-blue-500" @click.stop="onEdit(`${employeeShift.employeeShiftId}-BREAK-${index}`)" />
              <MinusCircleOutlined class="cursor-pointer hover:text-red-500" @click.stop="removeBreakTime(employeeShift.employeeShiftId ?? '', index)" />
            </div>
          </div>
        </div>
      </div>
      <div v-if="isEditable">
        <a-button class="flex items-center justify-center px-2 py-1" @click="handleAddBreakTime">
          <template #icon>
            <PlusOutlined />
          </template>
          {{ t('button.add-break') }}
        </a-button>
      </div>
    </div>

    <!-- Note -->
    <div class="flex justify-between">
      <div class="flex items-center gap-x-1">
        <span class="flex text-sm font-medium min-w-10">{{ t('note') }}:&nbsp;&nbsp;</span>
        <a-input
          v-model:value="description"
          :placeholder="t('placeholder.write-here')"
          class="2xl:min-w-100 xl:min-w-60"
          @press-enter="updateDescription(employeeShift?.employeeShiftId, description)"
        />
      </div>

      <!-- Request approval button -->
      <div class="flex items-center justify-end">
        <component :is="getStatusAttendanceComponent(employeeShift)" />
      </div>
    </div>

    <div class="flex justify-center items-center mt-2">
      <span class="text-sm font-medium text-gray-500">{{ index + 1 }}/{{ total }}</span>
    </div>
  </a-space>
  <WorkTimeModal
    :show="isWorkTimeModalOpened"
    :attendance-item="attendanceItem"
    :project-id="editedProject"
    @update:show="isWorkTimeModalOpened = $event"
    @save-attendance="onSaveAttendance"
  />
</template>
