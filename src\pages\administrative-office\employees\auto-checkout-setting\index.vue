<script lang="ts" setup>
import { SearchOutlined } from '@ant-design/icons-vue';
import type { ColumnGroupType, ColumnType } from 'ant-design-vue/es/table';
import dayjs from 'dayjs';
import { usePagination } from 'vue-request';
import type {
  EmployeePreference,
  EmployeePreferenceResponse,
} from '~@/api/employee/employee';
import {
  getEmployeePreference,
  updateEmployeePreference,
} from '~@/api/employee/employee';
import type { CheckedType } from '~@/layouts/basic-layout/typing';

type ColumnItemType<T> = ColumnGroupType<T> | ColumnType<T>;

type EmployeePreferenceCustom = EmployeePreference & {
  isLoading?: boolean;
};

const { t } = useI18n();
const keyword = ref<string>('');

async function queryData() {
  const { data } = await getEmployeePreference();
  if (!data)
    return ref<EmployeePreferenceResponse>({ items: [] }).value;
  return ref(data).value;
}

const { data: dataSource, runAsync } = usePagination(queryData);

const searchData = computed(() => {
  if (!dataSource.value)
    return [];
  if (!keyword.value)
    return dataSource.value.items || [];
  return dataSource.value.items.filter(
    (item: EmployeePreferenceCustom) =>
      item.employeeName.toLowerCase().includes(keyword.value.toLowerCase())
      || item.employeeCode.toLowerCase().includes(keyword.value.toLowerCase()),
  );
});

const columns = computed<ColumnItemType<EmployeePreferenceCustom>[]>(() => [
  { title: t('employeeCode'), dataIndex: 'employeeCode', width: 150 },
  { title: t('employeeName'), dataIndex: 'employeeName', width: 150 },
  { title: t('settings'), dataIndex: 'enableAutoCheckOut', width: 150 },
  { title: t('updatedBy'), dataIndex: 'updateBy', width: 150 },
  { title: t('updatedAt'), dataIndex: 'updateAt', width: 150 },
]);

async function handleChange(record: EmployeePreferenceCustom, enableAutoCheckOut: CheckedType) {
  record.isLoading = true;
  await updateEmployeePreference(
    record.employeeUid,
    enableAutoCheckOut as boolean,
  );
  await runAsync();
  record.isLoading = false;
}
</script>

<template>
  <page-container>
    <a-row>
      <a-col span="24">
        <div class="bg-white px-4 pt-4 pb-2 rounded-t-lg">
          <a-row :gutter="[12, 12]">
            <a-input
              v-model:value="keyword"
              :placeholder="t('search')"
              style="width: 270px"
              allow-clear
            >
              <template #prefix>
                <SearchOutlined class="text-gray-500" />
              </template>
            </a-input>
          </a-row>
        </div>
      </a-col>
      <a-col span="24">
        <a-table
          class="tableCategory"
          bordered
          :scroll="{ x: 'max-content', y: 'calc(100vh - 220px)' }"
          :columns="columns"
          :data-source="searchData"
          :pagination="false"
          row-key="employeeUid"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'enableAutoCheckOut'">
              <a-switch
                v-model:checked="record.enableAutoCheckOut"
                :loading="record.isLoading"
                :disabled="record.isLoading"
                :checked-children="t('button.yes')"
                :un-checked-children="t('button.no')"
                @change="
                  (checked) =>
                    handleChange(record as EmployeePreferenceCustom, checked)
                "
              />
            </template>
            <template v-if="column.dataIndex === 'updateAt'">
              <div>
                {{
                  record.updateAt
                    && dayjs(record.updateAt).format('YYYY-MM-DD HH:mm:ss')
                }}
              </div>
            </template>
          </template>
        </a-table>
      </a-col>
    </a-row>
  </page-container>
</template>
