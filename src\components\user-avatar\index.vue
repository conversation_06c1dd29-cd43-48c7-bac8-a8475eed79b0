<script setup lang="ts">
import {
  LogoutOutlined,
  ProfileOutlined,
  UserOutlined,
} from '@ant-design/icons-vue'

const message = useMessage()
const userStore = useUserStore()
const multiTabStore = useMultiTab()
const layoutMenuStore = useLayoutMenu()
const router = useRouter()
const { avatar, nickname } = storeToRefs(userStore)

const { t } = useI18n()

async function handleClick({ key }: any) {
  if (key === 'logout') {
    const hide = message.loading('ログアウト...', 0)
    try {
      await userStore.logout()
    }
    catch (error) {
    }
    finally {
      hide()
      message.success('ログアウト成功', 3)
      router
        .replace({
          path: '/login',
          query: {},
        })
        .then(() => {
          multiTabStore.clear()
          layoutMenuStore.clear()
        })
    }
  }
}
</script>

<template>
  <a-dropdown>
    <div
      hover="bg-[var(--hover-color)]"
      inline-flex
      cursor-pointer
      transition-all-300
      relative
      items-center
      h-full
      class="px-[24px] flex gap-2"
    >
      <div class="border-left" />
      <div class="anticon border-l">
        {{ nickname }}
      </div>
      <a-avatar :src="avatar" style="background-color: #D9D9D9;" />
    </div>
    <template #overlay>
      <a-menu @click="handleClick">
        <a-menu-item key="0">
          <template #icon>
            <UserOutlined />
          </template>
          <RouterLink to="/profile/basic">
            {{ t('profile') }}
          </RouterLink>
        </a-menu-item>
        <a-menu-item key="1">
          <template #icon>
            <ProfileOutlined />
          </template>
          <RouterLink to="/account/settings">
            {{ t('settings') }}
          </RouterLink>
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item key="logout">
          <template #icon>
            <LogoutOutlined />
          </template>
          {{ t('logout') }}
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<style scoped lang="less">
.anticon{
  font-size: 20px;
}

.border-left{
  border-left: 1px solid #B7B9B8;
  height: 35px;
  left: 0;
  font-size: 24px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
</style>
