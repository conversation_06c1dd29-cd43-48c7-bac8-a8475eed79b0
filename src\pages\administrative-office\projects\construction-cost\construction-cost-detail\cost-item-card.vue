<!-- CostItemCard.vue -->
<script setup lang="ts">
import type { CostItem } from './types'

defineProps<{
  item: CostItem
}>()
</script>

<template>
  <div class="bg-white rounded-lg shadow-md p-4 mb-3">
    <div class="flex justify-between items-start">
      <div>
        <h3 class="text-lg font-semibold text-gray-800">
          {{ item.categoryName }}
        </h3>
        <p v-if="item.description" class="text-gray-600 text-sm mt-1">
          {{ item.description }}
        </p>

        <div v-if="item.subCategories && item.subCategories.length > 0" class="mt-2">
          <p class="text-sm text-gray-700 font-medium">
            Sub-categories:
          </p>
          <ul class="list-disc pl-5 mt-1">
            <li
              v-for="(subCategory, index) in item.subCategories"
              :key="index"
              class="text-sm text-gray-600"
            >
              {{ subCategory }}
            </li>
          </ul>
        </div>
      </div>
      <div class="text-right">
        <span class="text-xl font-bold text-gray-800">
          {{ new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(item.totalAmount) }}
        </span>
      </div>
    </div>
  </div>
</template>
