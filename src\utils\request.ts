import type {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';
import axios from 'axios';
import { createVNode } from 'vue';
import { AxiosLoading } from './loading';
import {
  STORAGE_AUTHORIZE_KEY,
  useAuthorization,
} from '~/composables/authorization';
import { ContentTypeEnum, RequestEnum } from '~#/http-enum';

const router = useRouter();

// Đĩnh nghĩa interface cho response trả về từ API
export interface ResponseBody<T = any> {
  status: number;
  code: string;
  message?: string;
  data: T | null;
}

export interface ResponseBodyError {
  type: string;
  title: string;
  status: number;
  errors: Record<string, string[]>;
  traceId: string;
}

// Định nghĩa các tùy chọn mở rộng cho request
export interface RequestConfigExtra {
  token?: boolean;
  customDev?: boolean;
  loading?: boolean;
}

// Khởi tạo một instance của axios với cấu hình mặc định
const instance: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API ?? '/',
  timeout: 20000,
  headers: {
    'Content-Type': ContentTypeEnum.JSON,
  },
});

const axiosLoading = new AxiosLoading();

// Ham xu ly truoc khi gửi request
async function requestHandler(
  config: InternalAxiosRequestConfig & RequestConfigExtra,
): Promise<InternalAxiosRequestConfig> {
  // Xử lý baseURL nếu đang ở môi trường DEV và có cấu hình riêng
  if (
    import.meta.env.DEV
    && import.meta.env.VITE_APP_BASE_API_DEV
    && import.meta.env.VITE_APP_BASE_URL_DEV
    && config.customDev
  )
    config.baseURL = import.meta.env.VITE_APP_BASE_API_DEV;

  // Thêm token vào header nếu có
  const token = useAuthorization();
  if (token.value && config.token !== false)
    config.headers.set(STORAGE_AUTHORIZE_KEY, token.value);

  // Thêm header Accept language từ local storage
  const locale = ref<string>(lsLocaleState.value);
  config.headers.set('Accept-Language', locale.value ?? 'ja-JP');

  // Hiển thị loading nếu đươc cấu hình
  if (config.loading)
    axiosLoading.addLoading();
  return config;
}

// Xử lý response trả về từ server
function responseHandler(
  response: any,
): ResponseBody<any> | AxiosResponse<any> | Promise<any> | any {
  // const { data, status, message } = response.data
  // if (status !== 200) {
  //   notification?.error({
  //     message: status,
  //     description: message,
  //     duration: 3,
  //   })
  // }
  return response.data;
}

// Xử lý khi có lỗi từ server hoặc mạng
function errorHandler(error: AxiosError): Promise<any> {
  const token = useAuthorization();
  const notification = useNotification();

  if (error.response) {
    const { data } = error.response;
    const { errors, status, title } = data as ResponseBodyError;
    if (status === 401) {
      notification?.error({
        message: '401',
        description: title,
        duration: 3,
      });
      token.value = null;
      router.push({
        path: '/login',
        query: {
          redirect: router.currentRoute.value.fullPath,
        },
      });
    }
    else if (status === 403) {
      notification?.error({
        message: '403',
        description: title,
        duration: 3,
      });
    }
    else if (status === 500) {
      notification?.error({
        message: '500',
        description: title,
        duration: 3,
      });
    }
    else {
      const errorList = [];
      for (const key in errors) {
        const error = errors[key];
        errorList.push(...error.map(item => createVNode('div', null, item)));
      }
      // const description = createVNode('div', null, errorList);

      // if (errorList.length > 0) {
      //   notification?.error({
      //     message: status,
      //     description,
      //     duration: 3,
      //   });
      // }
    }
  }
  return Promise.reject(error);
}

// Định nghĩa interface tùy chọn cho các request
interface AxiosOptions<T> {
  url: string;
  params?: T;
  data?: T;
}

// Thêm interceptor xử lý request và response
instance.interceptors.request.use(requestHandler);
instance.interceptors.response.use(responseHandler, errorHandler);

export default instance;

// Hàm goi API với promise wrapper
function instancePromise<R = any, T = any>(
  options: AxiosOptions<T> & RequestConfigExtra,
): Promise<ResponseBody<R>> {
  const { loading } = options;
  return new Promise((resolve, reject) => {
    instance
      .request(options)
      .then((res) => {
        resolve(res as any);
      })
      .catch((e: Error | AxiosError) => {
        reject(e);
      })
      .finally(() => {
        if (loading)
          axiosLoading.closeLoading();
      });
  });
}

// Hàm thực hiện GET request
export function useGet<R = any, T = any>(
  url: string,
  params?: T,
  config?: AxiosRequestConfig & RequestConfigExtra,
): Promise<ResponseBody<R>> {
  const options = {
    url,
    params,
    method: RequestEnum.GET,
    ...config,
  };
  return instancePromise<R, T>(options);
}

// Hàm thực hiện POST request
export function usePost<R = any, T = any>(
  url: string,
  data?: T,
  config?: AxiosRequestConfig & RequestConfigExtra,
): Promise<ResponseBody<R>> {
  const options = {
    url,
    data,
    method: RequestEnum.POST,
    ...config,
  };
  return instancePromise<R, T>(options);
}

export function usePut<R = any, T = any>(
  url: string,
  data?: T,
  config?: AxiosRequestConfig & RequestConfigExtra,
): Promise<ResponseBody<R>> {
  const options = {
    url,
    data,
    method: RequestEnum.PUT,
    ...config,
  };
  return instancePromise<R, T>(options);
}

export function useDelete<R = any, T = any>(
  url: string,
  data?: T,
  config?: AxiosRequestConfig & RequestConfigExtra,
): Promise<ResponseBody<R>> {
  const options = {
    url,
    data,
    method: RequestEnum.DELETE,
    ...config,
  };
  return instancePromise<R, T>(options);
}
