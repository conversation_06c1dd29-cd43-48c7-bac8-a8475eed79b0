<script setup lang="ts">
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons-vue'
import type { SortingState } from '@tanstack/vue-table'
import { FlexRender, createColumnHelper, getCoreRowModel, getSortedRowModel, useVueTable } from '@tanstack/vue-table'
import type { AttendanceItem, BreakTimeItem } from '~@/api/attendance'
import { HHMMRegex, formatTimeToHHMM } from '~@/utils/apiTimer'

const props = defineProps({
  dataSource: {
    type: Array as PropType<AttendanceItem[]>,
    required: true,
  },
})

const emit = defineEmits<{
  (e: 'handleUpdatePersonalAttendance', item: AttendanceItem): void
}>()

const { t } = useI18n()
const { isWorkingTimeValid } = useValidateTime()
const columnHelper = createColumnHelper<AttendanceItem>()
const editingCell = ref<{ rowId: string; columnId: string } | null>(null)
const messageNotify = useMessage()

const sorting = ref<SortingState>([
  {
    id: 'workingDate',
    desc: false,
  },
])

function calculateRowSpans<T>(rows: T[], key: keyof T): number[] {
  const spans: number[] = Array(rows.length).fill(0)

  let prevValue: any = null
  let startIndex = 0

  for (let i = 0; i < rows.length; i++) {
    const currentValue = rows[i][key]

    if (currentValue !== prevValue) {
      if (i > startIndex)
        spans[startIndex] = i - startIndex

      startIndex = i
      prevValue = currentValue
    }

    if (i === rows.length - 1)
      spans[startIndex] = i - startIndex + 1
  }

  return spans
}

const columns = computed(() => [
  columnHelper.accessor('workingDate', {
    header: t('workingDate'),
    enableSorting: true,
  }),
  {
    header: t('projectName'),
    accessorKey: 'projectName',
    cell: (props: any) => {
      const projectName = props.row.original.projectName
      return h('div', {
        class: 'overflow-hidden text-ellipsis whitespace-nowrap max-w-[250px]',
      }, projectName || '-')
    },
  },
  columnHelper.accessor('checkInTime', {
    header: t('checkInTime'),
    cell: editableTimeCell('checkInTime'),
  }),
  columnHelper.accessor('checkOutTime', {
    header: t('checkOutTime'),
    cell: editableTimeCell('checkOutTime'),
  }),
  columnHelper.accessor('breakList', {
    header: t('breakTime'),
    cell: editableBreakListCell,
    minSize: 100,
  }),
  {
    header: t('totalWorkTime'),
    accessorKey: 'totalWorkTime',
  },
  {
    header: t('totalOverTime'),
    accessorKey: 'totalOverTime',
  },
  {
    header: t('totalBreakTime'),
    accessorKey: 'totalBreakTime',
  },
  {
    header: t('approvalStatus'),
    accessorKey: 'isApproved',
    cell: (props: any) => {
      const isApproved = props.row.original.isApproved
      return isApproved
        ? h('span', {
          class: 'text-green-500',
        }, t('status.approved'))
        : h('span', {
          class: 'text-red-500',
        }, t('status.notApproved'))
    },
  },
  {
    header: t('updateTime'),
    accessorKey: 'updateTime',
  },
])

const table = useVueTable({
  get data() {
    return props.dataSource
  },
  get columns() {
    return columns.value
  },
  getCoreRowModel: getCoreRowModel(),
  getSortedRowModel: getSortedRowModel(),
  state: {
    get sorting() {
      return sorting.value
    },
  },
  onSortingChange: (updaterOrValue: any) => {
    sorting.value
      = typeof updaterOrValue === 'function'
        ? updaterOrValue(sorting.value)
        : updaterOrValue
  },
  debugTable: true,
})

function isEditable(row: AttendanceItem) {
  return row.isApproved !== true && row.employeeShiftId
}

function editableTimeCell(fieldName: 'checkInTime' | 'checkOutTime' | string) {
  return ({ row, column, getValue }: any) =>
    h(
      'span',
      {
        contenteditable: true,
        class:
          'px-2 py-1 border border-transparent rounded hover:border-blue-300 focus:outline-none focus:bg-gray-200',
        onFocus: () => {
          editingCell.value = {
            rowId: row.original.employeeShiftId ?? '',
            columnId: column.id,
          }
        },
        onBlur: async (e: FocusEvent) => {
          const target = e.target as HTMLElement
          const newValue = target.textContent?.trim()

          const oldValue = !getValue() ? '--:--' : formatTimeToHHMM(row.original[fieldName] ?? '')

          if (!HHMMRegex.test(newValue ?? '')) {
            messageNotify.error(t('error.invalidTimeFormat'))
            target.textContent = oldValue
            return
          }

          if (newValue === oldValue) {
            editingCell.value = null
            return
          }

          row.original[fieldName] = newValue ?? ''
          editingCell.value = null

          emit('handleUpdatePersonalAttendance', row.original)
        },
        onKeydown: async (e: KeyboardEvent) => {
          if (e.key === 'Enter' || e.key === 'Tab') {
            e.preventDefault()
            const target = e.target as HTMLElement
            const newValue = target.textContent?.trim()
            const oldValue = formatTimeToHHMM(row.original[fieldName] ?? '')

            if (!HHMMRegex.test(newValue ?? '')) {
              messageNotify.error(t('error.invalidTimeFormat'))
              target.textContent = oldValue
              return
            }

            if (newValue === oldValue) {
              editingCell.value = null
              return
            }

            row.original[fieldName] = newValue ?? ''
            editingCell.value = null

            emit('handleUpdatePersonalAttendance', row.original)
          }
          else if (e.key === 'Escape') {
            editingCell.value = null
          }
        },
        onClick: () => {
          if (!isEditable(row.original))
            messageNotify.error(t('error.isApprovedRecord'))
        },
      },
      !getValue() ? '--:--' : formatTimeToHHMM(getValue()),
    )
}

function editableBreakTimeCell(index: number, fieldName: 'breakInTime' | 'breakOutTime' | string) {
  return ({ row, column, getValue }: any) =>
    h(
      'span',
      {
        contenteditable: true,
        class:
          'px-2 py-1 border border-transparent rounded hover:border-blue-300 focus:outline-none focus:bg-gray-200',
        onFocus: () => {
          editingCell.value = {
            rowId: row.original.employeeShiftId ?? '',
            columnId: column.id,
          }
        },
        onBlur: async (e: FocusEvent) => {
          if (!isEditable(row.original))
            return

          const target = e.target as HTMLElement
          const newValue = target.textContent?.trim()
          const oldValue = !getValue() ? '--:--' : formatTimeToHHMM(row.original.breakList?.[index]?.[fieldName] ?? '')

          if (!HHMMRegex.test(newValue ?? '')) {
            messageNotify.error(t('error.invalidTimeFormatHHMM'))
            target.textContent = oldValue
            return
          }

          if (newValue === oldValue) {
            editingCell.value = null
            return
          }

          row.original.breakList![index][fieldName] = newValue ?? ''
          editingCell.value = null

          if (!isWorkingTimeValid(row.original.breakList, row.original.checkInTime, row.original.checkOutTime))
            return

          emit('handleUpdatePersonalAttendance', row.original)
        },
        onKeydown: async (e: KeyboardEvent) => {
          if (e.key === 'Enter' || e.key === 'Tab') {
            e.preventDefault()
            if (!isEditable(row.original))
              return

            const target = e.target as HTMLElement
            const newValue = target.textContent?.trim()
            const oldValue = formatTimeToHHMM(row.original.breakList?.[index]?.[fieldName] ?? '')

            if (!HHMMRegex.test(newValue ?? '')) {
              messageNotify.error(t('error.invalidTimeFormat'))
              target.textContent = oldValue
              return
            }

            if (newValue === oldValue) {
              editingCell.value = null
              return
            }

            row.original.breakList![index][fieldName] = newValue ?? ''
            editingCell.value = null

            if (isWorkingTimeValid(row.original.breakList, row.original.checkInTime, row.original.checkOutTime))
              emit('handleUpdatePersonalAttendance', row.original)
          }
          else if (e.key === 'Escape') {
            editingCell.value = null
          }
        },
        onClick: () => {
          if (!isEditable(row.original))
            messageNotify.error(t('error.isApprovedRecord'))
        },
      },
      !getValue() ? '--:--' : formatTimeToHHMM(getValue()),
    )
}

function editableBreakListCell({ row, getValue }: any) {
  const breakList: BreakTimeItem[] = getValue() ?? []

  function removeBreak(index: number) {
    if (!isEditable(row.original)) {
      messageNotify.error(t('error.isApprovedRecord'))
      return
    }

    row.original.breakList?.splice(index, 1)
    emit('handleUpdatePersonalAttendance', row.original)
  }

  function addNewBreak() {
    if (!isEditable(row.original)) {
      messageNotify.error(t('error.isApprovedRecord'))
      return
    }

    // Thêm break mới với giá trị mặc định
    if (!row.original.breakList)
      row.original.breakList = []
    if (row.original.breakList.length > 2)
      return

    row.original.breakList.push({ breakInTime: '', breakOutTime: '' })
  }

  return h('div', {
    class: 'flex flex-col group gap-y-1',
  }, [
    ...breakList.map((item, index) =>
      h('div', {
        class: 'flex items-center gap-1',
      }, [
        editableBreakTimeCell(index, 'breakInTime')({
          row,
          column: { id: `breakInTime_${index}` },
          getValue: () => item.breakInTime,
        }),
        h('span', {
          class: 'text-gray-500 text-sm',
        }, '~'),
        editableBreakTimeCell(index, 'breakOutTime')({
          row,
          column: { id: `breakOutTime_${index}` },
          getValue: () => item.breakOutTime,
        }),
        h('button', {
          class: 'text-gray-500 text-sm invisible group-hover:visible hover:text-red-700',
          onClick: () => removeBreak(index),
        }, h(MinusCircleOutlined)),
        index === breakList.length - 1 && h('button', {
          class: 'invisible group-hover:visible hover:text-blue-700 text-gray-500',
          onClick: addNewBreak,
          type: 'button',
        }, h(PlusCircleOutlined)),
      ])),
  ])
}

const rowSpans = computed(() => calculateRowSpans(table.getRowModel().rows.map(row => row.original), 'workingDate'))
</script>

<template>
  <table class="min-w-full divide-y divide-gray-200 text-left">
    <!-- Enhanced Table Header -->
    <thead class="bg-gradient-to-r from-gray-50 via-gray-100 to-gray-50">
      <tr>
        <th
          v-for="column in table.getHeaderGroups()[0].headers"
          :key="column.id"
          class="group px-6 py-4 text-xs font-bold text-gray-700 uppercase tracking-wider border-b border-gray-200 hover:bg-gray-100 transition-all duration-200 cursor-pointer text-center"
        >
          <div class="flex items-center gap-2">
            <FlexRender
              :render="column.column.columnDef.header"
              :props="column.getContext()"
            />
            <!-- Sort indicator placeholder -->
            <div
              v-if="column.id === 'workingDate'"
              @click="table.getColumn('workingDate')?.getToggleSortingHandler()?.($event)"
            >
              <svg class="w-4 h-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
              </svg>
            </div>
          </div>
        </th>
      </tr>
    </thead>

    <!-- Enhanced Table Body -->
    <tbody class="bg-white divide-y divide-gray-100">
      <tr
        v-for="(row, rowIndex) in table.getRowModel().rows"
        :key="row.id"
        class="hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent transition-all duration-200 group"
      >
        <template v-for="cell in row.getVisibleCells()" :key="cell.id">
          <td
            v-if="cell.column.id === 'workingDate' && rowSpans[rowIndex] > 0"
            :rowspan="rowSpans[rowIndex]"
            class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-50 group-hover:border-blue-100 transition-colors"
          >
            <FlexRender
              :render="cell.column.columnDef.cell"
              :props="cell.getContext()"
            />
          </td>
          <td
            v-else-if="cell.column.id !== 'workingDate'"
            class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-50 group-hover:border-blue-100 transition-colors"
          >
            <FlexRender
              :render="cell.column.columnDef.cell"
              :props="cell.getContext()"
            />
          </td>
        </template>
      </tr>
    </tbody>
  </table>
</template>
