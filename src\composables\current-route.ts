import router from '@/router'
import logger from '~@/utils/logger'

export function useCurrentRoute() {
  const currentRoute = router.currentRoute
  const layoutMenuStore = useLayoutMenu()
  const { menuDataMap } = storeToRefs(layoutMenuStore)
  const pathsKeys = menuDataMap.value?.keys()
  const currentPath = currentRoute.value.path
  // router.
  // 通过校验判断是否在menuItem中
  logger.log('currentPath', currentPath, pathsKeys)
  return {
    currentRoute,
  }
}
