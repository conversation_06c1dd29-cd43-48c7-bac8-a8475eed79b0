const orgEndpoints = {
  getOrgsByCurrentAccount: 'v1/org',
  getOrganizationById: (orgId: string) => `v1/org/${orgId}`,
  getOwnedOrgs: 'v1/org/owned-orgs',
  createOrganization: 'v1/org',
  updateOrganization: (orgId: string) => `v1/org/${orgId}`,
}

export interface OrganizationParams {
  orgCode: string
  orgName: string
  orgSubName?: string
  postalCode?: string
  address?: string
  phoneNumber?: string
  email?: string
  fax?: string
  website?: string
  registrationNumber?: string
  registrationDate?: string
  registrationLicenseType?: boolean
  legalOrgNumber?: string
  legalTaxNumber?: string
  legalRepresentative?: string
  description?: string
  timeZone?: string
}

export interface OrganizationItem {
  createTime: string
  updateTime: string
  orgId: string
  orgCode: string
  orgName: string
  orgSubName: string
  postalCode: string
  address: string
  phoneNumber: string
  email: string
  fax: string
  website: string
  registrationNumber: string
  registrationDate: string
  registrationLicenseType: boolean
  legalOrgNumber: string
  legalTaxNumber: string
  legalRepresentative: string
  description: string
  logoUrl: string
  timeZone: string
}

export interface SimpleOrgItem {
  orgId: string
  orgCode: string
  orgName: string
}

export interface OrganizationResponse {
  items: SimpleOrgItem[]
}

export function getOrgsByCurrentAccountApi() {
  return useGet<OrganizationResponse>(orgEndpoints.getOrgsByCurrentAccount)
}

export function getOrganizationDetailApi(orgId: string) {
  return useGet<OrganizationItem>(orgEndpoints.getOrganizationById(orgId))
}

export function createOrganizationApi(data: OrganizationParams) {
  return usePost<OrganizationItem>(orgEndpoints.createOrganization, data)
}

export function updateOrganizationApi(orgId: string, data: OrganizationParams) {
  return usePut<OrganizationItem>(orgEndpoints.updateOrganization(orgId), data)
}

export function getOwnedOrgsApi() {
  return useGet<OrganizationResponse>(orgEndpoints.getOwnedOrgs)
}
