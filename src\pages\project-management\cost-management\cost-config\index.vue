<script lang="ts" setup>
import PaymentType from './tabs/payment-type.vue';
import EntryType from './tabs/entry-type.vue';

const { t } = useI18n();
</script>
<template>
  <a-tabs type="card" destroy-inactive-tab-pane>
    <a-tab-pane key="1" :tab="t('payment-type')">
      <PaymentType />
    </a-tab-pane>
    <a-tab-pane key="2" :tab="t('entry-type')">
      <EntryType />
    </a-tab-pane>
  </a-tabs>
</template>
