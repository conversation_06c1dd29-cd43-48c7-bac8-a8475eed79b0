export default {
  // LoginPage
  "pages.layouts.userLayout.title":
    "Antdv Pro is a universal middle-stage management system based on Ant Design Vue",
  "pages.login.accountLogin.tab": "Account Login",
  "pages.login.accountLogin.username.errorMessage":
    "Incorrect username",
  "pages.login.accountLogin.orgAndPassword.errorMessage":
    "Didn't select org or Incorrect password",
  "pages.login.failure": "<PERSON><PERSON> failed, please try again!",
  "pages.login.success": "Login successful!",
  "pages.login.username.placeholder": "User name",
  "pages.login.username.required": "Please input your username!",
  "pages.login.orgcode.placeholder": "Org code",
  "pages.login.orgcode.required": "Please input your org code!",
  "pages.login.password.placeholder": "Password",
  "pages.login.change.password.placeholder": "Change password",
  "pages.login.confirm.password.placeholder": "Confirm password",
  "button.change": "Change",
  "pages.login.password.required": "Please input your password!",
  "pages.login.phoneLogin.tab": "Phone Login",
  "pages.login.phoneLogin.errorMessage": "Verification Code Error",
  "pages.login.phoneNumber.placeholder": "Phone Number",
  "pages.login.phoneNumber.required": "Please input your phone number!",
  "pages.login.phoneNumber.invalid": "Phone number is invalid!",
  "pages.login.captcha.placeholder": "Verification Code",
  "pages.login.captcha.required": "Please input verification code!",
  "pages.login.phoneLogin.getVerificationCode": "Get Code",
  "pages.getCaptchaSecondText": "sec(s)",
  "pages.login.rememberMe": "Remember me",
  "pages.login.forgotPassword": "Forgot Password ?",
  "pages.login.submit": "Login",
  "pages.login.back": "Back",
  "pages.login.next": "Next",
  "pages.login.forgotAccount": "Forgot Account?",
  "pages.login.loginWith": "Login with :",
  "pages.login.registerAccount": "Register Account",
  "pages.login.tips": "Welcome to the system",
  "pages.login.title": "Login to your account",
};
