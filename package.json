{"name": "kantoku-admin", "type": "module", "version": "1.0.0-beta.3", "packageManager": "pnpm@8.10.0", "author": "nguyen<PERSON><PERSON><PERSON> <<EMAIL>>", "scripts": {"dev": "mist", "build": "vue-tsc && mist build", "preview": "mist preview", "lint": "eslint src --fix", "typecheck": "vue-tsc --noEmit", "bump:patch": "changelogen --bump --output CHANGELOG.md --release --prerelease", "bump:minor": "changelogen --bump --output CHANGELOG.md --release --minor", "bump:major": "changelogen --bump --output CHANGELOG.md --release --major", "dir-tree": "esno ./scripts/dir-tree", "prepare": "husky install", "gen:uno": "esno ./scripts/gen-unocss", "toJS": "esno scripts/to-js"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/g2plot": "^2.4.31", "@antv/l7": "^2.1.11", "@ctrl/tinycolor": "^4.0.2", "@fingerprintjs/fingerprintjs": "^4.6.2", "@firebase/messaging": "^0.12.17", "@headlessui/vue": "^1.7.23", "@holiday-jp/holiday_jp": "^2.5.1", "@pdf-lib/standard-fonts": "^1.0.0", "@pdfme/generator": "^5.4.2", "@tanstack/vue-table": "^8.21.3", "@v-c/utils": "^0.0.26", "@vueuse/core": "^10.7.0", "ant-design-vue": "^4.2.3", "axios": "^1.11.0", "date-fns": "^4.1.0", "dayjs": "^1.11.10", "echarts": "^5.6.0", "firebase": "^11.6.0", "google-font-installer": "^1.2.0", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.468.0", "mitt": "^3.0.1", "nitro-cors": "^0.7.1", "nitropack": "^2.9.6", "nprogress": "^0.2.0", "pinia": "^2.1.7", "qs": "^6.14.0", "ua-parser-js": "^2.0.3", "unplugin-vue-components": "^0.26.0", "uuid": "^11.0.5", "vue": "^3.5.4", "vue-advanced-cropper": "^2.8.9", "vue-echarts": "^7.0.3", "vue-i18n": "^9.8.0", "vue-request": "^2.0.4", "vue-router": "^4.2.5", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@eslint/js": "^9.2.0", "@mistjs/cli": "0.0.1-beta.6", "@mistjs/eslint-config": "^1.0.0", "@mistjs/vite-plugin-preload": "^0.0.1", "@types/file-saver": "^2.0.7", "@types/fs-extra": "^11.0.4", "@types/lodash-es": "^4.17.12", "@types/node": "^20.10.5", "@types/qs": "^6.9.18", "@types/treeify": "^1.0.3", "@vitejs/plugin-vue": "^4.5.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/test-utils": "^2.4.3", "antdv-component-resolver": "^1.0.7", "antdv-style": "0.0.1-beta.2", "changelogen": "^0.5.5", "cross-env": "^7.0.3", "directory-tree": "^3.5.1", "esbuild": "^0.19.9", "eslint": "^8.57.0", "esno": "^0.17.0", "execa": "^8.0.1", "fs-extra": "^11.2.0", "husky": "^8.0.3", "jsdom": "^22.1.0", "less": "^4.2.0", "lint-staged": "^14.0.1", "naive-ui": "^2.41.0", "npm-run-all": "^4.1.5", "picocolors": "^1.0.0", "treeify": "^1.1.0", "ts-node": "^10.9.2", "typescript": "^5.3.3", "unocss": "^0.57.7", "unocss-preset-chinese": "^0.3.0", "unocss-preset-ease": "^0.0.3", "unplugin-auto-import": "^0.16.7", "unplugin-config": "^0.1.4", "vite": "^5.0.10", "vitest": "^0.34.6", "vue-tsc": "^3.0.4"}, "lint-staged": {"**/*.{vue,ts,js,jsx,tsx}": "eslint src --fix"}}