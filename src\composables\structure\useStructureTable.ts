import { usePagination } from 'vue-request'
import { getStructureListApi } from '~@/api/company/struct'

export function useStructureTable() {
  const {
    data: structureDataSource,
    current,
    totalPage,
    loading,
    pageSize,
    run: fetchData,
  } = usePagination(getStructureListApi, {
    pagination: {
      currentKey: 'pageNum',
      pageSizeKey: 'pageSize',
      totalKey: 'totalRecords',
    },
  })

  const pagination = computed(() => ({
    total: totalPage.value,
    current: current.value,
    pageSize: pageSize.value,
  }))

  return {
    structureDataSource,
    loading,
    pagination,
    fetchData,
  }
}
