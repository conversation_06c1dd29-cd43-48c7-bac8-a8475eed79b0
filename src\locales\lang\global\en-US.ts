export default {
  'navBar.lang': 'Languages',
  'layout.user.link.help': 'Help',
  'layout.user.link.privacy': 'Privacy',
  'layout.user.link.terms': 'Terms',
  'app.copyright.produced': 'Produced by Ant Financial Experience Department',
  'app.preview.down.block': 'Download this page to your local project',
  'app.welcome.link.fetch-blocks': 'Get all block',
  'app.welcome.link.block-list':
    'Quickly build standard, pages based on `block` development',

  // SettingDrawer
  'app.setting.pagestyle': 'Page style setting',
  'app.setting.pagestyle.dark': 'Dark style',
  'app.setting.pagestyle.light': 'Light style',
  'app.setting.pagestyle.inverted': 'Inverted style',
  'app.setting.pagestyle.mode': 'Layout mode',
  'app.setting.pagestyle.top': 'Top layout mode',
  'app.setting.pagestyle.side': 'Side layout mode',
  'app.setting.pagestyle.mix': 'Mix layout mode',
  'app.setting.content-width.contentWidth': 'Content Width',
  'app.setting.content-width.fixed': 'Fixed',
  'app.setting.content-width.fluid': 'Fluid',
  'app.setting.content-width.fixedHeader': 'Fixed Header',
  'app.setting.content-width.fixSiderbar': 'Fixed Siderbar',
  'app.setting.content-width.splitMenus': 'Auto Split Menus',
  'app.setting.content-width.keepAlive': 'KeepAlive',
  'app.setting.content-width.accordionMode': 'Menu Accordion Mode',
  'app.setting.content-width.leftCollapsed': 'sideMenu Left',
  'app.setting.content-width.compactAlgorithm': 'Compact Mode',
  'app.setting.content-area.title': 'Content Area',
  'app.setting.content-area.header': 'Header',
  'app.setting.content-area.footer': 'Footer',
  'app.setting.content-area.menu': 'Menu',
  'app.setting.content-area.watermark': 'Watermark',
  'app.setting.content-area.menuHeader': 'Menu Header',
  'app.setting.content-area.multiTab': 'Multi Tab',
  'app.setting.content-area.multiTabFixed': 'Fixed Multi Tab',
  'app.setting.content-area.animationName': 'Animation',
  'app.setting.themecolor': 'Theme Color',
  'app.setting.themecolor.dust': 'Dust Red',
  'app.setting.themecolor.volcano': 'Volcano',
  'app.setting.themecolor.sunset': 'Sunset Orange',
  'app.setting.themecolor.cyan': 'Cyan',
  'app.setting.themecolor.green': 'Polar Green',
  'app.setting.themecolor.daybreak': 'Daybreak Blue',
  'app.setting.themecolor.techBlue': 'Technology (default)',
  'app.setting.themecolor.geekblue': 'Geek Glue',
  'app.setting.themecolor.purple': 'Golden Purple',
  'app.setting.navigationmode': 'Navigation Mode',
  'app.setting.sidemenu': 'Side Menu Layout',
  'app.setting.topmenu': 'Top Menu Layout',
  'app.setting.fixedheader': 'Fixed Header',
  'app.setting.fixedsidebar': 'Fixed Sidebar',
  'app.setting.fixedsidebar.hint': 'Works on Side Menu Layout',
  'app.setting.hideheader': 'Hidden Header when scrolling',
  'app.setting.hideheader.hint': 'Works when Hidden Header is enabled',
  'app.setting.othersettings': 'Other Settings',
  'app.setting.weakmode': 'Weak Mode',
  'app.setting.copy': 'Copy Setting',
  'app.setting.copyinfo':
    'copy success，please replace default-setting in config/default-setting.js',
  'app.setting.production.hint':
    'Setting panel shows in development environment only, please manually modify',
  'app.multiTab.title': 'Multi Tab',
  'app.multiTab.closeCurrent': 'Close Current',
  'app.multiTab.closeOther': 'Close Other',
  'app.multiTab.closeAll': 'Close All',
  'app.multiTab.refresh': 'Refresh',
  'app.multiTab.closeRight': 'Close Right',
  'app.multiTab.closeLeft': 'Close Left',

  // Menu
  'menu.dashboard': 'Dashboard',
  'menu.dashboard.manager': 'Manager (Site Info/Timecard)',
  'menu.dashboard.person': 'Employee (Timecard/Calendar)',
  'menu.dashboard.ps.calendar': 'Calendar',
  'menu.dashboard.ps.attendance': 'Attendance Management',

  'menu.project-management': 'Project Management',
  'menu.project-mng.attendance-management': 'Attendance Management',
  'menu.project-mng.attendance-mng.timecard': 'Timecard Management',
  'menu.project-mng.attendance-mng.request': 'Request Management',
  'menu.project-mng.cost-management': 'Cost Management',
  'menu.project-mng.cost-summary': 'Cost Summary',
  'menu.project-mng.cost-mng.summary-detail': 'Cost Summary Detail',
  'menu.project-mng.cost-mng.simulation-report': 'Simulation Report',

  'menu.ps-attendance-mng': 'Personal Attendance Management',
  'menu.ps-attendance-mng.monthly': 'Monthly Attendance List',
  'menu.ps-attendance-mng.request': 'Request Management',

  'menu.administrative-office': 'Administrative Office',
  'menu.administrative-office.employees': 'Employee Management',
  'menu.administrative-office.employees.list': 'Employee List',
  'menu.administrative-office.employees.detail': 'Employee Detail',
  'menu.administrative-office.employees.paid-leave': 'Paid Leave Management',
  'menu.administrative-office.employees.month-closing': 'Attendance Month Closing',
  'menu.administrative-office.employees.month-closing.detail': 'Attendance Month Closing Detail',
  'menu.company.work-shift': 'Work Shift',

  'menu.administrative-office.projects': 'Project Settings',
  'menu.administrative-office.projects.list': 'Project List',
  'menu.administrative-office.projects.schedule': 'Schedule Management',
  'menu.administrative-office.projects.work-shift': 'Work Shift Settings',
  'menu.administrative-office.projects.human-cost': 'Human Cost Settings',

  'menu.administrative-office.partners': 'Partner Management',
  'menu.administrative-office.partners.list': 'Partner List',
  'menu.administrative-office.partners.invoice': 'Invoice List',
  'menu.administrative-office.partners.vendor': 'Vendor Management',
  'menu.administrative-office.partners.manufacturer': 'Manufacturer Management',
  'menu.administrative-office.transaction-items': 'Transaction Item Management',
  'menu.administrative-office.transaction-items.list': 'Transaction Item List',
  'menu.administrative-office.transaction-items.settings': 'Transaction Item Category Settings',

  'menu.administrative-office.notification': 'Business Notification Management',
  'menu.administrative-office.notification.settings': 'Notification Management',
  'menu.administrative-office.notification.calendar': 'Event Calendar',

  'menu.setting': 'Settings',
  'menu.settings.organization': 'Organization Settings',
  'menu.settings.role': 'Role Settings',

  'menu.administrative-office.employees.auto-checkout-setting': 'Auto Checkout Setting',
}
