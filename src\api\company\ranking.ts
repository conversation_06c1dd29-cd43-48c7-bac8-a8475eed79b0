import type { QueryParams } from '../common-params'
import { formatData } from '~@/utils/tools'

export interface RankingItem {
  rankingId: string
  rankingName?: string
  description?: string
  minValue?: number | null
  maxValue?: number | null
  averageValue?: number | null
  createdTime?: string
  updatedTime?: string
}
export interface RankingEmployeeItemParams {
  employeeRankId: string
  employeeId: string
  employeeName: string
  rankId: string
  rankName: string
  rankValidDateFrom: string
  rankValidDateTo: string
  costAmount: number
  averageCostAmount: number
}

export type RankingItemParams = Partial<RankingItem>

export interface RankingItemCombo {
  id: string
  name: string
}
export interface SimpleRankingResponse {
  items: RankingItemCombo[]
  pageNum: number
  pageSize: number
  totalRecords: number
}
export interface RankingDataResponse {
  items: RankingItem[]
  pageNum?: number
  pageSize?: number
  totalRecords?: number
}

export interface RankingEmployeeDataResponse {
  items: RankingEmployeeItemParams[]
  pageNum?: number
  pageSize?: number
  totalRecords?: number
}

export interface GetRankingParams {
  keyword?: string
  pageNum?: number
  pageSize?: number
  minValue?: number
  maxValue?: number
}

export async function getRankingListApi(params?: GetRankingParams) {
  return useGet<RankingDataResponse>('v1/ranking', formatData(params))
}

export async function getRankingEmployeeListApi(params?: GetRankingParams) {
  return useGet<RankingEmployeeDataResponse>(
    'v1/ranking/employee',
    formatData(params),
  )
}

export async function getRankingByIdApi(id: number) {
  return useGet<RankingItem>(`v1/ranking/${id}`)
}

export async function getRankingComboApi(params?: QueryParams) {
  return useGet<SimpleRankingResponse>(
    'v1/ranking/simple-ranking-info',
    params,
  )
}

export async function createRankingApi(data: RankingItemParams) {
  return usePost<RankingItem>('v1/ranking', data)
}

export async function updateRankingApi(id: string, data: RankingItemParams) {
  return usePut<RankingItem>(`v1/ranking/${id}`, data)
}

export async function deleteRankingApi(id: string) {
  return useDelete(`v1/ranking/${id}`)
}
