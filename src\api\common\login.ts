import type { Dayjs } from 'dayjs'

export interface LoginParams {
  loginId: string
  password: string
  orgId: string
}
export interface ChangePasswordParams {
  oldPassword: string
  newPassword: string
  confirmNewPassword: string
}

export interface ForgotPasswordParams {
  accountLoginInfo: string
}

export interface UserOrgInfo {
  Id: string
  Name: string
  NameCompare: string
  DefaultValue: number
  DefaultID: string
  OrgKey: string
  OtherID: string
}

export function logoutApi() {
  return useGet('v1/logout')
}
export interface UserLoginResultModel {
  userId: string
  loginId: string
  name: string
  email?: string
  address?: string
  phone?: string
  gender?: boolean
  birthday?: Dayjs
  status?: boolean
  orgId: string
  orgName?: string
  roles?: string
  createTime?: Dayjs
  updateTime?: Dayjs
  firstChangePass?: boolean
  accessToken?: string
  refreshToken?: string
}

export interface EmployeeInfo {
  employeeId: string
  loginId: string
  name: string
  email: string
  address: string
  phone: string
  gender: boolean
  birthday: string
  orgId: string
  orgCode: string
  orgName: string
  firstChangePass: boolean
  isManager: boolean
  accessToken: string
  refreshToken: string
}

export interface OrgInfo {
  orgId: string
  orgCode?: string
  orgName?: string
  orgPostalCode?: string
  orgAddress?: string
  status: boolean
  createTime?: Dayjs
  updateTime?: Dayjs
}

export interface OrgInfoResponse {
  items: OrgInfo[]
  pageIndex: number
  pageSize: number
  totalRow: number
  pageCount: number
}

export interface ResetPasswordParams {
  employeeId: string
}

export function login(params: LoginParams) {
  return usePost<EmployeeInfo>('v1/auth/login', params)
}

// export function getOrgInfoFromLoginId(loginId: string) {
//   return useGet<OrgInfoResponse>(`v1/org/get-orgs-login?loginId=${loginId}`)
// }

export interface SignInResponse {
  accessToken: string
  refreshToken: string
}

export interface SignInParams {
  loginId: string
  password: string
}

export interface SignOnParams {
  orgId: string
}

export interface SignOnResponse {
  accessToken: string
  refreshToken: string
}

export function signInApi(params: SignInParams) {
  return usePost<SignInResponse>('v1/auth/sign-in', params)
}

export function signOnApi(params: SignOnParams) {
  return usePost<SignOnResponse>('v1/auth/sign-on', params)
}

export function resetPassword(params: ResetPasswordParams) {
  return usePost<any>('v1/auth/reset-password', params)
}

export function changePasswordApi(params: ChangePasswordParams) {
  return usePost<boolean>(
    'v1/auth/change-password',
    params,
  )
}

export function recoverPasswordApi(params: ForgotPasswordParams) {
  return usePost<any>('v1/auth/recover-password', params)
}
