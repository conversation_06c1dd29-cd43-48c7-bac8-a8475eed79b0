import type { RouteRecordRaw } from 'vue-router'
import { rootRoute } from '~@/router/dynamic-routes'
import {
  generateFlatRoutes,
  generateRoutes,
  generateTreeRoutes,
} from '~@/router/generate-route'
import { DYNAMIC_LOAD_WAY, DynamicLoadEnum } from '~@/utils/constant'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'
import logger from '~@/utils/logger'
import { STATIC_MENUS, menuLists } from '~@/utils/menuData'
import { getUserInfoApi } from '~@/api/company/user-info'
import { getCurrentRolesApi } from '~@/api/employee/employee'
import type { Role } from '~@/api/company/role'
import type { MenuDataItem } from '~@/layouts/basic-layout/typing'
import type { UserInfoItem } from '~@/types/company/user-info'
export const useUserStore = defineStore('user', () => {
  // State
  const routerData = ref<RouteRecordRaw>()
  const menuData = ref<MenuDataItem[]>([])
  const roles = ref<Role[]>([])
  const userInfo = ref<UserInfoItem>()
  const avatar = ref<string>('')
  const token = useAuthorization()

  // Getters
  const nickname = computed(() => userInfo.value?.userName)
  const hasMenuData = computed(() => menuData.value.length > 0)
  const messageNotification = useMessage()
  const resetState = () => {
    token.value = null
    userInfo.value = undefined
    routerData.value = undefined
    menuData.value = []
  }
  const handleApiError = (error: unknown, context: string) => {
    resetState()
    logger.error(`Error in ${context}:`, error)
    throw new Error(`Failed to ${context}`)
  }
  // Actions
  const fetchMenuData = async () => {
    menuData.value = [...menuLists, ...STATIC_MENUS]
    return generateTreeRoutes(menuData.value)
  }

  const fetchCurrentRoles = async () => {
    const { data, status, message } = await getCurrentRolesApi()
    if (status === ResponseStatusEnum.SUCCESS)
      roles.value = data?.items ?? []

    else
      messageNotification.error(message)
  }

  const setUserAvatar = (newAvatar: string) => {
    avatar.value = newAvatar
  }

  const getUserAvatar = async () => {
    const { userImgSrc, fetchUserAvatar } = useAvatar()
    await fetchUserAvatar()
    avatar.value = userImgSrc.value
  }

  const generateDynamicRoutes = async () => {
    const dynamicLoadWay
      = DYNAMIC_LOAD_WAY === DynamicLoadEnum.BACKEND
        ? fetchMenuData
        : generateRoutes
    const response = await dynamicLoadWay()
    if (!response)
      return
    const { menuData: treeMenuData, routeData } = response
    menuData.value = treeMenuData
    routerData.value = {
      ...rootRoute,
      children: generateFlatRoutes(routeData),
    }
    return routerData.value
  }

  async function getEmployeeInfo() {
    try {
      const { data, status, message } = await getUserInfoApi()
      if (status === ResponseStatusEnum.SUCCESS) {
        userInfo.value = data ?? undefined
        return true
      }
      else {
        messageNotification.error(message)
        resetState()
        return false
      }
    }
    catch (error) {
      handleApiError(error, 'initialize user')
      return false
    }
  }

  return {
    avatar,
    userInfo,
    hasMenuData,
    roles,
    logout: resetState,
    routerData,
    menuData,
    generateDynamicRoutes,
    fetchCurrentRoles,
    nickname,
    getEmployeeInfo,
    getUserAvatar,
    setUserAvatar,
  }
})
