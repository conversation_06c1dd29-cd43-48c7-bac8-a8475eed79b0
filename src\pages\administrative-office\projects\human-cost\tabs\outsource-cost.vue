<script lang="ts" setup>
import {
  CloseOutlined,
  LeftOutlined,
  PlusOutlined,
  RightOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import type {
  ColumnGroupType,
  ColumnType,
  TablePaginationConfig,
} from 'ant-design-vue/es/table';
import type { FilterValue } from 'ant-design-vue/es/table/interface';
import dayjs from 'dayjs';
import type { UnwrapRef } from 'vue';
import { usePagination } from 'vue-request';
import type {
  OutSourceItem,
  OutSourcePriceItem,
  OutSourcePriceResponse,
} from '~@/api/outsource';
import {
  createOutsourcePrice,
  fetchOutsourceListApi,
  getOutsourcePrices,
  updateOutsourcePrice,
} from '~@/api/outsource';
import { ResponseStatusEnum } from '~@/enums/response-status-enum';
import logger from '~@/utils/logger';

type ColumnItemType<T> = ColumnGroupType<T> | ColumnType<T>;

type OutsourceItemType = OutSourcePriceItem & {
  isEdit?: boolean;
  isAdd?: boolean;
  _treatAsAdd?: boolean;
  outSource?: {
    value: string;
    label: string;
    option: OutSourceItem;
  };
  itemsCount?: number;
};

interface Params {
  pageNum?: number;
  pageSize?: number;
  keyword?: string;
  startDate?: string;
  endDate?: string;
}

const { t } = useI18n();
const outsources = ref<OutSourceItem[]>([]);
const searchDate = ref<dayjs.Dayjs>(dayjs());
const editableData: UnwrapRef<Record<string, OutsourceItemType>> = reactive({});
const searchForm = ref<Params>({
  pageSize: 10,
  pageNum: 1,
  startDate: dayjs(searchDate.value).startOf('month').format('YYYY-MM-DD'),
  endDate: dayjs(searchDate.value).endOf('month').format('YYYY-MM-DD'),
});

async function queryData(params?: Params) {
  const { data: responseData } = await getOutsourcePrices(params);
  if (!responseData || !responseData.items)
    return ref<OutSourcePriceResponse>({ items: [] }).value;

  const processedItems = responseData.items.map((item, index) => {
    if (item.outSourcePriceId == null) {
      return {
        ...item,
        outSourcePriceId: `temp_api_${item.outSourceId || 'no_id'}_${Date.now()}_${index}`,
        _treatAsAdd: true,
      };
    }
    return item;
  });

  return ref({ ...responseData, items: processedItems }).value;
}

const {
  data: dataSource,
  loading,
  total,
  current,
  run,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [searchForm.value],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
});

const pagination = computed(() => ({
  total: total.value ?? 10,
  current: current.value,
  pageSize: pageSize.value,
  showSizeChanger: true,
}));

const columns = computed<ColumnItemType<OutsourceItemType>[]>(() => [
  {
    title: t('form.code'),
    dataIndex: 'code',
    width: 200,
    customCell: (record) => {
      return { rowSpan: record.itemsCount };
    },
  },
  {
    title: t('form.name'),
    dataIndex: 'name',
    width: 200,
    customCell: (record) => {
      return { rowSpan: record.itemsCount };
    },
  },
  { title: t('form.cost-by-day'), dataIndex: 'price-per-day', width: 200 },
  { title: t('form.description'), dataIndex: 'description', width: 200 },
  {
    title: t('action'),
    dataIndex: 'action',
    width: 150,
    align: 'center',
    fixed: 'right',
  },
]);

function handleTableChange(
  pagination: TablePaginationConfig,
  filters: Record<string, FilterValue>,
) {
  searchForm.value.pageSize = pagination.pageSize;
  searchForm.value.pageNum = pagination.current;
  run({ ...searchForm.value, ...filters });
}

function handlePaginationChange(page: number, pageSize: number) {
  searchForm.value.pageSize = pageSize;
  searchForm.value.pageNum = page;
  run(searchForm.value);
}

function handleLeftArrowClick() {
  searchDate.value = dayjs(searchDate.value).subtract(1, 'month');
  onSearch();
}

function handleRightArrowClick() {
  searchDate.value = dayjs(searchDate.value).add(1, 'month');
  onSearch();
}

function onSearch() {
  searchForm.value.startDate = dayjs(searchDate.value)
    .startOf('month')
    .format('YYYY-MM-DD');
  searchForm.value.endDate = dayjs(searchDate.value)
    .endOf('month')
    .format('YYYY-MM-DD');

  handleTableChange(
    { pageSize: searchForm.value.pageSize ?? 10, current: 1 },
    {},
  );
}

const sortedDataSource = computed(() => {
  const grouped = (dataSource.value?.items ?? []).reduce((acc, cur) => {
    const key = cur.outSourceId ?? '';
    if (!acc[key])
      acc[key] = [];
    acc[key].push(cur);
    return acc;
  }, {} as Record<string, OutsourceItemType[]>);

  const sortedItems = Object.keys(grouped).flatMap(uuid => grouped[uuid]);

  return sortedItems.map((item, index) => {
    const findIndex = sortedItems.findIndex(
      i => i.outSourceId === item.outSourceId,
    );
    const itemsCount = sortedItems.filter(
      i => i.outSourceId === item.outSourceId,
    ).length;
    const isFirst = findIndex === index;
    return { ...item, itemsCount: isFirst ? itemsCount : 0 };
  });
});

function handleAdd() {
  const newData: OutsourceItemType = {
    outSourcePriceId: `${Date.now()}`,
    outSource: {
      value: outsources.value[0]?.outSourceId ?? '',
      label: outsources.value[0]?.outSourceName ?? '',
      option: outsources.value[0] ?? {},
    },
    pricePerDay: 0,
    description: '',
    isAdd: true,
  };
  if (dataSource.value)
    dataSource.value.items.unshift(newData);
  editableData[newData.outSourcePriceId] = newData;
}

function handleEdit(key: string) {
  const itemToEdit = dataSource.value?.items.find(
    item => item.outSourcePriceId === key,
  ) as OutsourceItemType | undefined;
  if (!itemToEdit)
    return;

  // Clear other items that are in 'isEdit' mode
  const currentEditKeys = Object.keys(editableData);
  currentEditKeys.forEach((k) => {
    if (editableData[k]?.isEdit && k !== key)
      delete editableData[k];
  });

  // Set the current item for editing
  const isEffectivelyAdd = !!itemToEdit._treatAsAdd;
  editableData[key] = {
    ...itemToEdit,
    isEdit: !isEffectivelyAdd,
    isAdd: isEffectivelyAdd,
  };
}

async function handleCreate(key: string) {
  try {
    const currentEditableItem = editableData[key];
    const outSourceIdToUse = currentEditableItem.outSource?.value || currentEditableItem.outSourceId;

    if (!outSourceIdToUse) {
      message.error(t('message.required', { field: t('form.name') }));
      return;
    }

    const create = await createOutsourcePrice(outSourceIdToUse, {
      pricePerDay: currentEditableItem?.pricePerDay,
      description: currentEditableItem?.description,
    });
    if (create.status !== ResponseStatusEnum.SUCCESS)
      return;

    if (dataSource.value) {
      Object.assign(
        dataSource.value.items.filter(
          item => key === item.outSourcePriceId,
        )[0],
        create.data,
      );
    }
    delete editableData[key];
    message.success(create.message);
  }
  catch (error) {
    logger.error(error);
  }
}

async function handleUpdate(key: string) {
  try {
    const update = await updateOutsourcePrice(key, {
      pricePerDay: editableData[key]?.pricePerDay,
      description: editableData[key]?.description,
    });
    if (update.status !== ResponseStatusEnum.SUCCESS)
      return;

    if (dataSource.value) {
      Object.assign(
        dataSource.value.items.filter(
          item => key === item.outSourcePriceId,
        )[0],
        update.data,
      );
    }
    delete editableData[key];
    message.success(update.message);
  }
  catch (error) {
    logger.error(error);
  }
}

function handleCancel(key: string) {
  delete editableData[key];
}

onMounted(async () => {
  const data = await fetchOutsourceListApi();
  if (!data)
    return;
  outsources.value = data.items ?? [];
});
</script>

<template>
  <page-container>
    <a-row :wrap="false" :gutter="[12, 12]" class="h-[calc(100vh-160px)] flex-col">
      <a-col flex="none" span="24">
        <a-row :gutter="[12, 12]">
          <a-col span="24">
            <a-row :gutter="[12, 12]">
              <a-col flex="none">
                <a-row :gutter="[12, 12]">
                  <a-col>
                    <a-button class="flex flex-items-center" type="primary" @click="handleAdd">
                      <PlusOutlined />
                      {{ `${t('button.new')}` }}
                    </a-button>
                  </a-col>
                  <a-col>
                    <a-input
                      v-model:value="searchForm.keyword" :placeholder="t('search')" style="width: 25rem"
                      allow-clear @press-enter="onSearch"
                    >
                      <template #prefix>
                        <SearchOutlined class="text-gray-500" />
                      </template>
                    </a-input>
                  </a-col>
                </a-row>
              </a-col>
              <a-col flex="auto">
                <div class="flex gap-8 justify-end">
                  <div class="flex items-center">
                    <LeftOutlined
                      class="flex justify-center w-6 h-6 bg-white rounded-full"
                      @click="handleLeftArrowClick"
                    />
                    <a-date-picker
                      v-model:value="searchDate" picker="month" :allow-clear="false" :format="(value: dayjs.Dayjs) =>
                        `${value
                          .startOf('month')
                          .format('DD/MM/YYYY')} - ${value
                          .endOf('month')
                          .format('DD/MM/YYYY')}`
                      "
                      class="search-date" @change="onSearch"
                    />
                    <RightOutlined
                      class="flex justify-center w-6 h-6 bg-white rounded-full"
                      @click="handleRightArrowClick"
                    />
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-col>
          <a-col span="24">
            <a-table
              class="tableRanking" :scroll="{ x: 'max-content', y: 'calc(100vh - 320px)' }" :columns="columns"
              :data-source="sortedDataSource" :loading="loading" :pagination="false" row-key="outSourcePriceId" bordered
              @change="handleTableChange"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'code'">
                  <div>
                    <a-input
                      v-if="
                        editableData[record.outSourcePriceId]
                          && editableData[record.outSourcePriceId].isAdd
                      " :placeholder="editableData[record.outSourcePriceId].outSource?.option
                        ?.outSourceCode ?? t('form.code')
                      " disabled
                    />
                    <template v-else>
                      {{ record.outSourceCode }}
                    </template>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'name'">
                  <div>
                    <a-select
                      v-if="
                        editableData[record.outSourcePriceId]
                          && editableData[record.outSourcePriceId].isAdd
                      " v-model:value="editableData[record.outSourcePriceId].outSource
                      " :placeholder="t('form.name')" label-in-value :options="outsources" :field-names="{
                        label: 'outSourceName',
                        value: 'outSourceId',
                      }" class="w-full"
                    />
                    <template v-else>
                      {{ record.outSourceName }}
                    </template>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'price-per-day'">
                  <div>
                    <a-input
                      v-if="editableData[record.outSourcePriceId]" v-model:value="editableData[record.outSourcePriceId].pricePerDay
                      " :placeholder="t('form.cost-by-day')" type="number" min="0"
                    />
                    <template v-else>
                      {{ record.pricePerDay != null ? t('currency.unit') + record.pricePerDay : t('form.empty') }}
                    </template>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'description'">
                  <div>
                    <a-input
                      v-if="editableData[record.outSourcePriceId]" v-model:value="editableData[record.outSourcePriceId].description
                      " :placeholder="t('form.description')" allow-clear
                    />
                    <template v-else>
                      {{ record.description }}
                    </template>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'action'">
                  <div v-if="editableData[record.outSourcePriceId]" class="flex flex-justify-center gap-2">
                    <a-button
                      v-if="editableData[record.outSourcePriceId].isAdd" class="flex items-center" size="small"
                      type="primary" @click="handleCreate(record.outSourcePriceId)"
                    >
                      {{ t('button.save') }}
                    </a-button>
                    <a-button
                      v-if="editableData[record.outSourcePriceId].isEdit" class="flex items-center" size="small"
                      type="primary" @click="handleUpdate(record.outSourcePriceId)"
                    >
                      {{ t('button.save') }}
                    </a-button>
                    <a-button
                      v-if="editableData[record.outSourcePriceId].isAdd" class="flex items-center" size="small"
                      danger @click="handleCancel(record.outSourcePriceId)"
                    >
                      <CloseOutlined />
                    </a-button>
                    <a-button
                      v-if="editableData[record.outSourcePriceId].isEdit" class="flex items-center" size="small"
                      danger @click="handleCancel(record.outSourcePriceId)"
                    >
                      <CloseOutlined />
                    </a-button>
                  </div>
                  <div v-else class="flex flex-justify-center gap-2">
                    <a-button class="flex items-center" size="small" ghost @click="handleEdit(record.outSourcePriceId)">
                      <img src="/icon/edit.svg" class="w-[20px]">
                    </a-button>
                  </div>
                </template>
              </template>
            </a-table>
          </a-col>
        </a-row>
      </a-col>
      <a-col flex="auto" span="24">
        <div class="h-full flex items-end">
          <a-row justify="space-between" class="mt-4 w-full">
            <a-col>
              <a-pagination
                class="pagination" :total="pagination.total" :current="pagination.current"
                :page-size="pagination.pageSize" @change="handlePaginationChange"
              />
            </a-col>
            <a-col>
              <a-row :gutter="[12, 12]" justify="center" align="middle">
                <a-col>{{ t('show') }}</a-col>
                <a-col>
                  <a-pagination
                    class="pagination pagination-right" :total="pagination.total"
                    :current="pagination.current" :page-size="pagination.pageSize" show-size-changer
                    :build-option-text="(props: any) => props.value" @change="handlePaginationChange"
                  />
                </a-col>
                <a-col>{{ t('entries') }}</a-col>
              </a-row>
            </a-col>
          </a-row>
        </div>
      </a-col>
    </a-row>
  </page-container>
</template>

<style lang="less" scoped>
.pagination {
  :deep(.ant-pagination-item-active) {
    background: #f99649;
    border-color: #f99649;

    a {
      color: #fff;
    }
  }

  :deep(.ant-select-selector) {
    background: #f99649;
    border-color: #f99649 !important;

    .ant-select-selection-item {
      color: #fff;
    }
  }

  :deep(.ant-select-arrow) {
    color: #fff;
  }
}

.pagination-right {
  :deep(.ant-pagination-prev) {
    display: none;
  }

  :deep(.ant-pagination-next) {
    display: none;
  }

  :deep(.ant-pagination-item) {
    display: none;
  }

  :deep(.ant-pagination-options) {
    margin: 0;
  }

  :deep(.ant-pagination-jump-next) {
    display: none;
  }

  :deep(.ant-pagination-jump-prev) {
    display: none;
  }
}

.search-date {
  border: none;
  background: none;
  box-shadow: none;

  :deep(.ant-picker-suffix) {
    display: none;
  }

  :deep(input) {
    cursor: pointer;
    width: 165px;
  }
}
</style>
