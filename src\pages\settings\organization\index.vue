<script setup lang="ts">
import { ref } from 'vue'
import OrgTab from './org-tab.vue'
import StructureTab from './structure-tab.vue'
import PositionTab from './position-tab.vue'

const { t } = useI18n()
const activeKey = ref('company')
</script>

<template>
  <page-container>
    <div class="p-6">
      <h1 class="text-2xl font-bold mb-6">
        {{ t('settings.title') }}
      </h1>

      <a-tabs v-model:activeKey="activeKey">
        <!-- Thông tin công ty -->
        <a-tab-pane key="company" :tab="t('company.title')">
          <OrgTab />
        </a-tab-pane>

        <!-- Phòng ban -->
        <a-tab-pane key="structure" :tab="t('structure.title')">
          <StructureTab />
        </a-tab-pane>

        <!-- <PERSON><PERSON><PERSON> vụ -->
        <a-tab-pane key="position" :tab="t('position.title')">
          <PositionTab />
        </a-tab-pane>
      </a-tabs>
    </div>
  </page-container>
</template>
