<!-- eslint-disable antfu/top-level-function -->
<!-- eslint-disable curly -->
<script lang="ts" setup>
import {
  ClockCircleOutlined,
  CloseOutlined,
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  FolderViewOutlined,
  MoreOutlined,
  PlusOutlined,
  RedoOutlined,
} from '@ant-design/icons-vue'
import { Modal } from 'ant-design-vue'
import { createVNode } from 'vue'
import { usePagination } from 'vue-request'
import dayjs from 'dayjs'
import { useBreakpoints } from '@vueuse/core'
import type { EmployeeDetailsParams, EmployeeItem, EmployeeMail, EmployeePhone, RoleCombo } from '~@/api/employee/employee'
import {
  deleteEmployeeApi,
  inviteEmployeeApi,
  updateEmployeeApi,
} from '~@/api/employee/employee'
import type { PositionItem } from '~@/api/company/position'

// import { getWorkplaceListApi } from "~@/api/company/office";
// import { getRankingComboApi } from '~@/api/company/ranking'
import { resetPassword } from '~@/api/common/login'
import type {
  EmployeeLogParams,
  EntityChange,
} from '~@/api/dashboard/activity-logs'
import { getEmployeeLogApi } from '~@/api/dashboard/activity-logs'
import type { Role } from '~@/api/company/role'
import type { BaseLeaveParams } from '~@/api/company/leave'
import { createBaseLeaveApi } from '~@/api/company/leave'
import logger from '~@/utils/logger'
import type { QueryParams } from '~@/api/common-params'
import type { StatusItem } from '~@/api/common/common'
import type { InviteAccountParams } from '~@/types/account'
import { useStructure } from '~@/composables/structure/useStructure'
import { useRole } from '~@/composables/role/useRole'
import { usePosition } from '~@/composables/position/usePosition'
import { useWorkingStatus } from '~@/composables/status/useWorkingStatus'
import type { StructureItem } from '~@/api/company/struct'
import { useEmployee } from '~@/composables/employee/useEmployee'
import type { UserInfoItem } from '~@/types/company/user-info'
import { useAvatarStore } from '~@/stores/avatar'

const isMobile = useBreakpoints({
  xl: 1200,
}).smaller('xl')

const avatarStore = useAvatarStore()
// const { orgId } = useEmployeeStore()
const messageNotify = useMessage()
// const treeLine = ref(true)
// const showLeafIcon = ref(false)
const { t } = useI18n()
const roleData = ref<Role[]>([]) as Ref<Role[]>
const structData = ref<StructureItem[]>([]) as Ref<StructureItem[]>
const positionData = ref<PositionItem[]>([]) as Ref<PositionItem[]>
const employeeLogData = ref<EntityChange[]>([]) as Ref<EntityChange[]>

const editFormRef = ref()

const { fetchStructureList } = useStructure()
const { fetchRoleList } = useRole()
const { fetchPositionList } = usePosition()
const { fetchWorkingStatusList } = useWorkingStatus()
const { fetchEmployee } = useEmployee()

// Ref variable
const formFilterRef = ref()
const emailFormRef = ref()

// State variable
const editModalVisible = ref(false)

const invitedCurrentSteps = ref(0)
const editCurrentSteps = ref(0)
interface Params {
  pageNum: number
  pageSize: number
  [key: string]: any
}

const workingStatusChecked = ref<boolean>(true)

const searchForm = ref<Params>({
  pageSize: 20,
  pageNum: 1,
  workingStatus: 'WORKING',
})

const {
  data: dataSource,
  loading,
  total,
  current,
  run,
  refresh,
  pageSize,
} = usePagination(fetchEmployee, {
  defaultParams: [searchForm.value],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})
const columns: any = computed(() => {
  return [
    // {
    //   title: t('#'),
    //   dataIndex: 'index',
    //   key: 'index',
    //   width: 40,
    //   customRender: ({ index }: any) => {
    //     return (current.value - 1) * pageSize.value + index
    //   },
    // },
    {
      title: t('#'),
      dataIndex: 'index',
      key: 'index',
      width: 40,
      customRender: ({ index }: any) => {
        return (current.value - 1) * pageSize.value + index
      },
    },
    {
      title: t('employeeName'),
      dataIndex: 'employeeName',
      key: 'name',
      sorter: (a: EmployeeItem, b: EmployeeItem) => a.employeeName.localeCompare(b.employeeName),
      defaultSortOrder: 'ascend',
      width: 300,
    },
    {
      title: t('employeeCode'),
      dataIndex: 'employeeCode',
      sorter: true,
      key: 'loginId',
    },
    {
      title: t('table.contact'),
      dataIndex: 'contact',
      key: 'contact',
    },
    // {
    //   title: t('table.struct-rank'),
    //   dataIndex: 'strucRank',
    //   key: 'strucRank',
    //   sorter: true,
    // },
    {
      title: t('structureName'),
      dataIndex: 'structureName',
      key: 'structureName',
    },
    // {
    //   title: t('ranking'),
    //   dataIndex: 'rankingName',
    //   key: 'rankingName',
    //   sorter: true,
    // },
    {
      title: t('positionName'),
      dataIndex: 'positionName',
      key: 'positionName',
      sorter: true,
    },
    {
      title: t('workingStatus'),
      dataIndex: 'workingStatus',
      key: 'workingStatusName',
      sorter: true,
    },
    // {
    //   title: t('birthday'),
    //   dataIndex: 'birthday',
    //   key: 'birthday',
    //   customRender: ({ record }: any) => {
    //     if (!record.birthday)
    //       return t('no-data')
    //     return dayjs(record.birthday, 'YYYY-MM-DD').format('YYYY-MM-DD')
    //   },
    // },
    // {
    //   title: t('employeeType'),
    //   dataIndex: 'isWorkers',
    //   key: 'isWorkers',
    //   width: '150px',
    //   align: 'center',
    // },

    // {
    //   title: t('gender'),
    //   dataIndex: 'gender',
    //   key: 'gender',
    //   width: '150px',
    //   align: 'center',
    // },
    {
      title: t('action'),
      dataIndex: 'actions',
      key: 'actions',
      align: 'center',
      fixed: 'right',
    },
  ]
})

const visible = ref(false)

const gender = [
  {
    value: 1,
    label: () => t('gender.male'),
  },
  {
    value: 0,
    label: () => t('gender.female'),
  },
]

interface EditFormState extends Partial<EmployeeItem> {
  // The following properties are not in the EmployeeItem interface
  employeeMailNames: string[]
  employeePhoneNames: string[]
  userInfo: UserInfoItem
  roleIds: string[]
  employeeAddress: string
  roleNames: string[]
}

const editFormState = reactive<EditFormState>({
  userInfo: {
    userName: '',
    email: '',
    address: '',
    phone: '',
    gender: false as boolean,
    birthday: '',
    avatarUrl: '',
  },
  roleIds: [],
  structureId: undefined,
  positionId: undefined,
  employeeMailNames: [],
  employeePhoneNames: [],
  employeeAddress: '',
  workingStatus: undefined,
  employeeId: undefined,
  roleNames: [],
  structureName: '',
  positionName: '',
  salaryInDay: 0,
  salaryInHour: 0,
  workingStatusName: undefined,
  workingFromDate: '',
  workingToDate: '',
  salaryInMonth: 0,
  standardWorkingHours: 0,
})

interface InvitedFormState {
  employeeCode: string
  email: string
  roleIds: string[]
  description: string
}

const invitedFormState = reactive<InvitedFormState>({
  employeeCode: '',
  email: '',
  roleIds: [],
  description: '',
})

const openEditModal = (item: EmployeeItem) => {
  editModalVisible.value = true
  invitedCurrentSteps.value = 0
  editCurrentSteps.value = 0
  editFormState.employeeId = item.employeeId
  editFormState.workingStatus = item.workingStatus
  editFormState.roleIds = item.roles?.map((role: RoleCombo) => role.roleId)
  editFormState.roleNames = item.roles?.map((role: RoleCombo) => role.roleName)
  editFormState.structureId = item.structureId
  editFormState.structureName = item.structureName
  editFormState.positionId = item.positionId
  editFormState.positionName = item.positionName
  editFormState.employeeMailNames = (item?.employeeMails ?? []).map((mail: EmployeeMail) => mail.email)
  if (editFormState.employeeMailNames.length === 0) {
    editFormState.employeeMailNames.push('')
  }
  editFormState.employeeAddress = item.employeeAddress
  editFormState.employeePhoneNames = (item?.employeePhones ?? []).map((phone: EmployeePhone) => phone.phone)
  if (editFormState.employeePhoneNames.length === 0) {
    editFormState.employeePhoneNames.push('')
  }
  editFormState.salaryInDay = item.salaryInDay
  editFormState.salaryInHour = item.salaryInHour
  editFormState.workingFromDate = item.workingFromDate
  editFormState.workingToDate = item.workingToDate
  editFormState.workingStatusName = item.workingStatusName
  editFormState.salaryInMonth = item.salaryInMonth
  editFormState.standardWorkingHours = item.standardWorkingHours
}

const invitedModalVisible = ref(false)

const openInvitedModal = () => {
  invitedModalVisible.value = true
  invitedFormState.employeeCode = ''
  invitedFormState.email = ''
}

const handleTableChange: any = (
  pag: { pageSize: number; current: number },
  _: any,
) => {
  searchForm.value = {
    pageNum: pag.current,
    pageSize: pag.pageSize,
    workingStatus: workingStatusChecked.value ? 'WORKING' : undefined,
  }
  run(searchForm.value)
  visible.value = false
}

const onSearch = () => {
  const filter: any = {
    ...searchForm.value,
    workingStatus: workingStatusChecked.value ? 'WORKING' : undefined,
  }

  if (filter.gender != null)
    filter.gender = filter.gender === 1

  if (!filter.status)
    delete filter.status
  handleTableChange(
    {
      pageSize: 10,
      current: 1,
    },
    filter,
  )
}

async function resetPass(id: string) {
  Modal.confirm({
    title: `${t('button.resetPass')}`,
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', {}, `${t('alert.confirmResetPass')}`),
    cancelText: `${t('button.cancel')}`,
    okText: `${t('button.ok')}`,
    async onOk() {
      const { status, code } = await resetPassword({ employeeId: id })
      if (status === 200) {
        messageNotify.success('成功')
      }
      else {
        messageNotify.error(t(code))
      }
    },
    onCancel() {},
    class: 'test',
  })
}

const deleteEmployee = (id: string) => {
  Modal.confirm({
    title: `${t('button.delete')}`,
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', {}, `${t('alert.confirmDelete', { msg: t('employee') })}`),
    cancelText: `${t('button.cancel')}`,
    okText: `${t('button.ok')}`,
    async onOk() {
      const { status, message } = await deleteEmployeeApi(id)
      if (status === 200) {
        messageNotify.success('成功')
        refresh()
      }
      else {
        messageNotify.error(message)
      }
    },
    onCancel() {},
  })
}

const employeeLogVisible = ref<boolean>(false)

const openEmployeeLogModal = async (employeeId: string) => {
  await getEmployeeLog(employeeId)
  if (employeeLogData.value.length === 0) {
    messageNotify.warning('無資料')
    return
  }
  employeeLogVisible.value = true
}

const createBaseLeave = async (employeeId: string) => {
  logger.log(employeeId)
  const params: BaseLeaveParams = {
    baseLeave: 0,
    baseLeaveExpire: '2030-01-01',
  }
  await createBaseLeaveApi(employeeId, params)
  messageNotify.success('成功')
}

const resetFilter = () => {
  searchForm.value = {
    pageNum: 1,
    pageSize: 20,
  }
  handleTableChange({
    pageSize: 20,
    current: 1,
  })
}

async function getEmployeeLog(id: string) {
  try {
    const params: EmployeeLogParams = {
      employeeId: id,
    }
    const { data, status, code } = await getEmployeeLogApi(params)
    if (status === 200) {
      logger.log('data: ', data)
      employeeLogData.value = data ?? []
    }
    else {
      logger.error('status: ', status, ' code: ', code)
    }
  }
  catch (e) {
    logger.error(e)
  }
}

const invitedSteps = reactive([
  {
    title: t('steps.employeeInfo'),
    content: 'Invite email',
  },
  {
    title: t('steps.complete'),
    content: 'Complete',
  },
])

const handleInvite = async () => {
  const params: InviteAccountParams = {
    email: invitedFormState.email ?? '',
    employeeCode: invitedFormState.employeeCode ?? '',
    roleIds: invitedFormState.roleIds ?? [],
    invitationDescription: invitedFormState.description,
  }
  const { status, message } = await inviteEmployeeApi(params)
  if (status === 200) {
    invitedCurrentSteps.value += 1
  }
  else {
    messageNotify.error(message)
  }
}

const editSteps = reactive([
  {
    title: t('steps.employeeInfoSetting'),
    content: 'employeeInfoSetting',
  },
  {
    title: t('steps.confirm'),
    content: 'confirm',
  },
  {
    title: t('steps.complete'),
    content: 'complete',
  },
])

const workingStatusData = ref<StatusItem[]>([])

const handleAddEmail = (emails: string[]) => {
  emails.push('')
}

const handleAddPhone = (phones: string[]) => {
  phones.push('')
}

function refreshEmployeeEditModal() {
  editFormState.birthday = ''
  editFormState.gender = false
  editFormState.employeeMailNames = []
  editFormState.employeePhoneNames = []
  editFormState.employeeAddress = ''
  editFormState.workingStatus = ''
  editFormState.roleIds = []
  editFormState.structureId = undefined
  editFormState.positionId = undefined
  editFormState.salaryInDay = 0
  editFormState.salaryInHour = 0
  editFormState.workingStatusName = undefined
  editFormState.workingFromDate = ''
  editFormState.workingToDate = ''
  editFormState.salaryInMonth = 0
  editFormState.standardWorkingHours = 0
  editFormState.employeeId = undefined
  editFormState.roleNames = []
  editFormState.structureName = ''
  editFormState.positionName = ''
}

const closeEditModal = () => {
  editModalVisible.value = false
  editCurrentSteps.value = 0
  editFormState.employeeId = undefined
  refreshEmployeeEditModal()
}

const handleEditSave = async () => {
  const employeeId = editFormState.employeeId
  if (!employeeId) {
    messageNotify.error(t('error.employeeIdRequired'))
    return
  }
  const params: EmployeeDetailsParams = {
    employeeMails: editFormState.employeeMailNames.map((mail: string) => ({ email: mail, isPrimary: false })),
    employeePhones: editFormState.employeePhoneNames.map((phone: string) => ({ phone, isPrimary: false })),
    employeeAddress: editFormState.employeeAddress,
    structureId: editFormState.structureId,
    positionId: editFormState.positionId,
    workingStatus: editFormState.workingStatus,
    roleIds: editFormState.roleIds,
    salaryInMonth: Number(editFormState.salaryInMonth) ?? 0,
    standardWorkingHours: Number(editFormState.standardWorkingHours) ?? 0,
    workingFromDate: editFormState?.workingFromDate,
    workingToDate: editFormState?.workingToDate,
  }
  const { status, message } = await updateEmployeeApi(employeeId, params)
  if (status === 200) {
    messageNotify.success(message)
    refresh()
    editCurrentSteps.value += 1
  }
  else {
    messageNotify.error(message)
  }
}

const handleBack = () => {
  if (editCurrentSteps.value >= 1) {
    editCurrentSteps.value -= 1
  }
}

const handleNext = () => {
  editFormRef.value
    .validate()
    .then(() => {
      editCurrentSteps.value += 1
    })
    .catch((error: any) => {
      logger.error(error)
    })
}

const handlePositionChange = (value: any) => {
  editFormState.positionId = value
  const index = positionData.value.findIndex((item: PositionItem) => item.positionId === value)
  if (index !== -1) {
    editFormState.positionName = positionData.value[index].positionName
  }
}

const handleStructureChange = (value: any) => {
  editFormState.structureId = value
  const index = structData.value.findIndex((item: any) => item.structureId === value)
  if (index !== -1) {
    editFormState.structureName = structData.value[index].structureName
  }
}

// const handleRankChange = (value: any) => {
//   editFormState.rankingId = value
//   const index = rankingData.value.findIndex((item: any) => item.id === value)
//   if (index !== -1) {
//     editFormState.rankingName = rankingData.value[index].name
//   }
// }

const handleWorkingStatusChange = (value: any) => {
  editFormState.workingStatus = value
  const index = workingStatusData.value.findIndex((item: any) => item.statusCode === value)
  if (index !== -1) {
    editFormState.workingStatusName = workingStatusData.value[index].statusName
  }
}
const handleInvitedModalContinue = () => {
  invitedCurrentSteps.value = 0
  invitedFormState.email = ''
  invitedFormState.employeeCode = ''
  invitedFormState.roleIds = []
}

const handleInvitedModalClose = () => {
  invitedModalVisible.value = false
  invitedFormState.email = ''
  invitedFormState.employeeCode = ''
  invitedFormState.roleIds = []
}

const invitedFormRules = {
  email: [{ required: true, message: t('form.emailRequired') }],
  employeeCode: [{ required: true, message: t('form.employeeCodeRequired') }],
  roleIds: [{ required: true, message: t('form.employeeRoleRequired') }],
}

const handleRoleChange = (value: any, _: any) => {
  editFormState.roleIds = value
  editFormState.roleNames = []
  editFormState.roleNames = roleData.value.reduce((acc: string[], role: Role) => {
    if (value.includes(role.roleId)) {
      acc.push(role.roleName)
    }
    return acc
  }, [])
}

const getWorkingStatusColor = (workingStatusName: string) => {
  switch (workingStatusName) {
    case 'WORKING':
      return 'green'
    case 'INVITED':
      return 'blue'
    case 'ONBOARDED':
      return 'orange'
    case 'EXTLEAVE':
      return 'red'
    default:
      return 'gray'
  }
}

onMounted(async () => {
  const defaultParams: QueryParams = {
    pageNum: 1,
    pageSize: 1000,
  }
  const promises = [
    fetchRoleList(),
    fetchStructureList(defaultParams),
    fetchPositionList(defaultParams),
    fetchWorkingStatusList(),
  ]
  const [
    roleList,
    structureList,
    positionList,
    workingStatusList,
  ] = await Promise.all(promises) as [
    Role[],
    StructureItem[],
    PositionItem[],
    StatusItem[],
  ]
  roleData.value = roleList ?? []
  structData.value = structureList ?? []
  positionData.value = positionList ?? []
  workingStatusData.value = workingStatusList ?? []
  setTimeout(() => {
  }, 280)
})

// const handleEmployeeTypeChange = (value: string) => {
//   editFormState.employeeType = value === 'true'
// }
</script>

<template>
  <page-container>
    <a-card :bordered="false">
      <template #title>
        <a-row style="font-weight: normal">
          <a-col :span="14">
            <a-space v-if="!isMobile">
              <a-input-search
                v-model:value="searchForm.keyword"
                :placeholder="t('input.placeholder')"
                style="width: 270px"
                @search="onSearch"
              />
              {{ t('workingStatus') }}
              <a-switch
                v-model:checked="workingStatusChecked"
                @change="onSearch()"
              />
            </a-space>
          </a-col>
          <a-col :span="10" class="flex flex-justify-end">
            <a-space>
              <a-popover
                v-model:open="visible"
                trigger="click"
                placement="bottomRight"
                :arrow="false"
                overlay-class-name="filter-popover"
              >
                <template #title>
                  <div
                    class="flex flex-items-center flex-justify-between p-2"
                  >
                    <span class="flex flex-items-center">
                      <CarbonFilterNew class="mr-2" />
                      {{ t("button.filter") }}
                    </span>
                    <a-button type="text" @click="visible = false">
                      <CloseOutlined />
                    </a-button>
                  </div>
                  <a-divider class="m-0" />
                </template>
                <template #content>
                  <a-form
                    ref="formFilterRef"
                    layout="vertical"
                    class="p-2"
                    :model="searchForm"
                  >
                    <a-row :gutter="12">
                      <a-col span="24">
                        <a-form-item
                          class="mb-2"
                          :label="$t('form.birthday')"
                        >
                          <a-date-picker
                            v-model:value="searchForm.dob"
                            class="w-full"
                            value-format="YYYY-MM-DD"
                          />
                        </a-form-item>
                      </a-col>
                      <a-divider class="mb-2 mt-0" />
                      <a-col span="12" class="px-2">
                        <a-form-item
                          :label="t('form.orgStructureName')"
                          class="mb-2"
                        >
                          <a-tree-select
                            v-model:value="searchForm.structureId"
                            allow-clear
                            :placeholder="t('form.orgStructureName')"
                            :tree-data="structData"
                            :field-names="{
                              children: 'children',
                              label: 'structureName',
                              value: 'structureId',
                            }"
                          />
                        </a-form-item>
                      </a-col>
                      <!-- <a-col span="12">
                        <a-form-item
                          :label="t('ranking')"
                          name="rankingId"
                          class="mb-2"
                        >
                          <a-select
                            v-model:value="searchForm.rankingId"
                            :options="rankingData"
                            :placeholder="t('ranking')"
                            :field-names="{ label: 'name', value: 'id' }"
                          />
                        </a-form-item>
                      </a-col> -->
                      <a-divider class="mb-2 mt-0" />
                      <a-col span="12">
                        <a-form-item
                          :label="t('form.gender')"
                          name="gender"
                          class="mb-2"
                        >
                          <a-select
                            v-model:value="searchForm.gender"
                            :options="gender"
                            :placeholder="t('form.gender')"
                          />
                        </a-form-item>
                      </a-col>
                      <a-col span="12">
                        <a-form-item
                          class="mb-2"
                          :label="t('form.orgPositionName')"
                          name="positionId"
                        >
                          <a-select
                            v-model:value="searchForm.positionId"
                            allow-clear
                            :placeholder="t('form.orgPositionName')"
                            :field-names="{
                              label: 'positionName',
                              value: 'positionId',
                            }"
                            :options="positionData"
                          />
                        </a-form-item>
                      </a-col>
                      <a-divider class="mb-2 mt-0" />
                      <a-col span="24" class="p-4 text-right">
                        <a-space>
                          <a-button @click="visible = false">
                            {{ $t("button.cancel") }}
                          </a-button>
                          <a-button type="primary" @click="onSearch()">
                            {{ $t("button.filter") }}
                          </a-button>
                        </a-space>
                      </a-col>
                    </a-row>
                  </a-form>
                </template>
                <a-button class="flex flex-items-center">
                  <CarbonFilterNew size="16" class="mr-1" />
                  {{ t("button.filter") }} <DownOutlined />
                </a-button>
              </a-popover>
              <a-button @click="resetFilter()">
                {{ t("button.resetFilter") }}
              </a-button>

              <a-button class="flex items-center" type="primary" @click="openInvitedModal()">
                <PlusOutlined /> {{ t("button.invite") }}
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </template>
      <a-table
        :columns="columns"
        :row-key="(record: any) => record.employeeId"
        :data-source="dataSource?.items"
        :loading="loading"
        :scroll="{ x: 'max-content', y: 'calc(100vh - 327px)' }"
        :pagination="{
          pageSize,
          current,
          total,
        }"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'employeeName'">
            <div class="flex flex-items-center">
              <a-avatar :size="48" :src="avatarStore.getImageSrcByEmployeeId(record.employeeId) ?? ''" class="shadow-lg" />
              <div class="ml-2">
                <strong>{{ record.employeeName }}</strong>
                <br>
                <a-typography-text type="secondary">
                  {{ record.positionName }}
                </a-typography-text>
              </div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'strucRank'">
            <strong>{{ record.structureName }}</strong>
            <br>
            <!-- <a-typography-text type="secondary">
              {{ record.rankingName }}
            </a-typography-text> -->
          </template>
          <template v-if="column.dataIndex === 'contact'">
            <div class="flex flex-col gap-2">
              <div
                v-for="mail in record.employeeMails"
                :key="mail.email"
                class="flex gap-x-2 items-center"
              >
                <CarbonEmail />
                <span>
                  {{ mail.email }}
                </span>
              </div>
              <div class="flex flex-items-center">
                <CarbonPhone />
                <span v-for="phone in record.employeePhones" :key="phone.phone" class="ml-2">
                  {{ phone.phone }}
                </span>
              </div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'workingStatus'">
            <a-tag :color="getWorkingStatusColor(record.workingStatus)">
              {{ record.workingStatusName }}
            </a-tag>
          </template>

          <!-- <template v-if="column.dataIndex === 'gender'">
            <template v-if="record.gender === true">
              {{ t('male') }}
            </template>
            <template v-else>
              {{ t('female') }}
            </template>
          </template> -->
          <!-- <template v-if="column.dataIndex === 'isWorkers'">
            <a-typography-text
              v-if="record.isWorker"
              strong
              style="color: #dc6000"
            >
              {{ t('construction') }}
            </a-typography-text>
            <a-typography-text v-else strong style="color: #256cb5">
              {{ t("office") }}
            </a-typography-text>
          </template> -->
          <template v-if="column.dataIndex === 'actions'">
            <a-dropdown :trigger="['click']">
              <a class="ant-dropdown-link" @click.prevent>
                <MoreOutlined />
              </a>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="edit" @click="openEditModal(record as EmployeeItem)">
                    <EditOutlined /> {{ t("button.edit") }}
                  </a-menu-item>
                  <a-menu-item
                    key="reset"
                    @click="resetPass(record.employeeId)"
                  >
                    <RedoOutlined /> {{ t("button.resetPass") }}
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item
                    key="delete"
                    style="color: red"
                    @click="deleteEmployee(record.employeeId)"
                  >
                    <DeleteOutlined /> {{ t("button.delete") }}
                  </a-menu-item>
                  <a-menu-item
                    key="viewLog"
                    style="color: green"
                    @click="openEmployeeLogModal(record.employeeId)"
                  >
                    <FolderViewOutlined /> {{ t("button.view") }}
                  </a-menu-item>
                  <a-menu-item
                    key="createBaseLeave"
                    style="color: orange"
                    @click="createBaseLeave(record.employeeId)"
                  >
                    {{ t("button.createDefaultBaseLeave") }}
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>
        </template>
      </a-table>
    </a-card>

    <a-modal v-model:open="employeeLogVisible">
      <a-timeline>
        <template v-for="item in employeeLogData" :key="item">
          <a-timeline-item>
            <template #dot>
              <ClockCircleOutlined style="font-size: 16px" />
            </template>
            {{ dayjs(item.modifiedTime).format("YYYY-MM-DD HH:mm:ss") }}
          </a-timeline-item>
          <template v-for="change in item.changedList" :key="change">
            <a-timeline-item>
              {{
                `Update ${change.fieldName} from ${change.valueBefore} -> ${change.valueAfter} by ${item.modifiedEmployeeName}`
              }}
            </a-timeline-item>
          </template>
        </template>
      </a-timeline>
      <template #footer>
        <a-button type="primary" @click="employeeLogVisible = false">
          {{
            t("button.cancel")
          }}
        </a-button>
      </template>
    </a-modal>

    <a-modal
      v-model:open="invitedModalVisible"
      :ok-text="t('button.next')"
      :cancel-text="t('button.previous')"
      :title="t('title.invite-employee')"
      @ok="handleInvitedModalContinue"
      @cancel="handleInvitedModalClose"
    >
      <a-flex vertical gap="24">
        <a-steps :current="invitedCurrentSteps" :items="invitedSteps" />
        <div v-if="invitedCurrentSteps === 0">
          <a-form
            ref="emailFormRef"
            :model="invitedFormState"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 14 }"
            :rules="invitedFormRules"
          >
            <a-form-item name="email" :label="t('form.email')">
              <a-input
                v-model:value="invitedFormState.email"
                :placeholder="t('form.email')"
              />
            </a-form-item>
            <a-form-item
              name="employeeCode"
              :label="t('form.employeeCode')"
              :rules="[
                { required: true, message: t('form.employeeCode') },
              ]"
            >
              <a-input
                v-model:value="invitedFormState.employeeCode"
                :placeholder="t('form.employeeCode')"
              />
            </a-form-item>
            <a-form-item name="roleIds" :label="t('form.employeeRole')">
              <a-select
                v-model:value="invitedFormState.roleIds"
                mode="multiple"
                :options="roleData"
                :field-names="{ label: 'roleName', value: 'roleId' }"
                :placeholder="t('form.employeeRole')"
              />
            </a-form-item>
            <a-form-item name="description" :label="t('form.description')">
              <a-textarea
                v-model:value="invitedFormState.description"
                :placeholder="t('form.description')"
              />
            </a-form-item>
            <a-form-item :wrapper-col="{ offset: 6 }">
              <a-button type="primary" @click="handleInvite">
                {{ t("button.invite") }}
              </a-button>
            </a-form-item>
          </a-form>
        </div>
        <div v-if="invitedCurrentSteps === 1">
          <a-result
            status="success"
            :title="t('success.invite-employee')"
            :sub-title="t('success.invite-employee-sub-title')"
          >
            <template #extra>
              <a-button @click="handleInvitedModalClose">
                {{ t("button.close") }}
              </a-button>
              <a-button type="primary" @click="handleInvitedModalContinue">
                {{ t("button.continue") }}
              </a-button>
            </template>
          </a-result>
        </div>
      </a-flex>
      <template #footer />
    </a-modal>

    <a-modal
      v-model:open="editModalVisible"
      width="60%"
      @cancel="closeEditModal"
    >
      <template #title>
        <div class="flex justify-center items-center">
          <a-typography-title :level="3">
            {{ t('title.edit-employee') }}
          </a-typography-title>
        </div>
      </template>
      <a-steps :current="editCurrentSteps" :items="editSteps" class="p-4 mb-10" />

      <a-form
        v-if="editCurrentSteps === 0"
        ref="editFormRef"
        :model="editFormState"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 14 }"
      >
        <a-form-item
          name="employeeMails"
          :label="t('form.employeeMails')"
        >
          <div class="flex flex-col gap-y-2">
            <div class="flex flex-col gap-y-2">
              <a-input
                v-for="(_, index) in editFormState.employeeMailNames"
                :key="index"
                v-model:value="editFormState.employeeMailNames[index]"
                placeholder=""
              />
              <a-button
                type="default"
                class="flex items-center justify-center"
                @click="handleAddEmail(editFormState.employeeMailNames)"
              >
                <PlusOutlined />
              </a-button>
            </div>
          </div>
        </a-form-item>
        <a-form-item
          name="employeePhones"
          :label="t('form.employeePhones')"
        >
          <div class="flex flex-col gap-y-2">
            <a-input
              v-for="(_, index) in editFormState.employeePhoneNames"
              :key="index"
              v-model:value="editFormState.employeePhoneNames[index]"
            />
            <a-button
              type="default" class="flex items-center justify-center"
              @click="handleAddPhone(editFormState.employeePhoneNames)"
            >
              <PlusOutlined />
            </a-button>
          </div>
        </a-form-item>
        <a-form-item
          name="employeeAddress"
          :label="t('form.employeeAddress')"
        >
          <a-input v-model:value="editFormState.employeeAddress" />
        </a-form-item>
        <a-form-item
          name="structureId"
          :label="t('form.orgStructureName')"
        >
          <a-tree-select
            v-model:value="editFormState.structureId"
            :tree-data="structData"
            :field-names="{ label: 'structureName', value: 'structureId', children: 'children' }"
            @change="handleStructureChange"
          />
        </a-form-item>
        <a-form-item
          name="positionId"
          :label="t('form.orgPositionName')"
        >
          <a-select
            v-model:value="editFormState.positionId"
            :options="positionData"
            :field-names="{ label: 'positionName', value: 'positionId' }"
            @change="handlePositionChange"
          />
        </a-form-item>
        <!-- <a-form-item
          name="employeeType"
          :label="t('form.isEmployee')"
        >
          <a-switch
            v-model:checked="editFormState.employeeType"
          />
        </a-form-item> -->
        <a-form-item
          name="workingStatus"
          :label="t('form.workingStatus')"
          :rules="[{ required: true, message: t('form.workingStatus') }]"
        >
          <a-select
            v-model:value="editFormState.workingStatus"
            :options="workingStatusData"
            :field-names="{ label: 'statusName', value: 'statusCode' }"
            @change="handleWorkingStatusChange"
          />
        </a-form-item>
        <!-- <a-form-item
          name="rankingId"
          :label="t('form.rankingName')"
        >
          <a-select
            v-model:value="editFormState.rankingId"
            :options="rankingData"
            :field-names="{ label: 'rankingName', value: 'rankingId' }"
            @change="handleRankChange"
          />
        </a-form-item> -->
        <!-- <a-form-item
          name="rankingStartDate"
          :label="t('form.rankingStartDate')"
        >
          <a-date-picker
            v-model:value="editFormState.workingFromDate"
            value-format="YYYY-MM-DD"
          />
        </a-form-item> -->
        <!-- <a-form-item
          name="rankingEndDate"
          :label="t('form.rankingEndDate')"
        >
          <a-date-picker
            v-model:value="editFormState.rankingEndDate"
            value-format="YYYY-MM-DD"
          />
        </a-form-item> -->
        <a-form-item
          name="roleIds"
          :label="t('form.employeeRole')"
        >
          <a-select
            v-model:value="editFormState.roleIds"
            mode="multiple"
            :options="roleData"
            :field-names="{ label: 'roleName', value: 'roleId' }"
            @change="handleRoleChange"
          />
        </a-form-item>
        <a-form-item
          name="workingFromDate"
          :label="t('form.workingFromDate')"
        >
          <a-date-picker
            v-model:value="editFormState.workingFromDate"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
          />
        </a-form-item>
        <a-form-item
          name="workingToDate"
          :label="t('form.workingToDate')"
        >
          <a-date-picker
            v-model:value="editFormState.workingToDate"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
          />
        </a-form-item>
        <!-- <a-form-item
          name="salaryInHour"
          :label="t('form.salaryInHour')"
        >
          <a-input v-model:value="editFormState.salaryInHour" type="number" />
        </a-form-item>
        <a-form-item
          name="salaryInDay"
          :label="t('form.salaryInDay')"
        >
          <a-input v-model:value="editFormState.salaryInDay" type="number" />
        </a-form-item> -->
        <a-form-item
          name="salaryInMonth"
          :label="t('form.salaryInMonth')"
        >
          <a-input v-model:value="editFormState.salaryInMonth" type="number" />
        </a-form-item>
        <a-form-item
          name="standardWorkingHours"
          :label="t('form.standardWorkingHours')"
        >
          <a-input v-model:value="editFormState.standardWorkingHours" type="number" />
        </a-form-item>
      </a-form>

      <div v-if="editCurrentSteps === 1" class="flex flex-col gap-y-8 mb-10 justify-center">
        <a-descriptions :title="t('employeeInfo')">
          <a-descriptions-item :label="t('form.employeeMails')">
            {{ editFormState.employeeMailNames.join(', ') }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('form.employeePhones')">
            {{ editFormState.employeePhoneNames.join(', ') }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('form.employeeAddress')">
            {{ editFormState.employeeAddress }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('form.employeeType')">
            {{ editFormState.employeeType ? t('type.employee') : t('type.outsource') }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('form.employeeRole')">
            {{ editFormState.roleNames.join(', ') }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('form.positionName')">
            {{ editFormState.positionName }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('form.workingStatus')" :span="3">
            {{ editFormState.workingStatusName }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('form.orgStructureName')">
            {{ editFormState.structureName }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('form.workingFromDate')">
            {{ editFormState.workingFromDate }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('form.workingToDate')">
            {{ editFormState.workingToDate }}
          </a-descriptions-item>
          <!-- <a-descriptions-item :label="t('form.salaryInHour')">
            {{ editFormState.salaryInHour }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('form.salaryInDay')">
            {{ editFormState.salaryInDay }}
          </a-descriptions-item> -->
          <a-descriptions-item :label="t('form.salaryInMonth')">
            {{ editFormState.salaryInMonth }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('form.standardWorkingHours')">
            {{ editFormState.standardWorkingHours }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
      <div v-if="editCurrentSteps === 2">
        <a-result
          status="success"
          :title="t('result.success.title')"
        />
      </div>
      <template #footer>
        <div class="flex justify-center">
          <a-button v-if="editCurrentSteps < 2" @click="handleBack">
            {{ t('button.back') }}
          </a-button>
          <a-button v-if="editCurrentSteps < 1" type="primary" @click="handleNext">
            {{ t('button.next') }}
          </a-button>
          <a-button v-if="editCurrentSteps === 1" type="primary" @click="handleEditSave">
            {{ t('button.save') }}
          </a-button>
        </div>
      </template>
    </a-modal>
  </page-container>
</template>

<style lang="less">
.custom-layout {
  > div {
    gap: 12px;
    .ant-col {
      flex: 0 0 auto;
      padding-bottom: 0;
    }
  }
}
.employee-modal {
  .ant-modal-header {
    text-align: center;
    .ant-modal-title {
      font-size: 20px;
      color: #1c4771;
    }
  }
}
// .filter-popover {
//   .ant-popover-inner {
//     padding: 0;
//   }
// }
</style>
