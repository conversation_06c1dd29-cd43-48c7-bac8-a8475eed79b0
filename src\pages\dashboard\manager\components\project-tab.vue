// types.ts

// ProjectCard.vue
<script setup lang="ts">
import { ref } from 'vue'
import { usePagination } from 'vue-request'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash'
import ProjectSummaryItemComponent from './project-summary-item.vue'
import type { ProjectSummaryItem } from '~@/api/company/project'
import { getProjectSummaryApi } from '~@/api/company/project'
import type { DateRangeParams, QueryParams } from '~@/api/common-params'

const props = defineProps({
  searchDate: {
    type: Object as () => dayjs.Dayjs,
    required: true,
  },
  projectId: {
    type: String,
    required: false,
    default: '',
  },
  isShowProductionCost: {
    type: Boolean,
    required: false,
    default: false,
  },
})

type Params = QueryParams & Partial<DateRangeParams> & {
  projectId?: string
}

const initSearchForm: Params = {
  pageSize: 100,
  pageNum: 1,
  dateFrom: dayjs().format('YYYY-MM-DD'),
  dateTo: dayjs().format('YYYY-MM-DD'),
}

const { t } = useI18n()
const searchForm = ref<Params>({ ...cloneDeep(initSearchForm) })
const isLoading = ref(false)
const noMore = ref(false)

async function queryData(params?: Params) {
  isLoading.value = true
  const { data } = await getProjectSummaryApi(params as Params)
  if (!data)
    return ref<ProjectSummaryItem[]>([]).value
  if (data.items.length < (searchForm.value.pageSize ?? 0))
    noMore.value = true
  isLoading.value = false
  return ref(data.items).value
}

const { data: projectSummary } = usePagination(queryData, {
  defaultParams: [searchForm.value],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'total',
  },
})

// async function handleLoad() {
//   if (noMore.value)
//     return

//   searchForm.value.pageNum = (searchForm.value.pageNum ?? 1) + 1
//   const { data } = await getProjectSummaryApi(searchForm.value)
//   if (!data) {
//     noMore.value = true
//     return
//   }
//   projectSummary.value?.push(...(data?.items ?? []))

//   if (data.items.length < (searchForm.value.pageSize ?? 0))
//     noMore.value = true
// }

watch(() => props.searchDate, async (newDate) => {
  isLoading.value = true
  searchForm.value.dateFrom = newDate.format('YYYY-MM-DD')
  searchForm.value.dateTo = newDate.format('YYYY-MM-DD')
  searchForm.value.pageNum = 1
  noMore.value = false
  projectSummary.value = await queryData(searchForm.value)
  isLoading.value = false
})

watch(() => t('locale'), async () => {
  searchForm.value.pageNum = 1
  noMore.value = false
  isLoading.value = true
  projectSummary.value = await queryData(searchForm.value)
  isLoading.value = false
})

watch(() => props.projectId, async (newProjectId) => {
  isLoading.value = true
  searchForm.value.pageNum = 1
  searchForm.value.projectId = newProjectId || undefined
  noMore.value = false
  projectSummary.value = await queryData(searchForm.value)
  isLoading.value = false
})
</script>

<template>
  <a-spin :spinning="isLoading">
    <div class="overflow-y-auto h-full flex flex-col items-center justify-center bg-gray-100">
      <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 3xl:grid-cols-4 4xl:grid-cols-5 5xl:grid-cols-6 gap-x-4 gap-y-4 p-2">
        <div v-for="item in projectSummary" :key="item.projectId">
          <ProjectSummaryItemComponent
            :project-summary-item="item"
            :search-date="searchDate"
            :is-show-production-cost="isShowProductionCost"
          />
        </div>
      </div>
      <!-- <div v-if="!noMore" class="w-full flex justify-center mt-4">
        <a-spin />
      </div> -->
    </div>
  </a-spin>
</template>
