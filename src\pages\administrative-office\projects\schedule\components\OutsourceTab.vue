<script setup lang="ts">
import type { OutSourceItem, OutsourceQueryParams } from '~@/api/outsource'
import { fetchOutsourceListApi } from '~@/api/outsource'
import { useWindowScrollSize } from '@/composables/use-window-scroll-size'

const emits = defineEmits<{
  (event: 'dragOutsourceStart', evt: DragEvent, outsource: OutSourceItem): void
  (event: 'dragOutsourceEnd', evt: DragEvent): void
}>()

const { t } = useI18n()
const { height } = useWindowScrollSize()

const params = reactive<OutsourceQueryParams>({
  pageNum: 1,
  pageSize: 7,
})

const searchKey = ref<string>('')

// const {
//   data,
//   loading,
//   run,
// } = usePagination(fetchOutsourceListApi, {
//   defaultParams: [params],
//   pagination: {
//     currentKey: 'pageIndex',
//     pageSizeKey: 'pageSize',
//     totalKey: 'totalRecords',
//   },
// })

const outsourceData = ref<OutSourceItem[]>([])
const loading = ref(false)

function dragOutsourceStart(evt: DragEvent, outsource: OutSourceItem) {
  emits('dragOutsourceStart', evt, outsource)
}

function dragOutsourceEnd(evt: DragEvent) {
  emits('dragOutsourceEnd', evt)
}

async function onSearch() {
  if (searchKey.value === '') {
    params.keyword = undefined
    params.pageNum = 1
    params.pageSize = 8
    const res = await fetchOutsourceListApi(params)
    outsourceData.value = res?.items ?? []
  }
  else {
    params.keyword = searchKey.value
    params.pageNum = 1
    params.pageSize = 8
    const res = await fetchOutsourceListApi(params)
    outsourceData.value = res?.items ?? []
  }
}

onMounted(async () => {
  const data = await fetchOutsourceListApi(params)
  outsourceData.value = data?.items ?? []
})
</script>

<template>
  <div class="flex-none h-10">
    <a-input v-model:value="searchKey" :placeholder="t('search')" @press-enter="onSearch" />
  </div>
  <div class="overflow-y-auto" :style="{ height: `${height - 300}px` }">
    <div class="flex flex-col flex-1 gap-y-4 snap-y snap-mandatory">
      <div
        v-for="outsource in outsourceData" :key="outsource.outSourceId"
        class="flex gap-x-2 rounded-lg border-1 border-gray-300 border-solid p-2 bg-white snap-start"
      >
        <div
          class="cursor-move"
          :draggable="true"
          @dragstart="dragOutsourceStart($event, outsource)"
          @dragend="dragOutsourceEnd($event)"
        >
          <a-avatar
            :size="50"
          />
        </div>
        <div>
          <span class="font-bold">{{ outsource.outSourceName }}</span>
          <p class="text-[#74797A]">
            {{ t('code') }}: {{ outsource.outSourceCode }}
          </p>
          <!-- <span class="flex gap-x-2 items-center text-[#74797A]"><CarbonObservation />{{ outsource.description }}</span> -->
        </div>
      </div>
      <div
        v-if="loading"
        class="flex justify-center items-center"
      >
        <a-spin />
      </div>
    </div>
  </div>
</template>
