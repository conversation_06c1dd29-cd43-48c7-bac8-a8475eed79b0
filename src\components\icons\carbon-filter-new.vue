<script lang="ts" setup name="CarbonFilterNew">
const props = defineProps({
  size: {
    type: String,
    default: "18",
  },
});
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    :width="props.size"
    :height="props.size"
    viewBox="0 0 18 18"
  >
    <path
      d="M10.6378 18C10.4607 18 10.2884 17.9474 10.1467 17.85L6.87291 15.6C6.77126 15.5301 6.68876 15.4396 6.63194 15.3354C6.57512 15.2313 6.54553 15.1164 6.54553 15V10.785L0.803334 4.86525C0.39552 4.44366 0.129247 3.92311 0.0365182 3.36618C-0.0562109 2.80924 0.02855 2.23961 0.280613 1.72576C0.532677 1.21191 0.941317 0.775694 1.45742 0.469549C1.97352 0.163405 2.57512 0.000355814 3.18992 0H14.8118C15.4266 0.000660788 16.028 0.163979 16.5439 0.47033C17.0598 0.776681 17.4682 1.21303 17.72 1.72694C17.9718 2.24085 18.0563 2.81046 17.9633 3.36731C17.8703 3.92416 17.6039 4.44457 17.1959 4.866L11.4562 10.785V17.25C11.4562 17.4489 11.37 17.6397 11.2165 17.7803C11.063 17.921 10.8548 18 10.6378 18ZM8.18242 14.625L9.81931 15.75V10.5C9.81947 10.3164 9.89315 10.1392 10.0264 10.002L15.9748 3.86925C16.1732 3.66381 16.3026 3.41028 16.3476 3.13909C16.3925 2.8679 16.3511 2.59058 16.2283 2.34042C16.1056 2.09026 15.9066 1.87789 15.6553 1.72882C15.404 1.57974 15.1111 1.50029 14.8118 1.5H3.18992C2.89073 1.50042 2.59803 1.57991 2.34692 1.72895C2.09581 1.87799 1.89694 2.09025 1.77418 2.34026C1.65141 2.59028 1.60996 2.86744 1.65478 3.13851C1.69961 3.40958 1.82882 3.66304 2.02691 3.8685L7.97617 10.002C8.1091 10.1393 8.18248 10.3165 8.18242 10.5V14.625Z"
      fill="#74797A"
    />
  </svg>
</template>
