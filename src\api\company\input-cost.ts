// Define the FormInputCostState interface locally since the source file was deleted
export interface FormInputCostState {
  inputCostId?: string
  originalDocumentNumber?: string
  originalDocumentDate: string
  paymentDate?: string
  processTypeId?: string
  entryTypeId?: string
  providerId?: string
  paymentTypeId: string
  description?: string
  projectCostId?: string
  inputCostItems: InputCostItems[]
}

export interface InputCostItemResponse {
  items: InputCostItem[]
  pageIndex: number
  pageSize: number
}

interface InputCostItems {
  transactionDate: string
  inputCostId?: string
  itemId?: string
  unitId?: string
  quantity: number
  price: number
  total: number
  categoryId?: string
  manufacturerId?: string
  vatTax: number
  totalTaxed: number
  description: string
}

export interface InputCostItem {
  inputCostId?: string
  originalDocumentNumber?: string
  originalDocumentDate: string
  paymentDate?: string
  processTypeId?: string
  entryTypeId?: string
  providerId?: string
  paymentTypeId: string
  description?: string
  projectCostId?: string
  inputCostItems: InputCostItems[]
}

export async function getInputCostItem(params: any) {
  return useGet<InputCostItemResponse>('v1/cost/inputcost', params)
}

export async function getOneInputCostItem(id: string, params?: any) {
  return useGet<InputCostItem>(`v1/cost/inputcost/${id}`, params)
}

export async function createInputCostItem(data: FormInputCostState) {
  return usePost<InputCostItem>('v1/cost/inputcost', data)
}

export async function updateInputCostItem(id: string, data: FormInputCostState) {
  return usePut<InputCostItem>(`v1/cost/inputcost/${id}`, data)
}

export function fetchInputCostImage(url: string, orgId: string) {
  const host = import.meta.env.VITE_APP_BASE_API ?? ''
  return `${host}/v1/cost/inputcost/images?imageUrl=${url}&orgId=${orgId}`
}
