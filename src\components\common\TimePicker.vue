<script setup lang="ts">
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import lodash from 'lodash';
import { ref, computed, watch } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import { parseFormat } from '~@/utils/parseformat';

enum ValueType {
  STRING = 'string',
  DAYJS = 'dayjs',
}

enum DayPeriodEnum {
  AM = 'am',
  PM = 'pm',
}

enum TimeTypeEnum {
  WHEEL = 'wheel',
  CLOCK = 'clock',
  MANUAL = 'manual',
}

const props = defineProps({
  value: { type: Object as () => Dayjs | string },
  placeholder: { type: String },
  class: { type: String },
  allowClear: { type: Boolean },
  minuteStep: { type: Number, default: 1 },
  valueType: {
    default: ValueType.DAYJS,
    type: String as () => 'string' | 'dayjs',
  },
  valueFormat: {
    type: String,
    default: 'HH:mm:ss',
  },
  disabled: { type: Boolean, default: false },
});

const emit = defineEmits(['update:value', 'change']);

const hourRef = ref();
const minuteRef = ref();
const dayPeriodRef = ref();
const timeType = ref(TimeTypeEnum.WHEEL);
const hours = Array.from({ length: 12 }, (_, i) => i + 1);
const minutes = Array.from({ length: 60 }, (_, i) => i);
const dayPeriods = [DayPeriodEnum.AM, DayPeriodEnum.PM];

const dayPeriod = computed(() => {
  if (!props.value) return DayPeriodEnum.AM;

  if (dayjs.isDayjs(props.value)) {
    return props.value.hour() < 12 ? DayPeriodEnum.AM : DayPeriodEnum.PM;
  }

  const format = parseFormat(props.value);
  const hour = dayjs(props.value, format).hour();
  return hour < 12 ? DayPeriodEnum.AM : DayPeriodEnum.PM;
});

const updateValue = (value?: string | Dayjs) => {
  emit('update:value', value);
  emit('change', value);
};

const handleChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (!input.value) updateValue(undefined);
};

const clickHour = (hour: number) => {
  if (lodash.isEmpty(props.value)) {
    if (props.valueType === ValueType.STRING) {
      const newTime = dayjs().hour(hour).format(props.valueFormat);
      updateValue(newTime);
    } else {
      const newTime = dayjs().hour(hour);
      updateValue(newTime);
    }
  }

  if (dayjs.isDayjs(props.value)) {
    let newHour = hour;
    if (dayPeriod.value === DayPeriodEnum.AM) {
      newHour = hour === 12 ? 0 : hour;
    } else {
      newHour = hour === 12 ? 12 : hour + 12;
    }
    const newTime = props.value.hour(newHour);
    updateValue(newTime);
  }

  if (!lodash.isEmpty(props.value) && lodash.isString(props.value)) {
    const format = parseFormat(props.value);
    let newHour = hour;
    if (dayPeriod.value === DayPeriodEnum.AM) {
      newHour = hour === 12 ? 0 : hour;
    } else {
      newHour = hour === 12 ? 12 : hour + 12;
    }
    const newTime = dayjs(props.value, format).hour(newHour);
    updateValue(newTime.format(format));
  }

  scrollIntoViewHour();
  scrollIntoViewMinute();
  scrollIntoViewDayPeriod();
};

const clickMinute = (minute: number) => {
  if (lodash.isEmpty(props.value)) {
    if (props.valueType === ValueType.STRING) {
      const newTime = dayjs().minute(minute).format(props.valueFormat);
      updateValue(newTime);
    } else {
      const newTime = dayjs().minute(minute);
      updateValue(newTime);
    }
  }

  if (dayjs.isDayjs(props.value)) {
    const newTime = props.value.minute(minute);
    updateValue(newTime);
  }

  if (!lodash.isEmpty(props.value) && lodash.isString(props.value)) {
    const format = parseFormat(props.value);
    const newTime = dayjs(props.value, format).minute(minute);
    updateValue(newTime.format(format));
  }

  scrollIntoViewHour();
  scrollIntoViewMinute();
  scrollIntoViewDayPeriod();
};

const clickDayPeriod = (type: DayPeriodEnum) => {
  if (lodash.isEmpty(props.value)) {
    if (props.valueType === ValueType.STRING) {
      const newTime = dayjs().hour(0).format(props.valueFormat);
      updateValue(newTime);
    } else {
      const newTime = dayjs().hour(0);
      updateValue(newTime);
    }
  }

  if (dayjs.isDayjs(props.value)) {
    let hour = props.value.hour();
    if (type === DayPeriodEnum.AM && hour >= 12) {
      hour -= 12;
    } else if (type === DayPeriodEnum.PM && hour < 12) {
      hour += 12;
    }
    const newTime = props.value.hour(hour);
    updateValue(newTime);
  }

  if (!lodash.isEmpty(props.value) && lodash.isString(props.value)) {
    const format = parseFormat(props.value);
    let hour = dayjs(props.value, format).hour();
    if (type === DayPeriodEnum.AM && hour >= 12) {
      hour -= 12;
    } else if (type === DayPeriodEnum.PM && hour < 12) {
      hour += 12;
    }
    const newTime = dayjs(props.value, format).hour(hour);
    updateValue(newTime.format(format));
  }

  scrollIntoViewHour();
  scrollIntoViewMinute();
  scrollIntoViewDayPeriod();
};

const getValueTime = computed(() => {
  return (time?: string | dayjs.Dayjs) => {
    if (!time) return '';
    if (dayjs.isDayjs(time)) return time.format('HH:mm');

    const format = parseFormat(time);
    return dayjs(time, format).format('HH:mm');
  };
});

const getHour = computed(() => {
  if (!props.value) return 0;
  if (dayjs.isDayjs(props.value)) return props.value.format('hh');
  const format = parseFormat(props.value);
  return dayjs(props.value, format).format('hh');
});

const getMinute = computed(() => {
  if (!props.value) return 0;
  if (dayjs.isDayjs(props.value)) return props.value.format('mm');
  const format = parseFormat(props.value);
  return dayjs(props.value, format).format('mm');
});

const isActiveHour = computed(() => {
  return (index: number) => {
    if (!props.value) return false;

    let hour = 0;
    if (dayjs.isDayjs(props.value)) {
      hour = props.value.hour();
    } else {
      const format = parseFormat(props.value);
      hour = dayjs(props.value, format).hour();
    }

    const hour12 = hour % 12 === 0 ? 12 : hour % 12;
    return index === hour12;
  };
});

const isActiveMinute = computed(() => {
  return (index: number) => {
    if (!props.value) return false;

    let minute = 0;
    if (dayjs.isDayjs(props.value)) {
      minute = props.value.minute();
    } else {
      const format = parseFormat(props.value);
      minute = dayjs(props.value, format).minute();
    }

    return minute === index;
  };
});

const isActiveDayPeriod = computed(() => {
  return (type: DayPeriodEnum) => {
    if (!props.value) return false;

    switch (type) {
      case DayPeriodEnum.AM:
        if (dayjs.isDayjs(props.value)) return props.value.hour() < 12;
        return dayjs(props.value, parseFormat(props.value)).hour() < 12;
      case DayPeriodEnum.PM:
        if (dayjs.isDayjs(props.value)) return props.value.hour() >= 12;
        return dayjs(props.value, parseFormat(props.value)).hour() >= 12;
      default:
        return false;
    }
  };
});

const disabledMinutes = () => {
  return Array.from({ length: 60 })
    .map((_, index) => index)
    .filter((i) => {
      return i % props.minuteStep !== 0;
    });
};

const handleTimeTypeChange = (type: TimeTypeEnum) => {
  timeType.value = type;
  if (type === TimeTypeEnum.WHEEL) {
    scrollIntoViewHour();
    scrollIntoViewMinute();
    scrollIntoViewDayPeriod();
  }
};

const openChange = () => {
  scrollIntoViewHour();
  scrollIntoViewMinute();
  scrollIntoViewDayPeriod();
  timeType.value = TimeTypeEnum.WHEEL;
};

const scrollIntoViewHour = useDebounceFn(() => {
  if (!props.value) return;

  let hour = 0;
  if (dayjs.isDayjs(props.value)) {
    hour = props.value.hour();
  } else {
    const format = parseFormat(props.value);
    hour = dayjs(props.value, format).hour();
  }

  const hour12 = hour % 12 === 0 ? 12 : hour % 12;
  const index = hours.indexOf(hour12);
  hourRef.value?.[index]?.scrollIntoView({
    behavior: 'smooth',
    block: 'start',
  });
}, 100);

watch(
  () => hourRef.value,
  () => scrollIntoViewHour()
);

const scrollIntoViewMinute = useDebounceFn(() => {
  if (!props.value) return;

  let index = 0;
  if (dayjs.isDayjs(props.value)) {
    index = props.value.minute();
  } else {
    const format = parseFormat(props.value);
    index = dayjs(props.value, format).minute();
  }
  minuteRef.value?.[index]?.scrollIntoView({
    behavior: 'smooth',
    block: 'start',
  });
}, 100);

watch(
  () => minuteRef.value,
  () => scrollIntoViewMinute()
);

const scrollIntoViewDayPeriod = useDebounceFn(() => {
  if (!props.value) return;

  const index = dayPeriod.value === DayPeriodEnum.PM ? 1 : 0;
  dayPeriodRef.value?.[index]?.scrollIntoView({
    behavior: 'smooth',
    block: 'start',
  });
}, 100);

watch(
  () => dayPeriodRef.value,
  () => scrollIntoViewDayPeriod()
);

const isDragging = ref(false);
const isDraggingHour = ref(true);
const dragStartX = ref(0);
const dragStartY = ref(0);
const clockRadius = 120;
const clockCenter = { x: 120, y: 120 };
const minuteMarkers = [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55];

const hourAngle = computed(() => {
  const hour = getCurrentHour();
  return (hour % 12) * 30 - 180;
});

const minuteAngle = computed(() => {
  return getCurrentMinute() * 6 - 180;
});

function getCurrentHour(): number {
  if (!props.value) return 12;

  if (dayjs.isDayjs(props.value)) {
    return props.value.hour() % 12 || 12;
  } else {
    const format = parseFormat(props.value);
    return dayjs(props.value, format).hour() % 12 || 12;
  }
}

function getCurrentMinute(): number {
  if (!props.value) return 0;

  if (dayjs.isDayjs(props.value)) {
    return props.value.minute();
  } else {
    const format = parseFormat(props.value);
    return dayjs(props.value, format).minute();
  }
}

function setDraggingHour(value: boolean) {
  isDraggingHour.value = value;
}

function startDrag(event: MouseEvent | TouchEvent) {
  isDragging.value = true;
  let clientX: number, clientY: number;

  if ('touches' in event) {
    clientX = event.touches[0].clientX;
    clientY = event.touches[0].clientY;
  } else {
    clientX = event.clientX;
    clientY = event.clientY;
  }

  dragStartX.value = clientX;
  dragStartY.value = clientY;
  event.preventDefault();
  event.stopPropagation();
  handleDrag(event);
}

function handleDrag(event: MouseEvent | TouchEvent) {
  if (!isDragging.value) return;

  event.preventDefault();

  let clientX: number, clientY: number;
  if ('touches' in event) {
    clientX = event.touches[0].clientX;
    clientY = event.touches[0].clientY;
  } else {
    clientX = event.clientX;
    clientY = event.clientY;
  }

  const clockFace = event.currentTarget as HTMLElement;
  const rect = clockFace.getBoundingClientRect();
  const centerX = rect.left + rect.width / 2;
  const centerY = rect.top + rect.height / 2;

  const x = clientX - centerX;
  const y = clientY - centerY;

  const distance = Math.sqrt(x * x + y * y);
  const maxRadius = (rect.width / 2) * 0.9;

  if (distance > maxRadius) {
    const ratio = maxRadius / distance;
    const newX = x * ratio;
    const newY = y * ratio;
    const angle = Math.atan2(newY, newX) * (180 / Math.PI);
    updateTimeFromAngle(angle);
  } else {
    const angle = Math.atan2(y, x) * (180 / Math.PI);
    updateTimeFromAngle(angle);
  }
}

function updateTimeFromAngle(angle: number) {
  const normalizedAngle = (angle + 360) % 360;

  if (isDraggingHour.value) {
    let adjustedAngle = (normalizedAngle + 90) % 360;
    let hour = Math.round(adjustedAngle / 30) % 12;
    if (hour === 0) hour = 12;
    clickHour(hour);
  } else {
    let adjustedAngle = (normalizedAngle + 90) % 360;
    let minute = Math.round(adjustedAngle / 6) % 60;
    const roundedMinute = (Math.round(minute / 5) * 5) % 60;
    clickMinute(roundedMinute);
  }
}

function stopDrag(event?: MouseEvent | TouchEvent) {
  if (event) {
    event.preventDefault();
    event.stopPropagation();
  }
  isDragging.value = false;
}

function getHourPosition(hour: number) {
  const angle = ((hour % 12) * 30 - 90) * (Math.PI / 180);
  const radius = clockRadius * 0.75;
  const x = clockCenter.x + radius * Math.cos(angle);
  const y = clockCenter.y + radius * Math.sin(angle);

  return {
    left: `${x}px`,
    top: `${y}px`,
    transform: 'translate(-50%, -50%)',
  };
}

function getMinutePosition(minute: number) {
  const angle = (minute * 6 - 90) * (Math.PI / 180);
  const radius = clockRadius * 0.85;
  const x = clockCenter.x + radius * Math.cos(angle);
  const y = clockCenter.y + radius * Math.sin(angle);

  return {
    left: `${x}px`,
    top: `${y}px`,
    transform: 'translate(-50%, -50%)',
  };
}
</script>

<template>
  <a-popover
    trigger="click"
    placement="bottom"
    :arrow="false"
    :destroy-tooltip-on-hide="false"
    @open-change="openChange"
  >
    <a-input
      :class="props?.class ?? 'w-full'"
      allow-clear
      :value="getValueTime(props.value)"
      :placeholder="props.placeholder ?? $t('placeholder.select-time')"
      @change="handleChange"
      :disabled="props.disabled"
    />
    <template #content>
      <a-row v-if="timeType === TimeTypeEnum.WHEEL">
        <a-col>
          <div class="h-[240px] w-[67px] overflow-hidden hover:overflow-auto">
            <div v-for="hour in hours" :key="hour" ref="hourRef">
              <div
                class="flex justify-center items-center cursor-pointer hover:bg-gray-100 py-1"
                :class="{ 'bg-[#e6f7ff]': isActiveHour(hour) }"
                @click="() => clickHour(hour)"
              >
                {{ hour.toFixed(0).padStart(2, '0') }}
              </div>
            </div>
            <div class="h-[210px]" />
          </div>
        </a-col>
        <a-col>
          <a-divider type="vertical" class="h-[240px]" />
        </a-col>
        <a-col>
          <div class="h-[240px] w-[67px] overflow-hidden hover:overflow-auto">
            <div v-for="minute in minutes" :key="minute" ref="minuteRef">
              <div
                v-if="disabledMinutes().indexOf(minute) === -1"
                class="flex justify-center items-center cursor-pointer hover:bg-gray-100 py-1"
                :class="{ 'bg-[#e6f7ff]': isActiveMinute(minute) }"
                @click="() => clickMinute(minute)"
              >
                {{ minute.toFixed(0).padStart(2, '0') }}
              </div>
            </div>
            <div class="h-[210px]" />
          </div>
        </a-col>
        <a-col>
          <a-divider type="vertical" class="h-[240px]" />
        </a-col>
        <a-col>
          <div class="h-[240px] w-[67px] overflow-hidden hover:overflow-auto">
            <div v-for="dayPrd in dayPeriods" :key="dayPrd" ref="dayPeriodRef">
              <div
                class="flex justify-center items-center cursor-pointer hover:bg-gray-100 py-1"
                :class="{ 'bg-[#e6f7ff]': isActiveDayPeriod(dayPrd) }"
                @click="() => clickDayPeriod(dayPrd)"
              >
                {{ $t('common.' + dayPrd) }}
              </div>
            </div>
            <div class="h-[210px]" />
          </div>
        </a-col>
      </a-row>
      <div v-if="timeType === TimeTypeEnum.CLOCK" class="clock-container">
        <div class="time-display">
          <span
            class="bg-gray-100 w-14 text-center"
            :class="{ active: isDraggingHour }"
            @click="setDraggingHour(true)"
          >
            {{ getHour.toString().padStart(2, '0') }}
          </span>
          <span>:</span>
          <span
            class="bg-gray-100 w-14 text-center"
            :class="{ active: !isDraggingHour }"
            @click="setDraggingHour(false)"
          >
            {{ getMinute.toString().padStart(2, '0') }}
          </span>
          <div class="day-period-buttons">
            <a-button
              v-for="dayPrd in dayPeriods"
              :key="dayPrd"
              size="small"
              :type="dayPeriod === dayPrd ? 'primary' : 'default'"
              @click="() => clickDayPeriod(dayPrd)"
            >
              {{ $t('common.' + dayPrd) }}
            </a-button>
          </div>
        </div>

        <div
          class="clock-face"
          @mousedown="startDrag"
          @mousemove="handleDrag"
          @mouseup="stopDrag"
          @mouseleave="stopDrag"
          @touchstart="startDrag"
          @touchmove="handleDrag"
          @touchend="stopDrag"
        >
          <div class="clock-circle">
            <template v-if="isDraggingHour">
              <div
                v-for="hour in hours"
                :key="`h-${hour}`"
                class="hour-marker"
                :class="{ active: isActiveHour(hour) }"
                :style="getHourPosition(hour)"
              >
                {{ hour }}
              </div>
            </template>
            <template v-else>
              <div
                v-for="minute in minuteMarkers"
                :key="`m-${minute}`"
                class="minute-marker"
                :class="{ active: isActiveMinute(minute) }"
                :style="getMinutePosition(minute)"
              >
                {{ minute }}
              </div>
            </template>
            <div
              class="clock-hand"
              :style="{
                transform: `rotate(${isDraggingHour ? hourAngle : minuteAngle}deg)`,
                height: isDraggingHour ? '60px' : '80px',
                width: isDraggingHour ? '4px' : '2px',
                background: isDraggingHour ? '#1890ff' : '#1890ff',
              }"
            >
              <div class="hand-circle"></div>
            </div>
            <div class="center-point"></div>
          </div>
        </div>
      </div>
      <div
        v-if="timeType === TimeTypeEnum.MANUAL"
        class="flex items-center h-[140px]"
      >
        <div class="flex items-center gap-2">
          <a-input-number
            :value="getHour"
            :min="1"
            :max="12"
            :step="1"
            size="large"
            class="input-time"
            :formatter="(value: any) => value.toString().padStart(2, '0')"
            @change="(value) => clickHour(+value)"
          />
          <span class="text-[32px] font-semibold">:</span>
          <a-input-number
            :value="getMinute"
            :min="0"
            :max="59"
            :step="1"
            size="large"
            class="input-time"
            :formatter="(value: any) => value.toString().padStart(2, '0')"
            @change="(value) => clickMinute(+value)"
          />
          <div class="flex flex-col gap-2">
            <a-button
              v-for="dayPrd in dayPeriods"
              :key="dayPrd"
              size="middle"
              :type="dayPeriod === dayPrd ? 'primary' : 'default'"
              @click="() => clickDayPeriod(dayPrd)"
            >
              {{ $t('common.' + dayPrd) }}
            </a-button>
          </div>
        </div>
      </div>
      <a-divider class="my-2" />
      <div class="flex justify-end">
        <a-row :gutter="[20, 20]">
          <a-col>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              viewBox="0 0 24 24"
              class="w-5 h-5 cursor-pointer"
              :class="{ 'text-blue': timeType === TimeTypeEnum.WHEEL }"
              @click="() => handleTimeTypeChange(TimeTypeEnum.WHEEL)"
            >
              <path
                d="M7.34 6.41L.86 12.9l6.49 6.48l6.49-6.48l-6.5-6.49zM3.69 12.9l3.66-3.66L11 12.9l-3.66 3.66l-3.65-3.66zm15.67-6.26A8.95 8.95 0 0 0 13 4V.76L8.76 5L13 9.24V6c1.79 0 3.58.68 4.95 2.05a7.007 7.007 0 0 1 0 9.9a6.973 6.973 0 0 1-7.79 1.44l-1.49 1.49C10.02 21.62 11.51 22 13 22c2.3 0 4.61-.88 6.36-2.64a8.98 8.98 0 0 0 0-12.72z"
                fill="currentColor"
              />
            </svg>
          </a-col>
          <a-col>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              viewBox="0 0 24 24"
              class="w-5 h-5 cursor-pointer"
              :class="{ 'text-blue': timeType === TimeTypeEnum.CLOCK }"
              @click="() => handleTimeTypeChange(TimeTypeEnum.CLOCK)"
            >
              <path
                d="M20.13 3.87C18.69 2.17 15.6 1 12 1S5.31 2.17 3.87 3.87L2 2v5h5L4.93 4.93c1-1.29 3.7-2.43 7.07-2.43s6.07 1.14 7.07 2.43L17 7h5V2l-1.87 1.87z"
                fill="currentColor"
              />
              <path
                d="M13 12.5v-6c0-.83-.67-1.5-1.5-1.5S10 5.67 10 6.5v10.74l-4.04-.85l-1.21 1.23L10.13 23h8.97l1.09-7.64l-6.11-2.86H13z"
                fill="currentColor"
              />
            </svg>
          </a-col>
          <a-col>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              viewBox="0 0 24 24"
              class="w-5 h-5 cursor-pointer"
              :class="{ 'text-blue': timeType === TimeTypeEnum.MANUAL }"
              @click="() => handleTimeTypeChange(TimeTypeEnum.MANUAL)"
            >
              <path
                d="M21 4H3c-1.1 0-2 .9-2 2v13c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zM7 12v-2H5v2h2zm-2-2V8h2v2H5zm6 2v2H9v-2h2zm-2-2V8h2v2H9zm7 6v1H8v-1h8zm-1-4v2h-2v-2h2zm-2-2V8h2v2h-2zm4 4v-2h2v2h-2zm2-4h-2V8h2v2z"
                fill="currentColor"
              />
            </svg>
          </a-col>
        </a-row>
      </div>
    </template>
  </a-popover>
</template>

<style lang="less" scoped>
.input-time {
  width: 76px;
  height: 72px;
  :deep(.ant-input-number-input) {
    height: 72px;
    font-size: 32px;
    font-weight: 600;
    width: 100%;
  }
}

.clock-container {
  padding: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 280px;
}

.time-display {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  justify-content: center;

  span {
    cursor: pointer;
    padding: 0 4px;
    border-radius: 4px;

    &.active {
      background-color: rgba(24, 144, 255, 0.1);
    }
  }

  .day-period-buttons {
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
}

.clock-face {
  position: relative;
  width: 240px;
  height: 240px;
  user-select: none;
  touch-action: none;
  cursor: grab;
}

.clock-face:active {
  cursor: grabbing;
}

.clock-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #f5f5f5;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.hour-marker,
.minute-marker {
  position: absolute;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  border-radius: 50%;
  font-weight: 500;

  &.active {
    background-color: #1890ff;
    color: white;
  }
}

.clock-hand {
  position: absolute;
  background-color: #1890ff;
  top: 50%;
  left: 50%;
  margin-left: -1px;
  transform-origin: 50% 0;
  z-index: 5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.hand-circle {
  position: absolute;
  width: 14px;
  height: 14px;
  background-color: #1890ff;
  border-radius: 50%;
  left: 50%;
  top: 0;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  z-index: 6;
}

.center-point {
  width: 8px;
  height: 8px;
  background-color: #1890ff;
  border-radius: 50%;
  position: absolute;
}

.active {
  background-color: #1890ff !important;
  color: white;
}
</style>
