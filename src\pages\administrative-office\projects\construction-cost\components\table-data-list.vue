<script setup lang="ts">
import TableData from './table-data.vue'
import EstimateCost from './estimate-cost.vue'
import type { CostAmountItem, EstimateBudgetItem } from '~@/api/construction-cost'

defineProps({
  mainCostAmount: {
    type: Object as () => CostAmountItem,
    required: false,
  },
  subCostAmount: {
    type: Object as () => CostAmountItem,
    required: false,
  },
  overallCostAmount: {
    type: Object as () => CostAmountItem,
    required: false,
  },
  mainEstimateBudget: {
    type: Object as () => EstimateBudgetItem,
    required: false,
  },
  subEstimateBudget: {
    type: Object as () => EstimateBudgetItem,
    required: false,
  },
  overallEstimateBudget: {
    type: Object as () => EstimateBudgetItem,
    required: false,
  },
})
</script>

<template>
  <div class="p-4">
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-4">
      <div class="grid grid-cols-1 gap-4">
        <TableData :cost-amount="mainCostAmount" />
        <TableData :cost-amount="subCostAmount" />
        <TableData :cost-amount="overallCostAmount" />
      </div>
      <div class="grid grid-cols-1 gap-4">
        <EstimateCost :estimate-budget="mainEstimateBudget" />
        <EstimateCost :estimate-budget="subEstimateBudget" />
        <EstimateCost :estimate-budget="overallEstimateBudget" />
      </div>
    </div>
  </div>
</template>
