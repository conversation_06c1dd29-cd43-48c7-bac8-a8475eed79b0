/* eslint-disable style/member-delimiter-style */
export interface RoleCombo {
  roleId: string;
  roleName: string;
}

export interface EmployeeInfo {
  employeeId: string;
  employeeCode: string;
  employeeName: string;
  email: string;
  employeeMails: string[];
  address: string;
  phone: string;
  gender: boolean;
  birthday: string;
  workingStatus: string;
  workingStatusName: string;
  structureName: string;
  positionName: string;
  rankingName: string | null;
  isWorker: boolean;
  roles: RoleCombo[];
}

export interface EmployeeInfoCombo {
  id: number
  name: string
  loginId: string
}

export interface EmployeeComboResponse {
  items: EmployeeInfoCombo[]
  totalCount: number
  pageSize: number
  pageIndex: number
}

export interface UserDataResponse {
  items: EmployeeInfo[]
  pageIndex: number
  pageSize: number
  totalRecords: number
}

export interface EmployeePaginatedParams {
  pageNum: number
  pageSize: number
  dob?: string
  structureId?: number
  rankingId?: number
  gender?: boolean
  status?: boolean
  employeeType?: boolean
  positionId?: number
}

// export function getEmployeeInfoApi_Old() {
//   return useGet<EmployeeInfo>("/EndUser/GetEmployeeInfo");
// }
export function getEmployeeInfoApi() {
  return useGet<EmployeeInfo>('v1/employee/current/info')
}

export function getUserDataApi(params: EmployeePaginatedParams) {
  return useGet<UserDataResponse>('v1/employee/paginated', params)
}

export function getAllEmployeeAvatarApi() {
  return useGet<string>(`v1/employee/avatar`)
}
