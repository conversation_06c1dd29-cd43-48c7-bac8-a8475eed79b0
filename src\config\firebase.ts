import { initializeApp } from 'firebase/app';
import {
  getMessaging,
  getToken,
  MessagePayload,
  onMessage,
} from 'firebase/messaging';
import logger from '~@/utils/logger';

const firebaseConfig = {
  apiKey: 'AIzaSyA1CP0PrQuqOmFGH4RLZY-cM3wi3D2eb8A',
  authDomain: 'kantoku-547bb.firebaseapp.com',
  projectId: 'kantoku-547bb',
  storageBucket: 'kantoku-547bb.firebasestorage.app',
  messagingSenderId: '982394207074',
  appId: '1:982394207074:web:6cd5a706c139266dcdca3b',
  measurementId: 'G-NXDDNGVEFE',
};

const app = initializeApp(firebaseConfig);
const messaging = getMessaging(app);

export const registerServiceWorker = async () => {
  try {
    const registration = await navigator.serviceWorker.register(
      '/firebase-messaging-sw.js',
      { scope: '/' }
    );
    return registration;
  } catch (error) {
    return null;
  }
};

export const requestNotificationPermission = async () => {
  try {
    const permission = await Notification.requestPermission();
    if (permission === 'granted') {
      const currentToken = await getToken(messaging, {
        vapidKey:
          'BPEO-N7SumMxz46dl78HnZVWPPM8U3RP-OgBMDWRjPkyBqFvvmorbq7Hkm9tZNjhayI2Poyf8blRUgHaQ77sKl8',
      });

      if (currentToken) {
        logger.log('FCM token:', currentToken);
        return currentToken;
      } else {
        logger.log('No registration token available');
        return null;
      }
    } else {
      logger.log('Notification permission denied');
      return null;
    }
  } catch (error) {
    logger.error('An error occurred while retrieving token:', error);
    return null;
  }
};

export const onMessageListener = () => {
  return new Promise<MessagePayload>((resolve) => {
    onMessage(messaging, (payload) => {
      resolve(payload);
    });
  });
};

export { messaging };
