<script lang="ts" setup>
import EventCalendarRuleSetting from './tabs/event-calendar-rule-setting.vue'
import EventCalendar from './tabs/event-calendar.vue'

const { t } = useI18n()
</script>

<template>
  <a-tabs type="card" destroy-inactive-tab-pane>
    <a-tab-pane key="1" :tab="t('event-calendar')">
      <EventCalendar />
    </a-tab-pane>
    <a-tab-pane key="2" :tab="t('event-calendar-rule-setting')">
      <EventCalendarRuleSetting />
    </a-tab-pane>
  </a-tabs>
</template>
