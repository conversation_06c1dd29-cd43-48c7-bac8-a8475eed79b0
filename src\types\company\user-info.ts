export interface UserInfoItem {
  accountId?: string
  userName: string
  email: string
  address: string
  phone: string
  gender: boolean
  birthday: string
  avatarUrl: string | null
}

export interface UserInfoParams {
  name: string
  email: string
  address: string
  phone: string
  gender: boolean
  birthday: string
}

export interface UserInfoResponse {
  data: UserInfoItem
  status: number
  message?: string
}

export interface UserAvatar {
  avatarUrl: string
  avatarBase64: string
  avatarByteArr: string
  metadata: Record<string, string>
}
