<script lang="ts" setup>
import {
  DeleteOutlined,
  LeftOutlined,
  PlusOutlined,
  RightOutlined,
  SearchOutlined,
  SendOutlined,
} from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { usePagination } from 'vue-request';
import type {
  ColumnGroupType,
  ColumnType,
  TablePaginationConfig,
} from 'ant-design-vue/es/table';
import type { FilterValue } from 'ant-design-vue/es/table/interface';
import _, { cloneDeep } from 'lodash';
import { message } from 'ant-design-vue';
import ItemDetail from './item-detail.vue';
import type {
  NotificationItem,
  TargetItem,
} from '~@/api/company/notification';
import {
  createNotification,
  deleteNotification,
  getNotification,
  getOneNotification,
  sendNotificationTest,
  updateNotification,
} from '~@/api/company/notification';
import { ModalType } from '~@/enums/system-status-enum';
import { ResponseStatusEnum } from '~@/enums/response-status-enum';
import logger from '~@/utils/logger';
import type { Role } from '~@/api/company/role';
import { getListRolesApi } from '~@/api/company/role';
import type {
  EmployeeCombo,
} from '~@/api/employee/employee';
import {
  getSimpleEmployeeInfoApi,
} from '~@/api/employee/employee';

type ColumnItemType<T> = ColumnGroupType<T> | ColumnType<T>;

enum TargetEnum {
  INDIVIDUAL = 'INDIVIDUAL',
  ROLE = 'ROLE',
  ALL = 'ALL',
}

interface Params {
  pageNum?: number;
  pageSize?: number;
  keyword?: string;
  dateFrom?: string;
  dateTo?: string;
}

type TargetCustomItem = TargetItem & {
  isUpdated?: boolean;
};

interface FormState {
  notificationId: string;
  title?: string;
  body?: string;
  notificationType?: string;
  targets: TargetCustomItem[];
}

const initFormState: FormState = {
  notificationId: '',
  title: undefined,
  body: undefined,
  notificationType: undefined,
  targets: [],
};

const { t } = useI18n();
const searchForm = ref<Params>({
  pageSize: 10,
  pageNum: 1,
  keyword: '',
  dateFrom: dayjs().startOf('month').format('YYYY-MM-DD'),
  dateTo: dayjs().endOf('month').format('YYYY-MM-DD'),
});
const formRef = ref();
const transactionRef = ref();
const modalLoading = ref<boolean>(false);
const modalType = ref<ModalType>(ModalType.ADD);
const isOpenModal = ref<boolean>(false);
const searchDate = ref<dayjs.Dayjs>(dayjs());
const roles = ref<Role[]>([]);
const isOpenDetail = ref<boolean>(false);
const employees = ref<EmployeeCombo[]>([]);
const formState = reactive<FormState>({ ...cloneDeep(initFormState) });
const idDetail = ref<string>('');
const tableLeft = ref();
const { height: tableLeftHeight } = useElementSize(tableLeft);

async function queryData(params?: Params) {
  const { data } = await getNotification(params);
  return data;
}

const {
  data: dataSource,
  loading,
  refresh,
  total,
  current,
  run,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [searchForm.value],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
});

const pagination = computed(() => ({
  total: total.value ?? 10,
  current: current.value,
  pageSize: pageSize.value,
  showSizeChanger: true,
}));

const columns = computed<ColumnItemType<NotificationItem>[]>(() => [
  { dataIndex: 'notification' },
]);

function onReset() {
  Object.assign(formState, cloneDeep(initFormState));
}

async function onFinish() {
  try {
    await formRef.value.validate();

    switch (modalType.value) {
      case ModalType.ADD: {
        const create = await createNotification({
          title: formState.title,
          body: formState.body,
          notificationType: formState.notificationType,
          targets: formState.targets.map(target => ({
            targetType: target.targetType ?? '',
            targetIds: target.targetIds,
          })),
        });
        if (create.status !== ResponseStatusEnum.SUCCESS)
          break;

        message.success(create.message);
        break;
      }
      case ModalType.EDIT: {
        const filterTargets = formState.targets.filter(
          target => target.isUpdated,
        );
        const update = await updateNotification(formState.notificationId, {
          title: formState.title,
          body: formState.body,
          notificationType: formState.notificationType,
          targets: filterTargets.map(target => ({
            targetType: target.targetType ?? '',
            targetIds: target.targetIds,
            notificationTargetId: target.notificationTargetId,
            isDeleted: target.isDeleted ?? false,
          })),
        });
        if (update.status !== ResponseStatusEnum.SUCCESS)
          break;

        message.success(update.message);
        break;
      }
      default:
        break;
    }

    isOpenModal.value = false;
    onReset();
    refresh();
  }
  catch (error) {
    logger.error(error);
  }
}

async function openModal(id: string, type: ModalType) {
  isOpenDetail.value = false;
  idDetail.value = '';

  switch (type) {
    case ModalType.ADD:
      modalType.value = type;
      isOpenModal.value = true;
      break;
    case ModalType.COPY:
    case ModalType.EDIT: {
      isOpenModal.value = true;
      modalLoading.value = true;
      modalType.value = type;

      try {
        const update = await getOneNotification(id);
        formState.notificationId = id;
        formState.title = update.data?.title;
        formState.body = update.data?.body;
        formState.notificationType = update.data?.notificationType;
        formState.targets
          = update.data?.targets.map(target => ({
            targetType: target.targetType,
            targetIds: target.targetIds,
            notificationTargetId: target.notificationTargetId,
          })) || [];
      }
      catch (error) {
        logger.error(error);
      }
      finally {
        modalLoading.value = false;
      }

      break;
    }
    default:
      break;
  }
}

function handleTableChange(pagination: TablePaginationConfig, filters: Record<string, FilterValue>) {
  searchForm.value.pageSize = pagination.pageSize;
  searchForm.value.pageNum = pagination.current;
  run({ ...searchForm.value, ...filters });
}

const notificationTypes = computed(() => [
  { label: t('notification-type.push'), value: 'PUSH' },
  { label: t('notification-type.email'), value: 'EMAIL' },
]);

const targetTypes = computed(() => [
  { label: t('target-type.individual'), value: TargetEnum.INDIVIDUAL },
  { label: t('target-type.role'), value: TargetEnum.ROLE },
  { label: t('target-type.all'), value: TargetEnum.ALL },
]);

const targetIds = computed(() => {
  return (targetType?: string) => {
    if (targetType === TargetEnum.INDIVIDUAL) {
      return employees.value.map(employee => ({
        label: employee.employeeName,
        value: employee.employeeId,
      }));
    }
    if (targetType === TargetEnum.ROLE) {
      return roles.value.map(role => ({
        label: role.roleName,
        value: role.roleId,
      }));
    }
    return [];
  };
});

function addTarget() {
  formState.targets.push({
    targetType: undefined,
    targetIds: [],
  });
}

function removeTarget(index: number) {
  formState.targets.filter(item => !item.isDeleted)[index].isDeleted = true;
  formState.targets = formState.targets.filter((item) => {
    if (item.isDeleted && !item.notificationTargetId)
      return false;
    return true;
  });
}

function getTargetName(targetType?: string) {
  if (targetType === TargetEnum.INDIVIDUAL)
    return t('employee');

  if (targetType === TargetEnum.ROLE)
    return t('role');

  return '';
}

const renderTitle = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
    case ModalType.COPY:
      return _.startCase(_.toLower(`${t('button.create')} ${t('post')}`));
    case ModalType.EDIT:
      return `${t('button.edit')} ${t('post')}`;
    default:
      return '';
  }
});

const renderOkConfirm = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
    case ModalType.COPY:
      return t('message.add-confirmation');
    case ModalType.EDIT:
      return t('message.edit-confirmation');
    default:
      return '';
  }
});

function onSearch() {
  searchForm.value.dateFrom = dayjs(searchDate.value)
    .startOf('month')
    .format('YYYY-MM-DD');
  searchForm.value.dateTo = dayjs(searchDate.value)
    .endOf('month')
    .format('YYYY-MM-DD');
  handleTableChange(
    { pageSize: searchForm.value.pageSize ?? 10, current: 1 },
    {},
  );
}

const targets = computed(() => {
  return formState.targets.filter(target => !target.isDeleted);
});

function handlePaginationChange(page: number, pageSize: number) {
  searchForm.value.pageSize = pageSize;
  searchForm.value.pageNum = page;
  run(searchForm.value);
}

async function handleDeleteNotification(id: string) {
  try {
    const del = await deleteNotification(id);
    if (del.status !== ResponseStatusEnum.SUCCESS)
      return;

    message.success(del.message);
  }
  catch (error) {
    logger.error(error);
  }
  finally {
    refresh();
  }
}

async function sendNotification() {
  try {
    const send = await sendNotificationTest(
      formState.title ?? '',
      formState.body ?? '',
    );
    if (send.status !== ResponseStatusEnum.SUCCESS)
      return;

    message.success(send.message);
  }
  catch (error) {
    logger.error(error);
  }
}

const customRow = computed(() => (data: NotificationItem) => {
  return {
    onClick: () => {
      idDetail.value = data.notificationId;
      isOpenDetail.value = true;
    },
    class:
      data.notificationId === idDetail.value
        ? 'row-active shadow-lg'
        : 'shadow-lg',
  };
});

const showTargetIds = computed(() => {
  return (target: TargetCustomItem) => {
    const { INDIVIDUAL, ROLE } = TargetEnum;
    if ([INDIVIDUAL, ROLE].includes(target.targetType as TargetEnum))
      return true;

    return false;
  };
});

onMounted(async () => {
  const res = await Promise.all([
    getListRolesApi(),
    getSimpleEmployeeInfoApi(),
  ]);
  roles.value = res[0].data?.items ?? [];
  employees.value = res[1].data?.items ?? [];
});

onClickOutside(transactionRef, (event) => {
  if (document.getElementById('app')?.contains(event.target as Node))
    isOpenDetail.value = false;
});
</script>

<template>
  <page-container>
    <a-row ref="transactionRef" :wrap="false" class="gap-6">
      <a-col flex="auto">
        <a-row
          ref="tableLeft"
          :wrap="false"
          :gutter="[12, 12]"
          class="h-[calc(100vh-96px)] flex-col"
        >
          <a-col flex="none" span="24">
            <a-row>
              <a-col span="24">
                <a-row :gutter="[12, 12]" align="middle">
                  <a-col flex="none">
                    <a-row :gutter="[24, 24]" align="middle">
                      <a-col>
                        <a-button
                          class="flex flex-items-center"
                          type="primary"
                          @click="openModal('', ModalType.ADD)"
                        >
                          <PlusOutlined />
                          {{ `${t('button.new')} ${t('post')}` }}
                        </a-button>
                      </a-col>
                      <a-col class="hidden !xl:block">
                        <a-input
                          v-model:value="searchForm.keyword"
                          :placeholder="t('search')"
                          class="w-[18rem] 2xl:w-[25rem]"
                          allow-clear
                          @press-enter="onSearch"
                        >
                          <template #prefix>
                            <SearchOutlined class="text-gray-500" />
                          </template>
                        </a-input>
                      </a-col>
                    </a-row>
                  </a-col>
                  <a-col flex="auto">
                    <div class="flex gap-2 items-center justify-end">
                      <div class="flex gap-2 items-center">
                        <LeftOutlined
                          class="left-icon"
                          @click="
                            searchDate = dayjs(searchDate).subtract(1, 'month');
                            onSearch();
                          "
                        />
                        <a-date-picker
                          v-model:value="searchDate"
                          picker="month"
                          :allow-clear="false"
                          :format="
                            (value: dayjs.Dayjs) =>
                              `${value
                                .startOf('month')
                                .format('DD/MM/YYYY')} - ${value
                                .endOf('month')
                                .format('DD/MM/YYYY')}`
                          "
                          class="search-date"
                          @change="onSearch"
                        />
                        <RightOutlined
                          class="right-icon"
                          @click="
                            searchDate = dayjs(searchDate).add(1, 'month');
                            onSearch();
                          "
                        />
                      </div>
                      <img src="/icon/calendar_project_icon.svg">
                    </div>
                  </a-col>
                </a-row>
              </a-col>
              <a-col span="24">
                <a-table
                  class="tableNotification"
                  :scroll="{ x: 'max-content', y: 'calc(100vh - 200px)' }"
                  :columns="columns"
                  :data-source="dataSource?.items"
                  :loading="loading"
                  :pagination="false"
                  row-key="notificationId"
                  :custom-row="customRow"
                  :show-header="false"
                  @change="handleTableChange"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'notification'">
                      <div>
                        <div
                          class="flex items-center justify-between px-6 pt-6 pb-4"
                        >
                          <span class="font-medium text-xl">
                            {{ record.title }}
                          </span>
                          <div
                            class="flex items-center justify-center gap-4 text-xs font-normal text-[#74797A]"
                          >
                            <span>
                              {{
                                dayjs(record.createdTime).format('DD MMM, 2025')
                              }}
                            </span>
                            <div class="flex items-center justify-center gap-1">
                              <img src="/icon/clock.svg">
                              <span>
                                {{ dayjs(record.createdTime).format('HH:mm') }}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div class="px-6">
                          {{ record.body }}
                        </div>
                        <div class="flex justify-between p-6">
                          <div />
                          <div class="flex flex-justify-center gap-4">
                            <a-button
                              class="flex items-center !px-0.25"
                              ghost
                              size="small"
                              @click.stop="
                                openModal(record.notificationId, ModalType.EDIT)
                              "
                            >
                              <img src="/icon/edit.svg" class="w-[1.25rem]">
                            </a-button>
                            <a-popconfirm
                              :title="t('message.delete-confirmation')"
                              placement="leftTop"
                              @confirm="
                                () =>
                                  handleDeleteNotification(
                                    record.notificationId,
                                  )
                              "
                            >
                              <a-button
                                class="flex items-center !px-0.25"
                                size="small"
                                ghost
                                @click.stop
                              >
                                <img
                                  src="/icon/delete.svg"
                                  class="w-[1.25rem]"
                                >
                              </a-button>
                            </a-popconfirm>
                          </div>
                        </div>
                      </div>
                    </template>
                  </template>
                </a-table>
              </a-col>
            </a-row>
          </a-col>

          <a-col flex="auto" span="24">
            <div class="h-full flex items-end">
              <a-row justify="space-between" class="mt-4 w-full">
                <a-col>
                  <a-pagination
                    class="pagination"
                    :total="pagination.total"
                    :current="pagination.current"
                    :page-size="pagination.pageSize"
                    @change="handlePaginationChange"
                  />
                </a-col>
                <a-col>
                  <a-row :gutter="[12, 12]" justify="center" align="middle">
                    <a-col>{{ t('show') }}</a-col>
                    <a-col>
                      <a-pagination
                        class="pagination pagination-right"
                        :total="pagination.total"
                        :current="pagination.current"
                        :page-size="pagination.pageSize"
                        show-size-changer
                        :build-option-text="(props: any) => props.value"
                        @change="handlePaginationChange"
                      />
                    </a-col>
                    <a-col>{{ t('entries') }}</a-col>
                  </a-row>
                </a-col>
              </a-row>
            </div>
          </a-col>
        </a-row>
      </a-col>
      <a-col v-if="isOpenDetail" flex="none" class="bg-white">
        <ItemDetail :id="idDetail" :height="tableLeftHeight" />
      </a-col>
    </a-row>

    <a-modal
      v-model:open="isOpenModal"
      width="800px"
      :footer="false"
      :mask-closable="false"
      @cancel="onReset"
    >
      <template #title>
        <div class="flex justify-center items-center">
          <a-typography-title :level="4" class="!text-[#256CB5]">
            {{ renderTitle }}
          </a-typography-title>
        </div>
      </template>
      <a-card border-style="none" :loading="modalLoading" class="card">
        <a-form
          ref="formRef"
          :model="formState"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
          autocomplete="off"
          @finish="onFinish"
          @oncancel="onReset"
        >
          <a-row :gutter="[12, 12]">
            <a-col span="12">
              <a-form-item
                :label="t('form.title')"
                name="title"
                :rules="[{ required: true }]"
              >
                <a-input
                  v-model:value="formState.title"
                  :placeholder="t('form.title')"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item
                :label="t('form.notification-type')"
                name="notificationType"
                :rules="[{ required: true }]"
              >
                <a-select
                  v-model:value="formState.notificationType"
                  :placeholder="t('form.notification-type')"
                  :options="notificationTypes"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col span="24">
              <div class="py-1">
                <div class="flex justify-between">
                  <span class="flex items-center">
                    <span class="required me-[4px]">*</span>
                    {{ t('form.target') }}
                  </span>
                  <a-button
                    class="flex flex-items-center"
                    type="primary"
                    size="small"
                    @click="addTarget"
                  >
                    <PlusOutlined />
                  </a-button>
                </div>
              </div>
              <div class="flex flex-col gap-2 py-2">
                <div
                  v-if="formState.targets.length === 0"
                  class="text-center py-6 bg-gray-50 rounded-md"
                >
                  <div
                    class="i-carbon-time mb-2 mx-auto text-gray-400 text-2xl"
                  />
                  <p class="text-sm text-gray-500">
                    {{ t('noTarget') }}
                  </p>
                </div>
                <div
                  v-for="(target, index) in targets"
                  :key="index"
                  class="p-3 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors"
                >
                  <div class="flex items-center justify-between mb-2">
                    <div class="text-sm font-medium">
                      {{ t('form.target') }} #{{ index + 1 }}
                    </div>
                    <a-button
                      size="small"
                      class="flex items-center justify-center"
                      type="text"
                      @click="removeTarget(index)"
                    >
                      <template #icon>
                        <DeleteOutlined class="text-red-500" />
                      </template>
                    </a-button>
                  </div>
                  <a-row :gutter="[12, 12]">
                    <a-col span="12">
                      <a-form-item
                        :label="t('form.target-type')"
                        :name="['targets', index, 'targetType']"
                        :rules="[{ required: true }]"
                      >
                        <a-select
                          v-model:value="target.targetType"
                          :placeholder="t('form.target-type')"
                          :options="targetTypes"
                          @change="
                            () => {
                              target.isUpdated = true;
                            }
                          "
                        />
                      </a-form-item>
                    </a-col>
                    <a-col v-if="showTargetIds(target)" span="12">
                      <a-form-item
                        :label="getTargetName(target.targetType)"
                        :name="['targets', index, 'targetIds']"
                        :rules="[{ required: true }]"
                      >
                        <a-select
                          v-model:value="target.targetIds"
                          mode="multiple"
                          :options="targetIds(target.targetType)"
                          :placeholder="getTargetName(target.targetType)"
                          @change="
                            () => {
                              target.isUpdated = true;
                            }
                          "
                        />
                      </a-form-item>
                    </a-col>
                  </a-row>
                </div>
              </div>
            </a-col>
            <a-col span="24">
              <a-form-item
                :label="t('form.body')"
                name="body"
                :rules="[{ required: true }]"
              >
                <a-textarea
                  v-model:value="formState.body"
                  :placeholder="t('form.body')"
                  :auto-size="{ minRows: 4, maxRows: 4 }"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row justify="end">
            <a-row :gutter="[4, 4]">
              <a-col>
                <a-button
                  class="flex justify-center items-center"
                  @click="sendNotification"
                >
                  <SendOutlined />
                  {{ t('test') }}
                </a-button>
              </a-col>
              <a-col>
                <a-popconfirm :title="renderOkConfirm" @confirm="onFinish">
                  <a-button
                    type="primary"
                    :disabled="formState.targets.length === 0"
                  >
                    {{
                      modalType === ModalType.ADD
                        ? t('post')
                        : t('button.update')
                    }}
                  </a-button>
                </a-popconfirm>
              </a-col>
            </a-row>
          </a-row>
        </a-form>
      </a-card>
    </a-modal>
  </page-container>
</template>

<style lang="less" scoped>
.tableNotification {
  :deep(.ant-table) {
    background: transparent;
  }
  :deep(table) {
    border-collapse: separate;
    border-spacing: 0 24px;
    margin-top: -24px;
    padding-right: 10px;
  }
  :deep(.ant-table-tbody > tr.row-active) {
    background: #f2f8fd;
    td:first-child {
      border-left: 2px solid #b7d7f2;
    }
    td:last-child {
      border-right: 2px solid #b7d7f2;
    }
    td {
      background: #f2f8fd !important;
      border-top: 2px solid #b7d7f2 !important;
      border-bottom: 2px solid #b7d7f2 !important;
    }
  }
  :deep(.ant-table-container) {
    padding-top: 24px;
  }
  :deep(.ant-table-cell) {
    padding: 0;
  }
  :deep(.ant-table-tbody > tr) {
    background: #fff;
    box-shadow: 0px 2px 4px 0px #0000001a;
    border-radius: 8px;
  }
  :deep(.ant-table-tbody > tr > td:first-child) {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }
  :deep(.ant-table-tbody > tr > td:last-child) {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }
  :deep(.ant-table-tbody > tr > td) {
    background: #fff !important;
    transition: none;
  }
}
.left-icon {
  display: flex;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 0.25rem;
  border: 0.0625rem solid #cdcecd;
}
.right-icon {
  display: flex;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 0.25rem;
  border: 0.0625rem solid #cdcecd;
}
.pagination {
  :deep(.ant-pagination-item-active) {
    background: #f99649;
    border-color: #f99649;
    a {
      color: #fff;
    }
  }
  :deep(.ant-select-selector) {
    background: #f99649;
    border-color: #f99649 !important;
    .ant-select-selection-item {
      color: #fff;
    }
  }
  :deep(.ant-select-arrow) {
    color: #fff;
  }
}
.pagination-right {
  :deep(.ant-pagination-prev) {
    display: none;
  }
  :deep(.ant-pagination-next) {
    display: none;
  }
  :deep(.ant-pagination-item) {
    display: none;
  }
  :deep(.ant-pagination-options) {
    margin: 0;
  }
  :deep(.ant-pagination-jump-next) {
    display: none;
  }
  :deep(.ant-pagination-jump-prev) {
    display: none;
  }
}
.search-date {
  border: none;
  background: none;
  box-shadow: none;
  padding: 0;

  :deep(.ant-picker-suffix) {
    display: none;
  }
  :deep(input) {
    cursor: pointer;
    width: 165px;
  }
}
.required {
  color: #ff4d4f;
}
</style>
