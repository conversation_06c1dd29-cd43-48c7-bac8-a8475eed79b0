import dayjs from 'dayjs'
import type { BreakTimeItem } from '~@/api/attendance'
import { HHMMRegex, HHMMSSRegex } from '~@/utils/apiTimer'

export function useValidateTime() {
  const messageNotify = useNotification()
  const { t } = useI18n()
  function convertToSeconds(timeString: string | null | undefined) {
    if (!timeString)
      return 0

    // Regex cho định dạng yyyy-MM-dd HH:mm:ss
    const datetimeRegex = /^\d{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[12]\d|3[01]) (?:[01]\d|2[0-3]):(?:[0-5]\d):(?:[0-5]\d)$/

    // Nếu là datetime string
    if (datetimeRegex.test(timeString)) {
      const [datePart, timePart] = timeString.split(' ')
      const [year, month, day] = datePart.split('-').map(Number)
      const [hours, minutes, seconds] = timePart.split(':').map(Number)

      const date = new Date(year, month - 1, day, hours, minutes, seconds)
      const baseDate = new Date(1970, 0, 1) // Unix epoch

      return Math.floor((date.getTime() - baseDate.getTime()) / 1000)
    }

    // Nếu chỉ là time string (HH:mm:ss)
    const [hours, minutes, seconds = 0] = timeString.split(':').map(Number)
    return hours * 3600 + minutes * 60 + seconds
  }

  // Hàm kiểm tra xem một thời điểm có nằm giữa hai thời điểm khác không
  function isBetween(time: string | null | undefined, start: string | null | undefined, end: string | null | undefined) {
    if (!time || !start || !end)
      return false
    const timeSeconds = convertToSeconds(time)
    const startSeconds = convertToSeconds(start)
    const endSeconds = convertToSeconds(end)
    return timeSeconds >= startSeconds && timeSeconds <= endSeconds
  }

  function isWorkingTimeValid(
    breakTimes: BreakTimeItem[] | null | undefined,
    checkInTime: string | null | undefined,
    checkOutTime: string | null | undefined,
  ): boolean {
    if (!checkInTime) {
      messageNotify.warning({
        message: t('invalidTime'),
        description: `${t('workshift.error.check-in-time-is-required.')}`,
        duration: 5,
      })
      return false
    }
    if (!checkOutTime) {
      messageNotify.warning({
        message: t('invalidTime'),
        description: `${t('workshift.error.check-out-time-is-required.')}`,
        duration: 5,
      })
      return false
    }
    if (!breakTimes)
      return true
    // Kiểm tra thời gian checkOutTime lớn hơn thời gian checkInTime
    if (convertToSeconds(checkOutTime) <= convertToSeconds(checkInTime)) {
      messageNotify.warning({
        message: t('invalidTime'),
        description: `${t('workshift.error.check-out-time-is-later-than-the-check-in-time.')}`,
        duration: 5,
      })
      return false
    }

    // Kiểm tra thời gian breakOut lớn hơn thời gian breakIn của một break
    for (const breakTime of breakTimes) {
      const breakStart = breakTime.breakInTime
      const breakEnd = breakTime.breakOutTime
      if (convertToSeconds(breakEnd) <= convertToSeconds(breakStart)) {
        messageNotify.warning({
          message: t('invalidTime'),
          description: `${t('workshift.error.break-out-time-is-later-than-the-break-in-time-of-the-break.')}`,
          duration: 5,
        })
        return false
      }
    }

    // Kiểm tra thời gian nghỉ không nằm ngoài thời gian làm việc
    for (const breakTime of breakTimes) {
      let breakStart = breakTime?.breakInTime
      let breakEnd = breakTime?.breakOutTime
      if (!breakStart || !breakEnd)
        return false
      if (HHMMRegex.test(breakStart) && HHMMRegex.test(breakEnd)) {
        breakStart = `${breakStart}:00`
        breakEnd = `${breakEnd}:00`
      }
      if (HHMMSSRegex.test(breakStart) && HHMMSSRegex.test(breakEnd)) {
        if (!isBetween(breakStart, checkInTime, checkOutTime)
          || !isBetween(breakEnd, checkInTime, checkOutTime)) {
          messageNotify.warning({
            message: t('invalidTime'),
            description: `${t('workshift.error.the-break-time-is-not-within-the-working-hours.')}`,
            duration: 5,
          })
          return false
        }
      }
      else {
        const dateString = dayjs(checkInTime).format('YYYY-MM-DD')
        const breakStart = `${dateString} ${breakTime.breakInTime}`
        const breakEnd = `${dateString} ${breakTime.breakOutTime}`
        if (!isBetween(breakStart, checkInTime, checkOutTime)
            || !isBetween(breakEnd, checkInTime, checkOutTime)) {
          messageNotify.warning({
            message: t('invalidTime'),
            description: `${t('workshift.error.the-break-time-is-not-within-the-working-hours.')}`,
            duration: 5,
          })
          return false
        }
      }
    }

    // Kiểm tra thời gian break không gối đầu nhau
    for (let i = 0; i < breakTimes.length; i++) {
      for (let j = i + 1; j < breakTimes.length; j++) {
        const break1Start = breakTimes[i].breakInTime
        const break1End = breakTimes[i].breakOutTime
        const break2Start = breakTimes[j].breakInTime
        const break2End = breakTimes[j].breakOutTime

        if (isBetween(break1Start, break2Start, break2End)
            || isBetween(break1End, break2Start, break2End)
            || isBetween(break2Start, break1Start, break1End)
            || isBetween(break2End, break1Start, break1End)) {
          messageNotify.warning({
            message: t('invalidTime'),
            description: `${t('workshift.error.break-times-cannot-overlap.')}`,
            duration: 5,
          })
          return false
        }
      }
    }

    // Tính tổng thời gian break
    let totalBreakTime = 0
    for (const breakTime of breakTimes) {
      const breakStart = convertToSeconds(breakTime.breakInTime)
      const breakEnd = convertToSeconds(breakTime.breakOutTime)
      totalBreakTime += breakEnd - breakStart
    }

    // Kiểm tra thời gian làm việc thực tính
    const workingTime = convertToSeconds(checkOutTime) - convertToSeconds(checkInTime) - totalBreakTime
    if (workingTime <= 0) {
      messageNotify.warning({
        message: t('invalidTime'),
        description: `${t('workshift.error.the-actual-working-time-calculation-is-unreasonable.')}`,
        duration: 5,
      })
      return false
    }

    return true
  }

  return {
    isWorkingTimeValid,
  }
}
