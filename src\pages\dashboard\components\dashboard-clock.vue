<!-- eslint-disable antfu/top-level-function -->
<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import dayjs from 'dayjs'
import type { CascaderProps } from 'ant-design-vue'
import { RotateCcw } from 'lucide-vue-next'
import type { AttendanceItem, CheckInParams, CheckOutParams } from '~@/api/attendance'
import type { ProjectComboItem } from '~@/api/company/project'
import { useProjectStore } from '~@/stores/project'

const props = defineProps<{
  currentAttendance?: AttendanceItem
}>()

const emit = defineEmits<{
  (event: 'onTimeAction', action: string): void
  (event: 'normalCheckIn', params: CheckInParams): void
  (event: 'breakIn', employeeShiftId: string): void
  (event: 'breakOut', employeeShiftId: string): void
  (event: 'checkOut', employeeShiftId: string, params: CheckOutParams): void
}>()

const { t } = useI18n()
const { projects: projectComboData } = useProjectStore()
const lang = computed(() => {
  return t('locale')
})
const currentTime = ref(dayjs())
const today = ref(dayjs())
const projectType = ref<any>('')

setInterval(() => {
  today.value = dayjs()
  currentTime.value = dayjs()
}, 1000)

const isInputWorkplaceVisible = ref(false)
const inputWorkplace = ref<string | undefined>('')

// State Variables
const attendanceClockStatus = reactive({
  isCheckInSuccess: false,
  isBreakInSuccess: false,
  isBreakOutSuccess: false,
  isCheckOutSuccess: false,
  isCheckInLoading: false,
  isBreakInLoading: false,
  isBreakOutLoading: false,
  isCheckOutLoading: false,
  isCheckInError: false,
  isBreakInError: false,
  isBreakOutError: false,
  isCheckOutError: false,
  currentProjectId: '',
  currentShiftId: '',
  workingLocation: '',
})

const projectMenuOptions = ref<CascaderProps['options']>([
  {
    value: 'current_project',
    label: t('select.current-project'),
    isLeaf: true,
  },
  {
    value: 'other_project',
    label: t('select.other-project'),
    isLeaf: false,
  },
  {
    value: 'business',
    label: t('select.business'),
    isLeaf: true,
  },
  {
    value: 'remote',
    label: t('select.remote'),
    isLeaf: true,
  },
  {
    value: 'custom_workplace',
    label: t('enterWorkplace'),
    isLeaf: true,
  },
])

const checkInFormstate = reactive<CheckInParams>({
  latitude: '',
  longitude: '',
  projectId: undefined,
  workingLocation: undefined,
})

// Computed Properties
const canCheckIn = computed(() => {
  return !attendanceClockStatus.isCheckInSuccess && !attendanceClockStatus.isCheckOutSuccess
})

const canCheckOut = computed(() => {
  return attendanceClockStatus.isCheckInSuccess && !attendanceClockStatus.isCheckOutSuccess
})

const canBreakIn = computed(() => {
  return attendanceClockStatus.isCheckInSuccess && !attendanceClockStatus.isBreakInSuccess
})

const canBreakOut = computed(() => {
  return attendanceClockStatus.isBreakInSuccess && !attendanceClockStatus.isBreakOutSuccess
})

const projectOptions = computed(() => {
  return projectComboData.map((item: ProjectComboItem) => ({
    value: item.id,
    label: `${item.code} - ${item.name}`,
  }))
})

const selectedProject = ref<ProjectComboItem | undefined>(undefined)

// Sử dụng computed property để định dạng currentDate
const formattedTimeLL = computed(() => {
  if (lang.value === 'en')
    return dayjs().locale(lang.value).format('MMM D, YYYY')
  return dayjs().format('YYYY年MM月DD日')
})

const formattedTimeHHMM = computed(() => {
  return currentTime.value.format('HH:mm')
})

const updateAttendanceClockStatus = (newAttendance?: AttendanceItem) => {
  attendanceClockStatus.isCheckInSuccess = false
  attendanceClockStatus.isBreakInSuccess = false
  attendanceClockStatus.isBreakOutSuccess = false
  attendanceClockStatus.isCheckOutSuccess = false
  attendanceClockStatus.isCheckInLoading = false
  attendanceClockStatus.isBreakInLoading = false
  attendanceClockStatus.isBreakOutLoading = false
  attendanceClockStatus.isCheckOutLoading = false
  attendanceClockStatus.currentProjectId = ''
  if (!newAttendance) {
    isInputWorkplaceVisible.value = false
    inputWorkplace.value = ''
    checkInFormstate.projectId = undefined
    checkInFormstate.workingLocation = undefined
    return
  }
  attendanceClockStatus.isCheckInSuccess = !!newAttendance.checkInTime
  attendanceClockStatus.isCheckOutSuccess = !!newAttendance.checkOutTime
  attendanceClockStatus.currentProjectId = newAttendance.projectId ?? ''
  attendanceClockStatus.currentShiftId = newAttendance.employeeShiftId ?? ''
  if (!newAttendance.breakList)
    return
  for (const item of newAttendance?.breakList) {
    if (item.breakInTime === null || item.breakOutTime === null) {
      attendanceClockStatus.isBreakInSuccess = !!item.breakInTime
      attendanceClockStatus.isBreakOutSuccess = !!item.breakOutTime
      break
    }
  }
}

const normalCheckIn = () => {
  const params: CheckInParams = {
    latitude: '',
    longitude: '',
    projectId: checkInFormstate.projectId,
    workingLocation: isInputWorkplaceVisible.value ? inputWorkplace.value : checkInFormstate.workingLocation,
  }
  emit('normalCheckIn', params)
  projectType.value = ''
}

const breakIn = () => {
  emit('breakIn', attendanceClockStatus.currentShiftId)
}

const breakOut = () => {
  emit('breakOut', attendanceClockStatus.currentShiftId)
}

const checkOut = () => {
  const params: CheckOutParams = {
    latitude: '',
    longitude: '',
  }
  emit('checkOut', attendanceClockStatus.currentShiftId, params)
}

const loadProjectMenuData: CascaderProps['loadData'] = (selectedOptions) => {
  const targetOption = selectedOptions[selectedOptions.length - 1]
  targetOption.children = []

  targetOption.loading = false
  if (targetOption.value === 'other_project') {
    targetOption.children = projectComboData.map((item: ProjectComboItem) => ({
      value: item.address ?? '',
      label: `${item.code} - ${item.name}`,
      isLeaf: true,
    }))
  }
}

const filterOption = (input: string, option: any) => {
  return option.name.toLowerCase().includes(input.toLowerCase())
}

const onProjectChange = async (value: any, selectedOptions: any) => {
  checkInFormstate.workingLocation = undefined
  if (!value || !selectedOptions) {
    checkInFormstate.projectId = undefined
    isInputWorkplaceVisible.value = false
    inputWorkplace.value = ''
    return
  }
  selectedProject.value = projectComboData.find((item: ProjectComboItem) => item.id === value)
  checkInFormstate.projectId = value
  checkInFormstate.workingLocation = selectedProject.value?.address ?? ''

  // projectType.value = 'current_project'
  projectType.value = selectedProject.value?.address ?? ''
}

const onProjectMenuChange = async (value: any, _: any) => {
  if (!value) {
    checkInFormstate.workingLocation = undefined
    return
  }

  const stypeProject = value[0]
  switch (stypeProject) {
    case 'current_project':
      if (checkInFormstate?.projectId) {
        // const projectId = formState?.selectedProject
        // const currentProject:
        // formState.selectedWorkplace = currentProject?.workplaceId
        // Khong co gi thay doi
        checkInFormstate.workingLocation = selectedProject.value?.address ?? ''
      }
      break
    case 'other_project':
      checkInFormstate.workingLocation = value[1]
      break
    case 'business':
      checkInFormstate.workingLocation = 'BUSINESS'
      break
    case 'remote':
      checkInFormstate.workingLocation = 'REMOTE'
      // await getCurrentPosition()
      break
    case 'custom_workplace':
      checkInFormstate.workingLocation = value[0]
      isInputWorkplaceVisible.value = true
      break
    default:
      break
  }
}

const refreshLocation = () => {
  isInputWorkplaceVisible.value = false
  inputWorkplace.value = undefined
}

// Lifecycle Hooks
onMounted(() => {
})

watch(() => props.currentAttendance, (newVal) => {
  updateAttendanceClockStatus(newVal)
}, {
  deep: true,
})

watch(() => checkInFormstate.projectId, (newVal) => {
  if (newVal === '' || !newVal)
    projectType.value = ''
}, {
  deep: false,
})
</script>

<template>
  <!-- Clock and location section -->
  <div class="flex-col justify-center items-center p-[11px] rounded-[8px]">
    <div class="flex flex-col justify-center items-center">
      <div>
        <span style="font-size: 1.3rem; color: #101F23; font-weight: 400;">
          {{ formattedTimeLL }}
        </span>
      </div>
      <div>
        <span class="font-[500] text-[64px] text-[#101F23]">
          {{ formattedTimeHHMM }}
        </span>
      </div>
    </div>
    <!-- Select Project and Office  -->
    <div v-if="!attendanceClockStatus.isCheckInSuccess" class="flex justify-center gap-x-2">
      <ProjectSelect
        class="w-[132px]"
        :options="projectOptions"
        :placeholder="t('dashboard.workplace.project')"
        allow-clear
        :disabled="attendanceClockStatus.isCheckInSuccess"
        show-search
        :filter-option="filterOption"
        @change="onProjectChange"
      />
      <div class="flex flex-col items-center">
        <a-cascader
          v-if="!isInputWorkplaceVisible"
          v-model:value="projectType"
          class="w-[132px]"
          :options="checkInFormstate.projectId ? projectMenuOptions : []"
          :placeholder="t('dashboard.workplace.project-menu')"
          :load-data="loadProjectMenuData"
          change-on-select
          :disabled="!checkInFormstate.projectId || checkInFormstate.projectId === ''"
          @change="onProjectMenuChange"
        >
          <template #displayRender="{ labels, selectedOptions }">
            <span v-for="(label, index) in labels" :key="selectedOptions[index]?.value">
              <span v-if="labels.length > 1 && index === labels.length - 1">
                {{ label }}
              </span>
              <span v-if="labels.length === 1">
                {{ label }}
              </span>
            </span>
          </template>
        </a-cascader>

        <a-input
          v-else
          v-model:value="inputWorkplace"
          :placeholder="t('placeholder.enter-data', { msg: t('workplace') })"
          class="w-[132px]"
        >
          <template #suffix>
            <RotateCcw :size="15" class="cursor-pointer hover:text-primary" @click="refreshLocation" />
          </template>
        </a-input>
      </div>
    </div>
    <div class="flex flex-col justify-center items-center">
      <!-- CheckIn  -->
      <div class="flex flex-col justify-center items-center">
        <a-button
          v-if="!attendanceClockStatus.isCheckInSuccess"
          class="w-[270px] h-fit mt-2 text-[1.5rem]"
          type="primary"
          :disabled="!canCheckIn"
          :loading="attendanceClockStatus.isCheckInLoading"
          @click="normalCheckIn"
        >
          <span text-2xl>
            {{ t("dashboard.workplace.button.checkIn") }}
          </span>
        </a-button>
        <template v-if="attendanceClockStatus.isCheckInSuccess && !attendanceClockStatus.isCheckOutSuccess">
          <div class="flex justify-center gap-x-2">
            <a-button
              type="primary"
              class="w-[132px] text-[1rem] h-fit"
              :loading="attendanceClockStatus.isBreakInLoading"
              :disabled="!canBreakIn"
              @click="breakIn"
            >
              {{ t("dashboard.workplace.button.startBreak") }}
            </a-button>
            <a-button
              type="primary"
              class="w-[132px] text-[1rem] h-fit"
              :loading="attendanceClockStatus.isBreakOutLoading"
              :disabled="!canBreakOut"
              @click="breakOut"
            >
              {{ t("dashboard.workplace.button.endBreak") }}
            </a-button>
          </div>
          <!-- CheckOut  -->
          <a-button
            class="w-[270px] h-fit mt-2 text-[1.5rem]"
            type="primary"
            :disabled="!canCheckOut"
            :loading="attendanceClockStatus.isCheckOutLoading"
            @click="checkOut"
          >
            {{ t("dashboard.workplace.button.checkOut") }}
          </a-button>
        </template>
      </div>
    <!-- StartBreak, EndBreak  -->
    </div>
    <!-- Clock and location end -->
  </div>
</template>
