<script lang="ts" setup>
import { computed, reactive, ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { InputCost } from '~@/api/invoice'
import { parseDate } from '~@/utils/apiTimer'

const props = defineProps({
  // If you have an existing invoice, pass it as a prop
  invoiceData: {
    type: Object as () => Partial<InputCost>,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:invoice'])
// Create a reactive invoice object that combines props with default values
const invoice = reactive<Partial<InputCost>>({
  ...props.invoiceData,
})

// Create a copy for editing
const editableInvoice = reactive<Partial<InputCost>>({ ...invoice })

// Track if we're in edit mode
const editing = ref(false)

// Track which field is currently being edited
const currentEditField = ref<string | null>(null)

// Check if invoice is empty
const isEmpty = computed(() => {
  return !invoice.issueDate
               && !invoice.paymentDate
               && !invoice.originalNumber
               && !invoice.entryTypeName
               && !invoice.totalAmount
               && !invoice.paymentTypeName
               && !invoice.projectName
               && !invoice.vendorPresentativeName
               && !invoice.vendorAddress
               && !invoice.vendorName
               && !invoice.vendorPhoneNumber
               && !invoice.vendorEmail
})

// Check if there are unsaved changes
const hasChanges = computed(() => {
  return JSON.stringify(invoice) !== JSON.stringify(editableInvoice)
})

// Translation function
function t(key: string): string {
  const translations: Record<string, string> = {
    'release-date': 'Release Date',
    'payment-term': 'Payment Term',
    'invoice-number': 'Invoice Number',
    'type': 'Type',
    'total': 'Total',
    'payment-method': 'Payment Method',
    'project': 'Project',
    'representative-information': 'Representative Information',
    'address': 'Address',
    'supplier': 'Supplier',
    'phone-number': 'Phone Number',
    'email': 'Email',
    'save-all': 'Save All Changes',
    'cancel': 'Cancel',
    'edit': 'Edit Invoice',
  }
  return translations[key] || key
}

// Enable edit mode for all fields
function enableEditMode() {
  editing.value = true
}

// Enable edit mode for a specific field
function editField(fieldName: string) {
  currentEditField.value = fieldName
}

// Save changes for a single field
function onSingleFieldBlur(fieldName: string) {
  if (currentEditField.value === fieldName) {
    // Update the original invoice with the new value
    (invoice as any)[fieldName] = (editableInvoice as any)[fieldName]
    currentEditField.value = null

    // Emit the updated invoice
    emit('update:invoice', { ...invoice })

    // Show a success message
    message.success(`${fieldName} updated`)
  }
}

// Save all changes
function saveAllChanges() {
  // Copy all values from editable to original
  Object.assign(invoice, editableInvoice)

  // Exit edit mode
  editing.value = false

  // Emit the updated invoice
  emit('update:invoice', { ...invoice })

  // Show a success message
  message.success('All changes saved')
}

// Cancel editing and revert changes
function cancelEditing() {
  // Reset the editable invoice to match the original
  Object.assign(editableInvoice, invoice)

  // Exit edit mode
  editing.value = false
  currentEditField.value = null

  // Show a message
  message.info('Changes canceled')
}

// Watch for prop changes
watch(() => props.invoiceData, (newValue) => {
  if (newValue) {
    Object.assign(invoice, newValue)
    Object.assign(editableInvoice, newValue)
  }
}, { deep: true })
</script>

<template>
  <div class="space-y-4">
    <div class="grid grid-cols-2 gap-4">
      <div>
        <div class="text-gray-500 text-sm">
          {{ t('release-date') }}
        </div>
        <div class="font-medium">
          <a-date-picker
            v-if="editing || !invoice.issueDate"
            v-model:value="editableInvoice.issueDate"
            class="w-full"
            format="YYYY-MM-DD"
            @blur="onSingleFieldBlur('issueDate')"
          />
          <div v-else class="py-1" @click="editField('issueDate')">
            {{ parseDate(invoice.issueDate) }}
          </div>
        </div>
      </div>
      <div>
        <div class="text-gray-500 text-sm">
          {{ t('payment-term') }}
        </div>
        <div class="font-medium">
          <a-date-picker
            v-if="editing || !invoice.paymentDate"
            v-model:value="editableInvoice.paymentDate"
            class="w-full"
            format="YYYY-MM-DD"
            @blur="onSingleFieldBlur('paymentDate')"
          />
          <div v-else class="py-1" @click="editField('paymentDate')">
            {{ parseDate(invoice.paymentDate) }}
          </div>
        </div>
      </div>

      <div>
        <div class="text-gray-500 text-sm">
          {{ t('invoice-number') }}
        </div>
        <div class="font-medium">
          <a-input
            v-if="editing || !invoice.originalNumber"
            v-model:value="editableInvoice.originalNumber"
            @blur="onSingleFieldBlur('originalNumber')"
          />
          <div v-else class="py-1" @click="editField('originalNumber')">
            {{ invoice.originalNumber }}
          </div>
        </div>
      </div>
      <div>
        <div class="text-gray-500 text-sm">
          {{ t('type') }}
        </div>
        <div class="font-medium">
          <a-input
            v-if="editing || !invoice.entryTypeName"
            v-model:value="editableInvoice.entryTypeName"
            @blur="onSingleFieldBlur('entryTypeName')"
          />
          <div v-else class="py-1" @click="editField('entryTypeName')">
            {{ invoice.entryTypeName }}
          </div>
        </div>
      </div>

      <div>
        <div class="text-gray-500 text-sm">
          {{ t('total') }}
        </div>
        <div class="font-medium">
          <a-input-number
            v-if="editing || !invoice.totalAmount"
            v-model:value="editableInvoice.totalAmount"
            class="w-full"
            :formatter="(value: any) => `${value} ¥`"
            :parser="(value: any) => value.replace(' ¥', '')"
            @blur="onSingleFieldBlur('totalAmount')"
          />
          <div v-else class="py-1" @click="editField('totalAmount')">
            {{ invoice.totalAmount }} ¥
          </div>
        </div>
      </div>
      <div>
        <div class="text-gray-500 text-sm">
          {{ t('payment-method') }}
        </div>
        <div class="font-medium">
          <a-input
            v-if="editing || !invoice.paymentTypeName"
            v-model:value="editableInvoice.paymentTypeName"
            @blur="onSingleFieldBlur('paymentTypeName')"
          />
          <div v-else class="py-1" @click="editField('paymentTypeName')">
            {{ invoice.paymentTypeName }}
          </div>
        </div>
      </div>
    </div>

    <div class="border-t pt-4">
      <div class="grid grid-cols-2 gap-4">
        <div>
          <div class="text-gray-500 text-sm">
            {{ t('project') }}
          </div>
          <div class="font-medium flex items-center gap-1">
            <a-input
              v-if="editing || !invoice.projectName"
              v-model:value="editableInvoice.projectName"
              class="w-full"
              @blur="onSingleFieldBlur('projectName')"
            />
            <div v-else class="py-1" @click="editField('projectName')">
              {{ invoice.projectName }}
            </div>
          </div>
          <div class="text-gray-500 text-sm mt-2">
            {{ t('representative-information') }}
          </div>
          <div class="font-medium">
            <a-input
              v-if="editing || !invoice.vendorPresentativeName"
              v-model:value="editableInvoice.vendorPresentativeName"
              @blur="onSingleFieldBlur('vendorPresentativeName')"
            />
            <div v-else class="py-1" @click="editField('vendorPresentativeName')">
              {{ invoice.vendorPresentativeName }}
            </div>
          </div>
          <div class="text-gray-500 text-sm mt-2">
            {{ t('address') }}
          </div>
          <div class="font-medium">
            <a-textarea
              v-if="editing || !invoice.vendorAddress"
              v-model:value="editableInvoice.vendorAddress"
              :rows="1"
              @blur="onSingleFieldBlur('vendorAddress')"
            />
            <div v-else class="py-1 whitespace-pre-line" @click="editField('vendorAddress')">
              {{ invoice.vendorAddress }}
            </div>
          </div>
        </div>

        <div>
          <div class="text-gray-500 text-sm">
            {{ t('supplier') }}
          </div>
          <div class="font-medium">
            <a-input
              v-if="editing || !invoice.vendorName"
              v-model:value="editableInvoice.vendorName"
              @blur="onSingleFieldBlur('vendorName')"
            />
            <div v-else class="py-1" @click="editField('vendorName')">
              {{ invoice.vendorName }}
            </div>
          </div>
          <div class="text-gray-500 text-sm mt-2">
            {{ t('phone-number') }}
          </div>
          <div class="font-medium">
            <a-input
              v-if="editing || !invoice.vendorPhoneNumber"
              v-model:value="editableInvoice.vendorPhoneNumber"
              @blur="onSingleFieldBlur('vendorPhoneNumber')"
            />
            <div v-else class="py-1" @click="editField('vendorPhoneNumber')">
              {{ invoice.vendorPhoneNumber }}
            </div>
          </div>
          <div class="text-gray-500 text-sm mt-2">
            {{ t('email') }}
          </div>
          <div class="font-medium">
            <a-input
              v-if="editing || !invoice.vendorEmail"
              v-model:value="editableInvoice.vendorEmail"
              @blur="onSingleFieldBlur('vendorEmail')"
            />
            <div v-else class="py-1" @click="editField('vendorEmail')">
              {{ invoice.vendorEmail }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="pt-4 flex justify-end space-x-2">
      <a-button v-if="hasChanges" type="primary" @click="saveAllChanges">
        {{ t('save-all') }}
      </a-button>
      <a-button v-if="hasChanges" @click="cancelEditing">
        {{ t('cancel') }}
      </a-button>
      <a-button v-if="!editing && !isEmpty" type="primary" @click="enableEditMode">
        {{ t('edit') }}
      </a-button>
    </div>
  </div>
</template>

  <style scoped>
  .font-medium > div {
    min-height: 32px;
    cursor: pointer;
  }
  .font-medium > div:hover {
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 2px;
  }
  </style>
