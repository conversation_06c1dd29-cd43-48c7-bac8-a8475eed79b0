<script lang="ts" setup>
import {
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  FileSearchOutlined,
  PlusOutlined,
  SwapRightOutlined,
} from '@ant-design/icons-vue';
import type { ColumnGroupType, ColumnType } from 'ant-design-vue/es/table';
import { message } from 'ant-design-vue';
import { usePagination } from 'vue-request';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import { ResponseStatusEnum } from '~@/enums/response-status-enum';
import type {
  EventCalendarItem,
} from '~@/api/company/event-calendar';
import {
  createEventCalendar,
  deleteEventCalendar,
  getEventCalendar,
  getEventCalendarLogs,
  getOneEventCalendar,
  updateEventCalendar,
} from '~@/api/company/event-calendar';
import { ModalType, RecurringType } from '~@/enums/system-status-enum';

enum TimeLineColor {
  CREATE = 'green',
  UPDATE = 'blue',
  DELETE = 'red',
}

interface Params {
  pageNum?: number;
  pageSize?: number;
  [key: string]: any;
}

interface FormState {
  eventId: string;
  eventName: string;
  eventDate?: [string, string];
  eventTime?: [string, string];
  isRecurring: boolean;
  isDayOff: boolean;
  description?: string;
  recurringDate?: [string, string];
  recurringType?: string;
  recurringDay?: number[];
  recurringMonth?: number[];
  recurringWeek?: number[];
}

interface LogState {
  logId: string;
  pageSize: number;
  pageNum: number;
  hasMore: boolean;
}

const initFormState: FormState = {
  eventId: '',
  eventName: '',
  eventDate: undefined,
  eventTime: undefined,
  isRecurring: false,
  isDayOff: false,
  description: undefined,
  recurringDate: undefined,
  recurringType: undefined,
  recurringDay: undefined,
  recurringMonth: undefined,
  recurringWeek: undefined,
};

const modalType = ref<ModalType>(ModalType.ADD);
const modalLoading = ref<boolean>(false);
const logLoading = ref<boolean>(false);
const openLog = ref<boolean>(false);
const isOpenModal = ref<boolean>(false);
const logRef = ref();
const { arrivedState } = useScroll(logRef);
const { t } = useI18n();
const formState = reactive<FormState>({ ...initFormState });
const logState = reactive<LogState>({
  logId: '',
  pageSize: 10,
  pageNum: 1,
  hasMore: true,
});
const logData = ref<any[]>([]);
const searchForm = ref<Params>({
  pageSize: 10,
  pageNum: 1,
});

async function queryData(params?: Params) {
  const { data } = await getEventCalendar(params);
  return data;
}

const {
  data: dataSource,
  loading,
  refresh,
  total,
  current,
  run,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [searchForm.value],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
});

const pagination = computed(() => ({
  total: total.value ?? 10,
  current: current.value,
  pageSize: pageSize.value,
}));

const isDayOffs = [
  {
    text: t('button.yes'),
    value: true,
  },
  {
    text: t('button.no'),
    value: false,
  },
];

const columns = computed<
(ColumnGroupType<EventCalendarItem> | ColumnType<EventCalendarItem>)[]
  >(() => [
    {
      title: t('form.name'),
      dataIndex: 'eventName',
      key: 'eventName',
      width: 200,
      align: 'center',
      ellipsis: true,
    },
    {
      title: t('form.event-date'),
      dataIndex: 'eventDate',
      key: 'eventDate',
      width: 250,
      align: 'center',
    },
    {
      title: t('form.event-time'),
      dataIndex: 'eventTime',
      key: 'eventTime',
      width: 200,
      align: 'center',
    },
    {
      title: t('form.is-day-off'),
      dataIndex: 'isDayOff',
      key: 'isDayOff',
      width: 150,
      align: 'center',
      filters: isDayOffs,
      filterMultiple: false,
    },
    {
      title: t('form.is-recurring'),
      dataIndex: 'isRecurring',
      key: 'isRecurring',
      width: 150,
      align: 'center',
      filters: isDayOffs,
      filterMultiple: false,
    },
    {
      title: t('form.recurring-date'),
      dataIndex: 'recurringDate',
      key: 'recurringDate',
      width: 250,
      align: 'center',
    },
    {
      title: t('form.recurring-type'),
      dataIndex: 'recurringType',
      key: 'recurringType',
      width: 150,
      align: 'center',
    },
    {
      title: t('form.recurring-day'),
      dataIndex: 'recurringDay',
      key: 'recurringDay',
      width: 200,
      align: 'center',
    },
    {
      title: t('form.recurring-week'),
      dataIndex: 'recurringWeek',
      key: 'recurringWeek',
      width: 200,
      align: 'center',
    },
    {
      title: t('form.recurring-month'),
      dataIndex: 'recurringMonth',
      key: 'recurringMonth',
      width: 200,
      align: 'center',
    },
    {
      title: t('form.description'),
      dataIndex: 'description',
      key: 'description',
      width: 200,
      align: 'center',
      ellipsis: true,
    },
    {
      title: t('action'),
      dataIndex: 'actions',
      key: 'actions',
      width: 50,
      align: 'center',
      fixed: 'right',
    },
  ]);

const recurringTypes = computed<
  { label: string; value: string; color: string }[]
>(() => [
  {
    label: t('form.recurring-type.daily'),
    value: RecurringType.DAILY,
    color: 'blue',
  },
  {
    label: t('form.recurring-type.weekly'),
    value: RecurringType.WEEKLY,
    color: 'green',
  },
  {
    label: t('form.recurring-type.monthly'),
    value: RecurringType.MONTHLY,
    color: 'orange',
  },
  {
    label: t('form.recurring-type.annually'),
    value: RecurringType.ANNUALLY,
    color: 'red',
  },
]);

const recurringDays = computed<
  { label: string; value: number; color: string }[]
>(() => [
  {
    label: t('form.recurring-day.monday'),
    value: 1,
    color: 'blue',
  },
  {
    label: t('form.recurring-day.tuesday'),
    value: 2,
    color: 'green',
  },
  {
    label: t('form.recurring-day.wednesday'),
    value: 3,
    color: 'orange',
  },
  {
    label: t('form.recurring-day.thursday'),
    value: 4,
    color: 'red',
  },
  {
    label: t('form.recurring-day.friday'),
    value: 5,
    color: 'purple',
  },
  {
    label: t('form.recurring-day.saturday'),
    value: 6,
    color: 'cyan',
  },
  {
    label: t('form.recurring-day.sunday'),
    value: 7,
    color: 'magenta',
  },
]);

const recurringWeeks = computed<
  { label: string; value: number; color: string }[]
>(() => [
  {
    label: t('form.recurring-week.first'),
    value: 1,
    color: 'blue',
  },
  {
    label: t('form.recurring-week.second'),
    value: 2,
    color: 'green',
  },
  {
    label: t('form.recurring-week.third'),
    value: 3,
    color: 'orange',
  },
  {
    label: t('form.recurring-week.fourth'),
    value: 4,
    color: 'red',
  },
  {
    label: t('form.recurring-week.last'),
    value: 5,
    color: 'purple',
  },
]);

const recurringMonth = computed<
  { label: string; value: number; color: string }[]
>(() => [
  {
    label: t('form.recurring-month.january'),
    value: 1,
    color: 'default',
  },
  {
    label: t('form.recurring-month.february'),
    value: 2,
    color: 'green',
  },
  {
    label: t('form.recurring-month.march'),
    value: 3,
    color: 'orange',
  },
  {
    label: t('form.recurring-month.april'),
    value: 4,
    color: 'red',
  },
  {
    label: t('form.recurring-month.may'),
    value: 5,
    color: 'purple',
  },
  {
    label: t('form.recurring-month.june'),
    value: 6,
    color: 'cyan',
  },
  {
    label: t('form.recurring-month.july'),
    value: 7,
    color: 'magenta',
  },
  {
    label: t('form.recurring-month.august'),
    value: 8,
    color: 'volcano',
  },
  {
    label: t('form.recurring-month.september'),
    value: 9,
    color: 'geekblue',
  },
  {
    label: t('form.recurring-month.october'),
    value: 10,
    color: 'gold',
  },
  {
    label: t('form.recurring-month.november'),
    value: 11,
    color: 'lime',
  },
  {
    label: t('form.recurring-month.december'),
    value: 12,
    color: 'blue',
  },
]);

async function onFinish(values: FormState) {
  switch (modalType.value) {
    case ModalType.ADD:
    case ModalType.COPY: {
      const create = await createEventCalendar({
        eventName: values.eventName,
        eventStartDate: values.eventDate?.[0],
        eventEndDate: values.eventDate?.[1],
        eventStartTime: values.eventTime?.[0],
        eventEndTime: values.eventTime?.[1],
        recurringFrom: values.recurringDate?.[0],
        recurringTo: values.recurringDate?.[1],
        isRecurring: values.isRecurring,
        isDayOff: values.isDayOff,
        description: values.description,
        recurringType: values.recurringType,
        recurringDay: values.recurringDay,
        recurringMonth: values.recurringMonth,
        recurringWeek: values.recurringWeek,
      });
      if (create.status === ResponseStatusEnum.SUCCESS) {
        message.success(create.message);
      }
      else {
        message.error(create.message);
        return;
      }
      break;
    }
    case ModalType.EDIT: {
      const update = await updateEventCalendar(formState.eventId, {
        eventName: values.eventName,
        eventStartDate: values.eventDate?.[0],
        eventEndDate: values.eventDate?.[1],
        eventStartTime: values.eventTime?.[0],
        eventEndTime: values.eventTime?.[1],
        recurringFrom: values.recurringDate?.[0],
        recurringTo: values.recurringDate?.[1],
        isRecurring: values.isRecurring,
        isDayOff: values.isDayOff,
        description: values.description,
        recurringType: values.recurringType,
        recurringDay: values.recurringDay,
        recurringMonth: values.recurringMonth,
        recurringWeek: values.recurringWeek,
      });
      if (update.status === ResponseStatusEnum.SUCCESS) {
        message.success(update.message);
      }
      else {
        message.error(update.message);
        return;
      }
      break;
    }
    default:
      break;
  }
  isOpenModal.value = false;
  onReset();
  refresh();
}

async function openModal(id: string, type: ModalType) {
  switch (type) {
    case ModalType.ADD:
      modalType.value = type;
      isOpenModal.value = true;
      break;
    case ModalType.COPY:
    case ModalType.EDIT: {
      modalType.value = type;
      isOpenModal.value = true;
      modalLoading.value = true;

      const update = await getOneEventCalendar(id);
      formState.eventId = update.data?.eventId ?? '';
      formState.eventName = update.data?.eventName ?? '';

      formState.eventDate = undefined;
      if (update.data?.eventStartDate && update.data?.eventEndDate) {
        formState.eventDate = [
          update.data?.eventStartDate,
          update.data?.eventEndDate,
        ];
      }

      formState.eventTime = undefined;
      if (update.data?.eventStartTime && update.data?.eventEndTime) {
        formState.eventTime = [
          update.data?.eventStartTime,
          update.data?.eventEndTime,
        ];
      }

      formState.isRecurring = update.data?.isRecurring ?? false;
      formState.isDayOff = update.data?.isDayOff ?? false;
      formState.description = update.data?.description ?? '';

      formState.recurringDate = undefined;
      if (update.data?.recurringFrom && update.data?.recurringTo) {
        formState.recurringDate = [
          update.data?.recurringFrom,
          update.data?.recurringTo,
        ];
      }

      formState.recurringType = update.data?.recurringType ?? '';
      formState.recurringDay = update.data?.recurringDay ?? [];
      formState.recurringMonth = update.data?.recurringMonth ?? [];
      formState.recurringWeek = update.data?.recurringWeek ?? [];

      modalLoading.value = false;
      break;
    }
    case ModalType.LOG: {
      logLoading.value = true;
      openLog.value = true;
      modalType.value = type;
      logState.pageNum = 1;

      const logs = await getEventCalendarLogs(id, {
        pageSize: logState.pageSize,
        pageNum: logState.pageNum,
      });
      logData.value = logs.data?.entityChanges ?? [];

      logState.logId = id;
      logState.hasMore = logData.value.length === logState.pageSize;
      logLoading.value = false;
      break;
    }
    default:
      break;
  }
}

function onSearch() {
  handleTableChange(
    { pageSize: searchForm.value.pageSize ?? 10, current: 1 },
    { ...searchForm.value },
  );
}

function handleTableChange(
  pag: { pageSize?: number; current?: number },
  filters?: any,
) {
  run({
    pageSize: pag.pageSize,
    pageNum: pag.current,
    ...filters,
    isDayOff: filters?.isDayOff?.[0],
    isRecurring: filters?.isRecurring?.[0],
  });
}

function onReset() {
  Object.assign(formState, initFormState);
}

const renderTitle = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
    case ModalType.COPY:
      return t('add-event-calendar');
    case ModalType.EDIT:
      return t('edit-event-calendar');
    case ModalType.LOG:
      return t('log-event-calendar');
    default:
      return '';
  }
});

const renderOkText = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
    case ModalType.COPY:
      return t('button.add');
    case ModalType.EDIT:
      return t('button.update');
    default:
      return '';
  }
});

const getLabelSelect = computed(() => {
  return (value: string, list: any[]): { label: string; color: string } => {
    const item = list.find(item => item.value === value);
    return { label: item?.label, color: item?.color };
  };
});

async function handleDeleteEventCalendar(id: string) {
  try {
    const del = await deleteEventCalendar(id);
    if (del.status === ResponseStatusEnum.SUCCESS)
      message.success(del.message);
    else
      message.error(del.message);
  }
  catch (error) {
  }
  finally {
    refresh();
  }
}

const getTimeLineColor = computed(() => {
  return (action: keyof typeof TimeLineColor) => {
    return TimeLineColor[action];
  };
});

const getTimeLineTitle = computed(() => {
  return (field: string) => {
    switch (field.toLowerCase()) {
      case 'eventname':
        return t('form.name');
      case 'eventstartdate':
        return t('form.event-start-date');
      case 'eventenddate':
        return t('form.event-end-date');
      case 'eventstarttime':
        return t('form.event-start-time');
      case 'eventendtime':
        return t('form.event-end-time');
      case 'isdayoff':
        return t('form.is-day-off');
      case 'isrecurring':
        return t('form.is-recurring');
      case 'recurringfrom':
        return t('form.recurring-from');
      case 'recurringto':
        return t('form.recurring-to');
      case 'recurringtype':
        return t('form.recurring-type');
      case 'recurringday':
        return t('form.recurring-day');
      case 'recurringweek':
        return t('form.recurring-week');
      case 'recurringmonth':
        return t('form.recurring-month');
      case 'description':
        return t('form.description');
      case 'lastmodifiedtime':
        return t('log.lastmodifiedtime');
      case 'lastmodifiedby':
        return t('log.lastmodifiedby');
      default:
        return '';
    }
  };
});

const getTimeLineValue = computed(() => {
  return (field: string, value: string | boolean | number[]) => {
    switch (field.toLowerCase()) {
      case 'eventname':
      case 'description': {
        if (!value)
          return `''`;
        return value;
      }
      case 'isdayoff': {
        const item = isDayOffs.find(item => item.value === value);
        return item?.text;
      }
      case 'isrecurring': {
        const item = isDayOffs.find(item => item.value === value);
        return item?.text;
      }
      case 'recurringtype': {
        if (!value)
          return `''`;
        const item = recurringTypes.value.find(
          (item: any) => item.value === value,
        );
        return item?.label;
      }
      case 'recurringday': {
        if (!value)
          return `''`;
        const array = (value as number[]).map((val) => {
          const item = recurringDays.value.find(
            (item: any) => item.value === val,
          );
          return item?.label;
        });
        return `[${array.filter(item => item).toString()}]`;
      }
      case 'recurringweek': {
        if (!value)
          return `''`;
        const array = (value as number[]).map((val) => {
          const item = recurringWeeks.value.find(
            (item: any) => item.value === val,
          );
          return item?.label;
        });
        return `[${array.filter(item => item).toString()}]`;
      }
      case 'recurringmonth': {
        if (!value)
          return `''`;
        const array = (value as number[]).map((val) => {
          const item = recurringMonth.value.find(
            (item: any) => item.value === val,
          );
          return item?.label;
        });
        return `[${array.filter(item => item).toString()}]`;
      }
      case 'recurringfrom':
      case 'recurringto':
      case 'eventstartdate':
      case 'eventenddate':
      case 'lastmodifiedtime': {
        if (!value)
          return `''`;
        return dayjs(value as string).format('YYYY-MM-DD HH:mm:ss');
      }
      case 'eventstarttime':
      case 'eventendtime': {
        if (!value)
          return `''`;
        return value;
      }
      default:
        return value;
    }
  };
});

const showChangedItem = computed(() => {
  return (changedItem: any) => {
    if (changedItem.fieldName === 'LastModifiedBy')
      return false;
    if (changedItem.fieldName === 'OrgUid')
      return false;
    if (changedItem.fieldName === 'EventUid')
      return false;
    if (changedItem.fieldName === 'CreatedBy')
      return false;
    if (changedItem.fieldName === 'CreatedTime')
      return false;

    const valueBefore = isEmpty(changedItem.valueBefore)
      ? ''
      : changedItem.valueBefore;
    const valueAfter = isEmpty(changedItem.valueAfter)
      ? ''
      : changedItem.valueAfter;
    return valueBefore !== valueAfter;
  };
});

async function handleScroll() {
  if (!logState.hasMore)
    return;
  logState.pageNum += 1;

  const logs = await getEventCalendarLogs(logState.logId, {
    pageSize: logState.pageSize,
    pageNum: logState.pageNum,
  });
  const entityChanges = logs.data?.entityChanges ?? [];
  logData.value = [...logData.value, ...entityChanges];
  logState.hasMore = entityChanges.length === logState.pageSize;
}

function handlePaginationChange(page: number, pageSize: number) {
  searchForm.value.pageSize = pageSize;
  searchForm.value.pageNum = page;
  run(searchForm.value);
}

watch(
  () => arrivedState.bottom,
  async (value: boolean) => {
    if (value)
      await handleScroll();
  },
);

watch(
  () => formState.recurringType,
  (value?: string) => {
    switch (value) {
      case RecurringType.DAILY:
        formState.recurringDay = undefined;
        formState.recurringMonth = undefined;
        formState.recurringWeek = undefined;
        break;
      case RecurringType.WEEKLY:
        formState.recurringMonth = undefined;
        formState.recurringWeek = undefined;
        break;
      case RecurringType.MONTHLY:
        formState.recurringDay = undefined;
        formState.recurringMonth = undefined;
        break;
      case RecurringType.ANNUALLY:
        formState.recurringDay = undefined;
        formState.recurringWeek = undefined;
        break;
      default:
        break;
    }
  },
);
</script>

<template>
  <page-container>
    <a-row
      :wrap="false"
      :gutter="[12, 12]"
      class="h-[calc(100vh-165px)] flex-col"
    >
      <a-col flex="none" span="24">
        <a-row :gutter="[12, 12]">
          <a-col span="24">
            <a-row :gutter="[12, 12]">
              <a-col>
                <a-button
                  class="flex flex-items-center"
                  type="primary"
                  @click="openModal('', ModalType.ADD)"
                >
                  <PlusOutlined />
                  {{ `${t('button.new')}` }}
                </a-button>
              </a-col>
              <a-col>
                <a-input
                  v-model:value="searchForm.keyword"
                  :placeholder="t('search')"
                  style="width: 25rem"
                  allow-clear
                  @press-enter="onSearch"
                >
                  <template #prefix>
                    <SearchOutlined class="text-gray-500" />
                  </template>
                </a-input>
              </a-col>
            </a-row>
          </a-col>
          <a-col span="24">
            <a-table
              :scroll="{ x: 'max-content', y: 'calc(100vh - 275px)' }"
              :columns="columns"
              :data-source="dataSource?.eventCalendarRules"
              :loading="loading"
              :pagination="false"
              row-key="eventId"
              :row-expandable="(record: any) => record.hasChildren"
              @change="handleTableChange"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'eventDate'">
                  <div
                    v-if="record.eventStartDate && record.eventEndDate"
                    class="flex gap-2 flex-justify-center"
                  >
                    {{ record.eventStartDate }}
                    <SwapRightOutlined />
                    {{ record.eventEndDate }}
                  </div>
                </template>
                <template v-else-if="column.dataIndex === 'eventTime'">
                  <div
                    v-if="record.eventStartTime && record.eventEndTime"
                    class="flex gap-2 flex-justify-center"
                  >
                    {{ record.eventStartTime }}
                    <SwapRightOutlined />
                    {{ record.eventEndTime }}
                  </div>
                </template>
                <template v-else-if="column.dataIndex === 'isDayOff'">
                  <div class="flex flex-justify-center">
                    <a-switch
                      v-model:checked="record.isDayOff"
                      :disabled="true"
                    />
                  </div>
                </template>
                <template v-else-if="column.dataIndex === 'isRecurring'">
                  <div class="flex flex-justify-center">
                    <a-switch
                      v-model:checked="record.isRecurring"
                      :disabled="true"
                    />
                  </div>
                </template>
                <template v-else-if="column.dataIndex === 'recurringDate'">
                  <div
                    v-if="record.recurringFrom && record.recurringTo"
                    class="flex gap-2 flex-justify-center"
                  >
                    {{ record.recurringFrom }}
                    <SwapRightOutlined />
                    {{ record.recurringTo }}
                  </div>
                </template>
                <template v-else-if="column.dataIndex === 'recurringType'">
                  <div
                    v-if="record.recurringType"
                    class="flex flex-justify-center"
                  >
                    <a-tag
                      :color="
                        getLabelSelect(record.recurringType, recurringTypes)
                          .color
                      "
                    >
                      {{
                        getLabelSelect(record.recurringType, recurringTypes)
                          .label
                      }}
                    </a-tag>
                  </div>
                </template>
                <template v-else-if="column.dataIndex === 'recurringDay'">
                  <div class="flex flex-justify-center">
                    <a-tag
                      v-for="item in record.recurringDay"
                      :key="item"
                      :color="getLabelSelect(item, recurringDays).color"
                    >
                      {{ getLabelSelect(item, recurringDays).label }}
                    </a-tag>
                  </div>
                </template>
                <template v-else-if="column.dataIndex === 'recurringWeek'">
                  <div class="flex flex-justify-center">
                    <a-tag
                      v-for="item in record.recurringWeek"
                      :key="item"
                      :color="getLabelSelect(item, recurringWeeks).color"
                    >
                      {{ getLabelSelect(item, recurringWeeks).label }}
                    </a-tag>
                  </div>
                </template>
                <template v-else-if="column.dataIndex === 'recurringMonth'">
                  <div class="flex flex-justify-center">
                    <a-tag
                      v-for="item in record.recurringMonth"
                      :key="item"
                      :color="getLabelSelect(item, recurringMonth).color"
                    >
                      {{ getLabelSelect(item, recurringMonth).label }}
                    </a-tag>
                  </div>
                </template>
                <template v-else-if="column.dataIndex === 'actions'">
                  <div class="flex flex-justify-center">
                    <a-button
                      class="flex items-center justify-center"
                      type="text"
                      color="primary"
                      size="small"
                      @click="openModal(record.eventId, ModalType.EDIT)"
                    >
                      <EditOutlined />
                    </a-button>
                    <a-button
                      class="flex items-center justify-center"
                      type="text"
                      color="orange"
                      size="small"
                      @click="openModal(record.eventId, ModalType.COPY)"
                    >
                      <CopyOutlined />
                    </a-button>
                    <a-button
                      class="flex items-center justify-center"
                      type="text"
                      color="warning"
                      size="small"
                      @click="openModal(record.eventId, ModalType.LOG)"
                    >
                      <FileSearchOutlined />
                    </a-button>
                    <a-popconfirm
                      :title="t('message.delete-confirmation')"
                      @confirm="() => handleDeleteEventCalendar(record.eventId)"
                    >
                      <a-button
                        class="flex items-center justify-center"
                        type="text"
                        danger
                        size="small"
                      >
                        <DeleteOutlined />
                      </a-button>
                    </a-popconfirm>
                  </div>
                </template>
              </template>
            </a-table>
          </a-col>
        </a-row>
      </a-col>
      <a-col flex="auto" span="24">
        <div class="h-full flex items-end">
          <a-row justify="space-between" class="mt-4 w-full">
            <a-col>
              <a-pagination
                class="pagination"
                :total="pagination.total"
                :current="pagination.current"
                :page-size="pagination.pageSize"
                @change="handlePaginationChange"
              />
            </a-col>
            <a-col>
              <a-row :gutter="[12, 12]" justify="center" align="middle">
                <a-col>{{ t('show') }}</a-col>
                <a-col>
                  <a-pagination
                    class="pagination pagination-right"
                    :total="pagination.total"
                    :current="pagination.current"
                    :page-size="pagination.pageSize"
                    show-size-changer
                    :build-option-text="(props: any) => props.value"
                    @change="handlePaginationChange"
                  />
                </a-col>
                <a-col>{{ t('entries') }}</a-col>
              </a-row>
            </a-col>
          </a-row>
        </div>
      </a-col>
    </a-row>

    <a-modal
      v-model:open="isOpenModal"
      :title="renderTitle"
      width="800px"
      :footer="false"
      @cancel="onReset"
    >
      <a-card class="ant-pro-basicLayout" :loading="modalLoading">
        <a-form
          :model="formState"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
          autocomplete="off"
          @finish="onFinish"
        >
          <a-row :gutter="[12, 12]">
            <a-col span="12">
              <a-form-item
                :label="t('form.event-name')"
                name="eventName"
                :rules="[
                  { required: true, message: t('form.event-name.required') },
                ]"
              >
                <a-input
                  v-model:value="formState.eventName"
                  :placeholder="t('form.name')"
                />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item :label="t('form.event-date')" name="eventDate">
                <a-range-picker
                  v-model:value="formState.eventDate"
                  class="w-full"
                  value-format="YYYY-MM-DD"
                />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item :label="t('form.event-time')" name="eventTime">
                <a-range-picker
                  v-model:value="formState.eventTime"
                  class="w-full"
                  value-format="HH:mm:ss"
                  picker="time"
                />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item :label="t('form.is-day-off')" name="isDayOff">
                <a-switch v-model:checked="formState.isDayOff" />
              </a-form-item>
            </a-col>
            <a-col span="24">
              <a-form-item :label="t('form.description')" name="description">
                <a-textarea
                  v-model:value="formState.description"
                  :placeholder="t('form.description')"
                />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item :label="t('form.is-recurring')" name="isRecurring">
                <a-switch v-model:checked="formState.isRecurring" />
              </a-form-item>
            </a-col>
            <a-col v-if="formState.isRecurring" span="12">
              <a-form-item
                :label="t('form.recurring-date')"
                name="recurringDate"
              >
                <a-range-picker
                  v-model:value="formState.recurringDate"
                  class="w-full"
                  value-format="YYYY-MM-DD"
                />
              </a-form-item>
            </a-col>
            <a-col v-if="formState.isRecurring" span="12">
              <a-form-item
                :label="t('form.recurring-type')"
                name="recurringType"
              >
                <a-select
                  v-model:value="formState.recurringType"
                  class="w-full"
                  :placeholder="t('form.recurring-type')"
                  allow-clear
                >
                  <a-select-option
                    v-for="item in recurringTypes"
                    :key="item.value"
                    :value="item.value"
                  >
                    <a-tag :color="item.color">
                      {{ item.label }}
                    </a-tag>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              v-if="formState.recurringType === RecurringType.WEEKLY"
              span="12"
            >
              <a-form-item :label="t('form.recurring-day')" name="recurringDay">
                <a-select
                  v-model:value="formState.recurringDay"
                  class="w-full"
                  mode="multiple"
                  :placeholder="t('form.recurring-day')"
                  allow-clear
                  :options="recurringDays"
                >
                  <template #option="item">
                    <a-tag :color="item.color">
                      {{ item.label }}
                    </a-tag>
                  </template>
                  <template #tagRender="{ label, value, closable, onClose }">
                    <a-tag
                      :color="getLabelSelect(value, recurringDays).color"
                      :closable="closable"
                      @close="onClose"
                    >
                      {{ label }}
                    </a-tag>
                  </template>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              v-if="formState.recurringType === RecurringType.MONTHLY"
              span="12"
            >
              <a-form-item
                :label="t('form.recurring-week')"
                name="recurringWeek"
              >
                <a-select
                  v-model:value="formState.recurringWeek"
                  class="w-full"
                  mode="multiple"
                  :placeholder="t('form.recurring-week')"
                  allow-clear
                  :options="recurringWeeks"
                >
                  <template #option="item">
                    <a-tag :color="item.color">
                      {{ item.label }}
                    </a-tag>
                  </template>
                  <template #tagRender="{ label, value, closable, onClose }">
                    <a-tag
                      :color="getLabelSelect(value, recurringWeeks).color"
                      :closable="closable"
                      @close="onClose"
                    >
                      {{ label }}
                    </a-tag>
                  </template>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              v-if="formState.recurringType === RecurringType.ANNUALLY"
              span="12"
            >
              <a-form-item
                :label="t('form.recurring-month')"
                name="recurringMonth"
              >
                <a-select
                  v-model:value="formState.recurringMonth"
                  class="w-full"
                  mode="multiple"
                  :placeholder="t('form.recurring-month')"
                  allow-clear
                  :options="recurringMonth"
                >
                  <template #option="item">
                    <a-tag :color="item.color">
                      {{ item.label }}
                    </a-tag>
                  </template>
                  <template #tagRender="{ label, value, closable, onClose }">
                    <a-tag
                      :color="getLabelSelect(value, recurringMonth).color"
                      :closable="closable"
                      @close="onClose"
                    >
                      {{ label }}
                    </a-tag>
                  </template>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span="24" class="flex gap-2 flex-justify-end">
              <a-button v-if="modalType === ModalType.ADD" @click="onReset">
                {{ t('button.reset') }}
              </a-button>
              <a-button type="primary" html-type="submit">
                {{ renderOkText }}
              </a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </a-modal>

    <a-drawer
      :title="renderTitle"
      size="large"
      :open="openLog"
      :body-style="{ padding: 0, overflow: 'hidden' }"
      @close="openLog = false"
    >
      <a-card
        ref="logRef"
        :loading="logLoading"
        class="overflow-auto h-full log-event-calendar"
        border-style="none"
      >
        <a-timeline>
          <a-timeline-item
            v-for="item in logData"
            :key="item.auditLogId"
            :color="getTimeLineColor(item.action)"
          >
            <a-collapse ghost expand-icon-position="end">
              <a-collapse-panel key="1">
                <template #header>
                  <span color="blue">{{ item.modifiedUserName }}</span>
                  {{ t(`log.${item.action}`) }}
                  <div class="text-gray-400">
                    {{ dayjs(item.modifiedTime).format('YYYY-MM-DD HH:mm:ss') }}
                  </div>
                </template>
                <div
                  v-for="changedItem in item.changedList"
                  :key="changedItem.fieldName"
                  class="text-gray-400"
                >
                  <div
                    v-if="showChangedItem(changedItem)"
                    v-html="
                      t('log.changed', {
                        field: getTimeLineTitle(changedItem.fieldName),
                        oldValue: getTimeLineValue(
                          changedItem.fieldName,
                          changedItem.valueBefore,
                        ),
                        newValue: getTimeLineValue(
                          changedItem.fieldName,
                          changedItem.valueAfter,
                        ),
                      })
                    "
                  />
                </div>
              </a-collapse-panel>
            </a-collapse>
          </a-timeline-item>
        </a-timeline>
        <div v-if="logState.hasMore" class="flex justify-center items-center">
          <a-spin />
        </div>
      </a-card>
    </a-drawer>
  </page-container>
</template>

<style lang="less" scoped>
.log-event-calendar {
  :deep(.ant-collapse-header) {
    padding: 0;
  }
}
.pagination {
  :deep(.ant-pagination-item-active) {
    background: #f99649;
    border-color: #f99649;
    a {
      color: #fff;
    }
  }
  :deep(.ant-select-selector) {
    background: #f99649;
    border-color: #f99649 !important;
    .ant-select-selection-item {
      color: #fff;
    }
  }
  :deep(.ant-select-arrow) {
    color: #fff;
  }
}
.pagination-right {
  :deep(.ant-pagination-prev) {
    display: none;
  }
  :deep(.ant-pagination-next) {
    display: none;
  }
  :deep(.ant-pagination-item) {
    display: none;
  }
  :deep(.ant-pagination-options) {
    margin: 0;
  }
  :deep(.ant-pagination-jump-next) {
    display: none;
  }
  :deep(.ant-pagination-jump-prev) {
    display: none;
  }
}
</style>
