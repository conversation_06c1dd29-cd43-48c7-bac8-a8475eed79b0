@import "./motion.css";
html {
  --text-color: rgba(0,0,0,.85);
  --text-color-1: rgba(0,0,0,.45);
  --text-color-2: rgba(0,0,0,.2);
  --bg-color: #fff;
  --hover-color:rgba(0,0,0,.025);
  --bg-color-container: #f0f2f5;
  --c-shadow: 2px 0 8px 0 rgba(29,35,41,.05);
  --bg-color-new-container: #FFF;
  --bg-color-login: linear-gradient(90deg, rgba(21,53,84,1) 0%, rgba(14,120,211,1) 100%);
  --color-icon-calendar: #74797A;
}

html.dark{
  --text-color: rgba(229, 224, 216, 0.85);
  --text-color-1: rgba(229, 224, 216, 0.45);
  --text-color-2: rgba(229, 224, 216, 0.45);
  --bg-color: rgb(36, 37, 37);
  --hover-color:rgb(42, 44, 55);
  --bg-color-container: rgb(42, 44, 44);
  --c-shadow: rgba(13, 13, 13, 0.65) 0 2px 8px 0;
  --bg-color-new-container: #242526;
  --bg-color-login: #0D0D0D;
  --color-icon: #fff;
  --color-icon-calendar: #fff;
}

body{
  color: var(--text-color);
  background-color: var(--bg-color);
  text-rendering: optimizeLegibility;
  overflow: hidden;
}

#app, body, html{
  height: 100%;
  font-family: 'Noto Sans JP';
}

#app{
  overflow-x: hidden;
}
*, :after, :before{
  box-sizing: border-box;
}


.ant-picker-dropdown .ant-picker-time-panel{
  min-width: 150px;
}
.ant-picker-dropdown .ant-picker-time-panel li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner{
  text-align: center;
  padding: 0;
  width: 100%;
}

tr.ant-table-measure-row{
  visibility: collapse;
}

/* Custom th in calender in calendar page */
.ant-picker-calendar.ant-picker-calendar-full .ant-picker-panel .ant-picker-body th {
  height: auto;
  padding-inline-end: 12px;
  padding-bottom: 4px;
  line-height: 24px;
  text-align: center;
  font-weight: '500';
  font-size: 16px;
  color: #256CB5;
}

/* Custom time picker in dashboard page */
.dashboard-time-picker .ant-picker-input >input[disabled] {
  color: #1570EF;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5rem;
}

.dashboard-time-picker .ant-picker-input >input {
  font-size: 1rem;
  color: #1570EF;
}

/* Custom tag in project page */
.project-tag .ant-tag-close-icon {
  color: #374957;
  font-size: 11px;
}

/* Custom radio in project page */
.project-radio .ant-radio-wrapper .ant-radio-inner {
  box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px;
  width: 20px;
  height: 20px;
}

/* Custom pagination in project page */
.project-pagination .ant-pagination-item-active {
  background-color: #F99649;
  border-color: #F99649;
}
.project-pagination .ant-pagination-item-active a {
  color: white;
}

.project-select-page-size .ant-select:not(.ant-select-customize-input) .ant-select-selector {
  background-color: #F99649;
  color: white;
}

.icon-svg path{
  fill: var(--color-icon);
}

.icon-svg-calendar path{
  fill: var(--color-icon-calendar);
}

/* Custom checkbox in role page */
.role-custom-checkbox .ant-checkbox .ant-checkbox-inner {
  box-shadow: rgba(0, 0, 0, 0.12) 0px 1px 3px, rgba(0, 0, 0, 0.24) 0px 1px 2px;
}


/* Custom menu in function menu */
.functionMenu .ant-menu-light{
  background-color: #1B466F;
  color: #fff;
}

.functionMenu .ant-pro-basicLayout .ant-menu-light{
  color: #fff;
  background-color: #1B466F;
}

.functionMenu .ant-menu .ant-menu-item{
  border-radius: 0px !important;
  width: 100%;
}

.functionMenu .ant-menu-inline.ant-menu-root .ant-menu-submenu-title {
  border-radius: 0px !important;
  height: 52px;
  width: 100%;
}
.functionMenu .ant-menu-light .ant-menu-submenu-selected >.ant-menu-submenu-title{
  background: #1C4771!important;
  color: #fff;
}

.functionMenu .ant-menu-light .ant-menu-item-selected{
  background: #24598e !important;
  color: #fff;
  border-radius: 0 !important;
}

.functionMenu .ant-menu-light .ant-menu-item:hover:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected){
  color: #F99649 !important;
}

.functionMenu .ant-menu-light .ant-menu-submenu-title:hover:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected){
  color: #F99649 !important;
}

.functionMenu .ant-menu-item-selected::before {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  width: 6px;
  height: 100%;
  background-color: #F99649;
}

.functionMenu .ant-menu-inline .ant-menu-item {
  margin-left: 0px !important;
  margin-right: 0px !important;
  height: 52px!important;
}

.functionMenu .ant-pro-basicLayout .ant-pro-sider-menu{
  font-size: 20px ;
  line-height: 23px;
}

.functionMenu .ant-pro-basicLayout {
  .ant-menu {
    .ant-menu-item-icon , &.ant-menu-inline-collapsed .ant-menu-item-icon{
      font-size: 18px;
    }
  }
}

.functionMenu .ant-menu-submenu-popup {
  .ant-menu-sub {
    border-radius: 0;
  }

  .ant-menu-vertical >.ant-menu-item {
    margin: 0;
    width: 100%;
  }
}

.functionMenu .ant-pro-sider-collapsed-button{
  background-color: #153554;
}

 /* Custom n-carousel */
.n-carousel .n-carousel__dots.n-carousel__dots--dot .n-carousel__dot.n-carousel__dot--active {
  background-color: orange;
}

.n-carousel .n-carousel__dots.n-carousel__dots--dot .n-carousel__dot {
  background-color: gray;
}

