import dayjs from 'dayjs'
import { generate } from '@pdfme/generator'
import { table } from '@pdfme/schemas';
import type { Font } from '@pdfme/common'
import type { ProjectScheduleItem } from '~@/api/company/schedule'

async function loadFont(): Promise<Font> {
  const res = await fetch('/fonts/NotoSansJP-Regular.ttf')
  const buffer = await res.arrayBuffer()
  return {
    'NotoSansJP': {
      'data': new Uint8Array(buffer),
      'fallback': true,
      'subset': false,
    },
  }
}

export function useFileDownloader() {
  interface scheduleType {
    projectName: string
    plannedWorkload: number
    actualWorkload: number
    employees: Array<{
      name: string
      isOutsource: boolean
    }>
  }

  async function exportSchedulePdf(scheduleData: ProjectScheduleItem[]) {
    try {
      const font = await loadFont()
      // Process data by date (same logic as original)
      const scheduleTemplate: any = {
        'basePdf': {
          'width': 210,
          'height': 297,
          'padding': [20, 20, 20, 20],
        },
        'pdfmeVersion': '5.0.0',
        'schemas': [
          [
            {
              'name': 'scheduleTable',
              'type': 'table',
              'position': {
                'x': 28.92,
                'y': 51.36,
              },
              'width': 150,
              'height': 57.5184,
              'content': '[["Alice","New York","Alice is a freelance web designer and developer"],["Bob","Paris","Bob is a freelance illustrator and graphic designer"]]',
              'showHead': true,
              'head': ['プロジェクト名', '作業量 (予定/実績)', '従業員'],
              'headWidthPercentages': [30, 30, 40],
              'tableStyles': {
                'borderWidth': 0.3,
                'borderColor': '#000000',
              },
              'headStyles': {
                'fontName': 'NotoSansJP',
                'fontSize': 13,
                'characterSpacing': 0,
                'alignment': 'left',
                'verticalAlignment': 'middle',
                'lineHeight': 1,
                'fontColor': '#ffffff',
                'borderColor': '',
                'backgroundColor': '#2980ba',
                'borderWidth': {
                  'top': 0,
                  'right': 0,
                  'bottom': 0,
                  'left': 0,
                },
                'padding': {
                  'top': 5,
                  'right': 5,
                  'bottom': 5,
                  'left': 5,
                },
              },
              'bodyStyles': {
                'fontName': 'NotoSansJP',
                'fontSize': 13,
                'characterSpacing': 0,
                'alignment': 'left',
                'verticalAlignment': 'middle',
                'lineHeight': 1,
                'fontColor': '#000000',
                'borderColor': '#888888',
                'backgroundColor': '',
                'alternateBackgroundColor': '#f5f5f5',
                'borderWidth': {
                  'top': 0.1,
                  'right': 0.1,
                  'bottom': 0.1,
                  'left': 0.1,
                },
                'padding': {
                  'top': 5,
                  'right': 5,
                  'bottom': 5,
                  'left': 5,
                },
              },
              'columnStyles': {},
              'required': false,
              'readOnly': false,
            },
          ],
        ],
      }
      const dataByDate = scheduleData.reduce((acc, project) => {
        // Process schedules
        project.schedules?.forEach((schedule) => {
          if (!acc[schedule.workingDate]) {
            acc[schedule.workingDate] = {
              projectName: project.projectName,
              plannedWorkload: schedule.totalPlannedWorkload,
              actualWorkload: schedule.totalActualWorkload,
              employees: [],
            }
          }
          else {
            acc[schedule.workingDate].plannedWorkload += schedule.totalPlannedWorkload
            acc[schedule.workingDate].actualWorkload += schedule.totalActualWorkload
          }
        })

        // Process shifts
        project.shifts?.forEach((shift) => {
          if (!acc[shift.workingDate]) {
            acc[shift.workingDate] = {
              projectName: project.projectName,
              plannedWorkload: 0,
              actualWorkload: 0,
              employees: [],
            }
          }
          acc[shift.workingDate].employees.push({
            name: shift.employeeName,
            isOutsource: false,
          })
        })

        // Process outsource shifts
        project.outsourceShifts?.forEach((shift) => {
          if (!acc[shift.workingDate]) {
            acc[shift.workingDate] = {
              projectName: project.projectName,
              plannedWorkload: 0,
              actualWorkload: 0,
              employees: [],
            }
          }
          acc[shift.workingDate].employees.push({
            name: shift.outSourceName,
            isOutsource: true,
          })
        })

        return acc
      }, {} as Record<string, scheduleType>)

      // Create PDF pages for each date
      let contentData = Object.entries(dataByDate).map(([_, schedule]) => {
        // Format employee list with Japanese labels
        const employeeList = schedule.employees
          .map(emp => emp.isOutsource ? `${emp.name} (アウトソース)` : emp.name)
          .join('\n')

        return [
          schedule.projectName,
          `${schedule.plannedWorkload}/${schedule.actualWorkload}`,
          employeeList,
        ]
      })

      // Handle empty inputs with fallback data
      if (contentData.length === 0) {
        contentData = [
          ['データなし', '0/0', 'データが見つかりません'],
        ]
      }

      // Input data
      const inputs = [
        {
          'scheduleTable': contentData,
        },
      ]

      // Generate PDF with local font
      const pdf = await generate({
        options: {
          font: {
            NotoSansJP: font.NotoSansJP,
          },
        },
        template: scheduleTemplate,
        inputs,
        plugins: { Table: table },
      });

      // Generate PDF with Japanese font support
      // const pdf = await generate({
      //   template: scheduleTemplate,
      //   inputs,
      //   options: {
      //     font: {
      //       // Built-in Japanese font support
      //       NotoSansJP: font,
      //     },
      //   },
      // })

      // Download the PDF
      const blob = new Blob([pdf.buffer], { type: 'application/pdf' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `Schedule_${dayjs().format('YYYY-MM-DD')}.pdf`
      link.click()

      // Cleanup
      URL.revokeObjectURL(url)

      console.log('✅ PDF generated successfully with pdfme')
    }
    catch (error) {
      console.error('❌ PDF generation failed:', error)
      console.error('PDF生成に失敗しました。再度お試しください。')
    }
  }

  return {
    exportSchedulePdf,
  }
}
