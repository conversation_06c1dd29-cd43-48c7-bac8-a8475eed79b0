<script setup lang="ts">
import { DownloadOutlined } from '@ant-design/icons-vue'

defineProps({
  text: {
    type: String,
    default: 'Download',
  },
  onClick: {
    type: Function as PropType<(event: MouseEvent) => void>,
    default: () => {},
  },
})
</script>

<template>
  <button
    class="download-btn flex items-center gap-1 bg-gradient-to-r from-blue-500 to-blue-600 border-none shadow-md hover:shadow-lg transition-all duration-300"
    @click="onClick"
  >
    <span class="font-medium">{{ text }}</span>
    <DownloadOutlined class="text-white animate-bounce-subtle" />
  </button>
</template>

<style scoped>
.download-btn {
  color: white;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  outline: none;
  border: none;
}

.download-btn:hover {
  transform: translateY(-1px);
  background: linear-gradient(to right, #3b82f6, #2563eb);
}

.download-btn:active {
  transform: translateY(1px);
}

.download-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: 0.5s;
}

.download-btn:hover::before {
  left: 100%;
}

.animate-bounce-subtle {
  animation: bounce-subtle 2s infinite;
}

@keyframes bounce-subtle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}
</style>
