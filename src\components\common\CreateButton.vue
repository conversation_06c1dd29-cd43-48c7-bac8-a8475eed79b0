<script setup lang="ts">
import { PlusOutlined } from '@ant-design/icons-vue'

defineProps({
  text: {
    type: String,
    default: 'Create',
  },
  onClick: {
    type: Function as PropType<(event: MouseEvent) => void>,
    default: () => {},
  },
})
</script>

<template>
  <button
    class="create-btn flex items-center gap-1 bg-gradient-to-r from-green-500 to-green-600 border-none shadow-md hover:shadow-lg transition-all duration-300"
    @click="onClick"
  >
    <span class="font-medium">{{ text }}</span>
    <PlusOutlined class="text-white" />
  </button>
</template>

<style scoped>
.create-btn {
  color: white;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  outline: none;
  border: none;
}

.create-btn:hover {
  transform: translateY(-1px);
  background: linear-gradient(to right, #22c55e, #16a34a);
}

.create-btn:active {
  transform: translateY(1px);
}

.create-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: 0.5s;
}

.create-btn:hover::before {
  left: 100%;
}
</style>
