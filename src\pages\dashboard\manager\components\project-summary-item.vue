<script lang="ts" setup>
import { Progress } from 'ant-design-vue'
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  LeftOutlined,
  MinusCircleOutlined,
  RightOutlined,
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import { getOneProjectSummaryApi } from '~@/api/company/project'
import type { ConstructionProgress, ProjectSummaryItem } from '~@/api/company/project'
import { useAvatarStore } from '~@/stores/avatar'

const props = defineProps({
  projectSummaryItem: {
    type: Object as () => ProjectSummaryItem,
    required: true,
  },
  searchDate: {
    type: Object as () => dayjs.Dayjs,
    required: true,
  },
  isShowProductionCost: {
    type: Boolean,
    required: false,
    default: false,
  },
})

const { t } = useI18n()
const searchDate = ref<dayjs.Dayjs>(dayjs())
const isLoading = ref(false)
const avatarStore = useAvatarStore()
const router = useRouter()
const REDIRECT_NAME = 'ProjectCostSummary'

const isRouteExist = computed(() => {
  return router.getRoutes().some(route => route.name === REDIRECT_NAME)
})

async function onSearch() {
  isLoading.value = true
  try {
    const res = await getOneProjectSummaryApi(
      props.projectSummaryItem.projectId,
      {
        dateFrom: searchDate.value.format('YYYY-MM-DD'),
        dateTo: searchDate.value.format('YYYY-MM-DD'),
      },
    )
    if (res.data) {
      Object.assign(
        props.projectSummaryItem,
        res.data,
      )
    }
  }
  catch (error) {
  }
  finally {
    isLoading.value = false
  }
}

const overall = computed(() => {
  const value
    = props.projectSummaryItem.projectProgress.constructionsProgress.reduce(
      (acc: number, item: ConstructionProgress) => {
        return acc + item.disbursementProgressRatio
      },
      0,
    )
  return value
})

function getStatusIcon(isCheckedIn: boolean, isRequestedOff: boolean) {
  const status = isCheckedIn ? 'online' : isRequestedOff ? 'error' : 'away'
  switch (status) {
    case 'online':
      return h(CheckCircleOutlined, { style: { color: '#52c41a' } })
    case 'error':
      return h(CloseCircleOutlined, { style: { color: '#f5222d' } })
    case 'away':
      return h(MinusCircleOutlined, { style: { color: '#faad14' } })
    default:
      return h(MinusCircleOutlined, { style: { color: '#d9d9d9' } })
  }
}

watch(() => props.searchDate, async (newDate) => {
  searchDate.value = newDate
})
</script>

<template>
  <a-spin :spinning="isLoading">
    <div class="max-w-2xl bg-white rounded-lg shadow-md p-6 transition-shadow duration-300">
      <!-- Project Header -->
      <div class="mb-6">
        <div class="flex items-center justify-between">
          <div class="text-lg font-medium mb-2">
            {{ projectSummaryItem?.projectCode }} -
            {{ projectSummaryItem?.projectName }}
          </div>
          <div class="flex items-center text-gray-500">
            <LeftOutlined
              class="flex justify-center w-6 h-6 bg-white rounded-full"
              @click="
                searchDate = dayjs(searchDate).subtract(1, 'day');
                onSearch();
              "
            />
            <a-date-picker
              v-model:value="searchDate"
              :allow-clear="false"
              :format="(value: dayjs.Dayjs) => `${value.format('YYYY-MM-DD')}`"
              class="search-date"
              @change="onSearch"
            />
            <RightOutlined
              class="flex justify-center w-6 h-6 bg-white rounded-full"
              @click="
                searchDate = dayjs(searchDate).add(1, 'day');
                onSearch();
              "
            />
          </div>
        </div>
        <div class="flex gap-x-2">
          <CarbonAddress />
          <p class="text-gray-600 text-sm">
            {{ projectSummaryItem?.address }}
          </p>
        </div>
      </div>

      <!-- Project Details -->
      <div class="grid grid-cols-2 gap-4 mb-6">
        <div>
          <p class="text-gray-600 mb-1">
            <span> {{ t('prime-contractor') }}:</span>
            <span> {{ projectSummaryItem?.contractorName }} </span>
          </p>
          <p class="text-gray-600 mb-1">
            <span> {{ t('person-in-charge') }}:</span>
            <span>{{ projectSummaryItem?.primaryManager }}</span>
          </p>
          <div class="text-gray-600">
            <span class="mr-2">{{ t('expected-date') }}:</span>
            <span
              v-if="
                projectSummaryItem?.expectedStartDate
                  && projectSummaryItem?.expectedEndDate
              "
            >{{ projectSummaryItem?.expectedStartDate }} →
              {{ projectSummaryItem?.expectedEndDate }}</span>
          </div>
          <div class="flex flex-col text-gray-600">
            <span class="mr-2">{{ t('actual-date') }}:</span>
            <span
              v-if="
                projectSummaryItem?.actualStartDate
                  && projectSummaryItem?.actualEndDate
              "
            >{{ projectSummaryItem?.actualStartDate }} →
              {{ projectSummaryItem?.actualEndDate }}</span>
          </div>
        </div>

        <!-- Progress Bars -->
        <div>
          <div class="">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">{{
                t('main-construction')
              }}</span>
            </div>
            <Progress
              :percent="
                projectSummaryItem?.projectProgress?.constructionsProgress[0]
                  ?.disbursementProgressRatio ?? 0
              "
              size="small"
            />
          </div>
          <div class="">
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-600">{{
                t('sub-construction')
              }}</span>
            </div>
            <Progress
              :percent="
                projectSummaryItem.projectProgress.constructionsProgress[1]
                  ?.disbursementProgressRatio ?? 0
              "
              size="small"
            />
          </div>
          <div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">{{ t('overall') }}</span>
            </div>
            <Progress :percent="overall" size="small" />
          </div>
        </div>
      </div>

      <!-- Worksite Attendance -->
      <div class="grid grid-cols-2 gap-4">
        <div class="border border-gray-300 border-solid rounded-lg p-4">
          <h3 class="text-lg font-medium mb-4">
            {{ t('worksite-attendance') }}
          </h3>
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center gap-x-2">
              <span class="text-gray-600">{{
                projectSummaryItem?.projectAttendance.presignedWorkload
              }}</span>
              <span class="text-gray-600">{{ t('presigned') }}</span>
            </div>
            <div>
              <span class="text-gray-600">{{
                projectSummaryItem?.projectAttendance.actualWorkload
              }}</span>
            </div>
          </div>
          <div class="space-y-2">
            <div
              v-for="(member, index) in projectSummaryItem.projectAttendance
                .employeeAttendances"
              :key="index"
              class="flex items-center justify-between"
            >
              <div class="flex items-center">
                <a-avatar size="small" class="bg-blue-500 mr-2" :src="avatarStore.getImageSrcByEmployeeId(member.employeeId) ?? ''" />
                <span class="text-gray-600">{{ member.employeeName }}</span>
              </div>
              <component
                :is="getStatusIcon(member.isCheckedIn, member.isRequestedOff)"
              />
            </div>
          </div>
        </div>

        <!-- Cost Section -->
        <div class="border border-gray-300 border-solid rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <span class="text-lg font-medium mb-4">
                {{ t('cost') }}
              </span>
            </div>
            <div class="cursor-pointer">
              <router-link
                v-if="isRouteExist"
                :to="{
                  name: REDIRECT_NAME,
                  params: {
                    id: projectSummaryItem.projectId,
                  },
                }"
              >
                <CarbonRightCircle />
              </router-link>
            </div>
          </div>
          <div class="space-y-2">
            <div class="flex items-center justify-between font-medium">
              <span>{{ t('project.sum') }}</span>
              <span v-if="isShowProductionCost">{{ projectSummaryItem.projectCosts.totalCost }}</span>
              <span v-else>{{ projectSummaryItem.projectCosts.totalAvgCost }}</span>
            </div>
            <div
              v-for="(cost, index) in projectSummaryItem.projectCosts
                .rootCategorizedCosts"
              :key="index"
            >
              <div
                v-if="cost.totalAvgAmount || cost.categoryCode === 'EMPLOYEE' || cost.categoryCode === 'OUTSOURCE'"
                class="flex items-center justify-between text-gray-600"
              >
                <span>{{ cost.categoryName }}</span>
                <span v-if="isShowProductionCost">{{ cost?.totalAmount ?? 0 }}</span>
                <span v-else>{{ cost?.totalAvgAmount ?? 0 }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<style lang="less" scoped>
.search-date {
  border: none;
  background: none;
  box-shadow: none;

  :deep(.ant-picker-suffix) {
    display: none;
  }
  :deep(input) {
    cursor: pointer;
    width: 82px;
  }
}
</style>
