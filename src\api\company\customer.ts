import { UploadFile } from 'ant-design-vue';
import { useOrg } from '~@/composables/org';
import qs from 'qs';

export interface CustomerItemResponse {
  items: CustomerItem[];
  pageIndex: number;
  pageSize: number;
}

interface ProjectItem {
  projectId: string;
  projectCode: string;
  projectName: string;
  address: string;
}

export interface CustomerItem {
  logo?: UploadFile;
  logoUrl?: string;
  customerId: string;
  customerCode: string;
  customerName: string;
  customerSubName?: string;
  customerTypeCode?: string;
  customerTypeName?: string;
  description?: string;
  corporateNumber?: string;
  address?: string;
  phoneNumber?: string;
  email?: string;
  contactPerson: {
    name?: string;
    phoneNumber?: string;
    email?: string;
  };
  projects?: ProjectItem[];
}

interface CustomerLogsResponse {
  entityChanges: CustomerLogItem[];
  pageNum: number;
  pageSize: number;
  totalRecords: number;
}

export interface CustomerChangedListItem {
  fieldName: string;
  valueAfter: string | number | boolean | number[] | string[];
  valueBefore: string | number | boolean | number[] | string[];
}

export interface CustomerLogItem {
  action: string;
  auditLogId: string;
  changedList: CustomerChangedListItem[];
  entityId: string;
  description: string;
  modifiedTime: string;
  modifiedUserId: string;
  modifiedUserName: string;
}

export interface GetCustomerParams {
  keyword?: string;
  pageNum?: number;
  pageSize?: number;
  customerType?: string[];
}

interface GetCustomerLogsParams {
  dateFrom?: string;
  dateTo?: string;
  action?: string;
  pageNum?: number;
  pageSize?: number;
}

export async function getCustomer(params?: GetCustomerParams) {
  return useGet<CustomerItemResponse>('v1/customer', params, {
    paramsSerializer: (params) => qs.stringify(params, { indices: false }),
  });
}

export async function getCustomerItem(params?: GetCustomerParams) {
  return useGet<CustomerItemResponse>('v1/customer', params);
}

export function getCustomerLogo(id: string): string {
  const host = import.meta.env.VITE_APP_BASE_API ?? '';
  return `${host}/v1/customer/${id}/logo?orgId=${useOrg().value}`;
}

export async function getOneCustomer(id: string, params?: GetCustomerParams) {
  return useGet<CustomerItem>(`v1/customer/${id}`, params);
}

export async function createCustomer(data: Partial<CustomerItem>) {
  return usePost('v1/customer', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

export async function updateCustomer(id: string, data: Partial<CustomerItem>) {
  return usePut(`v1/customer/${id}`, data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

export async function deleteCustomer(id: string) {
  return useDelete(`v1/customer/${id}`);
}

export async function getCustomerLogs(
  id: string,
  params?: GetCustomerLogsParams
) {
  return useGet<CustomerLogsResponse>(`v1/customer/${id}/logs`, params);
}
