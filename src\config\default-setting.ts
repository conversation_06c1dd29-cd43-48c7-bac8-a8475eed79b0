import type { LayoutSetting } from '~@/stores/app'

export default {
  title: 'Kantoku Admin',
  theme: 'light',
  logo: '/logo.png',
  collapsed: false,
  drawerVisible: false,
  colorPrimary: '#0E78D3',
  layout: 'mix',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixedSider: true,
  splitMenus: false,
  header: true,
  menu: true,
  watermark: false,
  menuHeader: true,
  footer: false,
  colorWeak: false,
  multiTab: false,
  multiTabFixed: false,
  keepAlive: true,
  accordionMode: false,
  leftCollapsed: true,
  compactAlgorithm: false,
  headerHeight: 64,
  copyright: 'Kantoku Team 2023',
  animationName: 'slide-fadein-up',
} as LayoutSetting

export const animationNameList = [
  {
    label: 'None',
    value: 'none',
  },
  {
    label: 'Fadein Up',
    value: 'slide-fadein-up',
  },
  {
    label: 'Fadein Right',
    value: 'slide-fadein-right',
  },
  {
    label: 'Zoom Fadein',
    value: 'zoom-fadein',
  },
  {
    label: 'Fadein',
    value: 'fadein',
  },
]
export type AnimationNameValueType =
  | 'none'
  | 'slide-fadein-up'
  | 'slide-fadein-right'
  | 'zoom-fadein'
  | 'fadein'
