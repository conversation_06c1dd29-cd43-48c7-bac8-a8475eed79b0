<script lang="ts" setup>
import type { UnwrapRef } from 'vue'
import { computed, reactive, ref } from 'vue'
import {
  DeleteOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue'
import type { UploadProps } from 'ant-design-vue'
import { usePagination } from 'vue-request'
import type { ColumnsType } from 'ant-design-vue/es/table'
import type { SelectValue } from 'ant-design-vue/es/select'
import type { InputCost, InputCostItem, InputCostItemPostParams, InputCostItemPutParams } from '~@/api/invoice'
import { createInputCostApi } from '~@/api/invoice'
import type { ItemFilterRequest } from '~@/api/item'
import { getItemListApi } from '~@/api/item'
import type { ConstructionCostItem } from '~@/api/construction-cost'
import { getPaymentType } from '~@/api/company/payment-type'
import { getConstructionByProjectIdApi } from '~@/api/construction'
import type { GetVendorParams } from '~@/api/company/vendor'
import { getVendor } from '~@/api/company/vendor'
import type { QueryParams } from '~@/api/common-params'
import { getProjectComboApi } from '~@/api/company/project'
import { getEntryType } from '~@/api/company/entry-type'

// Props and Emits
const props = defineProps<{
  openModal: boolean
}>()

const emit = defineEmits<{
  (event: 'update:openModal', value: boolean): void
}>()

const isVisible = useVModel(props, 'openModal', emit)

// State
const { t } = useI18n()
const messageNotify = useMessage()

const inputCostItems = ref<InputCostItem[]>([])

// Sample items data
const fileList = ref<UploadProps['fileList']>([])

const searchText = ref('')
const editingKey = ref('')
const showAddItemModal = ref(false)
const showDeleteModal = ref(false)
const itemToDelete = ref<InputCostItem | null>(null)
const currentInputCostId = ref<string>()

const loading = ref(false)
const previewVisible = ref(false)
const previewImage = ref<string | undefined>(undefined)
const uploadedImage: string[] = []
const previewTitle = ref('')

const editableInvoice = reactive<Partial<InputCost>>({})

const inputCostItemDataSource = ref<InputCostItemPostParams[]>([])

// Table columns
const columns = reactive<ColumnsType<InputCostItem>>([
  {
    title: '#',
    dataIndex: 'index',
    key: 'index',
    width: 50,
    align: 'center',
    customRender: ({ index }) => index + 1,
  },
  {
    title: t('item-name'),
    dataIndex: 'itemName',
    key: 'itemName',
    align: 'center',
  },
  {
    title: t('unit'),
    dataIndex: 'unit',
    key: 'unit',
    sorter: (a: any, b: any) => a.unit - b.unit,
    align: 'center',
  },
  {
    title: t('quantity'),
    dataIndex: 'quantity',
    key: 'quantity',
    align: 'center',
  },
  {
    title: t('price'),
    dataIndex: 'price',
    key: 'price',
    sorter: (a: any, b: any) => a.price - b.price,
    align: 'center',
  },
  {
    title: t('tax-rate'),
    dataIndex: 'taxRate',
    key: 'taxRate',
    align: 'center',
  },
  // {
  //   title: t('update-time'),
  //   dataIndex: 'updateTime',
  //   key: 'updateTime',
  //   sorter: (a: InputCostItem, b: InputCostItem) => {
  //     if (!a.updateTime || !b.updateTime)
  //       return 0

  //     return a.updateTime.localeCompare(b.updateTime)
  //   },
  //   sortOrder: 'descend',
  //   align: 'center',
  // },
  {
    title: t('action'),
    dataIndex: 'action',
    key: 'action',
    align: 'center',
  },
])

// Editable data store
const editableData: UnwrapRef<Record<string, InputCostItem>> = reactive({})

// New item form
const newInputCostItem = reactive<InputCostItemPutParams>({
  transactionDate: '',
  item: {
    itemId: '',
    itemName: '',
  },
  unit: '',
  quantity: 0,
  price: 0,
  taxRate: 0,
  totalNonTaxed: 0,
  totalTaxed: 0,
  description: '',
  isDeleted: false,
})

function handleCancel() {
  isVisible.value = false
}

function refreshInvoice() {
  // Reset states
  editingKey.value = ''
  searchText.value = ''
  editableInvoice.title = ''
  editableInvoice.description = ''
  editableInvoice.totalAmount = 0
  editableInvoice.issueDate = ''
  editableInvoice.paymentDate = ''
  editableInvoice.originalNumber = ''
  editableInvoice.entryTypeId = ''
  editableInvoice.paymentTypeId = ''
  editableInvoice.vendorId = ''
  editableInvoice.vendorName = ''
  editableInvoice.constructionId = ''
  inputCostItems.value = []
  fileList.value = []
  previewImage.value = undefined
  inputCostItemDataSource.value = []
}

function handleCreate() {
  createInputCost()
  refreshInvoice()
}

// function handleUpdate() {
//   updateInputCost()
// }

// function handleCreateNewInvoice() {
// }

// Table row editing
// function editRow(record: InputCostItem) {
//   editingKey.value = record.inputCostItemId
//   editableData[record.inputCostItemId] = { ...record }
// }

// function cancelEdit() {
//   editingKey.value = ''
// }

// Delete item
// function showDeleteConfirm(record: InputCostItem) {
//   itemToDelete.value = record
//   showDeleteModal.value = true
// }

function handleDelete(index: number) {
  inputCostItemDataSource.value.splice(index, 1)
}
function confirmDelete() {
  if (!itemToDelete.value)
    return

  const key = itemToDelete.value.inputCostItemId
  inputCostItems.value = inputCostItems.value.filter(item => item.inputCostItemId !== key)

  // Reset states
  showDeleteModal.value = false
  itemToDelete.value = null

  // Show success message
  messageNotify.success('Item deleted successfully')
}

// Add new item
function calculateNewItemPrice() {
  if (!newInputCostItem.price || !newInputCostItem.quantity || !newInputCostItem.taxRate)
    return
  const subtotal = newInputCostItem.price * newInputCostItem.quantity
  newInputCostItem.totalTaxed = subtotal * (newInputCostItem.taxRate / 100)
  newInputCostItem.totalNonTaxed = subtotal
}

function getBase64(img: Blob, callback: (base64Url: string) => void) {
  const reader = new FileReader()
  reader.addEventListener('load', () => callback(reader.result as string))
  reader.readAsDataURL(img)
}

// function handleCancelPreview() {
//   previewVisible.value = false
//   previewTitle.value = ''
// }

async function handlePreview(file: any) {
  getBase64(file.originFileObj, (base64Url: string) => {
    previewImage.value = base64Url
    previewVisible.value = true
    previewTitle.value = file.name
  })
}

function handleRemove(file: any) {
  getBase64(file.originFileObj, (base64Url: string) => {
    uploadedImage.splice(uploadedImage.indexOf(base64Url), 1)
    if (previewImage.value === base64Url)
      previewImage.value = uploadedImage[uploadedImage.length - 1]
  })
}

function beforeUpload(file: File) {
  loading.value = true
  if (file?.size / 1024 / 1024 > 1) {
    messageNotify.error('Image must be smaller than 1MB')
    return false
  }
  getBase64(file, (base64Url: string) => {
    previewImage.value = base64Url
    uploadedImage.push(base64Url)
    loading.value = false
  })
  return false
}

// const isLoading = ref(false)

// async function refreshInputCost(inputCostId?: string) {
//   if (!inputCostId)
//     return
//   const { data, status } = await getInputCostByIdApi(inputCostId)
//   if (status === 200) {
//     invoiceInfo.value = data as InputCost
//     inputCostItems.value = data?.inputCostItems as InputCostItem[]
//   }
//   else {
//     console.error(data)
//   }
// }

// async function createInputCostItem(inputCostItem: InputCostItemPutParams) {
//   if (isLoading.value || !invoiceInfo.value?.inputCostId || !invoiceInfo.value?.inputCostId)
//     return
//   isLoading.value = true
//   const inputCostId = invoiceInfo.value?.inputCostId
//   const params: InputCostPutParams = {
//     inputCostItems: JSON.stringify(inputCostItem),
//   }

//   // const inputCostId = '01957dcc-2a5d-7081-8cdc-52d3a10c6733'
//   const { data, status, message } = await updateInputCostApi(inputCostId, params)
//   if (status === 200) {
//     await refreshInputCost(data?.inputCostId)
//     showAddItemModal.value = false
//     messageNotify.success(message)
//     refreshInputCost(inputCostId)
//   }
//   else {
//     messageNotify.error(message)
//   }

//   isLoading.value = false
// }

const itemFilters = ref<ItemFilterRequest>({
  pageNum: 1,
  pageSize: 100,
})

const {
  data: itemsData,
} = usePagination(
  queryItems,
  {
    defaultParams: [itemFilters.value],
    pagination: {
      currentKey: 'pageNum',
      pageSizeKey: 'pageSize',
      totalKey: 'totalRecords',
    },
  },
)

const itemOptions = computed(() => itemsData.value?.items ?? [])

async function handleCreateInputCostItem() {
  // Validate form
  if (!newInputCostItem?.item?.itemId) {
    messageNotify.error('Please fill in all required fields')
    return
  }

  // Create new item
  const newItemObject: InputCostItemPostParams = {
    item: {
      itemId: newInputCostItem.item.itemId,
    },
    unit: newInputCostItem.unit,
    quantity: newInputCostItem.quantity,
    taxRate: newInputCostItem.taxRate,
    totalNonTaxed: newInputCostItem.totalNonTaxed,
    totalTaxed: newInputCostItem.totalTaxed,
    price: newInputCostItem.price,
    description: newInputCostItem.description,
    transactionDate: newInputCostItem.transactionDate,
  }

  const itemName = itemOptions.value.find(item => item.itemId === newItemObject?.item?.itemId)?.itemName
  newItemObject.itemName = itemName || ''
  inputCostItemDataSource.value.push(newItemObject)
  refreshInputCostItem()
}

// const vendorParams = ref<GetVendorParams>({
//   pageNum: 1,
//   pageSize: 100,
// })

async function queryItems(itemFilters: ItemFilterRequest) {
  const response = await getItemListApi(itemFilters)
  return response.data
}

// async function queryVendor(vendorFilters: GetVendorParams) {
//   const response = await getVendor(vendorFilters)
//   return response.data
// }

// const vendors = computed(() => vendorData.value?.items ?? [])

const projectParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 50,
})
async function queryProject(params: QueryParams) {
  const { data } = await getProjectComboApi(params)
  return data
}
const {
  data: projectData,
} = usePagination(queryProject, {
  defaultParams: [projectParams],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})
const projectOptions = computed(() => {
  return projectData.value?.items || []
})

const entryTypeParams = reactive<any>({
  pageNum: 1,
  pageSize: 50,
})
async function queryEntryType(params: any) {
  const { data } = await getEntryType(params)
  return data
}
const {
  data: entryTypeData,
} = usePagination(queryEntryType, {
  defaultParams: [entryTypeParams],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})
const entryTypeOptions = computed(() => entryTypeData.value?.entryTypes ?? [])

const paymentTypeParams = reactive<any>({
  pageNum: 1,
  pageSize: 50,
})
async function queryPaymentType(params: any) {
  const { data } = await getPaymentType(params)
  return data
}
const {
  data: paymentTypeData,
} = usePagination(queryPaymentType, {
  defaultParams: [paymentTypeParams],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})
const paymentTypeOptions = computed(() => paymentTypeData.value?.items ?? [])

const constructionOptions = ref<Partial<ConstructionCostItem>[]>()

async function onProjectChange(projectId: SelectValue) {
  if (typeof projectId !== 'string')
    return
  const { data, status } = await getConstructionByProjectIdApi(projectId)
  if (status === 200)
    constructionOptions.value = data?.constructions ?? []
}

async function queryVendor(params: GetVendorParams) {
  const { data } = await getVendor(params)
  return data
}

const {
  data: vendorData,
} = usePagination(queryVendor, {
  defaultParams: [{
    pageNum: 1,
    pageSize: 10,
  }],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const vendorOptions = computed(() => vendorData.value?.items ?? [])

// async function saveEdit(inputCostItemId: string) {
//   if (!invoiceInfo.value?.inputCostId || !isLoading.value)
//     return
//   isLoading.value = true
//   const inputCostItemParams: InputCostItemPutParams = {
//     inputCostItemId,
//     item: {
//       itemId: editableData[inputCostItemId]?.itemId,
//     },
//     transactionDate: editableData[inputCostItemId]?.transactionDate,
//     unit: editableData[inputCostItemId]?.unit,
//     quantity: editableData[inputCostItemId]?.quantity,
//     price: editableData[inputCostItemId]?.price,
//     taxRate: editableData[inputCostItemId]?.taxRate,
//     totalNonTaxed: editableData[inputCostItemId]?.totalNonTaxed,
//     totalTaxed: editableData[inputCostItemId]?.totalTaxed,
//     description: editableData[inputCostItemId]?.description,
//     isDeleted: false,
//   }
//   const params: InputCostPutParams = {
//     inputCostItems: JSON.stringify(inputCostItemParams),
//   }

//   // const inputCostId = '01957dcc-2a5d-7081-8cdc-52d3a10c6733'
//   const inputCostId = invoiceInfo.value?.inputCostId
//   const { status, message, data } = await updateInputCostApi(inputCostId, params)
//   if (status !== ResponseStatusEnum.SUCCESS) {
//     messageNotify.error(message)
//   }
//   else {
//     inputCostItems.value = data?.inputCostItems ?? []
//     messageNotify.success(message)
//     cancelEdit()
//   }

//   isLoading.value = false
// }

async function createInputCost() {
  const formData = new FormData()

  // Add basic fields
  formData.append('IssueDate', editableInvoice.issueDate || '')
  formData.append('EntryTypeId', editableInvoice.entryTypeId || '')
  formData.append('ConstructionId', editableInvoice.constructionId || '')
  formData.append('PaymentDate', editableInvoice.paymentDate || '')
  formData.append('Title', editableInvoice.title || '')
  formData.append('OriginalNumber', editableInvoice.originalNumber || '')
  formData.append('PaymentTypeId', editableInvoice.paymentTypeId || '')
  formData.append('TotalAmount', editableInvoice.totalAmount?.toString() || '')
  formData.append('Description', editableInvoice.description || '')

  // Add vendor info
  formData.append('Vendor.VendorId', editableInvoice?.vendorId || '')
  formData.append('Vendor.VendorName', editableInvoice?.vendorName || '')
  formData.append('InputCostItems', JSON.stringify(inputCostItemDataSource.value))

  // Handle multiple images
  if (fileList.value && fileList.value.length > 0) {
    fileList.value.forEach((file) => {
      if (file?.originFileObj)
        formData.append('Images', file.originFileObj)
    })
  }

  const { data, status, message } = await createInputCostApi(formData)
  if (status === 200) {
    messageNotify.success(message)
    currentInputCostId.value = data?.inputCostId
  }
  else {
    messageNotify.error(message)
  }
}

// async function updateInputCost() {
//   if (!currentInputCostId.value) {
//     messageNotify.error('Input cost ID is missing')
//     return
//   }

//   const formData = new FormData()

//   // Add basic fields
//   formData.append('IssueDate', editableInvoice.issueDate || '')
//   formData.append('EntryTypeId', editableInvoice.entryTypeId || '')
//   formData.append('ConstructionId', editableInvoice.constructionId || '')
//   formData.append('PaymentDate', editableInvoice.paymentDate || '')
//   formData.append('Title', editableInvoice.title || '')
//   formData.append('OriginalNumber', editableInvoice.originalNumber || '')
//   formData.append('PaymentTypeId', editableInvoice.paymentTypeId || '')
//   formData.append('TotalAmount', editableInvoice.totalAmount?.toString() || '')
//   formData.append('Description', editableInvoice.description || '')

//   // Add vendor info
//   formData.append('Vendor.VendorId', editableInvoice?.vendorId || '')
//   formData.append('Vendor.VendorName', editableInvoice?.vendorName || '')

//   // Vì truyền vào là nó sẽ thêm vào nên update sẽ không truyền vào
//   // formData.append('InputCostItems', JSON.stringify(inputCostItemDataSource.value))

//   // Handle multiple images
//   if (fileList.value && fileList.value.length > 0) {
//     fileList.value.forEach((file) => {
//       if (file?.originFileObj)
//         formData.append('Images', file.originFileObj)
//     })
//   }
//   const { data, status, message } = await updateInputCostApi(currentInputCostId.value, formData)
//   if (status === 200) {
//     messageNotify.success(message)
//     invoiceInfoEditing.value = false
//   }
//   else {
//     console.error(data)
//     messageNotify.error(message)
//   }
// }

// Initialize calculation for new item
calculateNewItemPrice()

function refreshInputCostItem() {
  newInputCostItem.item.itemId = ''
  newInputCostItem.price = undefined
  newInputCostItem.quantity = undefined
  newInputCostItem.taxRate = undefined
  newInputCostItem.totalNonTaxed = undefined
  newInputCostItem.totalTaxed = undefined
  newInputCostItem.description = undefined
  newInputCostItem.transactionDate = undefined
  newInputCostItem.unit = undefined
}

function handleInputCostItemModalCancel() {
  showAddItemModal.value = false
  refreshInputCostItem()
}

onMounted(async () => {
  // const inputCostId = '01957dcc-2a5d-7081-8cdc-52d3a10c6733'
  // const { data } = await getInputCostByIdApi(inputCostId)
  // inputCostItems.value = data?.inputCostItems ?? []
})
</script>

<template>
  <a-modal
    v-model:visible="isVisible"
    width="1200px"
    :footer="null"
    class="invoice-detail-modal"
  >
    <template #title>
      <span class="text-lg font-semibold">
        {{ t('invoiceInput') }}
      </span>
    </template>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Left column - Image and thumbnails -->
      <div class="space-y-4 pt-5">
        <div class="bg-gray-100 h-80 flex items-center justify-center border rounded">
          <img
            v-if="previewImage"
            :src="previewImage"
            :alt="t('invoice-image')"
            class="w-full h-full object-cover"
          >
          <div v-else class="text-gray-500">
            {{ t('no-images') }}
          </div>
        </div>

        <a-upload
          v-model:file-list="fileList"
          list-type="picture-card"
          :loading="loading"
          :before-upload="beforeUpload"
          @preview="handlePreview"
          @remove="handleRemove"
        >
          <div v-if="fileList && fileList?.length < 8">
            <PlusOutlined />
            <div style="margin-top: 8px">
              {{ t('upload-image') }}
            </div>
          </div>
        </a-upload>
      </div>
      <!-- <InlineEditing
        :invoice="invoiceInfo"
        :invoice-info-editing="invoiceInfoEditing"
        @add-new-input-cost-info="addNewInputCostInfo"
        @update-input-cost-info="updateInputCostInfo"
      /> -->
      <div class="space-y-4">
        <div class="pt-4">
          <div class="grid grid-cols-3 gap-4">
            <div>
              <div class="text-gray-500 text-sm">
                {{ t('project') }}
              </div>
              <div class="font-medium flex items-center gap-1">
                <a-select
                  v-model:value="editableInvoice.projectId"
                  :options="projectOptions"
                  :field-names="{ label: 'name', value: 'id' }"
                  class="w-full"
                  @change="onProjectChange"
                />
              </div>
            </div>
            <div>
              <div class="text-gray-500 text-sm">
                {{ t('constructure') }}
              </div>
              <div class="font-medium flex items-center gap-1">
                <a-select
                  v-model:value="editableInvoice.constructionId"
                  :options="constructionOptions"
                  :field-names="{ label: 'constructionName', value: 'constructionId' }"
                  class="w-full"
                />
              </div>
            </div>
            <div>
              <div class="text-gray-500 text-sm">
                {{ t('vendor') }}
              </div>
              <div class="font-medium flex items-center gap-1">
                <a-select
                  v-model:value="editableInvoice.vendorId"
                  :options="vendorOptions"
                  :field-names="{ label: 'vendorName', value: 'vendorId' }"
                  class="w-full"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-4 gap-4">
          <div class="col-span-2">
            <div class="text-gray-500 text-sm">
              {{ t('invoice-title') }}
            </div>
            <div class="font-medium">
              <a-input
                v-model:value="editableInvoice.title"
              />
            </div>
          </div>
          <div class="col-span-2">
            <div class="text-gray-500 text-sm">
              {{ t('invoice-number') }}
            </div>
            <div class="font-medium">
              <a-input
                v-model:value="editableInvoice.originalNumber"
              />
            </div>
          </div>
          <div class="col-span-2">
            <div class="text-gray-500 text-sm">
              {{ t('release-date') }}
            </div>
            <div class="font-medium">
              <a-date-picker
                v-model:value="editableInvoice.issueDate"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </div>
          </div>
          <div class="col-span-2">
            <div class="text-gray-500 text-sm">
              {{ t('payment-term') }}
            </div>
            <div class="font-medium">
              <a-date-picker
                v-model:value="editableInvoice.paymentDate"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </div>
          </div>
          <div class="col-span-2">
            <div class="text-gray-500 text-sm">
              {{ t('type') }}
            </div>
            <div class="font-medium">
              <a-select
                v-model:value="editableInvoice.entryTypeId"
                :options="entryTypeOptions"
                :field-names="{ label: 'entryTypeName', value: 'entryTypeId' }"
                class="w-full"
              />
            </div>
          </div>
          <div class="col-span-2">
            <div class="text-gray-500 text-sm">
              {{ t('payment-method') }}
            </div>
            <div class="font-medium">
              <a-select
                v-model:value="editableInvoice.paymentTypeId"
                :options="paymentTypeOptions"
                :field-names="{ label: 'paymentTypeName', value: 'paymentTypeId' }"
                class="w-full"
              />
            </div>
          </div>
          <div class="col-span-4">
            <div class="text-gray-500 text-sm">
              {{ t('total') }}
            </div>
            <div class="font-medium">
              <a-input-number
                v-model:value="editableInvoice.totalAmount"
                class="w-full"
                :formatter="(value: any) => `${value} ¥`"
                :parser="(value: any) => value.replace(' ¥', '')"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Item list section -->
    <div class="mt-2">
      <div class="flex justify-between items-center mb-4">
        <span class="text-md font-medium">
          {{ t('input-cost-item-list') }}
        </span>
        <div class="flex items-center justify-center">
          <a-input-search
            v-model:value="searchText"
            :placeholder="t('placeholder-search')"
            style="width: 250px"
            class="mr-2"
          />
          <a-button type="default" class="flex items-center justify-center" @click="showAddItemModal = true">
            <template #icon>
              <PlusOutlined class="text-blue" />
            </template>
          </a-button>
        </div>
      </div>

      <a-table
        :data-source="inputCostItemDataSource"
        :columns="columns"
        :pagination="{ pageSize: 10, showSizeChanger: true, pageSizeOptions: ['5', '10', '20'] }"
        bordered
        size="middle"
        :scroll="{ x: 'max-content' }"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'itemName'">
            <!-- <a-input
              v-if="editingKey === record.inputCostItemId"
              v-model:value="editableData[record.inputCostItemId].itemName"
            /> -->
            <div class="flex items-center">
              <div>
                <div>{{ record.itemName }}</div>
                <div class="text-xs text-gray-500">
                  {{ t('vendor') }}: {{ record.vendorName }}
                </div>
              </div>
            </div>
          </template>

          <template v-if="column.dataIndex === 'unit'">
            <a-input
              v-if="editingKey === record.inputCostItemId"
              v-model:value="editableData[record.inputCostItemId].unit"
              style="width: 100px"
              :min="0"
            />
            <span v-else>{{ record.unit }}</span>
          </template>

          <template v-if="column.dataIndex === 'quantity'">
            <a-input-number
              v-if="editingKey === record.inputCostItemId"
              v-model:value="editableData[record.inputCostItemId].quantity"
              style="width: 100px"
              :min="1"
            />
            <span v-else>{{ record.quantity }}</span>
          </template>

          <template v-if="column.dataIndex === 'taxRate'">
            <a-select
              v-if="editingKey === record.inputCostItemId"
              v-model:value="editableData[record.inputCostItemId].taxRate"
              style="width: 100px"
            >
              <a-select-option :value="5">
                5%
              </a-select-option>
              <a-select-option :value="10">
                10%
              </a-select-option>
              <a-select-option :value="20">
                20%
              </a-select-option>
            </a-select>
            <span v-else>{{ record.taxRate }}%</span>
          </template>
          <template v-if="column.dataIndex === 'price'">
            <a-input
              v-if="editingKey === record.inputCostItemId"
              v-model:value="editableData[record.inputCostItemId].price"
              style="width: 100px"
            />
            <span v-else> {{ record.price }}</span>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <div class="flex space-x-1">
              <!-- <template v-if="editingKey === record.inputCostItemId">
                <a-button type="link" @click="saveEdit(record)">
                  <template #icon>
                    <CheckOutlined />
                  </template>
                </a-button>
                <a-button type="link" @click="cancelEdit">
                  <template #icon>
                    <CloseOutlined />
                  </template>
                </a-button>
              </template>
              <template v-else>
                <a-button type="link" @click="editRow(record as InputCostItem)">
                  <template #icon>
                    <EditOutlined />
                  </template>
                </a-button>
              </template> -->
              <a-button type="link" @click="handleDelete(index)">
                <template #icon>
                  <DeleteOutlined />
                </template>
              </a-button>
            </div>
          </template>
        </template>
      </a-table>
    </div>

    <div class="flex justify-end mt-6 space-x-2">
      <a-button @click="handleCancel">
        {{ t('button.cancel') }}
      </a-button>
      <a-button type="primary" @click="handleCreate">
        {{ t('button.create') }}
      </a-button>
      <!-- <a-button type="primary" @click="handleUpdate">
        {{ t('button.update') }}
      </a-button>
      <a-button type="primary" @click="handleCreateNewInvoice">
        {{ t('button.new-invoice') }}
      </a-button> -->
    </div>

    <!-- Upload Image Modal -->
    <!-- <a-modal
      v-model:visible="showUploadModal"
      :title="t('upload-image')"
      :footer="null"
      @cancel="showUploadModal = false"
    >
      <a-upload-dragger
        v-model:file-list="fileList"
        name="file"
        :multiple="false"
        :before-upload="beforeUpload"
      >
        <p class="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p class="ant-upload-text">
          {{ t('click-or-drag-file-to-this-area-to-upload') }}
        </p>
        <p class="ant-upload-hint">
          {{ t('support-for-a-single-upload') }}
        </p>
      </a-upload-dragger>

      <div class="flex justify-end mt-4">
        <a-button @click="showUploadModal = false">
          {{ t('button.cancel') }}
        </a-button>
        <a-button type="primary" class="ml-2" @click="confirmUpload">
          {{ t('button.upload') }}
        </a-button>
      </div>
    </a-modal> -->

    <!-- Add Item Modal -->
    <a-modal
      v-model:visible="showAddItemModal"
      :title="t('add-new-item')"
      :footer="null"
      @cancel="handleInputCostItemModalCancel"
    >
      <a-form
        :model="newInputCostItem"
        layout="vertical"
      >
        <a-form-item :label="t('item-name')" name="itemId">
          <a-select
            v-model:value="newInputCostItem.item.itemId"
            :options="itemOptions"
            :field-names="{ label: 'itemName', value: 'itemId' }"
            :placeholder="t('enter-item-name')"
          />
        </a-form-item>

        <!-- <a-form-item :label="t('vendor')" name="vendorId">
          <a-select
            v-model:value="newInputCostItem.vendorId"
            :options="vendors"
            :field-names="{ label: 'vendorName', value: 'vendorId' }"
            :placeholder="t('enter-vendor-name')"
          />
        </a-form-item> -->

        <a-form-item :label="t('transaction-date')" name="transactionDate">
          <a-date-picker
            v-model:value="newInputCostItem.transactionDate"
            style="width: 100%"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
          />
        </a-form-item>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <a-form-item :label="t('unit-price')" name="unitPrice">
            <a-input
              v-model:value="newInputCostItem.unit"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item :label="t('price')" name="price">
            <a-input-number
              v-model:value="newInputCostItem.price"
              :min="0"
              style="width: 100%"
              @change="calculateNewItemPrice"
            />
          </a-form-item>

          <a-form-item :label="t('quantity')" name="quantity">
            <a-input-number
              v-model:value="newInputCostItem.quantity"
              :min="1"
              style="width: 100%"
              @change="calculateNewItemPrice"
            />
          </a-form-item>

          <a-form-item :label="t('tax-rate')" name="taxRate">
            <a-select
              v-model:value="newInputCostItem.taxRate"
              style="width: 100%"
              @change="calculateNewItemPrice"
            >
              <a-select-option :value="5">
                5%
              </a-select-option>
              <a-select-option :value="10">
                10%
              </a-select-option>
              <a-select-option :value="20">
                20%
              </a-select-option>
            </a-select>
          </a-form-item>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <a-form-item :label="t('total-nontaxed')" name="totalNontaxed">
            <a-input-number
              v-model:value="newInputCostItem.totalNonTaxed"
              :disabled="true"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item :label="t('total-taxed')" name="totalTaxed">
            <a-input-number
              v-model:value="newInputCostItem.totalTaxed"
              :disabled="true"
              style="width: 100%"
            />
          </a-form-item>
        </div>

        <!-- <a-form-item label="Item Image">
          <a-upload
            list-type="picture-card"
            :file-list="fileList"
            :before-upload="beforeUpload"
            @change="handleItemImageChange"
          >
            <div v-if="fileList.length < 1">
              <PlusOutlined />
              <div style="margin-top: 8px">
                Upload
              </div>
            </div>
          </a-upload>
        </a-form-item> -->
      </a-form>

      <div class="flex justify-end mt-4">
        <a-button @click="showAddItemModal = false">
          {{ t("button.cancel") }}
        </a-button>
        <a-button type="primary" class="ml-2" @click="handleCreateInputCostItem">
          {{ t("button.add") }}
        </a-button>
      </div>
    </a-modal>

    <!-- Delete Confirmation Modal -->
    <a-modal
      v-model:visible="showDeleteModal"
      :title="t('delete-item')"
      :ok-text="t('button.ok')"
      :cancel-text="t('button.cancel')"
      @ok="confirmDelete"
      @cancel="showDeleteModal = false"
    >
      <p>{{ t("confirm-delete-item") }}</p>
    </a-modal>
  </a-modal>
</template>

  <style scoped>
  .invoice-detail-modal :deep(.ant-table-thead > tr > th) {
    background-color: #f9fafb;
    font-weight: 500;
  }

  .invoice-detail-modal :deep(.ant-upload-drag) {
    height: 200px;
  }
  </style>
