<script lang="ts" setup>
import { usePagination } from 'vue-request'
import { delayTimer } from '@v-c/utils'
import OutsourceItem from './components/OutsourceItem.vue'
import type { OutSourceData, OutSourceItem, OutsourceQueryParams } from '~@/api/outsource'
import { createOutsourceApi, deleteOutsourceApi, fetchOutsourceListApi, getOutsourceLogo, updateOutsourceApi } from '~@/api/outsource'
import { Skills } from '~@/utils/constant'

const { t } = useI18n()
const orgId = useOrg()

const visible = ref(false)
const isEdit = ref<boolean>(false)
const messageNotify = useMessage()

const searchForm = ref<OutsourceQueryParams>({
  pageSize: 10,
  pageNum: 1,
})

interface ContactPersonState {
  email?: string
  name?: string
  phoneNumber?: string
}

const contactPersonState = reactive<ContactPersonState>({
  email: '',
  name: '',
  phoneNumber: '',
})

const downloadUrl = ref('')

const fileList = ref([])
const currentOutsourceId = ref<string>('')

const formState = reactive<OutSourceData>({
  address: undefined,
  corporateNumber: undefined,
  description: undefined,
  email: undefined,
  expertise: [],
  outSourceCode: '',
  outSourceName: '',
  phoneNumber: undefined,
  logo: undefined,
})
function openModal(action: string, record?: OutSourceItem) {
  visible.value = true
  if (action === 'EDIT') {
    if (!record || !record.outSourceId || !record.outSourceCode || !record.outSourceName)
      return
    isEdit.value = true
    currentOutsourceId.value = record.outSourceId ?? undefined
    formState.outSourceCode = record.outSourceCode
    formState.outSourceName = record.outSourceName
    formState.email = record.email ?? undefined
    formState.description = record.description ?? undefined
    formState.address = record.address ?? undefined
    formState.corporateNumber = record.corporateNumber ?? undefined
    formState.phoneNumber = record.phoneNumber ?? undefined
    formState.expertise = record.expertise ?? undefined
    contactPersonState.email = record.contactPerson?.email ?? undefined
    contactPersonState.name = record.contactPerson?.name ?? undefined
    contactPersonState.phoneNumber = record.contactPerson?.phoneNumber ?? undefined
    // priceState.pricePerHour = record.price?.pricePerHour ?? undefined
    // priceState.pricePerDay = record.price?.pricePerDay ?? undefined
    // priceState.pricePerWeek = record.price?.pricePerWeek ?? undefined
    // priceState.pricePerMonth = record.price?.pricePerMonth ?? undefined
    downloadUrl.value = getOutsourceLogo(record.logoUrl ?? '', orgId.value ?? '')
    fileList.value = []
  }
  else {
    isEdit.value = false
  }
}
const {
  data,
  refresh,
  run,
} = usePagination(fetchOutsourceListApi, {
  defaultParams: [
    {
      pageNum: 1,
      pageSize: 10,
    },
  ],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const outsourceData = computed(() => data.value?.items || [])
const isLoading = ref(false)

async function deleteOutsource(outSourceId: string) {
  if (isLoading.value)
    return
  isLoading.value = true
  const { status, message } = await deleteOutsourceApi(outSourceId)
  if (status === 200) {
    message.success(message)
    refresh()
  }
  else {
    messageNotify.error(message)
  }
  isLoading.value = false
}

function closeModal() {
  visible.value = false
  formState.address = ''
  formState.contactPerson = {
    email: '',
    name: '',
    phoneNumber: '',
  }
  formState.corporateNumber = ''
  formState.description = ''
  formState.email = ''
  formState.expertise = []
  formState.outSourceCode = ''
  formState.outSourceName = ''
  formState.phoneNumber = ''
  formState.price = {
    pricePerDay: 0,
    pricePerHour: 0,
    pricePerMonth: 0,
    pricePerWeek: 0,
  }
}

async function handleOk() {
  const params: OutSourceData = {
    address: formState.address,
    outSourceCode: formState.outSourceCode,
    outSourceName: formState.outSourceName,
    email: formState.email,
    description: formState.description,
    contactPerson: {
      email: contactPersonState.email,
      name: contactPersonState.name,
      phoneNumber: contactPersonState.phoneNumber,
    },
    corporateNumber: formState.corporateNumber,
    expertise: formState.expertise,
    phoneNumber: formState.phoneNumber,
    logo: formState.logo,
  }
  if (isEdit.value) {
    const outsourceId = currentOutsourceId.value
    if (!outsourceId)
      return
    const { status, message } = await updateOutsourceApi(outsourceId, params)
    if (status === 200) {
      refresh()
      closeModal()
    }
    else {
      messageNotify.error(message)
    }
  }
  else {
    const { status, message } = await createOutsourceApi(params)
    if (status === 200) {
      refresh()
      closeModal()
    }
    else {
      messageNotify.error(message)
    }
  }
}

function beforeUpload(file: any) {
  formState.logo = file
  return false
}

function onSearch() {
  run(searchForm.value)
}
onMounted (async () => {
  await delayTimer(600)
})
</script>

<template>
  <page-container>
    <div class="w-full flex flex-col gap-4">
      <div class="flex items-center gap-x-2">
        <a-button
          class="flex flex-items-center"
          type="primary"
          @click="openModal('ADD-NEW')"
        >
          <PlusOutlined />
          {{ `${t('button.new')}` }}
        </a-button>
        <a-input
          v-model:value="searchForm.keyword"
          :placeholder="t('search')"
          style="width: 25rem"
          allow-clear
          @press-enter="onSearch"
        >
          <template #prefix>
            <SearchOutlined class="text-gray-500" />
          </template>
        </a-input>
      </div>
      <div v-for="(item) in outsourceData" :key="item.outSourceId">
        <OutsourceItem
          :outsource-data="item"
          @open-modal="openModal('EDIT', item)"
          @delete-outsource="deleteOutsource(item.outSourceId)"
        />
      </div>
    </div>
  </page-container>

  <a-modal
    v-model:open="visible"
    :width="800"
    :ok-text="`${isEdit ? t('button.edit') : t('button.add')}`"
    @ok="handleOk"
  >
    <template #title>
      <div class="flex items-center justify-center w-full">
        <a-typography-title :level="3">
          {{ isEdit ? `${t('button.edit')}` : `${t('button.new')}` }}
        </a-typography-title>
      </div>
    </template>
    <a-form class="flex gap-x-8" layout="vertical" :model="formState">
      <div>
        <a-form-item>
          <!-- <a-upload
            v-model:file-list="fileList"
            list-type="picture-card"
            @preview="handlePreview"
            @before-upload="beforeUpload"
          >
            <div>
              <plus-outlined />
              <div style="margin-top: 8px">
                Upload
              </div>
            </div>
          </a-upload> -->
          <a-upload
            v-model:file-list="fileList"
            list-type="picture-card"
            :max-count="1"
            :before-upload="beforeUpload"
          >
            <div v-if="fileList.length < 1">
              <plus-outlined />
              <div style="margin-top: 8px">
                Upload
              </div>
            </div>
            <!-- <img v-if="imageUrl" :src="imageUrl" alt="avatar">
            <div v-else>
              <loading-outlined v-if="loading" />
              <plus-outlined v-else />
              <div class="ant-upload-text">
                Upload
              </div>
            </div> -->
          </a-upload>
        </a-form-item>
      </div>
      <div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <a-form-item
            name="outSourceCode"
            :label="t('outsource.code')"
            :rules="[{ required: true, message: 'Please input outsource code' }]"
          >
            <a-input v-model:value="formState.outSourceCode" class="w-70" />
          </a-form-item>
          <a-form-item
            name="outSourceName"
            :label="t('outsource.name')"
            :rules="[{ required: true, message: 'Please input outsource name' }]"
          >
            <a-input v-model:value="formState.outSourceName" class="w-70" />
          </a-form-item>
          <a-form-item
            name="corporateNumber"
            :label="t('outsource.corporate-number')"
          >
            <a-input v-model:value="formState.corporateNumber" class="w-70" />
          </a-form-item>
          <a-form-item
            name="address"
            :label="t('outsource.address')"
          >
            <a-input v-model:value="formState.address" class="w-70" />
          </a-form-item>
          <a-form-item
            name="email"
            :label="t('outsource.email')"
          >
            <a-input v-model:value="formState.email" class="w-70" />
          </a-form-item>
          <a-form-item
            name="phoneNumber"
            :label="t('outsource.phone-number')"
          >
            <a-input v-model:value="formState.phoneNumber" class="w-70" />
          </a-form-item>
        </div>
        <div>
          <a-form-item
            name="expertise"
            :label="t('outsource.expertise')"
            :rules="[{ required: true, message: 'Please select expertise' }]"
          >
            <a-checkbox-group v-model:value="formState.expertise" name="roleRadioGroup" class="flex">
              <div class="grid grid-cols-3 gap-4">
                <a-checkbox :value="Skills.SUPERVISOR">
                  {{ t('supervisor') }}
                </a-checkbox>
                <a-checkbox :value="Skills.FOREMAN">
                  {{ t('foreman') }}
                </a-checkbox>
                <a-checkbox :value="Skills.WORKER">
                  {{ t('worker') }}
                </a-checkbox>
                <a-checkbox :value="Skills.HEAVY_MACHINERY_OPERATOR">
                  {{ t('heavyMachineryOperator') }}
                </a-checkbox>
                <a-checkbox :value="Skills.FORMWORK_WORKER">
                  {{ t('formworkWorker') }}
                </a-checkbox>
                <a-checkbox :value="Skills.REBAR_WORKER">
                  {{ t('rebarWorker') }}
                </a-checkbox>
              </div>
            </a-checkbox-group>
          </a-form-item>
          <a-form-item
            name="name"
            :label="t('outsource.contact-person')"
          >
            <a-input v-model:value="contactPersonState.name" />
          </a-form-item>
          <a-form-item
            name="email"
            :label="t('outsource.contact-person-email')"
          >
            <a-input v-model:value="contactPersonState.email" />
          </a-form-item>
          <a-form-item
            name="phoneNumber"
            :label="t('outsource.contact-person-phone-number')"
          >
            <a-input v-model:value="contactPersonState.phoneNumber" />
          </a-form-item>
        </div>
      </div>
      <!-- <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AFormItem label="Price Per Hour" name="['Price', 'PricePerHour']">
          <AInputNumber
            v-model:value="formState.Price.PricePerHour"
            placeholder="0.00"
            :min="0"
            :precision="2"
            style="width: 100%"
            addon-before="$"
          />
        </AFormItem>
        <AFormItem label="Price Per Day" name="['Price', 'PricePerDay']">
          <AInputNumber
            v-model:value="formState.Price.PricePerDay"
            placeholder="0.00"
            :min="0"
            :precision="2"
            style="width: 100%"
            addon-before="$"
          />
        </AFormItem>
        <AFormItem label="Price Per Week" name="['Price', 'PricePerWeek']">
          <AInputNumber
            v-model:value="formState.Price.PricePerWeek"
            placeholder="0.00"
            :min="0"
            :precision="2"
            style="width: 100%"
            addon-before="$"
          />
        </AFormItem>
        <AFormItem label="Price Per Month" name="['Price', 'PricePerMonth']">
          <AInputNumber
            v-model:value="formState.Price.PricePerMonth"
            placeholder="0.00"
            :min="0"
            :precision="2"
            style="width: 100%"
            addon-before="$"
          />
        </AFormItem>
      </div> -->
    </a-form>
  </a-modal>
</template>
