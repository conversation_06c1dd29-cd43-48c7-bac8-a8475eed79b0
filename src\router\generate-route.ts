import { isUrl } from '@v-c/utils'
import type { RouteRecordRaw } from 'vue-router'
import { omit } from 'lodash'
import { basicRouteMap, getRouterModule } from './router-modules'
import type { MenuData, MenuDataItem } from '~@/layouts/basic-layout/typing'
import dynamicRoutes, { ROOT_ROUTE_REDIRECT_PATH } from '~@/router/dynamic-routes'
import { i18n } from '~@/locales'
import { STATIC_MENUS } from '~@/utils/menuData'

let cache_key = 1

const getCacheKey = () => `Cache_Key_${cache_key++}`

function renderTitle(route: RouteRecordRaw) {
  const { title, locale } = route.meta || {}
  if (!title)
    return ''
  return locale ? (i18n.global as any).t(locale) : title
}

function formatMenu(route: RouteRecordRaw, path?: string) {
  return {
    parentName: route.meta?.parentName,
    title: () => renderTitle(route),
    icon: route.meta?.icon || '',
    path: path ?? route.path,
    hideInMenu: route.meta?.hideInMenu || false,
    hideInBreadcrumb: route.meta?.hideInBreadcrumb || false,
    hideChildrenInMenu: route.meta?.hideChildrenInMenu || false,
    locale: route.meta?.locale,
    keepAlive: route.meta?.keepAlive || false,
    name: route.name as string,
    url: route.meta?.url || '',
    target: route.meta?.target || '_blank',
  }
}

export function genRoutes(routes: RouteRecordRaw[], parent?: MenuDataItem) {
  const menuData: MenuData = []
  const { hasAccess } = useAccess()
  routes.forEach((route) => {
    if (route.meta?.access) {
      const isAccess = hasAccess(route.meta?.access)
      if (!isAccess)
        return
    }
    let path = route.path
    if (!path.startsWith('/') && !isUrl(path)) {
      if (parent)
        path = `${parent.path}/${path}`
      else path = `/${path}`
    }
    if (!route.name)
      route.name = getCacheKey()
    const item: MenuDataItem = formatMenu(route, path)
    item.children = []
    if (route.children && route.children.length)
      item.children = genRoutes(route.children, item)
    if (item.children?.length === 0)
      delete item.children
    menuData.push(item)
  })
  return menuData
}

function findAllParents(menuName: string, menus: MenuData): MenuDataItem[] {
  const parents: MenuDataItem[] = []

  function findParent(name: string): void {
    // Tìm menu hiện tại
    const currentMenu = menus.find(menu => menu.name === name)
    if (!currentMenu)
      return

    // Thêm menu hiện tại vào danh sách cha
    parents.push(currentMenu)

    // Nếu có parentName và không phải null, tiếp tục tìm cha
    if (currentMenu.parentName)
      findParent(currentMenu.parentName)
  }

  // Bắt đầu tìm từ menuId được cung cấp
  if (menuName)
    findParent(menuName)

  // Trả về danh sách các menu cha (không bao gồm menu hiện tại)
  return parents.slice(1)
}

// Hàm để xử lý tất cả các menu trong filteredMenus
function processMenusWithParents(filteredMenus: MenuData, menus: MenuData): MenuData {
  // Tạo một bản sao của filteredMenus để không ảnh hưởng đến dữ liệu gốc
  const result = [...filteredMenus]
  const processedNames = new Set(filteredMenus.map(menu => menu.name))

  // Duyệt qua từng menu trong filteredMenus
  for (const menu of filteredMenus) {
    if (!menu.name)
      continue

    // Tìm tất cả các menu cha
    const parents = findAllParents(menu.name, menus)

    // Thêm các menu cha vào kết quả nếu chưa có
    for (const parent of parents) {
      if (parent.name && !processedNames.has(parent.name)) {
        result.push(parent)
        processedNames.add(parent.name)
      }
    }
  }

  return result
}

export async function generateTreeRoutes(menus: MenuData) {
  // Ensure menus is an array
  const menuArray = Array.isArray(menus) ? menus : []
  const { hasReadPermission, loadMenuPermissions } = useMenuPermission()
  await loadMenuPermissions()

  // Filter menus based on permissions
  const filteredMenus = menuArray.filter((menu) => {
    if (!menu.name)
      return false
    return hasReadPermission(menu.name)
  })

  let finalMenus = processMenusWithParents(filteredMenus, menus)
  finalMenus = [
    ...finalMenus,
    ...STATIC_MENUS,
  ]

  const routeDataMap = new Map<string | number, RouteRecordRaw>()
  const menuDataMap = new Map<string | number, MenuDataItem>()

  // Build routes and menus maps
  for (const menuItem of finalMenus) {
    if (!menuItem.name) {
      console.warn(`Menu item missing name: ${JSON.stringify(menuItem)}`)
      continue
    }

    const route = {
      path: menuItem.path,
      name: menuItem.name || getCacheKey(),
      component: getRouterModule(menuItem.component!),
      redirect: menuItem.redirect,
      meta: {
        title: menuItem.title,
        icon: menuItem.icon,
        keepAlive: menuItem.keepAlive,
        parentName: menuItem.parentName,
        affix: menuItem.affix,
        url: menuItem.url,
        hideInMenu: menuItem.hideInMenu,
        hideChildrenInMenu: menuItem.hideChildrenInMenu,
        hideInBreadcrumb: menuItem.hideInBreadcrumb,
        target: menuItem.target,
        locale: menuItem.locale,
        isLeaf: menuItem.isLeaf,
      },
    } as RouteRecordRaw

    const menu = formatMenu(route)
    routeDataMap.set(menuItem.name, route)
    menuDataMap.set(menuItem.name, menu)
  }

  // Build tree structure
  const routeData: RouteRecordRaw[] = []
  const menuData: MenuData = []

  for (const menuItem of finalMenus) {
    if (!menuItem.name)
      continue

    const currentRoute = routeDataMap.get(menuItem.name)
    const currentItem = menuDataMap.get(menuItem.name)

    if (!menuItem.parentName) {
      // Root level items
      if (currentRoute && currentItem) {
        routeData.push(currentRoute)
        menuData.push(currentItem)
      }
    }
    else {
      // Child items
      const parentRoute = routeDataMap.get(menuItem.parentName)
      const parentItem = menuDataMap.get(menuItem.parentName)

      if (currentItem && currentRoute && parentRoute && parentItem) {
        if (!parentRoute.children)
          parentRoute.children = []
        if (!parentItem.children)
          parentItem.children = []

        parentRoute.children.push(currentRoute)
        parentItem.children.push(currentItem)
      }
    }
  }
  return {
    menuData,
    routeData,
  }
}

export async function generateRoutes() {
  const { hasReadPermission, loadMenuPermissions } = useMenuPermission()

  // Load permissions first
  await loadMenuPermissions()

  // Filter routes based on permissions
  const filteredRoutes = dynamicRoutes.filter((route) => {
    // Skip routes without id
    if (!route.name)
      return true

    // Check permission
    return hasReadPermission(route.name as string)
  })

  const menuData = genRoutes(filteredRoutes)

  return {
    menuData,
    routeData: filteredRoutes,
  }
}

function checkComponent(component: RouteRecordRaw['component']) {
  for (const componentKey in basicRouteMap) {
    if (component === (basicRouteMap as any)[componentKey])
      return undefined
  }
  return component
}

// Chuyển từ hàm đệ quy sang hàm queue
function flatRoutes(routes: RouteRecordRaw[], parentName?: string, parentComps: RouteRecordRaw['component'][] = []) {
  const queue = [...routes]
  const flatRouteData: RouteRecordRaw[] = []

  while (queue.length) {
    const route = queue.shift()!
    const parentComponents = [...parentComps]
    const currentRoute = omit(route, ['children']) as RouteRecordRaw

    if (!currentRoute.meta)
      currentRoute.meta = {}
    if (parentName)
      currentRoute.meta.parentName = parentName
    if (parentComponents.length > 0)
      currentRoute.meta.parentComps = parentComponents
    currentRoute.meta.originPath = currentRoute.path

    flatRouteData.push(currentRoute)

    if (route.children && route.children.length) {
      const comp = checkComponent(route.component)
      if (comp)
        parentComponents.push(comp)
      queue.push(...route.children.map(child => ({ ...child, parentName: route.name as string })))
    }
  }

  return flatRouteData
}

function isDashboardPageExist(path: string, flatRoutesList: RouteRecordRaw[]): boolean {
  const index = flatRoutesList.findIndex(route => route.path === path)
  return index !== -1
}

export function generateFlatRoutes(routes: RouteRecordRaw[]) {
  const flatRoutesList = flatRoutes(routes)

  let redirectPath = ROOT_ROUTE_REDIRECT_PATH
  if (!isDashboardPageExist(ROOT_ROUTE_REDIRECT_PATH, flatRoutesList))
    redirectPath = flatRoutesList.find(route => route.meta?.isLeaf)?.path || '/'

  const parentRoute: RouteRecordRaw = {
    path: '/dashboard',
    name: 'ROOT_EMPTY_PATH',
    redirect: redirectPath,
    component: getRouterModule('RouteView'),
    children: flatRoutesList,
  }
  return [parentRoute]
}
