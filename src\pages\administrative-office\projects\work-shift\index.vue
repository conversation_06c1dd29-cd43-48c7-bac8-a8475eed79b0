<!-- eslint-disable curly -->
<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
import {
  ArrowRightOutlined,
  CheckOutlined,
  CloseOutlined,
  ExclamationCircleOutlined,
  MinusOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue'
import { Modal } from 'ant-design-vue'
// import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import type { UnwrapRef } from 'vue'
import { createVNode } from 'vue'
import { usePagination } from 'vue-request'
import type { WorkshiftItem, WorkshiftParam } from '~@/api/company/work-shift'
import { createWorkshiftApi, deleteWorkshiftApi, getWorkshiftApi, updateWorkshiftApi } from '~@/api/company/work-shift'
import type { BreakTimeItem } from '~@/api/attendance'
import logger from '~@/utils/logger'

const { isWorkingTimeValid } = useValidateTime()

const messageNotify = useMessage()

const { t } = useI18n()

const isModalOpen = ref<boolean>(false)
const isAdd = shallowRef<boolean>(false)
const isLoading = shallowRef<boolean>(false)

const editTableData: UnwrapRef<Record<string, WorkshiftItem>> = reactive({})

// function onSearch() {
//   getWorkshift(defaultParams)
// }
const columns: any = computed(() => {
  return [
    {
      title: t('form.shiftWorkCode'),
      dataIndex: 'workShiftCode',
      key: 'workShiftCode',
    },
    {
      title: t('form.shiftWorkName'),
      dataIndex: 'workShiftName',
      key: 'workShiftName',
      align: 'center',
    },
    {
      title: t('form.checkInTime'),
      dataIndex: 'checkInTime',
      key: 'checkInTime',
      align: 'center',
    },
    {
      title: t('form.checkOutTime'),
      dataIndex: 'checkOutTime',
      key: 'checkOutTime',
      align: 'center',
    },
    {
      title: t('workShiftBreaks'),
      dataIndex: 'workShiftBreaks',
      key: 'workShiftBreaks',
      align: 'center',
      width: 300,
    },
    {
      title: t('notes'),
      dataIndex: 'description',
      key: 'description',
      align: 'center',
    },

    // {
    //   title: t('status'),
    //   dataIndex: 'status',
    //   key: 'status',
    //   align: 'center',
    // },
    // {
    //   title: t("form.totalRow"),
    //   dataIndex: "TotalRow",
    //   key: "TotalRow",
    // },
    {
      title: t('action'),
      dataIndex: 'actions',
      key: 'actions',
      align: 'center',
    },
  ]
})

const {
  data: dataSource,
  changeCurrent,
  refresh,
  total,
  current,
  loading,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [
    {
      pageNum: 1,
      pageSize: 10,
    },
  ],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const pagination = computed(() => ({
  total: total.value,
  current: current.value,
  pageSize: pageSize.value,
}))

const handleTableChange: any = (
  pag: { pageSize: number; current: number },
) => {
  changeCurrent(pag.current)
}

async function queryData(params: any) {
  const { data, status, code } = await getWorkshiftApi(params)
  if (status === 200) {
    return data
  }
  else {
    messageNotify.error(t(code))
    logger.error(t(code))
  }
}

async function createNewWorkshift(params: WorkshiftParam) {
  try {
    const { status, message } = await createWorkshiftApi(params)
    if (status === 200) {
      messageNotify.success(message)
    }
    else {
      messageNotify.error(message)
      logger.error('status: ', status)
    }
  }
  catch (e) {
    logger.error(e)
  }
}

async function updateWorkshift(workShiftId: string, params: WorkshiftParam) {
  const { data, status, message } = await updateWorkshiftApi(workShiftId, params)
  if (status === 200) {
    logger.log('data', data)
    messageNotify.success(message)
  }
  else {
    messageNotify.error(message)
    logger.error('status: ', status)
  }
}

// async function deleteWorkshift(workShiftId: string) {
//   try {
//     const { status, code, message } = await deleteWorkshiftApi(workShiftId)
//     if (status === 200) {
//       messageNotify.success(message)
//     }
//     else {
//       logger.error('status: ', status)
//       logger.error(t(code))
//     }
//   }
//   catch (e) {
//     logger.error(e)
//   }
// }

const formState = reactive<WorkshiftParam | any>({
  workShiftId: undefined,
  workShiftCode: undefined,
  workShiftName: '',
  checkInTime: '',
  checkOutTime: '',
  description: '',
  workShiftBreaks: [],
  breakInOne: '',
  breakOutOne: '',
  breakInTwo: '',
  breakOutTwo: '',
  breakInThree: '',
  breakOutThree: '',
})

// const onUpdateStatus = async (workShiftId?: number) => {
//   logger.log('onUpdateStatus')
//   if (!workShiftId)
//     return
//   await deleteWorkshift(workShiftId)
// }

// function showConfirm(item: WorkshiftItem) {
//   const params: WorkshiftParam = {
//     workShiftName: item.workShiftName,
//     checkInTime: dayjs(item.checkInTime, 'HH:mm').format('HH:mm:ss'),
//     checkOutTime: dayjs(item.checkOutTime, 'HH:mm').format('HH:mm:ss'),
//     description: item.description,
//   }
//   params.workShiftBreaks = item.workShiftBreaks?.map((breakTime: BreakTimeItem) => ({
//     breakInTime: dayjs(breakTime?.breakInTime, 'HH:mm').format('HH:mm:ss'),
//     breakOutTime: dayjs(breakTime?.breakOutTime, 'HH:mm').format('HH:mm:ss'),
//   }))
//   logger.log('params', params)
//   Modal.confirm({
//     title: t('confirm.updateStatus'),
//     icon: createVNode(ExclamationCircleOutlined),
//     content: createVNode('div', {}, item.status
//       ? `${t('warning.updateStatus', { msg1: t('status.deactive'), msg2: t('status.active') })}`
//       : `${t('warning.updateStatus', { msg1: t('status.active'), msg2: t('status.deactive') })}`),
//     cancelText: t('button.cancel'),
//     okText: t('button.ok'),
//     onOk: async () => {
//       if (!item?.workShiftId)
//         return
//       await updateWorkshift(item.workShiftId, params)
//       await changeCurrent(current.value)
//     },
//     onCancel: async () => {
//       await changeCurrent(current.value)
//     },
//   })
// }

async function deleteWorkshift(workShiftId: string) {
  try {
    const { status, code, message } = await deleteWorkshiftApi(workShiftId)
    if (status === 200) {
      messageNotify.success(message)
      refresh()
    }
    else {
      logger.error('status: ', status)
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

function showConfirm(item: WorkshiftItem) {
  Modal.confirm({
    title: t('confirm.delete'),
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', {}, `${t('alert.confirmDelete')}`),
    cancelText: t('button.cancel'),
    okText: t('button.ok'),
    onOk: async () => {
      if (!item?.workShiftId)
        return
      await deleteWorkshift(item.workShiftId)
      await changeCurrent(current.value)
    },
    onCancel: async () => {
      await changeCurrent(current.value)
    },
  })
}

const openModal = (items?: any, isAddNew?: boolean) => {
  logger.log('openModal')
  if (isAddNew) {
    isModalOpen.value = true
    isAdd.value = true
  }
  else {
    formState.workShiftId = items?.workShiftId
    formState.workShiftCode = items?.workShiftCode
    formState.workShiftName = items?.workShiftName
    formState.checkInTime = items?.checkInTime
    formState.checkOutTime = items?.checkOutTime
    formState.description = items?.desscription
    formState.workShiftBreaks = items?.workShiftBreaks
    isModalOpen.value = true
  }
}

const handleOk = async () => {
  if (!isLoading) {
    return
  }
  isLoading.value = true
  isModalOpen.value = false
  const param: WorkshiftParam = {
    workShiftCode: formState.workShiftCode,
    workShiftName: formState.workShiftName,
    checkInTime: formState.checkInTime,
    checkOutTime: formState.checkOutTime,
    description: formState.description,
    workShiftBreaks: [],
  }
  if (formState.breakInOne && formState.breakOutOne) {
    param.workShiftBreaks?.push({
      breakInTime: formState.breakInOne,
      breakOutTime: formState.breakOutOne,
    })
  }
  if (formState.breakInTwo && formState.breakOutTwo) {
    param.workShiftBreaks?.push({
      breakInTime: formState.breakInTwo,
      breakOutTime: formState.breakOutTwo,
    })
  }
  if (formState.breakInThree && formState.breakOutThree) {
    param.workShiftBreaks?.push({
      breakInTime: formState.breakInThree,
      breakOutTime: formState.breakOutThree,
    })
  }

  const isValid = isWorkingTimeValid(param.workShiftBreaks ?? [], param.checkInTime || '00:00:00', param.checkOutTime || '00:00:00')
  if (!isValid) {
    isModalOpen.value = true
    return
  }

  if (isAdd.value) {
    await createNewWorkshift(param)
    changeCurrent(current.value)
  }
  else {
    if (!formState.workShiftId)
      return
    await updateWorkshift(formState.workShiftId, param)
    changeCurrent(current.value)
  }
  isLoading.value = false
}

const handleCancel = () => {
  isModalOpen.value = false
}

// const onSearch = () => {
//   logger.log('onSearch')
// }

// const onSelectTime = (time: Dayjs, type: string) => {
//   logger.log('time', time)
//   switch (type) {
//     case 'checkInTime':
//       formState.checkInTime = time.format('HH:mm:ss')
//       break
//     case 'checkOutTime':
//       formState.checkOutTime = time.format('HH:mm:ss')
//       break
//     case 'breakInOne':
//       formState.breakInOne = time.format('HH:mm:ss')
//       break
//     case 'breakOutOne':
//       formState.breakOutOne = time.format('HH:mm:ss')
//       break
//     case 'breakInTwo':
//       formState.breakInTwo = time.format('HH:mm:ss')
//       break
//     case 'breakOutTwo':
//       formState.breakOutTwo = time.format('HH:mm:ss')
//       break
//     case 'breakInThree':
//       formState.breakInThree = time.format('HH:mm:ss')
//       break
//     case 'breakOutThree':
//       formState.breakOutThree = time.format('HH:mm:ss')
//       break
//     default:
//       break
//   }
// }

const editRecord = (record: WorkshiftItem) => {
  editTableData[record.workShiftId] = {
    ...record,
  }
  logger.log('editTableData', editTableData)
}

// const handleSelect = (time: Dayjs, workShiftId: string, typeOfTime: string, breakTimeIndex?: number) => {
//   const workShiftBreaks: BreakTimeItem[] = editTableData[workShiftId].workShiftBreaks ?? []
//   switch (typeOfTime) {
//     case 'checkInTime':
//       editTableData[workShiftId].checkInTime = time.format('HH:mm:ss')
//       break
//     case 'checkOutTime':
//       editTableData[workShiftId].checkOutTime = time.format('HH:mm:ss')
//       break
//     case 'breakIn':
//       workShiftBreaks[breakTimeIndex!].breakInTime = time.format('HH:mm:ss')
//       break
//     case 'breakOut':
//       workShiftBreaks[breakTimeIndex!].breakOutTime = time.format('HH:mm:ss')
//       break
//     default:
//       break
//   }
// }

const onRemoveBreak = (breakTime: BreakTimeItem[], index: number) => {
  breakTime.splice(index, 1)
}

// Thực hiện add break
const onAddBreak = (workShiftBreaks: BreakTimeItem[]) => {
  logger.log('workShiftBreaks', workShiftBreaks)
  if (workShiftBreaks.length < 3)
    workShiftBreaks.push({ breakInTime: '00:00:00', breakOutTime: '00:00:00' })
}

const cancelRecord = (record: WorkshiftItem) => {
  delete editTableData[record.workShiftId]
  changeCurrent(current.value)
}

const saveRecord = async (record: WorkshiftItem) => {
  const params: WorkshiftParam = {
    workShiftCode: editTableData[record.workShiftId].workShiftCode,
    workShiftName: editTableData[record.workShiftId].workShiftName,
    checkInTime: dayjs(editTableData[record.workShiftId].checkInTime, 'HH:mm').format('HH:mm:ss'),
    checkOutTime: dayjs(editTableData[record.workShiftId].checkOutTime, 'HH:mm').format('HH:mm:ss'),
    workShiftBreaks: (editTableData[record.workShiftId].workShiftBreaks ?? []).map((item: BreakTimeItem) => {
      return {
        breakInTime: dayjs(item.breakInTime, 'HH:mm').format('HH:mm:ss'),
        breakOutTime: dayjs(item.breakOutTime, 'HH:mm').format('HH:mm:ss'),
      }
    }),
    description: editTableData[record.workShiftId].description,
  }
  if (!isWorkingTimeValid(params.workShiftBreaks ?? [], params.checkInTime || '00:00:00', params.checkOutTime || '00:00:00')) {
    return
  }
  if (!record?.workShiftId)
    return
  await updateWorkshift(record.workShiftId, params)
  cancelRecord(record)
}

onMounted(() => {
})
</script>

<template>
  <page-container>
    <a-row :gutter="24">
      <a-col :xxl="24" :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
        <a-card :style="{ marginBottom: '24px' }" :bordered="false">
          <template #title>
            <a-card :bordered="false" :body-style="{ padding: 0 }">
              <a-row style="font-weight: normal">
                <a-col :span="14">
                  <!-- <a-input-search
                    v-model:value="defaultParams.SearchParams"
                    :placeholder="t('input.placeholder')"
                    style="width: 270px"
                    @search="onSearch"
                  /> -->
                </a-col>
                <a-col :span="10" class="flex flex-justify-end">
                  <a-button type="primary" class="flex items-center" @click="openModal(null, true)">
                    <PlusOutlined /> {{ t("button.add") }}
                  </a-button>
                </a-col>
              </a-row>
            </a-card>
          </template>
          <a-table
            :columns="columns"
            :data-source="dataSource?.items"
            :loading="loading"
            row-key="workShiftId"
            :pagination="pagination"
            @change="handleTableChange"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'workShiftCode'">
                <div v-if="!editTableData[record.workShiftId]">
                  <span v-if="record.workShiftCode">
                    {{ record.workShiftCode }}
                  </span>
                </div>
                <div v-else>
                  <a-input
                    v-model:value="editTableData[record.workShiftId].workShiftCode"
                  />
                </div>
              </template>
              <template v-if="column.dataIndex === 'workShiftName'">
                <div v-if="!editTableData[record.workShiftId]">
                  <span v-if="record.workShiftName">
                    {{ record.workShiftName }}
                  </span>
                </div>
                <div v-else>
                  <a-input
                    v-model:value="editTableData[record.workShiftId].workShiftName"
                  />
                </div>
              </template>
              <template v-if="column.dataIndex === 'checkInTime'">
                <div v-if="!editTableData[record.workShiftId]">
                  <span v-if="record.checkInTime">
                    {{ record.checkInTime.slice(0, 5) }}
                  </span>
                </div>
                <div v-else>
                  <!-- <a-time-picker
                    v-model:value="editTableData[record.workShiftId].checkInTime"
                    value-format="HH:mm"
                    format="HH:mm"
                    @select="handleSelect($event, record.workShiftId, 'checkInTime')"
                  /> -->
                  <TimePicker 
                    v-model:value="editTableData[record.workShiftId].checkInTime"
                    value-type="string"
                    value-format="HH:mm:ss"
                  />
                </div>
              </template>
              <template v-if="column.dataIndex === 'checkOutTime'">
                <div v-if="!editTableData[record.workShiftId]">
                  <span v-if="record.checkOutTime">
                    {{ record.checkOutTime.slice(0, 5) }}
                  </span>
                </div>
                <div v-else>
                  <!-- <a-time-picker
                    v-model:value="editTableData[record.workShiftId].checkOutTime"
                    value-format="HH:mm"
                    format="HH:mm"
                    @select="handleSelect($event, record.workShiftId, 'checkOutTime')"
                  /> -->
                  <TimePicker 
                    v-model:value="editTableData[record.workShiftId].checkOutTime"
                    value-type="string"
                    value-format="HH:mm:ss"
                  />
                </div>
              </template>
              <template v-if="column.dataIndex === 'status'">
                <a-switch
                  v-model:checked="record.status"
                  @change="showConfirm(record as WorkshiftItem)"
                />
              </template>
              <template v-if="column.dataIndex === 'workShiftBreaks'">
                <template v-if="editTableData[record.workShiftId]">
                  <div class="flex flex-col gap-y-[10px]">
                    <div
                      v-for="(item, index) in editTableData[record.workShiftId].workShiftBreaks" :key="index"
                      class="flex gap-x-[10px]"
                    >
                      <!-- <a-time-picker
                        v-if="item.breakInTime"
                        v-model:value="item.breakInTime"
                        value-format="HH:mm"
                        format="HH:mm"
                        @select="handleSelect($event, record.workShiftId, 'breakIn', index)"
                      />
                      <a-time-picker
                        v-if="item.breakOutTime"
                        v-model:value="item.breakOutTime"
                        value-format="HH:mm"
                        format="HH:mm"
                        @select="handleSelect($event, record.workShiftId, 'breakOut', index)"
                      /> -->
                      <TimePicker 
                        v-if="item.breakInTime"
                        v-model:value="item.breakInTime"
                        value-type="string"
                        value-format="HH:mm:ss"
                      />
                      <TimePicker 
                        v-if="item.breakOutTime"
                        v-model:value="item.breakOutTime"
                        value-type="string"
                        value-format="HH:mm:ss"
                      />
                      <div class="flex items-center">
                        <a-tooltip>
                          <template #title>
                            削除
                          </template>
                          <MinusOutlined class="hover:text-red-500" @click="onRemoveBreak(editTableData[record.workShiftId].workShiftBreaks!, index)" />
                        </a-tooltip>
                      </div>
                    </div>
                    <div class="w-full flex justify-end">
                      <a-tooltip>
                        <template #title>
                          休憩時間追加
                        </template>
                        <button
                          class="w-full min-w-[50px] h-[30px] mr-[22px] border border-gray-300 rounded hover:bg-blue-100 hover:border-blue-200"
                          @click="onAddBreak(editTableData[record.workShiftId].workShiftBreaks!)"
                        >
                          <PlusOutlined />
                        </button>
                      </a-tooltip>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <template v-for="(item) in record.workShiftBreaks" :key="item">
                    <span>{{ dayjs(item.breakInTime, 'HH:mm').format('HH:mm') }} - {{ dayjs(item.breakOutTime, 'HH:mm').format('HH:mm') }}</span><br>
                  </template>
                </template>
              </template>
              <template v-if="column.dataIndex === 'description'">
                <div v-if="!editTableData[record.workShiftId]">
                  <span v-if="record.description">
                    {{ record.description }}
                  </span>
                </div>
                <div v-else>
                  <a-input
                    v-model:value="editTableData[record.workShiftId].description"
                  />
                </div>
              </template>
              <template v-if="column.dataIndex === 'actions'">
                <div v-if="!editTableData[record.workShiftId]">
                  <a-button
                    type="text" @click="editRecord(record as WorkshiftItem)"
                  >
                    <CarbonEdit />
                  </a-button>
                  <a-button
                    type="text" @click="showConfirm(record as WorkshiftItem)"
                  >
                    <CarbonDelete />
                  </a-button>
                </div>
                <div v-else class="flex justify-center gap-x-[10px]">
                  <a-button type="text" class="text-red-500" @click="cancelRecord(record as WorkshiftItem)">
                    <CloseOutlined />
                  </a-button>
                  <a-button type="text" class="text-green-500" @click="saveRecord(record as WorkshiftItem)">
                    <CheckOutlined />
                  </a-button>
                </div>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>

    <a-modal
      v-model:open="isModalOpen"
      :title="t('button.add')"
      :ok-text="t('button.add')"
    >
      <template #footer>
        <a-button key="back" @click="handleCancel">
          {{ t('button.cancel') }}
        </a-button>
        <a-button key="submit" type="primary" @click="handleOk">
          {{ t('button.add') }}
        </a-button>
      </template>
      <a-form
        :model="formState"
        layout="vertical"
      >
        <a-form-item
          :label="t('form.workShiftCode')"
          name="workShiftCode"
          :rules="[{ required: true, message: t('form.required', { msg: t('form.workShiftCode') }) }]"
        >
          <a-input v-model:value="formState.workShiftCode" :placeholder="t('form.workShiftCode')" />
        </a-form-item>
        <a-form-item
          :label="t('form.workShiftName')"
          name="workShiftName"
          :rules="[{ required: true, message: t('form.required', { msg: t('form.workShiftName') }) }]"
        >
          <a-input v-model:value="formState.workShiftName" :placeholder="t('form.workShiftName')" />
        </a-form-item>
        <div class="flex justify-between gap-x-[30px]">
          <div class="flex flex-col gap-y-[10px]">
            <div>
              <p>{{ t('form.workTime') }}</p>
            </div>
            <div class="flex gap-x-[10px] h-[30px]">
              <a-form-item
                name="checkInTime"
              >
                <!-- <a-time-picker
                  v-model:value="formState.checkInTime"
                  value-format="HH:mm:ss"
                  format="HH:mm"
                  :minute-step="5"
                  @select="onSelectTime($event, 'checkInTime')"
                /> -->
                <TimePicker 
                  v-model:value="formState.checkInTime"
                  value-type="string"
                  value-format="HH:mm:ss"
                  :minute-step="5"
                />
              </a-form-item>
              <ArrowRightOutlined />
              <a-form-item
                name="checkOutTime"
              >
                <!-- <a-time-picker
                  v-model:value="formState.checkOutTime"
                  value-format="HH:mm:ss"
                  format="HH:mm"
                  :minute-step="5"
                  @select="onSelectTime($event, 'checkOutTime')"
                /> -->
                <TimePicker 
                  v-model:value="formState.checkOutTime"
                  value-type="string"
                  value-format="HH:mm:ss"
                  :minute-step="5"
                />
              </a-form-item>
            </div>
          </div>
          <div class="flex flex-col gap-y-[10px]">
            <div>
              <p>{{ t('form.breakTime') }}</p>
            </div>
            <div class="flex gap-x-[10px] h-[30px]">
              <a-form-item
                name="breakInOne"
              >
                <!-- <a-time-picker
                  v-model:value="formState.breakInOne"
                  value-format="HH:mm:ss"
                  format="HH:mm"
                  :minute-step="5"
                  @select="onSelectTime($event, 'breakInOne')"
                /> -->
                <TimePicker 
                  v-model:value="formState.breakInOne"
                  value-type="string"
                  value-format="HH:mm:ss"
                  :minute-step="5"
                />
              </a-form-item>
              <div class="flex items-center">
                <ArrowRightOutlined />
              </div>
              <a-form-item
                name="breakOutOne"
              >
                <!-- <a-time-picker
                  v-model:value="formState.breakOutOne"
                  value-format="HH:mm:ss"
                  format="HH:mm"
                  :minute-step="5"
                  @select="onSelectTime($event, 'breakOutOne')"
                /> -->
                <TimePicker 
                  v-model:value="formState.breakOutOne"
                  value-type="string"
                  value-format="HH:mm:ss"
                  :minute-step="5"
                />
              </a-form-item>
            </div>
            <div class="flex gap-x-[10px] h-[30px]">
              <a-form-item
                name="breakInTwo"
              >
                <!-- <a-time-picker
                  v-model:value="formState.breakInTwo"
                  value-format="HH:mm:ss"
                  format="HH:mm"
                  @select="onSelectTime($event, 'breakInTwo')"
                /> -->
                <TimePicker 
                  v-model:value="formState.breakInTwo"
                  value-type="string"
                  value-format="HH:mm:ss"
                />
              </a-form-item>
              <div class="flex items-center">
                <ArrowRightOutlined />
              </div>
              <a-form-item
                name="breakOutTwo"
              >
                <!-- <a-time-picker
                  v-model:value="formState.breakOutTwo"
                  value-format="HH:mm:ss"
                  format="HH:mm"
                  @select="onSelectTime($event, 'breakOutTwo')"
                /> -->
                <TimePicker 
                  v-model:value="formState.breakOutTwo"
                  value-type="string"
                  value-format="HH:mm:ss"
                />
              </a-form-item>
            </div>
            <div class="flex gap-x-[10px] h-[30px]">
              <a-form-item
                name="breakInThree"
              >
                <!-- <a-time-picker
                  v-model:value="formState.breakInThree"
                  value-format="HH:mm:ss"
                  format="HH:mm"
                  @select="onSelectTime($event, 'breakInThree')"
                /> -->
                <TimePicker 
                  v-model:value="formState.breakInThree"
                  value-type="string"
                  value-format="HH:mm:ss"
                />
              </a-form-item>
              <div class="flex items-center">
                <ArrowRightOutlined />
              </div>
              <a-form-item
                name="breakOutThree"
              >
                <!-- <a-time-picker
                  v-model:value="formState.breakOutThree"
                  value-format="HH:mm:ss"
                  format="HH:mm"
                  @select="onSelectTime($event, 'breakOutThree')"
                /> -->
                <TimePicker 
                  v-model:value="formState.breakOutThree"
                  value-type="string"
                  value-format="HH:mm:ss"
                />
              </a-form-item>
            </div>
          </div>
        </div>
        <!-- <div class="flex justify-between">
          <a-form-ite
            :label="t('form.breakTimeOne')"
            name="breakTimeOne"
          >
            <a-time-range-picker
              v-model:value="formState.breakTimeOne"
              value-format="HH:mm:ss"
              format="HH:mm"
              @select="onSelectTime($event, 'breakTimeOne')"
            />
          </a-form-item>
        </div> -->
        <a-form-item
          :label="t('form.description')"
          name="description"
        >
          <a-input v-model:value="formState.description" :placeholder="t('form.description')" />
        </a-form-item>
        <!-- <a-form-item
          :label="t('form.status')"
          name="status"
        >
          <a-switch v-model:checked="formState.status" />
        </a-form-item> -->
      </a-form>
    </a-modal>
  </page-container>
</template>
