const logger = {
  log: (message: any, ...optionalParams: any[]) => {
    if (import.meta.env.MODE === 'development')
      console.log(message, ...optionalParams)
  },
  warn: (message: any, ...optionalParams: any[]) => {
    if (import.meta.env.MODE === 'development')
      console.warn(message, ...optionalParams)
  },
  error: (message: any, ...optionalParams: any[]) => {
    if (import.meta.env.MODE === 'development')
      console.error(message, ...optionalParams)
  },
}

export default logger
