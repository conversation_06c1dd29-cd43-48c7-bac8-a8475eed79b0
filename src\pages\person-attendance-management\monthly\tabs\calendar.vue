<script lang="ts" setup>
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import _, { isEmpty } from 'lodash'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import SummaryCard from '../components/summary-card.vue'
import type { AttendanceEmployeeSummaryParams, AttendanceItem, EmployeeAttendanceSummaryResponse } from '~@/api/attendance'
import { getAttendanceByDateApi, getAttendanceEmployeeSummaryApi } from '~@/api/attendance'
import type { RequestItem } from '~@/api/dashboard/date-off-request'
import { getRequestByAuNoPaginationApi } from '~@/api/dashboard/date-off-request'
import logger from '~@/utils/logger'
import type { EventCalendarRuleItem } from '~@/api/company/event-calendar'
import { getEventCalendarDate } from '~@/api/company/event-calendar'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'

dayjs.extend(isSameOrBefore)
dayjs.extend(isSameOrAfter)

type AttendanceItemCustom = AttendanceItem & {
  isAttendance?: boolean
  isAbsent?: boolean
  isSchedule?: boolean
}

const { t } = useI18n()
const selectedDate = ref(dayjs())
const requestData = ref<RequestItem[]>([])
const attendanceData = ref<AttendanceItemCustom[]>([])
const eventCalendar = ref<EventCalendarRuleItem[]>([])
const attendances = ref<Record<string, number>>({})
const absents = ref<Record<string, number>>({})
const schedules = ref<Record<string, number>>({})
const requests = ref<Record<string, number>>({})
const calendarLeft = ref()
const { height: windowHeight } = useWindowScrollSize()
const { height: calendarLeftHeight } = useElementSize(calendarLeft)
const attendanceDataSummary = ref<EmployeeAttendanceSummaryResponse>({
  totalAbsentDays: 0,
  totalOverTime: 0,
  totalPaidLeaveUsed: 0,
  totalUnpaidLeaveUsed: 0,
  totalWorkingDays: 0,
  totalWorkTime: 0,
})

async function getRequestByDate() {
  try {
    const { data } = await getRequestByAuNoPaginationApi({
      fromDate: selectedDate.value.startOf('month').format('YYYY-MM-DD'),
      toDate: selectedDate.value.endOf('month').format('YYYY-MM-DD'),
    })
    requestData.value = data?.items ?? []
  }
  catch (e) {
    logger.error(e)
  }
}

function initializeDays(
  state: Record<string, number>,
  dateFrom: Dayjs,
  dateTo: Dayjs,
  reset: boolean = false,
) {
  if (reset)
    for (const key in state) delete state[key]

  const startDate = dayjs(dateFrom).startOf('month')
  const endDate = dayjs(dateTo).endOf('month')
  for (
    let current = startDate;
    current.isSameOrBefore(endDate, 'day');
    current = current.add(1, 'day')
  )
    state[current.format('YYYY-MM-DD')] = 0
}

function initializeWorkingDay() {
  initializeDays(
    attendances.value,
    selectedDate.value,
    selectedDate.value,
    true,
  )
  initializeDays(absents.value, selectedDate.value, selectedDate.value, true)
  initializeDays(schedules.value, selectedDate.value, selectedDate.value, true)
  initializeDays(requests.value, selectedDate.value, selectedDate.value, true)

  attendanceData.value.forEach((item: AttendanceItemCustom) => {
    const isBefore = dayjs(item.workingDate).isBefore(dayjs(), 'day')
    const isSameOrAfter = dayjs(item.workingDate).isSameOrAfter(dayjs(), 'day')

    if (!isEmpty(item.checkInTime)) {
      attendances.value[item.workingDate ?? '0'] += 1
      item.isAttendance = true
    }

    if (isEmpty(item.checkInTime) && isBefore) {
      absents.value[item.workingDate ?? '0'] += 1
      item.isAbsent = true
    }

    if (
      !isEmpty(item.scheduledStartTime)
      && isEmpty(item.checkInTime)
      && isSameOrAfter
    ) {
      schedules.value[item.workingDate ?? '0'] += 1
      item.isSchedule = true
    }
  })

  requestData.value.forEach((item: RequestItem) => {
    for (
      let current = dayjs(item.requestFrom);
      current.isSameOrBefore(dayjs(item.requestTo), 'day');
      current = current.add(1, 'day')
    )
      requests.value[current.format('YYYY-MM-DD') ?? '0'] += 1
  })
}

async function getAttendanceSummary() {
  try {
    const params: AttendanceEmployeeSummaryParams = {
      fromDate: selectedDate.value.startOf('month').format('YYYY-MM-DD'),
      toDate: selectedDate.value.endOf('month').format('YYYY-MM-DD'),
    }
    const { data, status } = await getAttendanceEmployeeSummaryApi(params)
    if (status === ResponseStatusEnum.SUCCESS) {
      attendanceDataSummary.value = {
        totalAbsentDays: data?.totalAbsentDays ?? 0,
        totalOverTime: data?.totalOverTime ?? 0,
        totalPaidLeaveUsed: data?.totalPaidLeaveUsed ?? 0,
        totalUnpaidLeaveUsed: data?.totalUnpaidLeaveUsed ?? 0,
        totalWorkingDays: data?.totalWorkingDays ?? 0,
        totalWorkTime: data?.totalWorkTime ?? 0,
      }
    }
    else {
      console.log(data)
    }
  }
  catch (e) {
    console.error(e)
  }
}

async function getAttendanceByDate() {
  try {
    const { data } = await getAttendanceByDateApi({
      fromDate: selectedDate.value.startOf('month').format('YYYY-MM-DD'),
      toDate: selectedDate.value.endOf('month').format('YYYY-MM-DD'),
    })

    attendanceData.value = data?.items ?? []
  }
  catch (e) {
    logger.error(e)
    throw e
  }
}

async function getEventCalendar() {
  try {
    const { data } = await getEventCalendarDate({
      from: selectedDate.value.startOf('month').format('YYYY-MM-DD'),
      to: selectedDate.value.endOf('month').format('YYYY-MM-DD'),
    })
    eventCalendar.value = data?.eventCalendars ?? []
  }
  catch (e) {
    logger.error(e)
  }
}

const isDayOff = computed(() => {
  return (current: Dayjs) => {
    const findItem = eventCalendar.value.find((item) => {
      const applyDate = dayjs(item.applyDate, 'YYYY-MM-DD')
      const isSame = applyDate.isSame(current, 'day')
      return isSame
    })
    return findItem?.isDayOff ?? false
  }
})

const showAttendance = computed(() => {
  return (current: Dayjs) => {
    const isBefore = dayjs(current.format('YYYY-MM-DD')).isBefore(
      dayjs(),
      'day',
    )
    const isSame = dayjs(current.format('YYYY-MM-DD')).isSame(dayjs(), 'day')
    let isAttendance = attendances.value[current.format('YYYY-MM-DD')] > 0
    let isSchedule = schedules.value[current.format('YYYY-MM-DD')] > 0
    let isAbsent = absents.value[current.format('YYYY-MM-DD')] > 0

    if (isBefore && isAttendance && isAbsent) {
      isAttendance = true
      isAbsent = false
    }

    if (isSame && isAttendance && isSchedule) {
      isSchedule = true
      isAttendance = false
    }

    return {
      isAttendance,
      isSchedule,
      isAbsent,
    }
  }
})

const itemHeight = computed(() => {
  const heightCalendar = windowHeight.value - 281
  return Math.floor(heightCalendar / 6)
})

onMounted(async () => {
  await Promise.all([
    getAttendanceByDate(),
    getAttendanceSummary(),
    getRequestByDate(),
    getEventCalendar(),
  ])
  initializeWorkingDay()
})

async function refreshCalendar() {
  await Promise.all([
    getAttendanceByDate(),
    getRequestByDate(),
    getEventCalendar(),
  ])
  initializeWorkingDay()
}

watch(selectedDate, async (newDate, oldDate) => {
  if (newDate.month() === oldDate.month())
    return
  await refreshCalendar()
})

watch(
  () => t('locale'),
  async () => {
    await refreshCalendar()
  },
)
</script>

<template>
  <page-container>
    <a-row
      :gutter="[
        { xxl: 24, xl: 12 },
        { xxl: 24, xl: 12 },
      ]"
    >
      <a-col span="17">
        <a-card ref="calendarLeft" class="p-2 box-shadow">
          <a-calendar
            v-model:value="selectedDate"
            class="calendar"
            :valid-range="[
              selectedDate.startOf('month'),
              selectedDate.endOf('month'),
            ]"
          >
            <template #headerRender="{ value: current, onChange }">
              <div class="flex justify-center pb-2">
                <div
                  class="flex items-center justify-center gap-10 font-bold text-base"
                >
                  <LeftOutlined @click="onChange(current.add(-1, 'month'))" />
                  <span>
                    {{ current.locale($t('locale')).format('YYYY年MM月') }}
                  </span>
                  <RightOutlined @click="onChange(current.add(1, 'month'))" />
                </div>
              </div>
            </template>
            <template #dateFullCellRender="{ current }">
              <div
                class="flex flex-col justify-between"
                :class="{
                  'bg-red-200': isDayOff(current),
                  '!bg-orange-300': selectedDate.isSame(current),
                }"
                :style="{ height: `${itemHeight}px` }"
              >
                <div class="flex justify-center font-medium text-base 2xl:p-2">
                  {{ current.date() }}
                </div>
                <div class="flex flex-col gap-1 px-2 pb-1 2xl:py-2">
                  <a-tag
                    v-if="requests[current.format('YYYY-MM-DD')]"
                    color="orange"
                    class="w-full text-center text-xs 2xl:text-sm"
                  >
                    {{ $t('calendar.request') }}
                    {{ `(${requests[current.format('YYYY-MM-DD')]})` }}
                  </a-tag>
                  <a-tag
                    v-if="showAttendance(current).isAttendance"
                    color="green"
                    class="w-full text-center text-xs 2xl:text-sm"
                  >
                    {{ $t('calendar.attendance') }}
                    {{ `(${attendances[current.format('YYYY-MM-DD')]})` }}
                  </a-tag>
                  <a-tag
                    v-if="showAttendance(current).isSchedule"
                    color="blue"
                    class="w-full text-center text-xs 2xl:text-sm"
                  >
                    {{ $t('calendar.scheduled') }}
                    {{ `(${schedules[current.format('YYYY-MM-DD')]})` }}
                  </a-tag>
                  <a-tag
                    v-if="showAttendance(current).isAbsent"
                    color="red"
                    class="w-full text-center text-xs 2xl:text-sm"
                  >
                    {{ $t('calendar.absent') }}
                    {{ `(${absents[current.format('YYYY-MM-DD')]})` }}
                  </a-tag>
                </div>
              </div>
            </template>
          </a-calendar>
        </a-card>
      </a-col>
      <a-col span="7">
        <SummaryCard
          :selected-date="selectedDate"
          :calendar-left-height="calendarLeftHeight"
          @refresh="refreshCalendar"
        />
      </a-col>
    </a-row>
  </page-container>
</template>

<style lang="less" scoped>
:deep(.calendar td) {
  border: 1px solid #cdcecd;
}
:deep(.calendar td.ant-picker-cell-disabled) {
  background-color: #f2f2f2;
}
.box-shadow {
  box-shadow: 0px 2px 4px 0px #0000001a;
}
</style>
