import * as XLSX from 'xlsx'
import { createSummarySheet } from './constructionCostSummaryExporter'
import { createConstructionCostWorkSheet } from './constructionCostExporter'
import { createConstructionSheet } from './constructionCostDetailExporter'
import type { ConstructionCostItem, CostAmountItem, EstimateBudgetItem } from '~@/api/construction-cost'

interface ExportOptions {
  mainCostAmount?: CostAmountItem
  subCostAmount?: CostAmountItem
  overallCostAmount?: CostAmountItem
  mainEstimateBudget?: EstimateBudgetItem
  subEstimateBudget?: EstimateBudgetItem
  overallEstimateBudget?: EstimateBudgetItem
  mainConstructionCost?: ConstructionCostItem
  subConstructionCost?: ConstructionCostItem
}

export async function exportSimulationToExcel({
  mainCostAmount,
  subCostAmount,
  overallCostAmount,
  mainEstimateBudget,
  subEstimateBudget,
  overallEstimateBudget,
  mainConstructionCost,
  subConstructionCost,
}: ExportOptions): Promise<void> {
  // Tạo workbook mới
  const wb = XLSX.utils.book_new()

  // Tạo các worksheets
  const constructionCostWS = createConstructionCostWorkSheet({ mainCostAmount, subCostAmount, overallCostAmount, mainEstimateBudget, subEstimateBudget, overallEstimateBudget })
  // Create a worksheet for each construction
  if (!mainConstructionCost || !subConstructionCost)
    return

  const mainConstructionSheet = createConstructionSheet(mainConstructionCost)
  const mainInputCostItemSheet = await createSummarySheet({
    constructionCostItem: mainConstructionCost,
    isPrimary: true,
  })
  const subConstructionSheet = createConstructionSheet(subConstructionCost)
  const subInputCostItemSheet = await createSummarySheet({
    constructionCostItem: subConstructionCost,
    isPrimary: false,
  })

  // Lấy dữ liệu từ cả hai sheets
  const constructionCostData = XLSX.utils.sheet_to_json(constructionCostWS, { header: 1 })
  const mainConstructionData = XLSX.utils.sheet_to_json(mainConstructionSheet, { header: 1 })
  const mainInputCostItemData = XLSX.utils.sheet_to_json(mainInputCostItemSheet, { header: 1 })
  const subConstructionData = XLSX.utils.sheet_to_json(subConstructionSheet, { header: 1 })
  const subInputCostItemData = XLSX.utils.sheet_to_json(subInputCostItemSheet, { header: 1 })

  // Thêm 2 dòng trống giữa hai bảng
  const combinedData = [
    ...constructionCostData,
    [], // Dòng trống
    [], // Dòng trống
    ...mainConstructionData,
    [], // Dòng trống
    [], // Dòng trống
    ...mainInputCostItemData,
    [], // Dòng trống
    [], // Dòng trống
    ...subConstructionData,
    [], // Dòng trống
    [], // Dòng trống
    ...subInputCostItemData,
  ]

  // Tạo worksheet mới từ dữ liệu đã kết hợp
  const combinedWS = XLSX.utils.aoa_to_sheet(combinedData as unknown[][])

  // Sao chép các thuộc tính định dạng từ cả hai sheets
  combinedWS['!merges'] = [
    ...(constructionCostWS['!merges'] || []),
    ...(mainConstructionSheet['!merges'] || []).map(merge => ({
      s: { r: merge.s.r + constructionCostData.length + 2, c: merge.s.c },
      e: { r: merge.e.r + constructionCostData.length + 2, c: merge.e.c },
    })),
    ...(mainInputCostItemSheet['!merges'] || []).map(merge => ({
      s: { r: merge.s.r + constructionCostData.length + mainConstructionData.length + 4, c: merge.s.c },
      e: { r: merge.e.r + constructionCostData.length + mainConstructionData.length + 4, c: merge.e.c },
    })),
    ...(subConstructionSheet['!merges'] || []).map(merge => ({
      s: { r: merge.s.r + constructionCostData.length + mainConstructionData.length + mainInputCostItemData.length + 6, c: merge.s.c },
      e: { r: merge.e.r + constructionCostData.length + mainConstructionData.length + mainInputCostItemData.length + 6, c: merge.e.c },
    })),
    ...(subInputCostItemSheet['!merges'] || []).map(merge => ({
      s: { r: merge.s.r + constructionCostData.length + mainConstructionData.length + mainInputCostItemData.length + subConstructionData.length + 8, c: merge.s.c },
      e: { r: merge.e.r + constructionCostData.length + mainConstructionData.length + mainInputCostItemData.length + subConstructionData.length + 8, c: merge.e.c },
    })),
  ]

  // Thêm sheet đã kết hợp vào workbook
  XLSX.utils.book_append_sheet(wb, combinedWS, '出来高書')

  // Export file
  const fileName = `出来高報告書_${new Date().toISOString().split('T')[0]}.xlsx`
  XLSX.writeFile(wb, fileName)
}
