<script setup lang="ts">
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import type { ColumnGroupType, ColumnType } from 'ant-design-vue/es/table'
import type { CheckboxChangeEvent } from 'ant-design-vue/es/checkbox/interface'
import { isEmpty } from 'lodash'
import {
  createRole<PERSON>pi,
  deleteRole<PERSON>pi,
  updateRoleApi,
} from '~@/api/company/role'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'
import type {
  FunctionMenuItem,
  RoleMenuItem,
  StructureMenuItem,
} from '~@/api/company/permission'
import {
  getAllPermissionMenu,
  getOnePermissionMenuStructure,
  updateOnePermissionMenu,
} from '~@/api/company/permission'
import type { MenuData, MenuDataItem } from '~@/layouts/basic-layout/typing'
import { ModalType } from '~@/enums/system-status-enum'
import logger from '~@/utils/logger'
import { menuLists } from '~@/utils/menuData'

type ColumnItemType<T> = (ColumnGroupType<T> | ColumnType<T>) & {
  parentKey?: string
}

type MenuDataType = MenuDataItem & { level: number }

interface FormState {
  roleId: string
  roleName: string
  description: string
  structureId: string
}

const { t } = useI18n()
const permissionMenuData = ref<StructureMenuItem[]>([])
const modalType = ref<ModalType>(ModalType.ADD)
const functionData = ref<MenuDataType[]>([])
const loading = ref<boolean>(false)
const isOpenModal = ref<boolean>(false)
const formState = reactive<FormState>({
  roleName: '',
  description: '',
  structureId: '',
  roleId: '',
})

const functionAccessColumns = computed<ColumnItemType<StructureMenuItem>[]>(
  () => {
    return [
      {
        title: '',
        dataIndex: 'Locale',
        key: 'id',
        width: 220,
        render: () => {
          return { props: { style: {} } }
        },
        fixed: 'left',
      },
      ...permissionMenuData.value.map((item: StructureMenuItem) => {
        return {
          title: item.StructureName,
          dataIndex: item.StructureId,
          key: item.StructureId,
          children: [
            ...(item.RoleItems ?? [])
              ?.sort((a, b) => a.RoleId.localeCompare(b.RoleId))
              ?.map((child) => {
                return {
                  title: child.RoleName,
                  dataIndex: 'Role',
                  key: child.RoleId,
                  parentKey: item.StructureId,
                  width: 150,
                }
              }),
            {
              title: 'action',
              dataIndex: 'action',
              key: item.StructureId,
              width: (item.RoleItems ?? []).length > 0 ? 50 : 150,
            },
          ],
        }
      }),
    ]
  },
)

async function loadPermissionMenu(structureId: string) {
  const structureData = await getOnePermissionMenuStructure(structureId)
  if (structureData.status !== ResponseStatusEnum.SUCCESS)
    return

  permissionMenuData.value = permissionMenuData.value.map(
    (item: StructureMenuItem) => {
      if (item.StructureId === structureId)
        return structureData.data ?? item
      return item
    },
  )
}

async function onFinish() {
  loading.value = true
  try {
    switch (modalType.value) {
      case ModalType.ADD: {
        const create = await createRoleApi({
          description: formState.description,
          roleName: formState.roleName,
          structureId: formState.structureId,
        })
        if (create.status !== ResponseStatusEnum.SUCCESS)
          break

        isOpenModal.value = false
        await loadPermissionMenu(formState.structureId)
        message.success(create.message)
        onReset()
        break
      }
      case ModalType.EDIT: {
        const update = await updateRoleApi(formState.roleId, {
          roleName: formState.roleName,
        })
        if (update.status !== ResponseStatusEnum.SUCCESS)
          break

        isOpenModal.value = false
        await loadPermissionMenu(formState.structureId)
        onReset()
        message.success(update.message)
        break
      }
      case ModalType.DELETE: {
        const del = await deleteRoleApi(formState.structureId)
        if (del.status !== ResponseStatusEnum.SUCCESS)
          break

        isOpenModal.value = false
        await loadPermissionMenu(formState.structureId)
        message.success(del.message)
        onReset()
        break
      }
    }
  }
  catch (error) {
  }
  finally {
    loading.value = false
  }
}

function openModal(column: ColumnItemType<StructureMenuItem>, type: ModalType) {
  const { key, parentKey, title } = column
  switch (type) {
    case ModalType.ADD:
      formState.structureId = key?.toString() ?? ''
      modalType.value = type
      isOpenModal.value = true
      break
    case ModalType.EDIT:
      formState.roleName = title?.toString() ?? ''
      formState.roleId = key?.toString() ?? ''
      formState.structureId = parentKey?.toString() ?? ''
      modalType.value = type
      isOpenModal.value = true
      break
    default:
      break
  }
}

const renderTitle = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
      return t('title.add-role')
    case ModalType.EDIT:
      return t('title.edit-role')
    default:
      return ''
  }
})

async function handleDeleteRole(column: ColumnItemType<StructureMenuItem>) {
  const { parentKey: structureId, key: roleId } = column
  loading.value = true

  try {
    const del = await deleteRoleApi(roleId?.toString() ?? '')
    if (del.status !== ResponseStatusEnum.SUCCESS)
      return

    await loadPermissionMenu(structureId?.toString() ?? '')
    message.success(del.message)
  }
  catch (error) {
    logger.log(error)
  }
  finally {
    loading.value = false
  }
}

function getMenuTree(data: MenuDataType[]) {
  const map = new Map()
  const result = []
  for (const item of data) {
    // if (item.hideInMenu)
    //   continue

    if (!map.has(item.name))
      map.set(item.name, { children: [], ...item })

    const parent = map.get(item.name)
    if (!item.parentName) {
      result.push(parent)
    }
    else {
      if (!map.has(item.parentName)) {
        map.set(item.parentName, {
          children: [],
          ...data.find((p: MenuDataType) => p.name === item.parentName),
        })
      }
      map.get(item.parentName).children.push(parent)
    }
  }

  const addLevel = (data: MenuDataType[], level: number) => {
    data.forEach((item: MenuDataType) => {
      item.level = level
      if (item.children)
        addLevel(item.children as MenuDataType[], level + 1)
    })
  }
  addLevel(result, 0)

  return result
}

onMounted(async () => {
  loading.value = true
  const res = await Promise.all([getAllPermissionMenu()])

  permissionMenuData.value = res[0].data?.StructureItems ?? []
  functionData.value = getMenuTree(menuLists as MenuDataType[])
  loading.value = false
})

function rowClassName(record: MenuDataType) {
  return `row-level-${record.level}`
}

function onReset() {
  formState.structureId = ''
  formState.roleId = ''
  formState.roleName = ''
}

const renderOkText = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
      return t('button.add')
    case ModalType.EDIT:
      return t('button.update')
    default:
      return ''
  }
})

const getExpandedRowKeys = computed(() => {
  const keys: string[] = []

  function deep(data: MenuData) {
    data.forEach((item: MenuDataItem) => {
      keys.push(item.name as string)
      if (item.children)
        deep(item.children)
    })
  }
  deep(functionData.value)

  return keys
})

const getValueCheckBox = computed(() => {
  return (
    column: ColumnItemType<StructureMenuItem>,
    functionName: string,
    permission: 'CanCreate' | 'CanDelete' | 'CanRead' | 'CanUpdate',
  ) => {
    const { key, parentKey } = column
    const findStructure = permissionMenuData.value.find(
      (item: StructureMenuItem) => item.StructureId === parentKey,
    )
    const findRole = findStructure?.RoleItems?.find(
      (item: RoleMenuItem) => item.RoleId === key,
    )
    const findFunction = findRole?.FunctionItems?.find(
      (item: FunctionMenuItem) => item.Title === functionName,
    )
    return findFunction?.[permission] ?? false
  }
})

const isShowRole = computed(() => {
  return (column: ColumnItemType<StructureMenuItem>) => {
    const { key, parentKey } = column
    const findStructure = permissionMenuData.value.find(
      (item: StructureMenuItem) => item.StructureId === parentKey,
    )
    const findRole = findStructure?.RoleItems?.find(
      (item: RoleMenuItem) => item.RoleId === key,
    )
    return !!findRole
  }
})

async function onChangeFunction(
  e: CheckboxChangeEvent,
  column: ColumnItemType<StructureMenuItem>,
  functionName: string,
  permission: 'CanCreate' | 'CanDelete' | 'CanRead' | 'CanUpdate',
) {
  const { key, parentKey } = column

  try {
    const findStructure = permissionMenuData.value.find(
      (item: StructureMenuItem) => item.StructureId === parentKey,
    )
    const findRole = findStructure?.RoleItems?.find(
      (item: RoleMenuItem) => item.RoleId === key,
    )
    const findFunction = findRole?.FunctionItems?.find(
      (item: FunctionMenuItem) => item.Title === functionName,
    ) as FunctionMenuItem
    if (findFunction && findRole) {
      findFunction[permission] = e.target.checked

      const update = await updateOnePermissionMenu({
        functionName: findFunction.Title,
        roleId: findRole?.RoleId,
        [permission]: e.target.checked,
      })
      if (update.status !== ResponseStatusEnum.SUCCESS)
        return

      message.success(update.message)
      await loadPermissionMenu(parentKey ?? '')
    }
  }
  catch (error) {
    logger.log(error)
  }
}
</script>

<template>
  <page-container>
    <a-table
      :loading="loading"
      class="tableRole"
      :data-source="functionData"
      row-key="name"
      children-column-name="children"
      bordered
      :pagination="false"
      :columns="functionAccessColumns"
      :expanded-row-keys="getExpandedRowKeys"
      :row-class-name="rowClassName"
      :scroll="{ x: 'max-content' }"
      :show-expand-column="false"
      :sticky="{ offsetHeader: 60 }"
    >
      <template #headerCell="{ column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="flex justify-center items-center">
            <a-button
              class="flex items-center justify-center"
              size="small"
              type="dashed"
              color="primary"
              shape="circle"
              @click="openModal(column, ModalType.ADD)"
            >
              <PlusOutlined />
            </a-button>
          </div>
        </template>
        <template v-if="column.dataIndex === 'Role'">
          <div class="flex flex-col items-center gap-1">
            {{ column.title }}
            <div class="flex gap-1">
              <a-button
                class="flex items-center justify-center"
                type="dashed"
                size="small"
                color="primary"
                shape="circle"
                @click="openModal(column, ModalType.EDIT)"
              >
                <EditOutlined />
              </a-button>
              <a-popconfirm
                :title="t('message.delete-confirmation')"
                @confirm="() => handleDeleteRole(column)"
              >
                <a-button
                  class="flex items-center justify-center"
                  type="dashed"
                  size="small"
                  danger
                  shape="circle"
                >
                  <DeleteOutlined />
                </a-button>
              </a-popconfirm>
            </div>
          </div>
        </template>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'Locale'">
          <span>{{ t(record?.locale) }}</span>
        </template>
        <template v-if="column.dataIndex === 'Role'">
          <template v-if="isEmpty(record?.children) && isShowRole(column)">
            <div class="flex justify-center items-center">
              <div class="flex flex-col gap-1 justify-center items-center">
                <SearchOutlined color="#87d068" />
                <a-checkbox
                  :checked="getValueCheckBox(column, record.name, 'CanRead')"
                  @change="
                    onChangeFunction($event, column, record.name, 'CanRead')
                  "
                />
              </div>
              <a-divider type="vertical" class="h-10" />
              <div class="flex flex-col gap-1 justify-center items-center">
                <PlusOutlined color="#2db7f5" />
                <a-checkbox
                  :checked="getValueCheckBox(column, record.name, 'CanCreate')"
                  @change="
                    onChangeFunction($event, column, record.name, 'CanCreate')
                  "
                />
              </div>
              <a-divider type="vertical" class="h-10" />
              <div class="flex flex-col gap-1 justify-center items-center">
                <EditOutlined color="#108ee9" />
                <a-checkbox
                  :checked="getValueCheckBox(column, record.name, 'CanUpdate')"
                  @change="
                    onChangeFunction($event, column, record.name, 'CanUpdate')
                  "
                />
              </div>
              <a-divider type="vertical" class="h-10" />
              <div class="flex flex-col gap-1 justify-center items-center">
                <DeleteOutlined color="#ff4d4f" />
                <a-checkbox
                  :checked="getValueCheckBox(column, record.name, 'CanDelete')"
                  @change="
                    onChangeFunction($event, column, record.name, 'CanDelete')
                  "
                />
              </div>
            </div>
          </template>
        </template>
      </template>
    </a-table>

    <a-modal
      v-model:open="isOpenModal"
      :title="renderTitle"
      width="600px"
      :footer="false"
      @cancel="onReset"
    >
      <a-card :loading="loading" class="ant-pro-basicLayout">
        <a-form
          :model="formState"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
          autocomplete="off"
          @finish="onFinish"
        >
          <a-row :gutter="[12, 12]">
            <a-col span="24">
              <a-form-item
                :label="t('roleName')"
                name="roleName"
                :rules="[
                  {
                    required: true,
                    message: 'Please input your role name!',
                  },
                ]"
              >
                <a-input v-model:value="formState.roleName" />
              </a-form-item>
            </a-col>
            <a-col v-show="modalType === ModalType.ADD" span="24">
              <a-form-item
                :label="t('description')"
                name="description"
                :rules="[
                  {
                    required: false,
                    message: t('placeholder.select-data', {
                      msg: t('description'),
                    }),
                  },
                ]"
              >
                <a-input v-model:value="formState.description" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span="24" class="flex gap-2 flex-justify-end">
              <a-button @click="onReset">
                {{ t('button.reset') }}
              </a-button>
              <a-button type="primary" html-type="submit">
                {{ renderOkText }}
              </a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </a-modal>
  </page-container>
</template>

<style scoped lang="less">
.tableRole {
  :deep(.row-level-0 > .ant-table-cell) {
    background-color: oklch(0.707 0.022 261.325) !important;
  }
  :deep(.row-level-1 > .ant-table-cell:first-child) {
    padding-left: 25px;
  }
  :deep(.row-level-1 > .ant-table-cell) {
    background: oklch(0.872 0.01 258.338) !important;
  }
  :deep(.row-level-2 > .ant-table-cell:first-child) {
    padding-left: 50px;
  }
  :deep(.row-level-2 > .ant-table-cell) {
    background: oklch(0.928 0.006 264.531) !important;
  }
  :deep(.row-level-3 > .ant-table-cell:first-child) {
    padding-left: 75px;
  }
  :deep(.row-level-3 > .ant-table-cell) {
    background: oklch(0.967 0.003 264.542) !important;
  }
  :deep(.row-level-4 > .ant-table-cell:first-child) {
    padding-left: 100px;
  }
  :deep(.row-level-4 > .ant-table-cell) {
    background: oklch(0.985 0.002 247.839) !important;
  }
}
</style>
