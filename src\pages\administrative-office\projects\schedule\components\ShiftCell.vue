<script lang="ts" setup>
import dayjs from 'dayjs'
import type { EmployeeShift, MergedOutsourceShift, MergedShift, OutsourceShift } from '~@/api/company/schedule'
import { Skills } from '~@/utils/constant'
import { useAvatarStore } from '~@/stores/avatar'

const props = defineProps({
  projectId: {
    type: String,
    required: true,
  },
  titleDate: {
    type: String,
    required: true,
  },
  shifts: {
    type: Array as PropType<EmployeeShift[]>,
    required: true,
  },
  outsourceShifts: {
    type: Array as PropType<OutsourceShift[]>,
    required: true,
  },
  isEmployeeDragging: {
    type: Boolean,
    required: true,
  },
  isDraggingEnter: {
    type: Object as PropType<Record<string, boolean>>,
    required: true,
  },
  isClickedEmployeeShift: {
    type: Object as PropType<Record<string, boolean>>,
    required: true,
  },
})

const emit = defineEmits<{
  (event: 'handleProjectClicked', projectId: string): void
  (event: 'saveTargetInfo', projectId: string, workingDate: string): void
  (event: 'onEmployeeDrop', evt: DragEvent, projectId: string, workingDate: string, key: string): void
  (event: 'onOutsourceDrop', evt: DragEvent, projectId: string, workingDate: string, key: string): void
  (event: 'onEmployeeDragOver', evt: DragEvent): void
  (event: 'onEmployeeDragEnter', evt: DragEvent, key: string): void
  (event: 'onEmployeeDragLeave', evt: DragEvent, key: string): void
  (event: 'showShiftInfo', employeeShift: EmployeeShift, projectId: string): void
  (event: 'showOutsourceShiftInfo', outsourceShift: OutsourceShift, projectId: string): void
}>()

const avatarStore = useAvatarStore()

const generateWeekDays = computed(() => {
  const startOfWeek = dayjs(props.titleDate).startOf('week')
  const weekDays = []
  for (let i = 0; i < 7; i++)
    weekDays.push(startOfWeek.add(i, 'day').format('YYYY-MM-DD'))

  return weekDays
})

const shiftsComputed = computed(() => {
  const startOfWeek = dayjs(props.titleDate).startOf('week')
  const newShifts: MergedShift[] = []
  for (let i = 0; i < 7; i++) {
    const date = startOfWeek.add(i, 'day').format('YYYY-MM-DD')
    const shifts = props.shifts?.filter(shift => shift.workingDate === date)
    newShifts.push({
      workingDate: date,
      employeeShifts: shifts ?? [],
    })
  }
  return newShifts
})

const outsourceShiftsComputed = computed(() => {
  const startOfWeek = dayjs(props.titleDate).startOf('week')
  const newShifts: MergedOutsourceShift[] = []
  for (let i = 0; i < 7; i++) {
    const date = startOfWeek.add(i, 'day').format('YYYY-MM-DD')
    const shifts = props.outsourceShifts?.filter(shift => shift.workingDate === date)
    newShifts.push({
      workingDate: date,
      outsourceShifts: shifts ?? [],
    })
  }
  return newShifts
})

const { t } = useI18n()
function saveTargetInfo(projectId: string, workingDate: string) {
  emit('saveTargetInfo', projectId, workingDate)
}

function onEmployeeDrop(event: DragEvent, projectId: string, workingDate: string, key: string) {
  event.preventDefault()
  if (event.target instanceof HTMLElement)
    event.target?.classList.remove('active')
  emit('onEmployeeDrop', event, projectId, workingDate, key)
}

// function onOutsourceDrop(event: DragEvent, projectId: string, workingDate: string, key: string) {
//   event.preventDefault()
//   if (event.target instanceof HTMLElement)
//     event.target?.classList.remove('active')
//   emit('onOutsourceDrop', event, projectId, workingDate, key)
// }

function onEmployeeDragOver(event: DragEvent) {
  event.preventDefault()
}

function onEmployeeDragEnter(event: DragEvent, key: string) {
  event.preventDefault()
  if (event.target instanceof HTMLElement)
    event.target?.classList.add('active')
  emit('onEmployeeDragEnter', event, key)
}

function onEmployeeDragLeave(event: DragEvent, key: string) {
  if (event.target instanceof HTMLElement)
    event.target?.classList.remove('active')
  emit('onEmployeeDragLeave', event, key)
}

function getStyleByWorkingrole(assignedRole: string) {
  let style = ''
  switch (assignedRole) {
    case Skills.SUPERVISOR:
      style = 'bg-[#FFEAE8] text-[#A72326]'
      break
    case Skills.FOREMAN:
      style = 'bg-[#D0F3E9] text-[#11661E]'
      break
    case Skills.WORKER:
      style = 'bg-[#F2EEFF] text-[#53318E]'
      break
    case Skills.FORMWORK_WORKER:
      style = 'bg-[#F6F6D3] text-[#E66B0D]'
      break
    case Skills.HEAVY_MACHINERY_OPERATOR:
      style = 'bg-[#DEF0FF] text-[#24598E]'
      break
    case Skills.REBAR_WORKER:
      style = 'bg-[#FFE5CC] text-[#E66B0D]'
      break
    case Skills.CONTRACT_TYPE:
      style = 'bg#F1F6FF]'
      break
    case Skills.UNASSIGNED:
      style = 'bg-[#F1F6FF]'
      break

    default:
      style = 'bg-[#F1F6FF] text-[#1554A3]'
      break
  }
  return style
}

function getBorderStyleByWorkingRole(assignedRole: string) {
  let style = ''
  switch (assignedRole) {
    case Skills.SUPERVISOR:
      style = 'border-2 border-solid border-[#DDA2AC]'
      break
    case Skills.FOREMAN:
      style = 'border-2 border-solid border-[#A3DCCC]'
      break
    case Skills.ENGINEER:
      style = 'border-2 border-solid border-[#CDBEE7]'
      break
    case Skills.FORMWORK_WORKER:
      style = 'border-2 border-solid border-[#BE8800]'
      break
    case Skills.HEAVY_MACHINERY_OPERATOR:
      style = 'border-2 border-solid border-[#B7D7F2]'
      break
    case Skills.REBAR_WORKER:
      style = 'border-2 border-solid border-[#EED161]'
      break
    case Skills.CONTRACT_TYPE:
      style = 'border-2 border-solid border-[#B7B9B8]'
      break
    case Skills.UNASSIGNED:
      style = 'border-2 border-solid border-[#B7B9B8]'
      break

    default:
      style = 'border-2 border-solid border-[#B7B9B8]'
      break
  }
  return style
}

function getEmployeeShiftStyle(employeeShift: EmployeeShift) {
  let style = ''
  style = getStyleByWorkingrole(employeeShift.workingRole)
  if (props.isClickedEmployeeShift[employeeShift.employeeShiftId]) {
    style += ' border-2 border-solid border-[#1554A3] shadow-lg '
    style += getBorderStyleByWorkingRole(employeeShift.workingRole)
  }
  return style
}
function getOutsourceShiftStyle(outsouceShift: OutsourceShift) {
  let style = ''
  style = getStyleByWorkingrole(outsouceShift.workingRole)
  if (props.isClickedEmployeeShift[outsouceShift.outSourceShiftId]) {
    style += ' border-2 border-solid border-[#1554A3] shadow-lg '
    style += getBorderStyleByWorkingRole(outsouceShift.workingRole)
  }
  return style
}

function showShiftInfo(employeeShift: EmployeeShift, projectId: string) {
  emit('showShiftInfo', employeeShift, projectId)
}

function showOutsourceShiftInfo(outsourceShift: OutsourceShift, projectId: string) {
  emit('showOutsourceShiftInfo', outsourceShift, projectId)
}
</script>

<template>
  <template v-for="(workingDate, index) in generateWeekDays" :key="index">
    <div
      class="flex flex-col overflow-y-hidden w-1/7 border-r-1 border-l-0 border-t-0 border-b-0 border-gray-300 border-solid gap-y-2 p-1 overflow-x-hidden"
      :class="{
        'border-dashed border-gray-400': isEmployeeDragging,
      }"
      @click.stop="saveTargetInfo(projectId, workingDate)"
      @drop="onEmployeeDrop($event, projectId, workingDate, `${projectId}_${index}`)"
      @dragover.prevent="onEmployeeDragOver($event)"
      @dragenter.prevent="onEmployeeDragEnter($event, `${projectId}_${index}`)"
      @dragleave.prevent="onEmployeeDragLeave($event, `${projectId}_${index}`)"
    >
      <template v-for="employeeShift in shiftsComputed[index].employeeShifts" :key="employeeShift.employeeShiftId">
        <div v-if="employeeShift.employeeShiftId">
          <div
            v-if="employeeShift.workingRole !== 'UNASSIGNED'"
            class="employee-shift-card"
            :class="getEmployeeShiftStyle(employeeShift)"
            @click.stop="showShiftInfo(employeeShift, projectId)"
          >
            <div class="flex items-center gap-x-2 justify-start">
              <a-avatar :size="32" :src="avatarStore.getImageSrcByEmployeeId(employeeShift?.employeeId ?? '') ?? ''" class="shadow-lg" />
              <div class="flex flex-col">
                <span>{{ employeeShift.employeeName }}</span>
                <span class="text-#74797A">{{ employeeShift.employeeCode }}</span>
              </div>
            </div>
            <p class="flex justify-between">
              <span>{{ dayjs(employeeShift.scheduledStartTime, 'HH:mm:ss').format('HH:mm') }} ~ {{ dayjs(employeeShift.scheduledEndTime, 'HH:mm:ss').format('HH:mm') }}</span>
              <span class="text-[#101F23]"> {{ employeeShift.assignedWorkload }}</span>
            </p>
            <a-progress :percent="50" :size="4" :show-info="false" />
            <div class="flex items-center gap-x-2">
              <CarbonTruct />
              <span class="text-#74797A">{{ t(`${employeeShift.workingRole}`) }}</span>
            </div>
          </div>
          <div
            v-else
            class="employee-shift-card bg-[#F1F6FF]"
            :class="getEmployeeShiftStyle(employeeShift)"
            @click="showShiftInfo(employeeShift, projectId)"
          >
            <div class="flex items-center justify-center">
              <a-avatar :size="32" :src="avatarStore.getImageSrcByEmployeeId(employeeShift?.employeeId ?? '') ?? ''" class="shadow-lg" />
            </div>
            <div class="flex flex-col items-center justify-center">
              <span>{{ employeeShift.employeeName }}</span>
              <span>Code: {{ employeeShift.employeeCode }}</span>
            </div>
          </div>
        </div>
      </template>
      <template v-for="outsourceShift in outsourceShiftsComputed[index].outsourceShifts" :key="outsourceShift.outSourceShiftId">
        <div v-if="outsourceShift.outSourceShiftId">
          <div
            v-if="outsourceShift.workingRole !== 'UNASSIGNED'"
            class="employee-shift-card"
            :class="getOutsourceShiftStyle(outsourceShift)"
            @click.stop="showOutsourceShiftInfo(outsourceShift, projectId)"
          >
            <div class="flex items-center gap-x-2 justify-start">
              <a-avatar :size="32" />
              <div class="flex flex-col">
                <span>{{ outsourceShift.outSourceName }}</span>
                <span class="text-#74797A">{{ outsourceShift.outSourceCode }}</span>
              </div>
            </div>
            <p class="flex justify-between">
              <span>{{ dayjs(outsourceShift.scheduledStartTime, 'HH:mm:ss').format('HH:mm') }} ~ {{ dayjs(outsourceShift.scheduledEndTime, 'HH:mm:ss').format('HH:mm') }}</span>
              <span class="text-[#101F23]"> {{ outsourceShift.assignedWorkload }}</span>
            </p>
            <a-progress :percent="50" :size="4" :show-info="false" />
            <div class="flex items-center gap-x-2">
              <CarbonTruct />
              <span class="text-#74797A">{{ t(`${outsourceShift.workingRole}`) }}</span>
            </div>
          </div>
          <div
            v-else
            class="employee-shift-card bg-[#F1F6FF]"
            :class="getOutsourceShiftStyle(outsourceShift)"
            @click="showOutsourceShiftInfo(outsourceShift, projectId)"
          >
            <div class="flex items-center justify-center">
              <a-avatar :size="32" />
            </div>
            <div class="flex flex-col items-center justify-center">
              <span>{{ outsourceShift.outSourceName }}</span>
              <span>Code: {{ outsourceShift.outSourceCode }}</span>
            </div>
          </div>
        </div>
      </template>
      <div
        class="min-h-20"
      />
    </div>
  </template>
</template>

<style scoped>
.employee-shift-card {
  padding: 0.5rem;
  /* border: 2px solid #dda2ac; */
  /* background-color: #ffeae8; */
  border-radius: 0.5rem;
  gap: 0.5rem;
  cursor: pointer;
  transition: box-shadow 0.3s, transform 0.3s, background-color 0.3s, color 0.3s;
}
.employee-shift-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transform: scale(1.05);
  /* border: 2px solid #dda2ac; */
}

.active {
    background-color: #F5F5F5;
}
</style>
