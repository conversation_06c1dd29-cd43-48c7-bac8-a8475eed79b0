<script setup lang="ts">
const props = defineProps({
  isDashboard: {
    type: Boolean,
    default: true,
  },
})
const { locale, setLocale } = useI18nLocale()
const localSelected = ref(locale)

function handleChange(value: any) {
  setLocale(value)
}

function handleClick(value: any) {
  setLocale(value.key)
}
</script>

<template>
  <template v-if="!props.isDashboard">
    <a-select ref="select" v-model:value="localSelected" @change="handleChange">
      <a-select-option value="ja-JP">
        <div class="flex items-center">
          <carbon-jp />
          <div class="ml-2">
            Japan
          </div>
        </div>
      </a-select-option>
      <a-select-option value="en-US">
        <div class="flex items-center">
          <carbon-en />
          <div class="ml-2">
            English
          </div>
        </div>
      </a-select-option>
    </a-select>
  </template>
  <template v-else>
    <a-dropdown>
      <div
        hover="bg-[var(--hover-color)]"
        cursor-pointer
      >
        <a-avatar :size="48" shape="square" class="flag-icon-box">
          <template #icon>
            <template v-if="localSelected === 'ja-JP'">
              <carbon-jp-dashboard />
            </template>
            <template v-else>
              <carbon-en-dashboard />
            </template>
          </template>
        </a-avatar>
      </div>
      <template #overlay>
        <a-menu :selected-keys="[locale]" @click="handleClick">
          <a-menu-item key="ja-JP">
            <template #icon>
              <span class="icon-flag"><carbon-jp /></span>
            </template>
            日本語
          </a-menu-item>
          <a-menu-item key="en-US">
            <template #icon>
              <span><carbon-en /></span>
            </template>
            English
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </template>
</template>

<style scoped lang="less">
.flag-icon {
  font-size: 32px;
}

.flag-icon-box{
  height: 48px;
  width: 48px;
}
</style>
