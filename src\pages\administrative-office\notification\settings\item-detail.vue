<script setup lang="ts">
import {
  DeleteOutlined,
  PlusOutlined,
  SendOutlined,
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import _, { cloneDeep } from 'lodash';
import {
  getOneNotification,
  NotificationItem,
  sendNotification,
  TargetItem,
  updateNotification,
} from '~@/api/company/notification';
import { getListRolesApi, Role } from '~@/api/company/role';
import {
  EmployeeCombo,
  getSimpleEmployeeInfoApi,
} from '~@/api/employee/employee';
import { ResponseStatusEnum } from '~@/enums/response-status-enum';
import {
  ModalType,
  NotificationStatus,
  NotificationStatusEnum,
  NotificationStatusEnumKey,
} from '~@/enums/system-status-enum';
import logger from '~@/utils/logger';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  height: {
    type: Number,
    default: 0,
  },
});

enum TargetEnum {
  INDIVIDUAL = 'INDIVIDUAL',
  ROLE = 'ROLE',
  ALL = 'ALL',
}

const initFormState: TargetItem = {
  notificationTargetId: undefined,
  targetIds: [],
  targetType: undefined,
  isDeleted: false,
};

const { t } = useI18n();
const isOpenModal = ref<boolean>(false);
const notification = ref<NotificationItem>();
const modalType = ref<ModalType>(ModalType.ADD);
const roles = ref<Role[]>([]);
const employees = ref<EmployeeCombo[]>([]);
const tableHeader = ref();
const formRef = ref();
const isLoading = ref(false);
const formState = reactive<TargetItem>({ ...cloneDeep(initFormState) });
const { height: tableHeaderHeight } = useElementSize(tableHeader);

type TargetEnumKey = keyof typeof TargetEnum;

const TargetType = computed(() => ({
  [TargetEnum.INDIVIDUAL]: {
    label: t('target-type.individual'),
    value: TargetEnum.INDIVIDUAL,
  },
  [TargetEnum.ROLE]: { label: t('target-type.role'), value: TargetEnum.ROLE },
  [TargetEnum.ALL]: { label: t('target-type.all'), value: TargetEnum.ALL },
}));

const onReset = () => {
  Object.assign(formState, cloneDeep(initFormState));
};

const getTargetOptions = (targetType?: string) => {
  if (targetType === TargetEnum.INDIVIDUAL) {
    return employees.value.map((employee) => ({
      label: employee.employeeName,
      value: employee.employeeId,
    }));
  }
  if (targetType === TargetEnum.ROLE) {
    return roles.value.map((role) => ({
      label: role.roleName,
      value: role.roleId,
    }));
  }
  return [];
};

const getTargetName = (targetType?: string) => {
  if (targetType === TargetEnum.INDIVIDUAL) {
    return t('employee');
  }
  if (targetType === TargetEnum.ROLE) {
    return t('role');
  }
  return '';
};

const openModal = async (id: string, type: ModalType) => {
  switch (type) {
    case ModalType.ADD:
      modalType.value = type;
      isOpenModal.value = true;
      break;
    case ModalType.COPY:
    case ModalType.EDIT: {
      formState.notificationTargetId = id;
      isOpenModal.value = true;

      const findTarget = notification.value?.targets.find(
        (target) => target.notificationTargetId === id
      );
      formState.targetType = findTarget?.targetType;
      formState.targetIds = findTarget?.targetIds ?? [];

      modalType.value = type;
      break;
    }
    default:
      break;
  }
};

const onFinish = async () => {
  try {
    if (!notification.value) return;

    await formRef.value.validate();
    switch (modalType.value) {
      case ModalType.ADD:
        const create = await updateNotification(
          notification.value?.notificationId,
          {
            targets: [
              {
                targetType: formState.targetType,
                targetIds: formState.targetIds,
              },
            ],
          }
        );
        if (create.status !== ResponseStatusEnum.SUCCESS) break;

        message.success(create.message);
        break;
      case ModalType.EDIT:
        const findTarget = notification.value.targets.find(
          (target) =>
            target.notificationTargetId === formState.notificationTargetId
        );
        const update = await updateNotification(
          notification.value.notificationId,
          {
            targets: [
              {
                ...findTarget,
                targetType: formState.targetType,
                targetIds: formState.targetIds,
              },
            ],
          }
        );
        if (update.status !== ResponseStatusEnum.SUCCESS) break;

        message.success(update.message);
        break;
      default:
        break;
    }

    isOpenModal.value = false;
    onReset();
    refresh();
  } catch (error) {
    logger.error(error);
  }
};

const targetIds = computed(() => {
  return (targetType?: string, targets?: string[]) => {
    const options = getTargetOptions(targetType);
    return options.filter((option) => targets?.includes(option.value));
  };
});

const targetTypes = computed(() => [
  { label: t('target-type.individual'), value: TargetEnum.INDIVIDUAL },
  { label: t('target-type.role'), value: TargetEnum.ROLE },
  { label: t('target-type.all'), value: TargetEnum.ALL },
]);

const refresh = async () => {
  isLoading.value = true;
  const res = await getOneNotification(props.id);
  notification.value = res.data ?? undefined;
  isLoading.value = false;
};

const removeTarget = async (id?: string) => {
  try {
    if (!id || !notification.value) return;

    const findTarget = notification.value.targets.find(
      (target) => target.notificationTargetId === id
    );
    if (!findTarget) return;

    findTarget.isDeleted = true;
    const del = await updateNotification(notification.value?.notificationId, {
      targets: [findTarget],
    });
    if (del.status !== ResponseStatusEnum.SUCCESS) return;

    message.success(del.message);
  } catch (error) {
    logger.error(error);
  } finally {
    await refresh();
  }
};

const sendNoti = async (targetId: string) => {
  try {
    if (!notification.value) return;

    const res = await sendNotification(
      notification.value.notificationId,
      targetId
    );
    if (res.status !== ResponseStatusEnum.SUCCESS) return;

    message.success(res.message);
    await refresh();
  } catch (error) {
    logger.error(error);
  }
};

const renderTitle = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
    case ModalType.COPY:
      return _.startCase(
        _.toLower(`${t('button.create')} ${t('form.target')}`)
      );
    case ModalType.EDIT:
      return `${t('button.edit')} ${t('post')}`;
    default:
      return '';
  }
});

const renderOkConfirm = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
    case ModalType.COPY:
      return t('message.add-confirmation');
    case ModalType.EDIT:
      return t('message.edit-confirmation');
    default:
      return '';
  }
});

onMounted(async () => {
  const res = await Promise.all([
    getListRolesApi(),
    getSimpleEmployeeInfoApi(),
  ]);
  roles.value = res[0].data?.items ?? [];
  employees.value = res[1].data?.items ?? [];
});

watch(
  () => props.id,
  async () => {
    await refresh();
  }
);

const getHeight = () => {
  return `${props.height - tableHeaderHeight.value - 16}px`;
};
</script>

<template>
  <a-spin :spinning="isLoading">
    <div class="w-[360px]">
      <div ref="tableHeader">
        <div class="px-4 py-6">
          <div class="header">
            <span class="font-medium">{{ t('form.target') }}</span>
            <a-button
              class="flex flex-items-center"
              type="primary"
              size="small"
              @click="openModal('', ModalType.ADD)"
            >
              <PlusOutlined />
            </a-button>
          </div>
        </div>
      </div>
      <div
        class="flex flex-col gap-4 px-4 overflow-y-auto"
        :style="{ height: getHeight() }"
      >
        <div
          v-for="target in notification?.targets"
          :key="target.notificationTargetId"
        >
          <div
            class="p-4 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors shadow"
          >
            <div class="flex items-center justify-between mb-2">
              <div>
                <span class="font-medium"> {{ t('form.target-type') }}: </span>
                <span>
                  {{ TargetType[target.targetType as TargetEnumKey]?.label }}
                </span>
              </div>
              <div
                class="flex gap-1"
                v-if="NotificationStatusEnum.PUBLISHED !== target.publishStatus"
              >
                <a-button
                  class="flex items-center !px-0.25"
                  ghost
                  @click="
                    openModal(target.notificationTargetId ?? '', ModalType.EDIT)
                  "
                  size="small"
                >
                  <img src="/icon/edit.svg" class="w-[1.25rem]" />
                </a-button>
                <a-popconfirm
                  :title="t('message.delete-confirmation')"
                  @confirm="() => removeTarget(target.notificationTargetId)"
                  placement="leftTop"
                >
                  <a-button
                    size="small"
                    class="flex items-center justify-center"
                    type="text"
                  >
                    <template #icon>
                      <DeleteOutlined class="text-red-500" />
                    </template>
                  </a-button>
                </a-popconfirm>
              </div>
            </div>
            <div v-if="target.targetType !== TargetEnum.ALL">
              <span class="font-medium">
                {{ getTargetName(target?.targetType) }}:
              </span>
              <span>
                {{
                  targetIds(target?.targetType, target?.targetIds)
                    .map((item) => item.label)
                    .join(', ')
                }}
              </span>
            </div>
            <div class="flex justify-between mt-2">
              <div
                :color="
                  NotificationStatus[target.publishStatus as NotificationStatusEnumKey]
                    ?.color
                "
                class="flex items-center justify-center gap-1 font-medium"
              >
                <img
                  v-if="[NotificationStatusEnum.PENDING, NotificationStatusEnum.PARTIALLY_FAILED, NotificationStatusEnum.PARTIALLY_PUBLISHED].includes(target.publishStatus as NotificationStatusEnum)"
                  src="/icon/in_review.svg"
                />
                <img
                  v-if="
                    target.publishStatus === NotificationStatusEnum.PUBLISHED
                  "
                  src="/icon/correct.svg"
                />
                <img
                  v-if="target.publishStatus === NotificationStatusEnum.FAILED"
                  src="/icon/close.svg"
                />
                {{ t(target.publishStatus ?? '') }}
              </div>
              <a-button
                class="flex justify-center items-center"
                @click="() => sendNoti(target.notificationTargetId ?? '')"
                size="small"
                v-if="NotificationStatusEnum.PUBLISHED !== target.publishStatus"
              >
                <SendOutlined />
                {{ t('button.send') }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-spin>

  <a-modal
    v-model:open="isOpenModal"
    width="500px"
    :footer="false"
    @cancel="onReset"
    :maskClosable="false"
  >
    <template #title>
      <div class="flex justify-center items-center">
        <a-typography-title :level="4" class="!text-[#256CB5]">
          {{ renderTitle }}
        </a-typography-title>
      </div>
    </template>
    <a-card border-style="none" class="card">
      <a-form
        ref="formRef"
        :model="formState"
        :label-col="{ span: 24 }"
        :wrapper-col="{ span: 24 }"
        autocomplete="off"
        @finish="onFinish"
        @oncancel="onReset"
      >
        <a-row :gutter="[12, 12]">
          <a-col span="24">
            <a-form-item
              :label="t('form.target-type')"
              name="targetType"
              :rules="[{ required: true }]"
            >
              <a-select
                v-model:value="formState.targetType"
                :placeholder="t('form.target-type')"
                :options="targetTypes"
              />
            </a-form-item>
          </a-col>
          <a-col span="24" v-if="formState.targetType !== TargetEnum.ALL">
            <a-form-item
              :label="t('form.target-id')"
              name="targetIds"
              :rules="[{ required: true }]"
            >
              <a-select
                v-model:value="formState.targetIds"
                mode="multiple"
                :placeholder="t('form.target-id')"
                :options="getTargetOptions(formState.targetType)"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row justify="end">
          <a-row :gutter="[4, 4]">
            <a-col> </a-col>
            <a-col>
              <a-popconfirm :title="renderOkConfirm" @confirm="onFinish">
                <a-button type="primary">
                  {{
                    modalType === ModalType.ADD
                      ? t('button.create')
                      : t('button.update')
                  }}
                </a-button>
              </a-popconfirm>
            </a-col>
          </a-row>
        </a-row>
      </a-form>
    </a-card>
  </a-modal>
</template>

<style lang="less" scoped>
.header {
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  background-color: #f2f2f0;
  box-shadow: 0px 2px 4px 0px #0000001a;
}
.shadow {
  box-shadow: 0px 2px 4px 0px #0000001a;
}
</style>
