<!-- eslint-disable antfu/top-level-function -->
<!-- eslint-disable curly -->
<script lang="ts" setup>
import {
  createVNode,
  onMounted,
  ref,
} from 'vue'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { AxiosError } from 'axios'
import {
  Modal,
} from 'ant-design-vue'
import {
  DownOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons-vue'
import { usePagination } from 'vue-request'
import type {
  LeaveTypeItem,
  RequestTypeItem,
  StatusItem,
  UnitItem,
} from '~@/api/common/common'
import {
  getLeaveTypeList,
  getRequestTypeList,
  getStatusList,
  getUnitList,
} from '~@/api/common/common'
import type { ActionRequestParams, RequestDataParams } from '~@/api/dashboard/date-off-request'
import { cancelRequestApi, createNewRequestApi, getRequestByAuApi, updateRequestApi } from '~@/api/dashboard/date-off-request'
import type { ProjectComboItem } from '~@/api/company/project'
import { getProjectComboApi } from '~@/api/company/project'
import logger from '~@/utils/logger'
import type { QueryParams } from '~@/api/common-params'

const unitData = ref<UnitItem[]>([])
const projectComboData = ref<ProjectComboItem[]>([])
const requestTypeData = ref<RequestTypeItem[]>([])
const leaveTypeData = ref<LeaveTypeItem[]>([])

const { t } = useI18n()
const messageNotify = useMessage()
const isEdit = ref<boolean>(false)
const formRef = ref()
const formFilterRef = ref()
type RangeValue = [Dayjs, Dayjs]
const dateRange = ref<RangeValue>([dayjs().startOf('month'), dayjs().endOf('month')])
const now = dayjs()
const statusData = ref<StatusItem[]>([])

const initialFormState = reactive<any>({
  requestId: undefined,
  date: [],
  requestFrom: '',
  requestTo: '',
  requestTypeCode: undefined,
  leaveTypeCode: undefined,
  approver1Notes: '',
  quantity: undefined,
  unitId: undefined,
  description: '',
  projectId: undefined,
})

const defaultTitle = 'add-new-request'

const formState = ref({ ...initialFormState })
const titleRequest = ref<string>(defaultTitle)
const visibleFilter = ref<boolean>(false)

const initialFormFilterState = reactive<any>({
  requestTypeCode: undefined,
  status: undefined,
})

const formFilterState = ref({ ...initialFormFilterState })

const {
  data: listRequest,
  refresh,
  changeCurrent,
  current,
  totalPage,
  loading,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [
    {
      pageNum: 1,
      pageSize: 4,
      dateFrom: dateRange.value[0].format('YYYY-MM-DD'),
      dateTo: dateRange.value[1].format('YYYY-MM-DD'),
    },
  ],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalPageKey: 'totalRecords',
  },
})

async function createNewRequest(newRequest: RequestDataParams) {
  try {
    const { status, code, message } = await createNewRequestApi(newRequest)
    if (status === 200) {
      messageNotify.success(message)
    }
    else {
      logger.error(t(code))
      messageNotify.error(message)
    }
  }
  catch (e) {
    logger.error(e)
    throw e
  }
}

async function updateRequest(updatedRequest: RequestDataParams) {
  try {
    const { status, code, message } = await updateRequestApi('', updatedRequest)
    if (status === 200) {
      messageNotify.success(message)
    }
    else {
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
    throw e
  }
}

async function handleOk() {
  formRef.value?.validate().then(async () => {
    const data: RequestDataParams = {
      requestFrom: dayjs(formState.value.date[0]).toISOString(),
      requestTo: dayjs(formState.value.date[1]).toISOString(),
      quantity: formState.value.quantity,
      unitId: formState.value.unitId,
      description: formState.value.description,
      projectId: formState.value.projectId,
      requestTypeCode: formState.value.requestTypeCode,
    }

    if (formState.value.leaveTypeCode) {
      data.leaveTypeCode = formState.value.leaveTypeCode
    }

    if (!isEdit.value) {
      await createNewRequest(data)
    }
    else {
      data.requestId = formState.value.requestId
      await updateRequest(data)
    }

    resetFormState()
    changeCurrent(1)
  })
}

function resetFormState() {
  titleRequest.value = defaultTitle
  formState.value = { ...initialFormState }
}

async function getLeaveType() {
  try {
    const { data, status, code } = await getLeaveTypeList()
    if (status === 200) {
      leaveTypeData.value = data?.items ?? []
    }
    else {
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

async function getUnitType() {
  try {
    const { data, status, code } = await getUnitList()
    if (status === 200) {
      unitData.value = data?.items ?? []
    }
    else {
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

async function getProjectCombo() {
  const params: QueryParams = {
    pageNum: 1,
    pageSize: 1000,
  }
  try {
    const { data, status, code } = await getProjectComboApi(params)
    if (status === 200) {
      projectComboData.value = data?.items ?? []
    }
    else {
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

async function getRequestType() {
  try {
    const { data, status, code } = await getRequestTypeList()
    if (status === 200) {
      requestTypeData.value = data?.items ?? []
    }
    else {
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

async function getRequestStatus() {
  try {
    const { data, status, code } = await getStatusList()
    if (status === 200) {
      statusData.value = data?.items ?? []
    }
    else {
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

async function cancelRequest(actionData: ActionRequestParams) {
  try {
    const { status, code, message } = await cancelRequestApi(actionData)
    if (status === 200) {
      messageNotify.success(message)
      refresh()
    }
    else {
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
    throw e
  }
}

const confirmCancelRequest = (requestId: string) => {
  const actionData: ActionRequestParams = {
    requestId,
  }

  Modal.confirm({
    title: `${t('confirm')}`,
    icon: createVNode(ExclamationCircleOutlined),
    content: `${t('confirmCancelRequest')}`,
    okText: `${t('button.confirm')}`,
    cancelText: `${t('button.cancel')}`,
    onOk() {
      cancelRequest(actionData)
    },
  })
}

async function queryData(params: any) {
  try {
    params = {
      ...params,
      requestType: formFilterState.value.requestTypeCode || '',
      status: formFilterState.value.status || '',
    }

    const { data, status, code } = await getRequestByAuApi(params)
    if (status === 200) {
      return data ?? []
    }
    else {
      logger.error(t(code))
    }
  }
  catch (e) {
    if (e instanceof AxiosError) {
      logger.error(e)
    }
  }
}

const handleEdit = (request: any) => {
  titleRequest.value = 'edit-request'
  if (request) {
    isEdit.value = true
    formState.value.requestId = request.requestId
    formState.value.date = [dayjs(request.requestFrom), dayjs(request.requestTo)]
    formState.value.requestTypeCode = request.requestTypeCode
    formState.value.leaveTypeCode = request.leaveTypeCode
    formState.value.quantity = request.quantity
    formState.value.unitId = request.unitId
    formState.value.description = request.description
  }
}

const handlefilter = () => {
  visibleFilter.value = false
  changeCurrent(1)
}

const handleClosefilter = () => {
  visibleFilter.value = false
}

const handleFieldFilter = () => {
  formFilterState.value = { ...initialFormFilterState }
}

const handleClearFilter = () => {
  handleFieldFilter()
  changeCurrent(1)
}

onMounted(async () => {
  const promises = [getProjectCombo(), getLeaveType(), getUnitType(), getRequestType(), getRequestStatus()]
  await Promise.all(promises)
})
</script>

<template>
  <page-container>
    <div class="flex justify-between">
      <div class="flex flex-col gap-[24px] w-[calc(100%-432px)] h-full">
        <div class="flex justify-between">
          <h2 class="text-[20px] m-0 text-[#101F23] dark:text-[#fff]">
            {{ t('your-request') }}
          </h2>
          <a-space :size="12">
            <a-popover
              v-model:open="visibleFilter" trigger="click" placement="bottomRight"
              overlay-class-name="filter-popover"
            >
              <template #content>
                <div class="flex justify-between items-center h-[36px] px-[12px]">
                  <a-space :size="4">
                    <CarbonFilter size="12" />
                    <div class="text-[12px]">
                      {{ t('filter') }}
                    </div>
                  </a-space>
                  <div class="cursor-pointer" @click="handleClosefilter">
                    <CarbonClose />
                  </div>
                </div>
                <a-divider :style="{ margin: 0 }" />
                <a-form
                  ref="formFilterRef" class="form-filter" layout="vertical" :model="formFilterState"
                  @finish="handlefilter"
                >
                  <a-form-item class="m-0 px-[12px] pt-[8px] pb-[16px]" :label="t('RequestType')" name="requestTypeCode">
                    <a-select
                      v-model:value="formFilterState.requestTypeCode" :placeholder="t('RequestType')"
                      :options="requestTypeData" :field-names="{ label: 'requestTypeName', value: 'requestTypeCode' }"
                      @change="
                        formFilterState.leaveTypeCode = formFilterState.requestTypeCode === 1 ? 1 : undefined;
                      "
                    />
                  </a-form-item>
                  <a-divider :style="{ margin: 0 }" />
                  <a-form-item class="m-0 px-[12px] pt-[8px] pb-[16px]" :label="t('status')" name="status">
                    <a-select
                      v-model:value="formFilterState.status" :placeholder="t('status')" :field-names="{ label: 'statusName', value: 'statusId' }"
                      :options="statusData"
                    />
                  </a-form-item>
                  <a-divider :style="{ margin: 0 }" />
                  <div class="flex justify-between p-[12px]">
                    <a-button @click="handleFieldFilter">
                      {{ t('button.reset') }}
                    </a-button>
                    <a-button type="primary" html-type="submit">
                      {{ t('button.apply')}}
                    </a-button>
                  </div>
                </a-form>
              </template>
              <a-button>
                {{ t('filter') }}
                <DownOutlined />
              </a-button>
            </a-popover>
            <a-button @click="handleClearFilter">
              {{ t('button.reset') }}
            </a-button>
          </a-space>
        </div>
        <template v-if="listRequest && (listRequest as any).items.length > 0">
          <a-spin :spinning="loading">
            <template v-for="item in (listRequest as any).items" :key="item">
              <div class="w-full shadow-sm rounded-lg overflow-hidden mb-[24px] bg-[#fff] dark:bg-[#383838]">
                <div class="py-[12px] px-[48px]">
                  <div class="flex justify-between mb-[12px]">
                    <div class="flex">
                      <h3 class="text-[20px] mb-0 mr-[24px] text-[#101F23] dark:text-[#fff]">
                        {{ item.requestTypeName }}
                      </h3>
                      <div
                        class="text-[16px] py-[4px] px-[12px] rounded-md" :class="{
                          'bg-[#FFE3E5] text-[#BD3D44]': item.statusCode === 'REJECTED',
                          'bg-[#DCF6E0] text-[#106B1E]': item.statusCode === 'APPROVED',
                          'bg-[#DEF0FF] text-[#24598E]': item.statusCode === 'PENDING',
                          'bg-[#E4E4E2] text-[#3A3B3C] dark:bg-[#606060] dark:text-[#f3f3f3]': item.statusCode === 'CANCELLED',
                        }"
                      >
                        {{ item.statusName }}
                      </div>
                    </div>
                    <div class="px-[12px] py-[4px] bg-[#FAF9F7] rounded text-[#8E9495] dark:bg-[#606060] dark:text-[#f3f3f3]">
                      {{ now.diff(item.createTime,
                                  'day') > 30 ? 30 : now.diff(item.createTime, 'day') }} days ago
                    </div>
                  </div>

                  <div class="flex items-center">
                    <div v-if="item.leaveTypeName" class="flex mr-[48px]">
                      <div class="mr-[8px] text-[#3A3B3C] dark:text-[#fff]">
                        {{ t('type') }}
                      </div>
                      <div class="text-[#74797A] dark:text-[#fff]">
                        {{ item.leaveTypeName }}
                      </div>
                    </div>
                    <div class="flex items-center">
                      <div class="mr-[5px]">
                        <carbon-calendar />
                      </div>
                      <div class="mr-[12px] text-[#3A3B3C] dark:text-[#fff]">
                        {{ item.quantity }} {{ item.unitCounter }}
                      </div>
                      <div class="mr-[8px] text-[#74797A] dark:text-[#fff]">
                        {{ dayjs(item.requestFrom).format('YYYY/MM/DD') }}
                      </div>
                      <div class="mr-[8px]">
                        <carbon-arrow />
                      </div>
                      <div class="text-[#74797A] dark:text-[#fff]">
                        {{ dayjs(item.requestTo).format('YYYY/MM/DD') }}
                      </div>
                    </div>
                  </div>
                  <div class="flex">
                    <div class="mr-[8px] font-medium text-[#3A3B3C] dark:text-[#fff]">
                      {{ t('reason') }}
                    </div>
                    <div class="text-[#74797A] dark:text-[#fff]">
                      {{ item.description }}
                    </div>
                  </div>
                </div>
                <div
                  class="flex h-[36px] py-[8px] px-[48px] justify-between" :class="{
                    'bg-[#FFE3E5]': item.statusCode === 'REJECTED',
                    'bg-[#DCF6E0]': item.statusCode === 'APPROVED',
                    'bg-[#DEF0FF]': item.statusCode === 'PENDING',
                    'bg-[#E4E4E2] dark:bg-[#606060]': item.statusCode === 'CANCELLED',
                  }"
                >
                  <div class="flex">
                    <template v-if="item.statusCode !== 'CANCELLED' && item.statusCode !== 'PENDING'">
                      <div class="flex items-center mr-[8px]">
                        <template v-if="item.statusCode === 'REJECTED'">
                          <carbon-user color="#BD3D44" />
                          <div class="text-[12px] text-[#BD3D44] ml-[8px]">
                            {{ t('reject-by') }}
                          </div>
                        </template>
                        <template v-else>
                          <carbon-user />
                          <div class="text-[12px] text-[#106B1E] ml-[8px]">
                            {{ t('apply-by') }}
                          </div>
                        </template>
                      </div>
                      <div v-if="item.approver1Name" class="rounded-[4px] px-[8px] bg-white text-[#256CB5]">
                        {{ item.approver1Name }}
                      </div>
                      <div v-if="item.approver2Name" class="rounded-[4px] px-[8px] bg-white text-[#256CB5] ml-[8px]">
                        {{ item.approver2Name }}
                      </div>
                    </template>
                  </div>
                  <template v-if="item.statusCode === 'PENDING'">
                    <div class="flex gap-x-[24px]">
                      <carbon-edit class="cursor-pointer" @click="handleEdit(item)" />
                      <carbon-delete class="cursor-pointer" @click="confirmCancelRequest(item.requestId)" />
                    </div>
                  </template>
                </div>
              </div>
            </template>
          </a-spin>
          <div v-if="totalPage > 4" class="flex justify-center">
            <a-pagination
              v-model:current="current" :total="totalPage" :page-size="pageSize" show-less-items
              class="pagination-request"
            />
          </div>
        </template>
        <template v-else>
          <a-empty />
        </template>
      </div>
      <div class="w-[384px] shadow-sm rounded-lg bg-[#fff] overflow-hidden h-max dark:bg-[#383838]">
        <div class="w-full  h-[8px] bg-[#1570EF] mb-[8px]" />
        <div class="p-[32px]">
          <h2 class="text-[20px] text-center mb-[30px] text-[#101F23] dark:text-[#fff]">
            {{ t(titleRequest) }}
          </h2>
          <a-form ref="formRef" layout="vertical" :model="formState" @finish="handleOk">
            <a-form-item :label="t('Date')" required name="date" :rules="[{ required: true, message: 'Date' }]">
              <a-range-picker v-model:value="formState.date" type="date" style="width: 100%" value-format="YYYY-MM-DD" />
            </a-form-item>
            <a-form-item
              :label="t('RequestType')" name="requestTypeCode"
              :rules="[{ required: true, message: 'RequestType' }]"
            >
              <a-select
                v-model:value="formState.requestTypeCode" :placeholder="t('RequestType')" :options="requestTypeData"
                :field-names="{ label: 'requestTypeName', value: 'requestTypeCode' }" @change="
                  formState.leaveTypeCode = formState.requestTypeCode === 1 ? 1 : undefined;
                "
              />
            </a-form-item>
            <a-form-item
              :label="t('LeaveType')" name="leaveTypeCode"
              :rules="[{ required: false, message: 'LeaveTypeKey' }]"
            >
              <a-select
                v-model:value="formState.leaveTypeCode" :disabled="!formState.leaveTypeCode" :options="leaveTypeData"
                :field-names="{ label: 'leaveTypeName', value: 'leaveTypeCode' }" :placeholder="t('LeaveType')"
              />
            </a-form-item>
            <a-form-item :label="t('Quantity')" name="quantity" :rules="[{ required: true, message: 'Quantity' }]">
              <a-input-number v-model:value="formState.quantity" class="w-full" :placeholder="t('Quantity')" />
            </a-form-item>
            <a-form-item :label="t('Unit')" name="unitId" :rules="[{ required: true, message: 'Unit' }]">
              <a-select
                v-model:value="formState.unitId" :placeholder="t('Unit')" :options="unitData"
                :field-names="{ label: 'unitName', value: 'unitId' }"
              />
            </a-form-item>
            <a-form-item :label="t('project')" name="projectId" :rules="[{ required: true, message: 'project' }]">
              <a-select
                v-model:value="formState.projectId" :placeholder="t('project')" :options="projectComboData"
                :field-names="{ label: 'name', value: 'id' }"
              />
            </a-form-item>
            <a-form-item :label="t('Note')" name="description" :rules="[{ required: true, message: 'Note' }]">
              <a-textarea v-model:value="formState.description" />
            </a-form-item>
            <a-form-item class="text-center">
              <a-button type="primary" html-type="submit">
                {{ t('button.send') }}
              </a-button>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>
  </page-container>
</template>

<style lang="less">
.filter-popover {
  max-width: 384px;
  width: 100%;

  .ant-popover-inner {
    padding: 0;
  }
}

.pagination-request {
  .ant-pagination-item a,
  .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis,
  .ant-pagination-item-link svg {
    color: #74797A;
  }

  .ant-pagination-item-active {
    background-color: #F99649;
    border: none;
    a {
      color: #fff;
    }
  }

  .ant-pagination-item-active:hover {
    a {
      color: #fff;
    }
  }
}
</style>
