<!-- eslint-disable antfu/top-level-function -->
<script setup lang="ts">
import { CameraOutlined } from '@ant-design/icons-vue'
import { delayTimer } from '@v-c/utils'
import { message } from 'ant-design-vue'
import axios from 'axios'
import { CircleStencil, Cropper } from 'vue-advanced-cropper'
import 'vue-advanced-cropper/dist/style.css'
import logger from '~@/utils/logger'

defineOptions({
  name: 'ProfileBasic',
})
const { t } = useI18n()
const { userInfo, roles } = useUserStore()
const baseUrl = import.meta.env.VITE_APP_BASE_API ?? '/'
const token = useAuthorization()
const imageUrl = ref('')
const cropModalVisible = shallowRef<boolean>(false)
const cropperRef = ref<typeof Cropper | null>(null)
const { avatar, setUserAvatar } = useUserStore()
const { userImgSrc, fetchUserAvatar, loading } = useAvatar()
const imageSrc = ref(avatar)
const headers = {
  authorization: token.value as string,
}

const stencilProps = {
  radius: 200,
  resizable: false,
}

// const beforeUpload = (file: UploadProps['fileList'][number]) => {
//   const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
//   if (!isJpgOrPng)
//     message.error('You can only upload JPG/PNG file!')

//   const isLt2M = file.size / 1024 / 1024 < 2
//   if (!isLt2M)
//     message.error('Image must smaller than 2MB!')

//   return isJpgOrPng && isLt2M
// }

const onFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement | null
  const file = target?.files?.[0]
  imageUrl.value = file ? URL.createObjectURL(file) : ''
  cropModalVisible.value = true
}

async function uploadProfileImage(imageDataUrl: string) {
  try {
    const response = await fetch(imageDataUrl)
    const blob = await response.blob()

    const formData = new FormData()
    formData.append('file', blob, 'avatar.png')

    const result = await axios.put(`${baseUrl}/v1/userinfo/avatar`, formData, {
      headers: {
        ...headers,
        'Content-Type': 'multipart/form-data',
      },
    })
    logger.log('result', result)
    message.success(t('avatar.updateSuccess'))
    await fetchUserAvatar()
    imageSrc.value = userImgSrc.value
    setUserAvatar(userImgSrc.value)
  }
  catch (error) {
    logger.error('Error uploading image:', error)
    throw error
  }
}

const handleCrop = async () => {
  try {
    if (!cropperRef.value)
      return

    const { canvas } = cropperRef.value.getResult()
    const blob = await new Promise<Blob | null>((resolve) => {
      canvas.toBlob((blob: Blob) => resolve(blob))
    })

    if (!blob)
      throw new Error('Failed to create blob')

    const dataUrl = canvas.toDataURL()
    await uploadProfileImage(dataUrl)
    cropModalVisible.value = false
  }
  catch (error) {
    message.error(t('avatar.updateFailed'))
    logger.error(error)
  }
}

function handleClick() {
  document.getElementById('avatarInput')?.click()
}

onMounted(() => {
  delayTimer(500).then(() => {
    logger.log('delay 500ms')
  })

  // createCropper()

  fetchUserAvatar()
})
</script>

<template>
  <page-container>
    <div class="p-8 bg-white rounded-lg min-h-[calc(100vh-12rem)]">
      <div class="flex flex-row justify-between items-center border-b pb-6 mb-8">
        <div class="flex items-center mb-4 lg:mb-0">
          <!-- <img src="https://via.placeholder.com/100" alt="User Avatar" class="w-24 h-24 rounded-full mr-6"> -->
          <a-avatar
            :size="100"
            shape="circle"
            class="mr-6 relative group cursor-pointer"
            @click="() => handleClick()"
          >
            <template #icon>
              <img :src="imageSrc" alt="avatar" class="w-full h-full object-cover">
              <!-- Overlay khi hover -->
              <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-full flex flex-col items-center justify-center">
                <CameraOutlined class="text-white text-xl" />
                <span class="text-white text-xs ml-1">{{ t('changePhoto') }}</span>
              </div>
            </template>
            <!-- Input file ẩn -->
          </a-avatar>
          <input
            id="avatarInput"
            type="file"
            accept="image/*"
            class="hidden"
            @change="onFileChange"
          >
          <div>
            <h2 class="text-3xl font-bold text-gray-800">
              {{ userInfo?.userName }}
            </h2>
            <p class="text-gray-500">
              {{ userInfo?.birthday }}
            </p>
          </div>
        </div>
        <div class="flex">
          <!-- <a-upload
            v-model:fileList="fileList"
            name="file"
            :headers="headers"
            :action="uploadUrl"
            :before-upload="beforeUpload"
            method="PUT"
            @change="uploadAvatar"
          >
            <a-button>
              <UploadOutlined />
              {{ t('uploadAvatar') }}
            </a-button>
          </a-upload> -->
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-gray-100 p-6 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-gray-700 mb-4">
            {{ t('profile.basic.contactInfo') }}
          </h3>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">{{ t('profile.email') }}</label>
              <p class="text-gray-800 bg-white rounded-md p-2">
                {{ userInfo?.email }}
              </p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">{{ t('profile.phone') }}</label>
              <p class="text-gray-800 bg-white rounded-md p-2">
                {{ userInfo?.phone }}
              </p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">{{ t('profile.address') }}</label>
              <p class="text-gray-800 bg-white rounded-md p-2">
                {{ userInfo?.address }}
              </p>
            </div>
          </div>
        </div>

        <div class="bg-gray-100 p-6 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-gray-700 mb-4">
            {{ t('profile.basic.roleInfo') }}
          </h3>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">{{ t('profile.role') }}</label>
              <p class="text-gray-800 bg-white rounded-md p-2">
                {{ roles.map(role => role.roleName).join(', ') }}
              </p>
            </div>
            <!-- <div>
              <label class="block text-sm font-medium text-gray-700">{{ t('profile.position') }}</label>
              <p class="text-gray-800 bg-white rounded-md p-2">
                {{ userInfo?. }}
              </p>
            </div> -->
            <!-- <div>
              <label class="block text-sm font-medium text-gray-700">{{ t('profile.level') }}</label>
              <p class="text-gray-800 bg-white rounded-md p-2">
                {{ userInfo?.rankingName }}
              </p>
            </div> -->
          </div>
        </div>
      </div>
    </div>
    <a-modal
      v-model:open="cropModalVisible"
      :title="t('cropYourNewAvatar')"
    >
      <template #footer>
        <a-button
          key="submit"
          :loading="loading"
          class="w-full"
          type="primary"
          @click="handleCrop"
        >
          {{ t('setNewAvatar') }}
        </a-button>
      </template>
      <div v-if="imageUrl" class="max-w-[600px]">
        <Cropper
          ref="cropperRef"
          :src="imageUrl"
          :stencil-component="CircleStencil"
          :stencil-props="stencilProps"
          :bounds="true"
        />
      </div>
    </a-modal>
  </page-container>
</template>

<style lang="less" scoped>
.title {
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
}

img {
  display: block;

  /* This rule is very important, please don't ignore this */
  max-width: 100%;
}

.cropper-container {
  height: 400px;
  width: 400px;
  background: #f0f0f0;
  margin-top: 20px;
}
</style>
