<!-- App.vue (Updated) -->
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import type { CostsData } from './types.ts'
import CostsSection from './cost-section.vue'
import { fetchCostsData } from './mock-api-service.ts'

const costsData = ref<CostsData>({
  contractualCosts: [],
  estimatedCosts: [],
  accumulatedCosts: [],
})

const loading = ref(true)
const error = ref<string | null>(null)

async function loadCostsData() {
  try {
    loading.value = true
    error.value = null

    // Using our mock API service
    costsData.value = await fetchCostsData()
  }
  catch (err) {
    error.value = err instanceof Error ? err.message : 'An unknown error occurred'
  }
  finally {
    loading.value = false
  }
}

onMounted(() => {
  loadCostsData()
})
</script>

<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
      Cost Analysis Dashboard
    </h1>

    <div v-if="loading" class="text-center py-10">
      <p class="text-gray-600">
        Loading cost data...
      </p>
    </div>

    <div v-else-if="error" class="bg-red-50 text-red-700 p-4 rounded-lg mb-6">
      <p>{{ error }}</p>
      <button
        class="mt-3 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        @click="loadCostsData"
      >
        Retry
      </button>
    </div>

    <div v-else>
      <div class="mb-6">
        <button
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          @click="loadCostsData"
        >
          Refresh Data
        </button>
      </div>

      <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <CostsSection
          title="Contractual Costs"
          :items="costsData.contractualCosts"
        />

        <CostsSection
          title="Estimated Costs"
          :items="costsData.estimatedCosts"
        />

        <CostsSection
          title="Accumulated Costs"
          :items="costsData.accumulatedCosts"
        />
      </div>
    </div>
  </div>
</template>
