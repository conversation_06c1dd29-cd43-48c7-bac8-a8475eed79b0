<!-- src/components/CompanyBasicInfo.vue -->
<script setup lang="ts">
import { PlusOutlined } from '@ant-design/icons-vue'

defineProps({
  form: {
    type: Object,
    required: true,
  },
})
</script>

<template>
  <a-form :model="form.basic" layout="vertical">
    <a-row :gutter="16">
      <a-col :span="12">
        <a-form-item label="Tên công ty" required>
          <a-input v-model:value="form.basic.name" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="Website">
          <a-input v-model:value="form.basic.website" />
        </a-form-item>
      </a-col>
    </a-row>
    <a-row :gutter="16">
      <a-col :span="12">
        <a-form-item label="Ngày thành lập">
          <a-date-picker
            v-model:value="form.basic.establishedDate"
            class="w-full"
          />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="Logo công ty">
          <a-upload
            list-type="picture-card"
            :max-count="1"
          >
            <div v-if="!form.basic.logo">
              <PlusOutlined />
              <div class="mt-2">
                Upload
              </div>
            </div>
          </a-upload>
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>
