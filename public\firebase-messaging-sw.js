importScripts(
  'https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js'
);
importScripts(
  'https://www.gstatic.com/firebasejs/9.23.0/firebase-messaging-compat.js'
);

firebase.initializeApp({
  apiKey: 'AIzaSyA1CP0PrQuqOmFGH4RLZY-cM3wi3D2eb8A',
  authDomain: 'kantoku-547bb.firebaseapp.com',
  projectId: 'kantoku-547bb',
  storageBucket: 'kantoku-547bb.firebasestorage.app',
  messagingSenderId: '982394207074',
  appId: '1:982394207074:web:6cd5a706c139266dcdca3b',
  measurementId: 'G-NXDDNGVEFE',
});

const messaging = firebase.messaging();

messaging.onBackgroundMessage((payload) => {
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: '/logo.svg',
    data: payload.data || {},
  };

  return self.registration.showNotification(
    notificationTitle,
    notificationOptions
  );
});

self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  const urlToOpen = event.notification.data?.url || '/';
  event.waitUntil(
    clients
      .matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList) => {
        for (const client of clientList) {
          if (client.url === urlToOpen && 'focus' in client) {
            return client.focus();
          }
        }
        return clients.openWindow(urlToOpen);
      })
  );
});
