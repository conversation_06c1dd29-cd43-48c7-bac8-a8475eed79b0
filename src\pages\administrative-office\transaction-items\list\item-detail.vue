<script lang="ts" setup>
import {
  ArrowRightOutlined,
  CloseOutlined,
  DownOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import type {
  ColumnGroupType,
  ColumnType,
  FilterValue,
  TablePaginationConfig,
} from 'ant-design-vue/es/table/interface';
import dayjs from 'dayjs';
import _, { cloneDeep } from 'lodash';
import type { UnwrapRef } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { BarChart } from 'echarts/charts';
import {
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
} from 'echarts/components';
import VChart from 'vue-echarts';
import type { ECBasicOption } from 'echarts/types/dist/shared';
import { usePagination } from 'vue-request';
import { getVendor } from '~@/api/company/vendor';
import type { VendorItem } from '~@/api/company/vendor';
import {
  createCostItemPrice,
  getCostItemPrice,
} from '~@/api/company/cost-item';
import type {
  CostItemPrice,
  CostItemPriceResponse,
  GetCostItemParams,
} from '~@/api/company/cost-item';
import { ResponseStatusEnum } from '~@/enums/response-status-enum';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  height: {
    type: Number,
    default: 0,
  },
});

use([
  CanvasRenderer,
  GridComponent,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
]);

interface MonthlyPriceType {
  name: string;
  value: string;
  option?: ECBasicOption;
}

type ColumnItemType<T> = ColumnGroupType<T> | ColumnType<T>;

type FromState = CostItemPrice & {
  time_period?: [dayjs.Dayjs, dayjs.Dayjs];
  vendor?: { value: string; label: string };
  itemsCount?: number;
};

const initSearchForm: GetCostItemParams = {
  keyword: '',
  fromDate: undefined,
  toDate: undefined,
  priceMin: undefined,
  priceMax: undefined,
  pageNum: 1,
  pageSize: 10,
};

const { t } = useI18n();
const supplierChart = ref<InstanceType<typeof VChart> | null>(null);
const monthlyPriceChart = ref<InstanceType<typeof VChart> | null>(null);
const searchForm = ref<GetCostItemParams>({ ...cloneDeep(initSearchForm) });
const isOpenFilter = ref<boolean>(false);
const vendors = ref<VendorItem[]>([]);
const editableData: UnwrapRef<Record<string, FromState>> = reactive({});

async function queryData(params?: GetCostItemParams) {
  const { data } = await getCostItemPrice(props.id, params);
  if (!data) return ref<CostItemPriceResponse>({ items: [] }).value;

  return ref(data).value;
}

const {
  data: dataSource,
  loading,
  total,
  current,
  run,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [searchForm.value],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
});

const pagination = computed(() => ({
  total: total.value ?? 10,
  current: current.value,
  pageSize: pageSize.value,
  showSizeChanger: true,
}));

const sortedDataSource = computed(() => {
  const grouped = (dataSource.value?.items ?? []).reduce((acc, cur) => {
    const key = cur.vendorId ?? '';
    if (!acc[key]) acc[key] = [];
    acc[key].push(cur);
    return acc;
  }, {} as Record<string, FromState[]>);

  const sortedItems = Object.keys(grouped).flatMap((uuid) => grouped[uuid]);

  return sortedItems.map((item, index) => {
    const findIndex = sortedItems.findIndex(
      (i) => i.vendorId === item.vendorId
    );
    const itemsCount = sortedItems.filter(
      (i) => i.vendorId === item.vendorId
    ).length;
    const isFirst = findIndex === index;
    return { ...item, itemsCount: isFirst ? itemsCount : 0 };
  });
});

function getVendorGroup() {
  return (dataSource.value?.items ?? []).reduce((acc, cur) => {
    const key = cur.vendorId ?? '';
    const name = cur.vendorName ?? '';
    if (!acc[key]) acc[key] = { name, value: 0 };
    acc[key].value += Number(cur.price ?? 0);
    return acc;
  }, {} as Record<string, { name: string; value: number }>);
}

const supplierOption = computed<ECBasicOption>(() => {
  const vendorGroup = getVendorGroup();
  return {
    xAxis: {
      max: 'dataMax',
    },
    yAxis: {
      type: 'category',
      data: Object.values(vendorGroup).map((item) => item.name),
      inverse: true,
      animationDuration: 300,
      animationDurationUpdate: 300,
    },
    series: [
      {
        realtimeSort: true,
        name: t('form.provider'),
        type: 'bar',
        data: Object.values(vendorGroup).map((item) => item.value),
        label: {
          show: true,
          position: 'right',
          valueAnimation: true,
        },
      },
    ],
    legend: {
      show: true,
    },
  };
});

const monthlyPrices = computed<MonthlyPriceType[]>(() => {
  const vendorGroup = getVendorGroup();
  const monthNames = Array.from({ length: 12 }, (_, i) =>
    dayjs().month(i).format('MMMM')
  );

  return Object.keys(vendorGroup).map((vendorId) => {
    const data = monthNames.map((month) => {
      const monthData = (dataSource.value?.items ?? []).filter(
        (item) =>
          item.vendorId === vendorId &&
          dayjs(item.validFrom).format('MMMM') === month
      );
      return monthData.reduce((acc, cur) => acc + Number(cur.price ?? 0), 0);
    });

    return {
      name: vendorGroup[vendorId].name,
      value: vendorId,
      option: {
        xAxis: {
          type: 'category',
          data: monthNames,
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data,
            type: 'bar',
          },
        ],
      },
    };
  }) as MonthlyPriceType[];
});

const columns = computed<ColumnItemType<FromState>[]>(() => [
  {
    dataIndex: 'vendor',
    title: t('form.provider'),
    width: 200,
    customCell: (record) => {
      return { rowSpan: record.itemsCount };
    },
  },
  { dataIndex: 'unit', title: t('form.unit'), width: 150 },
  { dataIndex: 'price', title: t('form.price'), width: 150 },
  { dataIndex: 'time_period', title: t('form.time_period'), width: 300 },
  {
    dataIndex: 'action',
    title: t('action'),
    width: 150,
    fixed: 'right',
    align: 'center',
  },
]);

function handleTableChange(
  pagination: TablePaginationConfig,
  filters: Record<string, FilterValue>
) {
  searchForm.value.pageSize = pagination.pageSize ?? 10;
  searchForm.value.pageNum = pagination.current ?? 1;
  run({ ...searchForm.value, ...filters });
}

function handlePaginationChange(page: number, pageSize: number) {
  searchForm.value.pageSize = pageSize;
  searchForm.value.pageNum = page;
  run(searchForm.value);
}

async function onSearch() {
  handleTableChange(
    { pageSize: searchForm.value.pageSize ?? 10, current: 1 },
    {}
  );
}

function handleAdd() {
  const newData: FromState = {
    itemPriceId: `${Date.now()}`,
    price: 0,
    time_period: [dayjs(), dayjs()],
    vendor: {
      value: vendors.value?.[0]?.vendorId,
      label: vendors.value?.[0]?.vendorName,
    },
  };
  if (dataSource.value) dataSource.value.items.unshift(newData);
  editableData[newData.itemPriceId] = newData;
}

async function handleSave(key: string) {
  const create = await createCostItemPrice(props.id, {
    vendorId: editableData[key]?.vendor?.value,
    unit: editableData[key]?.unit,
    price: editableData[key]?.price,
    validFrom: editableData[key]?.time_period?.[0]?.format('YYYY-MM-DD'),
    validTo: editableData[key]?.time_period?.[1]?.format('YYYY-MM-DD'),
  });
  if (create.status === ResponseStatusEnum.SUCCESS) {
    message.success(create.message);
  } else {
    message.error(create.message);
    return;
  }

  if (dataSource.value) {
    Object.assign(
      dataSource.value.items.filter((item) => key === item.itemPriceId)[0],
      {
        ...create.data,
        vendor: { value: create.data.vendorId, label: create.data.vendorName },
        time_period: [dayjs(create.data.validFrom), dayjs(create.data.validTo)],
      }
    );
  }
  delete editableData[key];
  loading.value = false;
}

function handleCancel(key: string) {
  if (dataSource.value) {
    dataSource.value.items = dataSource.value.items.filter(
      (item) => key !== item.itemPriceId
    );
  }
  delete editableData[key];
}

async function loadVendorData() {
  const { data } = await getVendor();
  vendors.value = data?.items ?? [];
}

onMounted(async () => {
  await Promise.all([loadVendorData()]);
});

function onResetSearch() {
  Object.assign(searchForm.value, cloneDeep(initSearchForm));
}

const getHeight = computed(() => {
  return (minus?: number) => {
    return `${props.height / 2 - (minus ?? 0)}px`;
  };
});

watch(
  () => props.id,
  async () => {
    await onSearch();
  }
);
</script>

<template>
  <a-row :gutter="[12, 12]">
    <a-col
      span="24"
      class="bg-white !p-4 rounded-md shadow-lg"
      :style="{ height: getHeight() }"
    >
      <a-row :gutter="[12, 12]">
        <a-col span="24">
          <a-row :gutter="[12, 12]" :wrap="false">
            <a-col flex="none">
              <div class="2xl:w-[10rem] w-[8rem]">
                <a-typography-title :level="4" ellipsis>
                  {{ t('form.subcontractor') }}
                </a-typography-title>
              </div>
            </a-col>
            <a-col flex="auto">
              <a-input
                v-model:value="searchForm.keyword"
                :placeholder="t('search')"
                style="width: 100%"
                allow-clear
                @press-enter="onSearch"
              >
                <template #prefix>
                  <SearchOutlined class="text-gray-500" />
                </template>
              </a-input>
            </a-col>
            <a-col flex="none">
              <a-popover
                v-model:open="isOpenFilter"
                trigger="click"
                placement="bottomRight"
                :arrow="false"
                class="w-[6rem]"
                :overlay-style="{ width: '300px' }"
              >
                <template #title>
                  <div class="flex items-center justify-between p-2">
                    <span class="flex items-center gap-1">
                      <CarbonFilterNew size="14" />
                      {{ t('button.filters') }}
                    </span>
                    <a-button
                      type="text"
                      size="small"
                      class="flex items-center justify-center"
                      @click="isOpenFilter = false"
                    >
                      <CloseOutlined />
                    </a-button>
                  </div>
                  <a-divider class="m-0" />
                </template>
                <template #content>
                  <div>
                    <a-form layout="vertical">
                      <a-row :gutter="[12, 12]">
                        <a-col span="12">
                          <a-form-item
                            :label="t('form.time-period-from')"
                            class="mb-0"
                          >
                            <a-date-picker
                              v-model:value="searchForm.fromDate"
                              :placeholder="t('form.time-period-from')"
                              style="width: 100%"
                              value-format="YYYY-MM-DD"
                            />
                          </a-form-item>
                        </a-col>
                        <a-col span="12">
                          <a-form-item :label="t('form.to')" class="mb-0">
                            <a-date-picker
                              v-model:value="searchForm.toDate"
                              :placeholder="t('form.to')"
                              style="width: 100%"
                              value-format="YYYY-MM-DD"
                            />
                          </a-form-item>
                        </a-col>
                        <a-divider class="mb-0 mt-0" />
                        <a-col span="12">
                          <a-form-item
                            :label="t('form.price-from')"
                            class="mb-0"
                          >
                            <a-input-number
                              v-model:value="searchForm.priceMin"
                              :placeholder="t('form.price-from')"
                              style="width: 100%"
                              min="0"
                            />
                          </a-form-item>
                        </a-col>
                        <a-col span="12">
                          <a-form-item :label="t('form.to')" class="mb-0">
                            <a-input-number
                              v-model:value="searchForm.priceMax"
                              :placeholder="t('form.to')"
                              style="width: 100%"
                              min="0"
                            />
                          </a-form-item>
                        </a-col>
                        <a-divider class="mb-0 mt-0" />
                        <a-col span="24" class="p-2 text-right">
                          <div class="flex justify-between">
                            <a-button @click="onResetSearch">
                              {{ $t('button.reset') }}
                            </a-button>
                            <a-button
                              type="primary"
                              @click="
                                () => {
                                  isOpenFilter = false;
                                  onSearch();
                                }
                              "
                            >
                              {{ $t('button.apply') }}
                            </a-button>
                          </div>
                        </a-col>
                      </a-row>
                    </a-form>
                  </div>
                </template>
                <a-button class="w-[7rem]">
                  <div class="flex items-center gap-1">
                    <CarbonFilterNew size="14" />
                    {{ t('button.filters') }}
                    <DownOutlined />
                  </div>
                </a-button>
              </a-popover>
            </a-col>
            <a-col flex="none">
              <a-button
                class="flex items-center"
                type="primary"
                @click="handleAdd"
              >
                <PlusOutlined />
                {{ `${t('button.new')} ${t('form.item')}` }}
              </a-button>
            </a-col>
          </a-row>
        </a-col>
        <a-col span="24">
          <a-row>
            <a-col span="24">
              <div :style="{ height: `${getHeight(126)}` }">
                <a-table
                  class="tableCostItemPrice"
                  :scroll="{ x: 'max-content', y: getHeight(180) }"
                  :columns="columns"
                  :data-source="sortedDataSource"
                  :loading="loading"
                  :pagination="false"
                  row-key="itemPriceId"
                  @change="handleTableChange"
                  bordered
                >
                  <template #bodyCell="{ column, text, record }">
                    <template v-if="column.dataIndex === 'vendor'">
                      <div>
                        <a-select
                          v-if="editableData[record.itemPriceId]"
                          v-model:value="
                            editableData[record.itemPriceId].vendor
                          "
                          style="width: 100%"
                          :allow-clear="false"
                          :placeholder="t('form.provider')"
                          label-in-value
                          :options="vendors"
                          :field-names="{
                            label: 'vendorName',
                            value: 'vendorId',
                          }"
                        />

                        <template v-else>
                          {{ record.vendorName }}
                        </template>
                      </div>
                    </template>
                    <template v-if="column.dataIndex === 'unit'">
                      <div>
                        <a-input
                          v-if="editableData[record.itemPriceId]"
                          v-model:value="editableData[record.itemPriceId].unit"
                          :placeholder="t('form.unit')"
                        />
                        <template v-else>
                          {{ text }}
                        </template>
                      </div>
                    </template>
                    <template v-if="column.dataIndex === 'price'">
                      <div>
                        <a-input
                          v-if="editableData[record.itemPriceId]"
                          v-model:value="editableData[record.itemPriceId].price"
                          type="number"
                          :placeholder="t('form.price')"
                          min="0"
                        />
                        <template v-else>
                          {{ text?.toLocaleString() }}
                        </template>
                      </div>
                    </template>
                    <template v-if="column.dataIndex === 'time_period'">
                      <div>
                        <a-range-picker
                          v-if="editableData[record.itemPriceId]"
                          v-model:value="
                            editableData[record.itemPriceId].time_period
                          "
                          :allow-clear="false"
                        />
                        <template v-else>
                          {{ record.validFrom }} <ArrowRightOutlined />
                          {{ record.validTo }}
                        </template>
                      </div>
                    </template>
                    <template v-else-if="column.dataIndex === 'action'">
                      <div
                        v-if="editableData[record.itemPriceId]"
                        class="flex flex-justify-center gap-2"
                      >
                        <a-button
                          class="flex items-center"
                          size="small"
                          type="primary"
                          @click="handleSave(record.itemPriceId)"
                        >
                          <PlusOutlined />
                        </a-button>
                        <a-popconfirm
                          :title="t('message.cancel-confirmation')"
                          @confirm="handleCancel(record.itemPriceId)"
                        >
                          <a-button
                            class="flex items-center"
                            size="small"
                            danger
                          >
                            <CloseOutlined />
                          </a-button>
                        </a-popconfirm>
                      </div>
                    </template>
                  </template>
                </a-table>
              </div>
            </a-col>
            <a-col span="24">
              <a-row justify="space-between" class="mt-4">
                <a-col>
                  <a-pagination
                    class="pagination"
                    :total="pagination.total"
                    :current="pagination.current"
                    :page-size="pagination.pageSize"
                    @change="handlePaginationChange"
                  />
                </a-col>
                <a-col>
                  <a-row :gutter="[12, 12]" justify="center" align="middle">
                    <a-col>{{ t('show') }}</a-col>
                    <a-col>
                      <a-pagination
                        class="pagination pagination-right"
                        :total="pagination.total"
                        :current="pagination.current"
                        :page-size="pagination.pageSize"
                        show-size-changer
                        :build-option-text="(props: any) => props.value"
                        @change="handlePaginationChange"
                      />
                    </a-col>
                    <a-col>{{ t('entries') }}</a-col>
                  </a-row>
                </a-col>
              </a-row>
            </a-col>
          </a-row>
        </a-col>
      </a-row>
    </a-col>
    <a-col
      span="24"
      class="bg-white !p-4 rounded-md shadow-lg"
      :style="{ height: getHeight() }"
    >
      <a-row>
        <a-col span="12">
          <a-typography-title :level="4" ellipsis>
            {{ t('suppliers-comparison') }}
          </a-typography-title>
          <VChart
            ref="supplierChart"
            class="suppliers-comparison-chart"
            :style="{ height: getHeight(64) }"
            :option="supplierOption"
          />
        </a-col>
        <a-col span="12">
          <a-typography-title :level="4" ellipsis>
            {{ t('monthly-price-comparison') }}
          </a-typography-title>
          <div class="monthly-price" :style="{ height: getHeight(64) }">
            <div
              v-for="item in monthlyPrices"
              :key="item.value"
              class="flex flex-col"
            >
              <span class="font-medium">{{ item.name }}</span>
              <VChart
                ref="monthlyPriceChart"
                class="monthly-price-chart"
                :option="item.option"
              />
              <a-divider />
            </div>
          </div>
        </a-col>
      </a-row>
    </a-col>
  </a-row>
</template>

<style lang="less" scoped>
.tableCostItemPrice {
  :deep(.ant-table-tbody > tr.row-active) {
    background: #f2f8fd;
  }
  :deep(.ant-table-tbody > tr) {
    background: #fff;
  }
  :deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
    background: #f2f8fd;
  }
}
.suppliers-comparison-chart {
  overflow-y: auto;
  overflow-x: hidden;
}
.monthly-price {
  overflow-y: auto;
  overflow-x: hidden;
}
.monthly-price-chart {
  height: 250px;
}
.pagination {
  :deep(.ant-pagination-item-active) {
    background: #f99649;
    border-color: #f99649;
    a {
      color: #fff;
    }
  }
  :deep(.ant-select-selector) {
    background: #f99649;
    border-color: #f99649 !important;
    .ant-select-selection-item {
      color: #fff;
    }
  }
  :deep(.ant-select-arrow) {
    color: #fff;
  }
}
.pagination-right {
  :deep(.ant-pagination-prev) {
    display: none;
  }
  :deep(.ant-pagination-next) {
    display: none;
  }
  :deep(.ant-pagination-item) {
    display: none;
  }
  :deep(.ant-pagination-options) {
    margin: 0;
  }
  :deep(.ant-pagination-jump-next) {
    display: none;
  }
  :deep(.ant-pagination-jump-prev) {
    display: none;
  }
}
</style>
