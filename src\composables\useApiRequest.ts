import { ref } from 'vue'

// Quản lý gọi API, loading, error, data, Dùng cho mọi API
export function useApiRequest<T extends any[]>(apiFn: (...args: T) => Promise<any>, options = { immediate: false, showNotify: false }) {
  const loading = ref(false)
  const error = ref<any>(null)
  const data = ref<any>(null)
  const messageNotify = useMessage()

  const execute = async (...args: T) => {
    loading.value = true
    error.value = null
    try {
      const res = await apiFn(...args)
      data.value = res.data
      if (options.showNotify) {
        if (res.status === 200)
          messageNotify.success(res.message)
        else
          messageNotify.error(res.message)
      }
      return res
    }
    catch (err) {
      error.value = err
      return err
    }
    finally {
      loading.value = false
    }
  }

  if (options.immediate)
    execute(...([] as unknown as T))

  return {
    loading,
    error,
    data,
    execute,
  }
}
