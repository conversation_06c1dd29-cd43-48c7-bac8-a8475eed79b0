<script lang="ts" setup>
import { computed, reactive, ref, watch } from 'vue'
import { usePagination } from 'vue-request'
import type { SelectValue } from 'ant-design-vue/es/select'
import type { InputCost, InputCostPostParams, InputCostPutParams } from '~@/api/invoice'
import { parseDate } from '~@/utils/apiTimer'
import { getEntryType } from '~@/api/company/entry-type'
import { getPaymentType } from '~@/api/company/payment-type'
import type { QueryParams } from '~@/api/common-params'
import { getProjectComboApi } from '~@/api/company/project'
import { getConstructionByProjectIdApi } from '~@/api/construction'
import type { ConstructionCostItem } from '~@/api/construction-cost'
import type { GetVendorParams } from '~@/api/company/vendor'
import { getVendor } from '~@/api/company/vendor'

const props = defineProps({
  // If you have an existing invoice, pass it as a prop
  invoice: {
    type: Object as () => Partial<InputCost>,
    required: true,
    default: () => ({}),
  },
  invoiceInfoEditing: {
    type: Boolean,
    default: false,
  },
})

// const emit = defineEmits(['update:invoice'])
const emit = defineEmits<{
  (event: 'addNewInputCostInfo', invoice: InputCostPostParams): void
  (event: 'updateInputCostInfo', inputCostId: string, invoice: InputCostPutParams): void
}>()

const { t } = useI18n()

const editableInvoice = reactive<Partial<InputCost>>({
  ...props.invoice,
})

const editing = ref(false)

// Track which field is currently being edited
const currentEditField = ref<string | null>(null)

// Check if invoice is empty
const isEmpty = computed(() => {
  return !props.invoice.issueDate
               && !props.invoice.paymentDate
               && !props.invoice.originalNumber
               && !props.invoice.title
               && !props.invoice.entryTypeName
               && !props.invoice.totalAmount
               && !props.invoice.paymentTypeName
               && !props.invoice.projectName
               && !props.invoice.vendorPresentativeName
               && !props.invoice.vendorAddress
               && !props.invoice.vendorName
               && !props.invoice.vendorPhoneNumber
               && !props.invoice.vendorEmail
})

// Check if there are unsaved changes
// const hasChanges = computed(() => {
//   return JSON.stringify(props.invoice) !== JSON.stringify(editableInvoice)
// })

// Enable edit mode for all fields
function enableEditMode() {
  editing.value = true
}

// Enable edit mode for a specific field
function editField(fieldName: string) {
  currentEditField.value = fieldName
}

// Save changes for a single field
// function onSingleFieldBlur(fieldName: string) {
//   if (currentEditField.value === fieldName) {
//     // Update the original invoice with the new value
//     (invoice as any)[fieldName] = (editableInvoice as any)[fieldName]
//     currentEditField.value = null

//     // Emit the updated invoice
//     emit('update:invoice', { ...invoice })

//     // Show a success message
//     message.success(`${fieldName} updated`)
//   }
// }

async function saveAllChanges() {
  if (!props.invoice.inputCostId || !editableInvoice?.vendorId) {
    const params: InputCostPostParams = {
      issueDate: editableInvoice?.issueDate,
      constructionId: editableInvoice?.constructionId,
      paymentDate: editableInvoice?.paymentDate,
      originalNumber: editableInvoice?.originalNumber,
      title: editableInvoice?.title,
      entryTypeId: editableInvoice?.entryTypeId,
      totalAmount: editableInvoice?.totalAmount,
      paymentTypeId: editableInvoice?.paymentTypeId,
      vendor: {
        vendorId: editableInvoice?.vendorId ?? '',
        vendorName: editableInvoice?.vendorName,
      },
    }
    // addNewInputCost(params)
    emit('addNewInputCostInfo', params)
  }
  else {
    const params: InputCostPutParams = {
      issueDate: editableInvoice?.issueDate,
      constructionId: editableInvoice?.constructionId,
      paymentDate: editableInvoice?.paymentDate,
      originalNumber: editableInvoice?.originalNumber,
      title: editableInvoice?.title,
      entryTypeId: editableInvoice?.entryTypeId,
      totalAmount: editableInvoice?.totalAmount,
      paymentTypeId: editableInvoice?.paymentTypeId,
      vendorId: editableInvoice?.vendorId,
    }
    if (!editableInvoice.inputCostId)
      return
    // await updateInputCost(editableInvoice.inputCostId, params)
    emit('updateInputCostInfo', editableInvoice.inputCostId, params)
  }
}

// Cancel editing and revert changes
// function cancelEditing() {
//   editing.value = false
//   currentEditField.value = null

//   message.info('Changes canceled')
// }

const projectParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 50,
})
async function queryProject(params: QueryParams) {
  const { data } = await getProjectComboApi(params)
  return data
}
const {
  data: projectData,
} = usePagination(queryProject, {
  defaultParams: [projectParams],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})
const projectOptions = computed(() => {
  return projectData.value?.items || []
})

const entryTypeParams = reactive<any>({
  pageNum: 1,
  pageSize: 50,
})
async function queryEntryType(params: any) {
  const { data } = await getEntryType(params)
  return data
}
const {
  data: entryTypeData,
} = usePagination(queryEntryType, {
  defaultParams: [entryTypeParams],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})
const entryTypeOptions = computed(() => entryTypeData.value?.entryTypes ?? [])

const paymentTypeParams = reactive<any>({
  pageNum: 1,
  pageSize: 50,
})
async function queryPaymentType(params: any) {
  const { data } = await getPaymentType(params)
  return data
}
const {
  data: paymentTypeData,
} = usePagination(queryPaymentType, {
  defaultParams: [paymentTypeParams],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})
const paymentTypeOptions = computed(() => paymentTypeData.value?.items ?? [])

const constructionOptions = ref<Partial<ConstructionCostItem>[]>()

async function onProjectChange(projectId: SelectValue) {
  if (typeof projectId !== 'string')
    return
  const { data, status } = await getConstructionByProjectIdApi(projectId)
  if (status === 200)
    constructionOptions.value = data?.constructions ?? []
}

async function queryVendor(params: GetVendorParams) {
  const { data } = await getVendor(params)
  return data
}

const {
  data: vendorData,
} = usePagination(queryVendor, {
  defaultParams: [{
    pageNum: 1,
    pageSize: 10,
  }],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const vendorOptions = computed(() => vendorData.value?.items ?? [])

// Watch for prop changes
watch(() => props.invoice, (newValue) => {
  if (newValue) {
    editableInvoice.constructionId = newValue.constructionId
    editableInvoice.entryTypeId = newValue.entryTypeId
    editableInvoice.paymentTypeId = newValue.paymentTypeId
    editableInvoice.vendorId = newValue.vendorId
    editableInvoice.issueDate = newValue.issueDate
    editableInvoice.paymentDate = newValue.paymentDate
    editableInvoice.originalNumber = newValue.originalNumber
    editableInvoice.title = newValue.title
    editableInvoice.projectName = newValue.projectName
    editableInvoice.entryTypeName = newValue.entryTypeName
    editableInvoice.paymentTypeName = newValue.paymentTypeName
    editableInvoice.vendorName = newValue.vendorName
    editableInvoice.vendorPresentativeName = newValue.vendorPresentativeName
    editableInvoice.constructionName = newValue.constructionName
    editableInvoice.vendorAddress = newValue.vendorAddress
    editableInvoice.vendorPhoneNumber = newValue.vendorPhoneNumber
    editableInvoice.vendorEmail = newValue.vendorEmail
    editableInvoice.totalAmount = newValue.totalAmount
    editableInvoice.totalAmount = newValue.totalAmount ?? 0
  }
}, {
  deep: true,
  immediate: true,
})

watch(() => props.invoiceInfoEditing, (newValue) => {
  if (newValue)
    editing.value = newValue
}, {
  immediate: true,
})
</script>

<template>
  <div class="space-y-4">
    <div class="pt-4">
      <div class="grid grid-cols-3 gap-4">
        <div>
          <div class="text-gray-500 text-sm">
            {{ t('project') }}
          </div>
          <div class="font-medium flex items-center gap-1">
            <a-select
              v-if="invoiceInfoEditing || !invoice.projectId || editing"
              v-model:value="editableInvoice.projectId"
              :options="projectOptions"
              :field-names="{ label: 'name', value: 'id' }"
              class="w-full"
              @change="onProjectChange"
            />
            <div v-else class="py-1" @click="editField('projectName')">
              {{ invoice.projectName }}
            </div>
          </div>
        </div>
        <div>
          <div class="text-gray-500 text-sm">
            {{ t('constructure') }}
          </div>
          <div class="font-medium flex items-center gap-1">
            <a-select
              v-if="invoiceInfoEditing || !invoice.constructionId || editing"
              v-model:value="editableInvoice.constructionId"
              :options="constructionOptions"
              :field-names="{ label: 'constructionName', value: 'constructionId' }"
              class="w-full"
            />
            <div v-else class="py-1" @click="editField('constructionName')">
              {{ invoice.constructionName }}
            </div>
          </div>
        </div>
        <div>
          <div class="text-gray-500 text-sm">
            {{ t('vendor') }}
          </div>
          <div class="font-medium flex items-center gap-1">
            <a-select
              v-if="invoiceInfoEditing || !invoice.vendorId || editing"
              v-model:value="editableInvoice.vendorId"
              :options="vendorOptions"
              :field-names="{ label: 'vendorName', value: 'vendorId' }"
              class="w-full"
            />
            <div v-else class="py-1" @click="editField('vendorName')">
              {{ invoice.vendorName }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-4 gap-4">
      <div class="col-span-2">
        <div class="text-gray-500 text-sm">
          {{ t('invoice-title') }}
        </div>
        <div class="font-medium">
          <a-input
            v-if="invoiceInfoEditing || !invoice.title || editing"
            v-model:value="editableInvoice.title"
          />
          <div v-else class="py-1" @click="editField('title')">
            {{ invoice.title }}
          </div>
        </div>
      </div>
      <div class="col-span-2">
        <div class="text-gray-500 text-sm">
          {{ t('invoice-number') }}
        </div>
        <div class="font-medium">
          <a-input
            v-if="invoiceInfoEditing || !invoice.originalNumber || editing"
            v-model:value="editableInvoice.originalNumber"
          />
          <div v-else class="py-1" @click="editField('originalNumber')">
            {{ invoice.originalNumber }}
          </div>
        </div>
      </div>
      <div class="col-span-2">
        <div class="text-gray-500 text-sm">
          {{ t('release-date') }}
        </div>
        <div class="font-medium">
          <a-date-picker
            v-if="invoiceInfoEditing || !invoice.issueDate || editing"
            v-model:value="editableInvoice.issueDate"
            class="w-full"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
          <div v-else class="py-1" @click="editField('issueDate')">
            {{ parseDate(invoice.issueDate) }}
          </div>
        </div>
      </div>
      <div class="col-span-2">
        <div class="text-gray-500 text-sm">
          {{ t('payment-term') }}
        </div>
        <div class="font-medium">
          <a-date-picker
            v-if="invoiceInfoEditing || !invoice.paymentDate || editing"
            v-model:value="editableInvoice.paymentDate"
            class="w-full"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
          <div v-else class="py-1" @click="editField('paymentDate')">
            {{ parseDate(invoice.paymentDate) }}
          </div>
        </div>
      </div>
      <div class="col-span-2">
        <div class="text-gray-500 text-sm">
          {{ t('type') }}
        </div>
        <div class="font-medium">
          <a-select
            v-if="invoiceInfoEditing || !invoice.entryTypeName || editing"
            v-model:value="editableInvoice.entryTypeId"
            :options="entryTypeOptions"
            :field-names="{ label: 'entryTypeName', value: 'entryTypeId' }"
            class="w-full"
          />
          <div v-else class="py-1" @click="editField('entryTypeName')">
            {{ invoice.entryTypeName }}
          </div>
        </div>
      </div>
      <div class="col-span-2">
        <div class="text-gray-500 text-sm">
          {{ t('payment-method') }}
        </div>
        <div class="font-medium">
          <a-select
            v-if="invoiceInfoEditing || !invoice.paymentTypeId || editing"
            v-model:value="editableInvoice.paymentTypeId"
            :options="paymentTypeOptions"
            :field-names="{ label: 'paymentTypeName', value: 'paymentTypeId' }"
            class="w-full"
          />
          <div v-else class="py-1" @click="editField('paymentTypeName')">
            {{ invoice.paymentTypeName }}
          </div>
        </div>
      </div>
      <div class="col-span-4">
        <div class="text-gray-500 text-sm">
          {{ t('total') }}
        </div>
        <div class="font-medium">
          <a-input-number
            v-if="invoiceInfoEditing || !invoice.totalAmount || editing"
            v-model:value="editableInvoice.totalAmount"
            class="w-full"
            :formatter="(value: any) => `${value} ¥`"
            :parser="(value: any) => value.replace(' ¥', '')"
          />
          <div v-else class="py-1" @click="editField('totalAmount')">
            {{ invoice.totalAmount }} ¥
          </div>
        </div>
      </div>
    </div>

    <div class="pt-4 flex justify-end space-x-2">
      <a-button v-if="editing" type="primary" @click="saveAllChanges">
        {{ t('button.save-all') }}
      </a-button>
      <!-- <a-button v-if="editing" @click="cancelEditing">
        {{ t('button.cancel') }}
      </a-button> -->
      <a-button v-if="!editing && !isEmpty" type="primary" @click="enableEditMode">
        {{ t('button.edit') }}
      </a-button>
    </div>
  </div>
</template>

  <style scoped>
  .font-medium > div {
    min-height: 32px;
    cursor: pointer;
  }
  .font-medium > div:hover {
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 2px;
  }
  </style>
