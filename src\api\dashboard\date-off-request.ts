import qs from 'qs'

export interface RequestItem {
  requestId: string
  requestUserName?: string
  requestFrom?: string
  requestTo?: string
  requestTypeCode?: string
  requestTypeName?: string
  leaveTypeCode?: string
  leaveTypeName?: string
  duration?: {
    days: number
    hours: number
    minutes: number
  }
  description?: string
  statusCode?: string
  statusName?: string
  approver1Name?: string
  approver2Name?: string
  createUserName?: string
  projectId?: string
  projectName?: string
  status?: string
  approver1Status?: string
  approver1Notes?: string
  approver1Time?: string
  approver2Status?: string
  approver2Notes?: string
  approver2Time?: string
  createTime?: string
  updateTime?: string
}

export interface RequestDataResponse {
  items: RequestItem[]
  pageIndex?: number
  pageSize?: number
  totalRecords?: number
}

export interface RequestDataParams {
  requestId?: string
  requestFrom: string
  requestTo: string
  requestTypeCode?: string
  leaveTypeCode?: string
  quantity?: number
  unitId?: string
  description?: string
  projectId?: string
  workplaceId?: string
}

export type OfficeRequestParams = Pick<
  RequestDataParams,
  'requestFrom' | 'requestTo' | 'projectId' | 'requestTypeCode'
>

export interface GetRequestParams {
  fromDate?: string
  toDate?: string
  statusCode?: string[]
  requestTypeCode?: string
  leaveTypeCode?: string
  requestTypeCodes?: string[]
  leaveTypeCodes?: string[]
  isAttendanceRequest?: boolean
  pageNum?: number
  pageSize?: number
}

export interface RequestSearchParams {
  fromDate: string
  toDate: string
  pageNum: number
  pageSize: number
}

export interface ApproverRequestSearchParams {
  dateFrom?: string
  dateTo?: string
  pageNum?: number
  pageSize?: number
  status?: string
  requestTypeId?: string
  isAttendanceRequest?: boolean
}

export interface ActionRequestParams {
  requestId: string
  rejectReason?: string
}

export async function getOneRequest(id: string) {
  return useGet<RequestItem>(`v1/request/${id}`)
}

export async function getRequest(params?: GetRequestParams) {
  return useGet<RequestDataResponse>('v1/request/by-au', params, {
    paramsSerializer: params => qs.stringify(params, { indices: false }),
  })
}

export async function getRequestByAuApi(params?: RequestSearchParams) {
  return useGet<RequestDataResponse>('v1/request/by-au', params)
}

export async function getRequestByAuNoPaginationApi(
  params?: Partial<RequestSearchParams>,
) {
  return useGet<RequestDataResponse>('v1/request/by-au/no-pagination', params)
}

export async function getApproverRequestApi(
  params?: ApproverRequestSearchParams,
) {
  return useGet<RequestDataResponse>('v1/request/by-approver', params)
}

export async function createNewRequestApi(
  data: Partial<RequestDataParams | OfficeRequestParams>,
) {
  return usePost('v1/request', data)
}

export async function updateRequestApi(
  id: string,
  data: Partial<RequestDataParams>,
) {
  return usePut(`v1/request/${id}`, data)
}

export async function approveRequestApi(id: string) {
  return usePut(`v1/request/${id}/approve`)
}

export async function rejectRequestApi(id: string) {
  return usePut(`v1/request/${id}/reject`)
}

export async function cancelRequestApi(data: ActionRequestParams) {
  return usePut<any>('v1/request/cancel', data)
}

export async function deleteRequest(id: string) {
  return useDelete(`v1/request/${id}`)
}
