<script lang='ts' setup>
import type { ConstructionItem } from '~@/api/construction'
import { getConstructionByIdApi } from '~@/api/construction'
import type { ConstructionCostItem } from '~@/api/construction-cost'

const props = defineProps({
  constructionCost: {
    type: Object as () => ConstructionCostItem,
    required: true,
  },
})

const { t } = useI18n()
const messageNotify = useMessage()

const construction = ref<ConstructionItem | null>()

const estimateBudgetAmount = computed(() => {
  return construction.value?.estimatedCosts?.totalEstimateCost ?? 0
})

const accumulatedRequestAmount = computed(() => {
  return props.constructionCost?.currentAccumulateCost?.requestAmount ?? 0
})

const accumulatedTotalAmount = computed(() => {
  return props.constructionCost.currentAccumulateCost.totalCost
})
const againstBudget = computed(() => {
  return accumulatedRequestAmount.value
         - estimateBudgetAmount.value
})

const actualProfitMargin = computed(() => {
  if (accumulatedRequestAmount.value === 0)
    return 0
  return ((accumulatedRequestAmount.value - accumulatedTotalAmount.value) / accumulatedRequestAmount.value)
})

const mcEstimatedProfitMargin = computed(() => {
  if (construction.value?.contractualCosts.totalInitialCost === 0)
    return 0
  return ((construction.value?.contractualCosts?.totalInitialCost ?? 0) - (construction.value?.estimatedCosts?.totalEstimateCost ?? 0)) / (construction.value?.contractualCosts?.totalInitialCost ?? 0) * 100
})

async function fetchConstructionById() {
  const constructionId = props.constructionCost.constructionId
  const { data, status, message } = await getConstructionByIdApi(constructionId)
  if (status === 200) {
    construction.value = data
  }
  else {
    messageNotify.error(message)
  }
}

onMounted(() => {
  fetchConstructionById()
})
</script>

<template>
  <div class="bg-white shadow-lg rounded-xl p-6">
    <div class="flex justify-between gap-x-2 items-center mb-4">
      <h2 class="text-xl font-bold text-gray-800">
        {{ construction?.constructionName }}
      </h2>
      <a-tag v-if="constructionCost?.isPrimary" color="blue">{{ t('mainConstruction') }}</a-tag>
      <a-tag v-else color="green">{{ t('subConstruction') }}</a-tag>
    </div>

    <div class="space-y-4">
      <div class="flex justify-between items-center">
        <span class="text-gray-600">{{ t('estimateBudgetAmount') }}</span>
        <span class="font-bold text-green-700">{{ estimateBudgetAmount }}</span>
      </div>

      <div class="flex justify-between items-center">
        <span class="text-gray-600">{{ t('againstBudget') }}</span>
        <span class="font-bold text-blue-700">{{ againstBudget }}</span>
      </div>

      <div class="border-t pt-4 space-y-2">
        <div class="flex justify-between items-center">
          <span class="text-gray-600">{{ t('actualProfitMargin') }}</span>
          <div
            class="font-bold px-3 py-1 rounded-full text-sm"
            :class="actualProfitMargin > 0
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'"
          >
            {{ actualProfitMargin.toFixed(2) }}%
          </div>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-gray-600">{{ t('mcEstimatedProfitMargin') }}</span>
          <div
            class="font-bold px-3 py-1 rounded-full text-sm"
            :class="mcEstimatedProfitMargin > 0
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'"
          >
            {{ mcEstimatedProfitMargin.toFixed(2) }}%
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
