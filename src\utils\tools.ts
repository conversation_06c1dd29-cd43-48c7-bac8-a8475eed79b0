import { get } from 'lodash-es';
import dayjs from 'dayjs';
import _ from 'lodash';
import router from '@/router';

export function getQueryParam(param: string | string[], defaultVal = '') {
  const query = router.currentRoute.value?.query ?? {};
  const val = get(query, param) ?? defaultVal;
  return decodeURIComponent(val);
}

export function getTimeFromMins(minute: number) {
  return dayjs().startOf('day').add(minute, 'minute').format('H:mm');
}

export function formatData(data: any) {
  const body = _.pickBy(data, (x) => {
    if (x === false)
      return true;
    if (x === 0)
      return true;
    if (x === null)
      return true;
    if (x === '')
      return true;
    return x;
  });
  return _.cloneDeepWith(body, (value) => {
    if (value === '')
      return null;
  });
}
