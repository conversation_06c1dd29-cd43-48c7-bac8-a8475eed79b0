import { isUrl } from '@v-c/utils'
import type { RouteRecordRaw } from 'vue-router'
import { omit } from 'lodash'
import { basicRouteMap, getRouterModule } from './router-modules'
import type { MenuData, MenuDataItem } from '~@/layouts/basic-layout/typing'
import dynamicRoutes, { ROOT_ROUTE_REDIRECT_PATH } from '~@/router/dynamic-routes'
import { i18n } from '~@/locales'

let cache_key = 1

const getCacheKey = () => `Cache_Key_${cache_key++}`

function renderTitle(route: RouteRecordRaw) {
  const { title, locale } = route.meta || {}
  if (!title)
    return ''
  return locale ? (i18n.global as any).t(locale) : title
}

function formatMenu(route: RouteRecordRaw, path?: string) {
  return {
    parentId: route.meta?.parentId,
    title: () => renderTitle(route),
    icon: route.meta?.icon || '',
    path: path ?? route.path,
    hideInMenu: route.meta?.hideInMenu || false,
    parentKeys: route.meta?.parentKeys || [],
    hideInBreadcrumb: route.meta?.hideInBreadcrumb || false,
    hideChildrenInMenu: route.meta?.hideChildrenInMenu || false,
    locale: route.meta?.locale,
    keepAlive: route.meta?.keepAlive || false,
    name: route.name as string,
    url: route.meta?.url || '',
    target: route.meta?.target || '_blank',
  }
}

export function genRoutes(routes: RouteRecordRaw[], parent?: MenuDataItem) {
  const menuData: MenuData = []
  const { hasAccess } = useAccess()
  routes.forEach((route) => {
    if (route.meta?.access) {
      const isAccess = hasAccess(route.meta?.access)
      if (!isAccess)
        return
    }
    let path = route.path
    if (!path.startsWith('/') && !isUrl(path)) {
      if (parent)
        path = `${parent.path}/${path}`
      else path = `/${path}`
    }
    if (!route.name)
      route.name = getCacheKey()
    const item: MenuDataItem = formatMenu(route, path)
    item.children = []
    if (route.children && route.children.length)
      item.children = genRoutes(route.children, item)
    if (item.children?.length === 0)
      delete item.children
    menuData.push(item)
  })
  return menuData
}

// Hàm này giúp trả về menuData -> Khung ở menu
// routeData -> Cấu hình router cho ứng dụng
export function generateTreeRoutes(menus: MenuData) {
  const routeDataMap = new Map<string | number, RouteRecordRaw>()
  const menuDataMap = new Map<string | number, MenuDataItem>()
  for (const menuItem of menus) {
    if (!menuItem.name)
      continue
    const route = {
      path: menuItem.path,
      name: menuItem.name || getCacheKey(),
      component: getRouterModule(menuItem.component!),
      redirect: menuItem.redirect || undefined,
      meta: {
        title: menuItem?.title as string,
        icon: menuItem?.icon as string,
        keepAlive: menuItem?.keepAlive ?? true,
        parentName: menuItem?.parentName,
        affix: menuItem?.affix,
        parentKeys: menuItem?.parentKeys,
        url: menuItem?.url,
        hideInMenu: menuItem?.hideInMenu,
        hideChildrenInMenu: menuItem?.hideChildrenInMenu,
        hideInBreadcrumb: menuItem?.hideInBreadcrumb,
        target: menuItem?.target,
        locale: menuItem?.locale,
      },
    } as RouteRecordRaw
    const menu = formatMenu(route)
    routeDataMap.set(menuItem.name, route)
    menuDataMap.set(menuItem.name, menu)
  }
  const routeData: RouteRecordRaw[] = []
  const menuData: MenuData = []

  for (const menuItem of menus) {
    if (!menuItem.name)
      continue
    const currentRoute = routeDataMap.get(menuItem.name)
    const currentItem = menuDataMap.get(menuItem.name)
    if (!menuItem.parentName) {
      if (currentRoute && currentItem) {
        routeData.push(currentRoute)
        menuData.push(currentItem)
      }
    }
    else {
      const pRoute = routeDataMap.get(menuItem.parentName)
      const pItem = menuDataMap.get(menuItem.parentName)
      if (currentItem && currentRoute && pRoute && pItem) {
        if (pRoute.children && pItem.children) {
          pRoute.children.push(currentRoute)
          pItem.children.push(currentItem)
        }
        else {
          pItem.children = [currentItem]
          pRoute.children = [currentRoute]
        }
      }
    }
  }
  return {
    menuData,
    routeData,
  }
}

export async function generateRoutes() {
  const menuData = genRoutes(dynamicRoutes)

  return {
    menuData,
    routeData: dynamicRoutes,
  }
}

function checkComponent(component: RouteRecordRaw['component']) {
  for (const componentKey in basicRouteMap) {
    if (component === (basicRouteMap as any)[componentKey])
      return undefined
  }
  return component
}

function flatRoutes(
  routes: RouteRecordRaw[],
  parentName?: string,
  parentComps: RouteRecordRaw['component'][] = [],
) {
  const flatRouteData: RouteRecordRaw[] = []
  for (const route of routes) {
    const parentComponents = [...parentComps]
    const currentRoute = omit(route, ['children']) as RouteRecordRaw
    if (!currentRoute.meta)
      currentRoute.meta = {}
    if (parentName)
      currentRoute.meta.parentName = parentName
    if (parentComponents.length > 0)
      currentRoute.meta.parentComps = parentComponents
    currentRoute.meta.originPath = currentRoute.path
    flatRouteData.push(currentRoute)
    if (route.children && route.children.length) {
      const comp = checkComponent(route.component)
      if (comp)
        parentComponents.push(comp)
      flatRouteData.push(
        ...flatRoutes(route.children, route.name as string, [
          ...parentComponents,
        ]),
      )
    }
  }
  return flatRouteData
}

export function generateFlatRoutes(routes: RouteRecordRaw[]) {
  const flatRoutesList = flatRoutes(routes)
  const parentRoute: RouteRecordRaw = {
    path: '/',
    redirect: ROOT_ROUTE_REDIRECT_PATH,
    name: 'ROOT_EMPTY_PATH',
    component: getRouterModule('RouteView'),
    children: flatRoutesList,
  }
  return [parentRoute]
}
