import type { OrganizationParams } from '@/api/company/org'
import { getOrganizationDetailApi, getOrgsByCurrentAccountApi, updateOrganizationApi } from '@/api/company/org'

export function useOrganization() {
  const messageNotification = useMessage()

  const fetchOrgList = async () => {
    const { data, status, message } = await getOrgsByCurrentAccountApi()
    if (status === 200)
      return data?.items ?? []
    messageNotification.error(message ?? 'L<PERSON><PERSON> danh sách công ty thất bại')
  }

  const fetchOrgDetail = async (id: string) => {
    const { data, status, message } = await getOrganizationDetailApi(id)
    if (status === 200)
      return data
    messageNotification.error(message ?? 'Lấy chi tiết công ty thất bại')
  }

  const updateOrg = async (id: string, params: OrganizationParams) => {
    const { data, status, message } = await updateOrganizationApi(id, params)
    if (status === 200)
      return data
    messageNotification.error(message ?? 'Cậ<PERSON> nhật công ty thất bại')
  }

  return {
    fetchOrgList,
    fetchOrgDetail,
    updateOrg,
  }
}
