import type { STATUS } from '~@/utils/constant'

interface ConsultTableModel {
  id: number
  name: string
  callNo: 805
  desc: string
  status: STATUS
  updatedAt: string

  current?: number
  pageSize?: number
}

type ConsultTableParams = Partial<Omit<ConsultTableModel, 'id'>>

export async function getListApi(params?: ConsultTableParams) {
  return usePost<ConsultTableModel[]>('v1/list/consult-list', params)
}

export async function deleteApi(id: string | number) {
  return useDelete(`/list/${id}`)
}

export type { ConsultTableParams, STATUS, ConsultTableModel }
