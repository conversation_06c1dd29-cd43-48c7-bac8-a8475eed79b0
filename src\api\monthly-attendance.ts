import type { BreakTimeItem, EmployeeAttendanceSummaryResponse } from './attendance'
import type { QueryParams } from './common-params'

export interface AttendanceSummary {
  employeeId: string
  employeeName: string
  code: string
  workTime: number
  overtime: number
  workDays: number
  offdays: number
  usedLeaves: number
  remainLeaves: number
  comment: string
  isRequested: boolean
  isApproved: boolean
  approverName: string
  approvedTime: string
}

export interface MonthlyRequestParams {
  reportFrom: string
  reportTo: string
  description: string
}

export interface AttendanceQueryParams {
  keyword?: string
  workingStatus?: string
  dateFrom: string
  dateTo: string
  pageNum?: number // default 1
  pageSize?: number // default 10
}

export interface MonthlyAttendanceItem {
  originalEmployeeShiftId: string
  employeeAttendanceResultId: string
  projectCode: string
  projectName: string
  checkInTime: string
  checkOutTime: string
  breakTimes: BreakTimeItem[]
  workingDate: string
  totalWorkTime: number
  totalBreakTime: number
  totalOverTime: number
  totalMovingTime: number
  description: string
  isApproved: boolean
  approverName: string
  approvedDateTime: string
}

export interface InlineMonthlyAttendanceItem {
  originalEmployeeShiftIds?: string[]
  employeeAttendanceResultIds?: string[]
  projectCodes?: string[]
  projectNames?: string[]
  checkInTimes?: string[]
  checkOutTimes?: string[]
  breakTimes?: string[]
  workingDate?: string
  totalWorkTime?: number
  totalBreakTime?: number
  totalOverTime?: number
  totalMovingTime?: number
  descriptions?: string[]
  isApproved?: boolean
  approverNames?: string[]
  approvedDateTimes?: string[]
}

export interface AttendanceResponse {
  items: MonthlyAttendanceItem[]
}

export interface UpdateMonthlyAttendanceParams {
  employeeId: string
  dateFrom: string
  dateTo: string
}

export interface AttendanceRecordParams {
  checkInTime?: string
  checkOutTime?: string
  breakTimes?: BreakTimeItem[]
  totalWorkTime?: number
  totalBreakTime?: number
  totalOverTime?: number
  totalMovingTime?: number
  description?: string
}

export interface MonthlyAttendanceItemData {
  employeeId: string
  projectId: string
  workingLocation: string
  workingDate: string
  checkInTime: string
  checkOutTime: string
  breakTimes: BreakTimeItem[]
}

export async function getMonthlyAttendanceResultApi(employeeId: string, params: AttendanceQueryParams) {
  return useGet<AttendanceResponse>(`v1/monthlyattendance/${employeeId}/result`, params)
}

export async function getMonthlyAttendanceSummaryApi(employeeId: string, params: QueryParams) {
  return useGet<EmployeeAttendanceSummaryResponse>(`v1/monthlyattendance/${employeeId}/summary`, params)
}
export async function syncMonthlyAttendanceApi(params: UpdateMonthlyAttendanceParams) {
  return usePost<AttendanceResponse>('v1/monthlyattendance/result', params)
}

export async function createMonthlyAttendanceApi(data: MonthlyAttendanceItemData) {
  return usePost<AttendanceResponse>('v1/monthlyattendance/result/new', data)
}

export async function requestApprovalMonthly(params: MonthlyRequestParams) {
  return usePut<AttendanceSummary>('v1/monthlyattendance/approval', params)
}

export async function updateMonthlyAttendanceResultApi(resultId: string, params: AttendanceRecordParams) {
  return usePut<AttendanceResponse>(`v1/monthlyattendance/result/${resultId}`, params)
}
