<!-- eslint-disable antfu/top-level-function -->
<!-- eslint-disable curly -->
<script setup lang="ts">
import { h } from 'vue'
import { LeftOutlined, LockOutlined } from '@ant-design/icons-vue'
import { delayTimer } from '@v-c/utils'

import type { Rule } from 'ant-design-vue/es/form'
import { recoverPasswordApi, signInApi, signOnApi } from '~@/api/common/login'
import type { ForgotPasswordParams, SignInParams } from '~@/api/common/login'

import { ResponseStatusEnum } from '~@/enums/response-status-enum'
import { getInvitationsApi } from '~@/api/account/account'
import type { InvitationItem } from '~@/types/account'
import type { SimpleOrgItem } from '~@/api/company/org'
import { getOrgsByCurrentAccountApi } from '~@/api/company/org'
import { useFcmToken, useOrg } from '~@/composables/org'
import {
  registerServiceWorker,
  requestNotificationPermission,
} from '~@/config/firebase'
import logger from '~@/utils/logger'

const router = useRouter()
const token = useAuthorization()
const org = useOrg()
const passwordInputRef = ref<HTMLInputElement>()
const usernameInputRef = ref<HTMLInputElement>()

const signInFormState = reactive<SignInParams & { type: string }>({
  loginId: '',
  password: '',
  type: 'account',
})
const forgotAccountformState = reactive<{ accountLoginInfo: string }>({
  accountLoginInfo: '',
})
const { t } = useI18n()
const formRef = shallowRef()
const submitLoading = shallowRef(false)
const errorAlert = shallowRef(false)
const isUsernameExists = ref(false)
const orgSelected = ref()
const userOrgList = ref<SimpleOrgItem[]>([])

const isForgotAccount = ref(false)
const prevStep = ref('')
const submitLoadingFA = shallowRef(false)
const messageNotification = useMessage()
// async function submit() {
//   submitLoading.value = true
//   try {
//     await formRef.value?.validate()
//     const params: LoginParams = {
//       loginId: signInFormState.loginId,
//       password: signInFormState.password,
//       orgId: orgSelected.value,
//     }
//     const { data, status } = await login(params)
//     if (status === ResponseStatusEnum.SUCCESS) {
//       if (!data) {
//         throw new Error('No data returned from login API')
//       }
//       const employeeInfo: EmployeeInfo = data
//       token.value = `Bearer ${data?.accessToken}`
//       // notification.success({
//       //   message: "",
//       //   duration: 3,
//       // });
//       const userStore = useUserStore()
//       await userStore.getEmployeeInfo()
//       const currentRoute = await userStore.generateDynamicRoutes()
//       router.addRoute(currentRoute)
//       logger.log('currentRouter: ', currentRoute)
//       // const { isChangedPass } = storeToRefs(userStore)
//       const isChangedPass = employeeInfo.firstChangePass
//       if (!isChangedPass) {
//         const redirect = getQueryParam('redirect', '/change-password')
//         router.push({
//           path: redirect,
//         })
//       }
//       else {
//         const timeout = setTimeout(() => window.location.reload(), 5000)
//         try {
//           router.push({
//             name: 'rootPath',
//             replace: true,
//           })
//         }
//         catch (e) {
//           logger.error(e)
//         }
//         finally {
//           clearTimeout(timeout)
//           submitLoading.value = false
//           // submit()
//         }
//       }
//     }
//     else {
//       errorAlert.value = true
//       submitLoading.value = false
//     }
//   }
//   catch (e) {
//     logger.error(e)
//     if (e instanceof AxiosError) {
//       errorAlert.value = true
//     }
//     submitLoading.value = false
//   }
// }

function backToUsername() {
  isUsernameExists.value = false
  signInFormState.loginId = ''
}

// Forgot account
function forgotAccount(type: string) {
  prevStep.value = type
  isForgotAccount.value = true
}

function backFromFA() {
  if (prevStep.value === 'account') {
    isForgotAccount.value = false
  }
  else {
    isUsernameExists.value = true
  }
}

// Hàm validator phải là hàm async và return một Promise
const validateEmailAndUsername = async (_rule: Rule, value: string) => {
  if (value === '')
    return Promise.reject(new Error('電子メール/ユーザー名を入力してください'))

  if (errorAlert.value) {
    errorAlert.value = false
    return Promise.reject(new Error('電子メール/ユーザー名が存在しない'))
  }
}

const rules: Record<string, Rule[]> = {
  accountLoginInfo: [
    { required: true, validator: validateEmailAndUsername, trigger: 'blur' },
  ],
}

async function submitFA() {
  submitLoadingFA.value = true
  try {
    await formRef.value?.validate()
    const params: ForgotPasswordParams = {
      accountLoginInfo: forgotAccountformState.accountLoginInfo,
    }
    const { status, message: mess } = await recoverPasswordApi(params)
    if (status === 200) {
      // token.value = Data?.AccessToken;
      messageNotification.success(mess || 'メールを送信しました')
      isForgotAccount.value = false
      submitLoadingFA.value = false
      forgotAccountformState.accountLoginInfo = ''
    }
    else {
      errorAlert.value = true
      submitLoadingFA.value = false
      messageNotification.error(mess || '電子メール/ユーザー名が存在しない')
      formRef.value?.validate()
      submitLoadingFA.value = false
    }
  }
  catch (e) {
  }
  finally {
    submitLoadingFA.value = false
  }
}

const handleUsernamePress = () => {
  passwordInputRef.value?.focus()
}

async function getInvitations() {
  const { data, status } = await getInvitationsApi()
  if (status === ResponseStatusEnum.SUCCESS) {
    return data?.items ?? []
  }
}

const signOn = async () => {
  try {
    const params = {
      orgId: orgSelected.value,
    }
    const { data, status, message: mess } = await signOnApi(params)
    if (status === ResponseStatusEnum.SUCCESS) {
      token.value = `Bearer ${data?.accessToken}`
      org.value = orgSelected.value
      // Thay vì lưu vào token.value, bạn có thể lưu vào cookie httpOnly như sau:
      // document.cookie = `access_token=Bearer ${data?.accessToken}; path=/; HttpOnly; Secure; SameSite=Strict`

      router.push({
        path: '/',
        replace: true,
      })
    }
    else {
      messageNotification.error(mess || 'Có lỗi xảy ra khi đăng nhập')
    }
  }
  catch (e) {
    logger.error(e)
  }
}

async function getOrgsByCurrentAccount() {
  try {
    const { data, status, message: mess } = await getOrgsByCurrentAccountApi()
    if (status === ResponseStatusEnum.SUCCESS) {
      userOrgList.value = data?.items ?? []
      if (userOrgList.value.length) {
        orgSelected.value = userOrgList.value[0].orgId
      }
    }
    else {
      messageNotification.error(
        mess || 'Có lỗi xảy ra khi tải danh sách tổ chức',
      )
    }
  }
  catch (e) {
    logger.error(e)
  }
}

const signIn = async () => {
  submitLoading.value = true
  try {
    await formRef.value?.validate()
    const params: SignInParams = {
      loginId: signInFormState.loginId,
      password: signInFormState.password,
    }
    const { data, status, message } = await signInApi(params)
    if (status === ResponseStatusEnum.SUCCESS) {
      token.value = `Bearer ${data?.accessToken}`
      // document.cookie = `access_token=Bearer ${data?.accessToken}; path=/; HttpOnly; Secure; SameSite=Strict`
      const invitations: InvitationItem[] = (await getInvitations()) ?? []
      if (invitations.length) {
        router.push({
          path: '/invitation/accept',
        })
      }
      else {
        await getOrgsByCurrentAccount()
        if (userOrgList.value.length === 1) {
          await signOn()
        }
        else {
          isUsernameExists.value = true
        }
        // const redirect = getQueryParam('redirect', '/')
        // router.push({
        //   path: redirect,
        //   replace: true,
        // })
      }
    }
    else {
      messageNotification.error(message)
    }
  }
  catch (e) {
    messageNotification.error('An error occurred while logging in')
  }
  finally {
    submitLoading.value = false
  }
}

// const signUp = () => {
//   router.push({
//     path: '/account/sign-up',
//   })
// }

onMounted(async () => {
  await delayTimer(300)
  usernameInputRef.value?.focus()
  // pageBubble.init(unref(bubbleCanvas)!);

  await registerServiceWorker()
  const token = await requestNotificationPermission()
  if (token)
    useFcmToken().value = token
})

onBeforeUnmount(() => {
  // pageBubble.removeListeners();
})
</script>

<template>
  <div class="login-container">
    <!-- <div h-screen w-screen absolute z-10>
      <canvas ref="bubbleCanvas" />
    </div> -->
    <div class="login-bg absolute w-full h-full" />
    <div
      class="login-content flex items-center md:justify-end justify-center max-h-[928px] py-[81px] md:pr-[81px]"
    >
      <div class="ant-pro-form-login-main rounded-[20px] h-full relative">
        <!-- 登录头部 -->
        <div class="absolute z-2 right-[24px] top-[24px]">
          <div class="flex-end">
            <div class="ant-pro-form-login-header">
              <span class="ant-pro-form-login-title" />
            </div>
          </div>
          <div class="login-lang flex-center relative z-11">
            <!-- <span
              class="flex-center cursor-pointer text-16px mr-3" @click="
                appStore.toggleTheme(
                  layoutSetting.theme === 'dark' ? 'light' : 'dark',
                )
              "
            >
              <template v-if="layoutSetting.theme === 'light'">
                <carbon-moon />
              </template>
              <template v-else>
                <carbon-sun />
              </template>
            </span> -->
            <SelectLang :is-dashboard="false" />
          </div>
        </div>
        <!-- 登录主体 -->
        <div class="box-border flex h-full relative z-1">
          <!-- 登录框左侧 -->
          <!-- 登录框右侧 -->
          <div
            class="ant-pro-form-login-main-right p-[24px] w-[592px] flex-center flex-col relative z-11"
          >
            <div class="max-w-[384px]">
              <div class="flex items-center">
                <div class="w-[74px]">
                  <img class="h-auto w-full" src="/logo.png">
                </div>
                <a-divider type="vertical" class="h-[48px] bg-[#E4E4E2]" />
                <div class="text-[26px]">
                  KanTok Systems
                </div>
              </div>
              <div
                v-if="!isForgotAccount && !isUsernameExists"
                class="py-6 text-2xl"
              >
                {{ t('pages.login.title') }}
              </div>
              <template v-if="isForgotAccount">
                <div class="py-6 text-2xl">
                  {{ t('ResetYourAccount') }}
                </div>
                <a-form
                  ref="formRef"
                  layout="vertical"
                  class="w-full"
                  :model="forgotAccountformState"
                  :rules="rules"
                >
                  <a-form-item has-feedback name="accountLoginInfo">
                    <a-input
                      v-model:value="forgotAccountformState.accountLoginInfo"
                      allow-clear
                      :placeholder="t('EmailOrUsername')"
                      size="large"
                    />
                  </a-form-item>
                  <a-form-item>
                    <a-button
                      type="primary"
                      block
                      size="large"
                      :loading="submitLoadingFA"
                      @click="submitFA"
                    >
                      {{ t('button.sendLink') }}
                    </a-button>
                    <div class="mt-2">
                      <a-button
                        type="link"
                        :icon="h(LeftOutlined)"
                        class="p-0"
                        @click="backFromFA"
                      >
                        {{ t('pages.login.back') }}
                      </a-button>
                    </div>
                  </a-form-item>
                </a-form>
              </template>
              <template v-else>
                <div v-if="isUsernameExists" class="py-6 text-2xl">
                  {{ t('pages.login.tips') }}
                </div>
                <a-form ref="formRef" :model="signInFormState">
                  <!-- 判断是否存在error -->
                  <a-alert
                    v-if="
                      errorAlert
                        && signInFormState.type === 'account'
                        && isUsernameExists === false
                    "
                    mb-24px
                    :message="
                      t('pages.login.accountLogin.username.errorMessage')
                    "
                    type="error"
                    show-icon
                  />
                  <a-alert
                    v-if="
                      errorAlert
                        && signInFormState.type === 'account'
                        && isUsernameExists
                    "
                    mb-24px
                    :message="
                      t('pages.login.accountLogin.orgAndPassword.errorMessage')
                    "
                    type="error"
                    show-icon
                  />
                  <a-alert
                    v-if="errorAlert && signInFormState.type === 'mobile'"
                    mb-24px
                    :message="t('pages.login.phoneLogin.errorMessage')"
                    type="error"
                    show-icon
                  />

                  <template v-if="signInFormState.type === 'account'">
                    <template v-if="isUsernameExists">
                      <a-form-item
                        name="orgKey"
                        :rules="[
                          {
                            required: false,
                            message: t('pages.login.password.required'),
                          },
                        ]"
                      >
                        <a-select
                          v-model:value="orgSelected"
                          size="large"
                          :options="userOrgList"
                          :field-names="{
                            label: 'orgName',
                            value: 'orgId',
                          }"
                          placeholder="Please select your org"
                        />
                      </a-form-item>

                      <!-- <a-form-item
                        name="password" :rules="[
                          {
                            required: true,
                            message: t('pages.login.password.required'),
                          },
                        ]"
                      >
                        <a-input-password
                          v-model:value="signInFormState.password" allow-clear
                          :placeholder="t('pages.login.password.placeholder')" size="large" @press-enter="submit"
                        >
                          <template #prefix>
                            <LockOutlined />
                          </template>
                        </a-input-password>
                      </a-form-item> -->

                      <a-form-item>
                        <a-button
                          type="primary"
                          block
                          size="large"
                          :loading="submitLoading"
                          @click="signOn"
                        >
                          {{ t('button.access') }}
                        </a-button>
                        <div class="flex justify-between mt-2">
                          <a-button
                            type="link"
                            :icon="h(LeftOutlined)"
                            class="p-0"
                            @click="backToUsername"
                          >
                            {{ t('pages.login.back') }}
                          </a-button>
                          <a-button
                            type="link"
                            class="p-0"
                            @click="forgotAccount('account')"
                          >
                            {{ t('pages.login.forgotAccount') }}
                          </a-button>
                        </div>
                      </a-form-item>
                    </template>

                    <template v-if="!isUsernameExists">
                      <a-form-item
                        name="loginId"
                        :rules="[
                          {
                            required: true,
                            message: t('pages.login.username.required'),
                          },
                        ]"
                      >
                        <a-input
                          ref="usernameInputRef"
                          v-model:value="signInFormState.loginId"
                          allow-clear
                          autocomplete="off"
                          :placeholder="t('pages.login.username.placeholder')"
                          size="large"
                          @press-enter="handleUsernamePress"
                        />
                      </a-form-item>
                      <a-form-item>
                        <a-input-password
                          ref="passwordInputRef"
                          v-model:value="signInFormState.password"
                          allow-clear
                          :placeholder="t('pages.login.password.placeholder')"
                          size="large"
                          @press-enter="signIn"
                        >
                          <template #prefix>
                            <LockOutlined />
                          </template>
                        </a-input-password>
                      </a-form-item>
                      <a-button
                        type="primary"
                        block
                        :loading="submitLoading"
                        size="large"
                        @click="signIn"
                      >
                        {{ t('button.login') }}
                      </a-button>
                      <div class="flex justify-between mt-2">
                        <div class="mt-2">
                          <a-button
                            type="link"
                            block
                            @click="forgotAccount('account')"
                          >
                            {{ t('pages.login.forgotAccount') }}
                          </a-button>
                        </div>
                        <div class="mt-2">
                          <!-- <a-button type="link" block @click="signUp()">
                            {{ t('button.signUp') }}
                          </a-button> -->
                        </div>
                      </div>
                    </template>
                  </template>
                </a-form>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.demo-dropdown-wrap :deep(.ant-dropdown-button) {
  margin-right: 8px;
  margin-bottom: 8px;
}

.login-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto;
  background: var(--bg-color-login);
}
.login-bg {
  background-image: url(@/assets/images/login-bg.png);
  background-size: cover;
  background-position: left center;
  background-repeat: no-repeat;
}

.login-lang {
  height: 40px;
  line-height: 44px;
}

.login-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.ant-pro-form-login-container {
  display: flex;
  flex: 1 1;
  flex-direction: column;
  height: 100%;
  padding: 32px 0;
  overflow: auto;
  background: inherit;
}

.ant-pro-form-login-header a {
  text-decoration: none;
}

.ant-pro-form-login-title {
  color: var(--text-color);
  font-weight: 600;
  font-size: 33px;
  line-height: 1;
}

.ant-pro-form-login-logo {
  // width: 44px;
  height: 40px;
  margin-right: 16px;
  vertical-align: top;
}

.ant-pro-form-login-desc {
  color: var(--text-color-1);
  font-size: 14px;
  margin-left: 16px;
}

.ant-pro-form-login-main-right {
  .ant-tabs-nav-list {
    margin: 0 auto;
    font-size: 16px;
  }

  .ant-pro-form-login-other {
    line-height: 22px;
    text-align: center;
  }
}

.ant-pro-form-login-main {
  box-shadow: var(--c-shadow);
}

.icon {
  margin-left: 8px;
  color: var(--text-color-2);
  font-size: 24px;
  vertical-align: middle;
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: var(--pro-ant-color-primary);
  }
}

.ant-pro-form-login-main {
  background-color: var(--bg-color-new-container);
}

.login-media(@width: 100%) {
  .ant-pro-form-login-main {
    width: @width;
  }

  .ant-pro-form-login-main-left {
    display: none;
  }

  .ant-pro-form-login-main-right {
    width: 100%;
  }

  .ant-pro-form-login-desc {
    display: none;
  }
}

@media (min-width: 992px) {
  .ant-pro-form-login-main-left {
    width: 700px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .ant-pro-login-divider {
    display: none;
  }

  .login-media(400px);
}

@media screen and (max-width: 767px) {
  .login-media(350px);

  .ant-pro-login-divider {
    display: none;
  }
}
</style>
