<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
import { ref } from 'vue'
import { EditFilled } from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import type { UnwrapRef } from 'vue'
import logger from '~@/utils/logger'

const props = defineProps<{
  label: string
  value: string
  employeeShiftId: string
  type: string
}>()

const emit = defineEmits<{
  (event: 'onSave', employeeShiftId: string, type: string, updateTime: string): void
}>()

const isEdit = ref(false)
const localValue = ref(dayjs(props.value, 'HH:mm:ss'))
const breakIn: UnwrapRef<Record<number, string>> = reactive({})
const breakOut: UnwrapRef<Record<number, string>> = reactive({})

const checkInOutTimePickerRef = ref()

const onSave = () => {
  logger.log('onSave')
  checkInOutTimePickerRef.value?.close()
  emit('onSave', props.employeeShiftId, props.type, localValue.value.format('HH:mm:ss'))
  isEdit.value = false
}

// const onCancel = () => {
//   isEdit.value = false
// }

const onEdit = () => {
  isEdit.value = true
}
</script>

<template>
  <a-col :span="12">
    <a-typography-text strong>
      {{ label }}
    </a-typography-text>
    <template v-if="type === 'breakInOut'">
      <template v-for="index in 3" :key="index">
        <template v-if="isEdit">
          <a-time-picker
            v-model:value="breakIn[index]"
            show-time
            format="HH:mm"
            @change="onSave"
            @ok="onSave"
          />
          <a-time-picker
            v-model:value="breakOut[index]"
            show-time
            format="HH:mm"
            @change="onSave"
            @ok="onSave"
          />
          <!-- <a-button type="text" @click="onSave">
            <CheckOutlined />
          </a-button>
          <a-button type="text" @click="onCancel">
            <CloseOutlined />
          </a-button> -->
        </template>
        <template v-else>
          <template v-if="value">
            <span class="display-value">
              {{ localValue.format('HH:mm') }}
            </span>
            <span
              class="edit-icon"
              @click="onEdit"
            ><EditFilled />
            </span>
          </template>
          <template v-else>
            <span class="display-value">
              {{ "-- : --" }}
            </span>
          </template>
        </template>
      </template>
    </template>
    <template v-else>
      <template v-if="isEdit">
        <a-time-picker
          ref="checkInOutTimePickerRef"
          v-model:value="localValue"
          show-time
          format="HH:mm"
          @change="onSave"
          @ok="onSave"
        />
        <!-- <a-button type="text" @click="onSave">
          <CheckOutlined />
        </a-button>
        <a-button type="text" @click="onCancel">
          <CloseOutlined />
        </a-button> -->
      </template>
      <template v-else>
        <template v-if="value">
          <span class="display-value">
            {{ localValue.format('HH:mm') }}
          </span>
          <span
            class="edit-icon"
            @click="onEdit"
          ><EditFilled />
          </span>
        </template>
        <template v-else>
          <span class="display-value">
            {{ "-- : --" }}
          </span>
        </template>
      </template>
    </template>
  </a-col>
</template>

<style scoped>
.display-value {
  color: blue;
}

.edit-icon {
  cursor: pointer;
  margin-left: 4px;
  color: #b7b9b8;
  /* font-size: 20px; */
}
</style>
