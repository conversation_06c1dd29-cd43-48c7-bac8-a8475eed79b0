import type { OutSourceItem, OutsourceQueryParams } from '~@/api/outsource'
import { getOutsourceApi } from '~@/api/outsource'

export function useOutsourcess() {
  const outsources = ref<OutSourceItem[]>()
  const loading = ref(true)
  const error = ref()
  const messageNotify = useMessage()

  const fetchOutsources = async (queryParams?: OutsourceQueryParams) => {
    loading.value = true
    try {
      const { data, status, message } = await getOutsourceApi(queryParams)
      if (status === 200) {
        outsources.value = data?.items ?? []
      }
      else {
        messageNotify.error(message)
        error.value = message
      }
    }
    catch (error) {
    }
    finally {
      loading.value = false
    }
  }
  const outsourcesOptions = computed(() => {
    return outsources.value?.map((outsource: OutSourceItem) => ({
      value: outsource.outSourceId,
      label: `${outsource.outSourceCode} - ${outsource.outSourceName}`,
    })) ?? []
  })

  return {
    outsources,
    outsourcesOptions,
    fetchOutsources,
    loading,
    error,
  }
}
