<script lang="ts" setup>
import type { ConstructionItem } from '~@/api/construction'

const props = defineProps({
  construction: {
    type: Object as () => ConstructionItem,
    required: true,
  },
})

const { t } = useI18n()

// interface Construction {
//   constructionId: string
//   constructionName: string
//   contractualCosts: {
//     initialCostItems: { sequenceNumber: number; amount: number }[]
//     totalInitialCost: number
//     modifiedCostItems: { sequenceNumber: number; amount: number }[]
//     totalModifiedCost: number
//   }
//   estimatedCosts: {
//     estimateCostItems: { sequenceNumber: number; amount: number }[]
//     totalEstimateCost: number
//   }
//   accumulatedCosts: {
//     riskAmount: number
//     totalAccumulatedCost: number
//   }
// }

const totalContractualCost = ref(
  props.construction.contractualCosts.totalInitialCost
      + props.construction.contractualCosts.totalModifiedCost,
)

const profitRatio = computed(() => {
  const totalInitialCost = props.construction.contractualCosts.totalInitialCost
  const estimate = props.construction.estimatedCosts.totalEstimateCost
  if (totalInitialCost === 0)
    return 0
  return ((totalInitialCost - estimate) / totalInitialCost * 100).toFixed(2)
})
</script>

<template>
  <div class="grid grid-cols-2 gap-4">
    <!-- 契約金額 (First Column) -->
    <div>
      <div class="bg-green-100 p-2 text-center text-green-600 font-bold rounded-t-lg">
        {{ t('contractualCost') }}
      </div>

      <div class="border rounded-b-lg">
        <div class="font-bold p-2 text-center">
          <span v-if="construction.isPrimary">{{ t('mainConstructionCost') }}</span>
          <span v-else>{{ t('subConstructionCost') }}</span>
        </div>

        <div>
          <div class="grid grid-cols-2 border-y">
            <div class="p-2 font-semibold border-r">
              {{ t('contractualCost') }}
            </div>
            <div class="p-2 font-semibold">
              {{ t('modifiedCost') }}
            </div>
          </div>

          <div class="grid grid-cols-2">
            <div class="border-r">
              <div class="grid grid-cols-2 border-b">
                <div class="p-2 border-r">
                  {{ t('initialCost') }}
                </div>
                <div class="p-2 text-right">
                  ¥{{ construction.contractualCosts.totalInitialCost.toLocaleString() }}
                </div>
              </div>

              <template v-for="(item, index) in construction.contractualCosts.initialCostItems" :key="index">
                <div v-if="index < 5" class="grid grid-cols-2 border-b">
                  <div class="p-2 border-r border-green-300">
                    第{{ item.sequenceNumber }}回
                  </div>
                  <div class="p-2 text-right">
                    ¥{{ item.amount.toLocaleString() }}
                  </div>
                </div>
              </template>

              <div class="grid grid-cols-2 border-b">
                <div class="p-2 border-r font-semibold">
                  {{ t('subtotal') }}
                </div>
                <div class="p-2 text-right font-semibold">
                  ¥{{ construction.contractualCosts.totalInitialCost.toLocaleString() }}
                </div>
              </div>
            </div>

            <div>
              <div class="grid grid-cols-2 border-b">
                <div class="p-2 border-r">
                  {{ t('initialCost') }}
                </div>
                <div class="p-2 text-right">
                  ¥0
                </div>
              </div>

              <template v-for="(item, index) in construction.contractualCosts.modifiedCostItems" :key="index">
                <div v-if="index < 5" class="grid grid-cols-2 border-b">
                  <div class="p-2 border-r">
                    第{{ item.sequenceNumber }}回
                  </div>
                  <div class="p-2 text-right">
                    ¥{{ item.amount.toLocaleString() }}
                  </div>
                </div>
              </template>

              <div class="grid grid-cols-2 border-b">
                <div class="p-2 border-r font-semibold">
                  {{ t('subtotal') }}
                </div>
                <div class="p-2 text-right font-semibold">
                  ¥{{ construction.contractualCosts.totalModifiedCost.toLocaleString() }}
                </div>
              </div>
            </div>
          </div>

          <div class="p-2 font-semibold text-center">
            {{ t('total') }}: ¥{{ totalContractualCost.toLocaleString() }}
          </div>
        </div>
      </div>
    </div>

    <!-- 予算金額 (Second Column) -->
    <div>
      <div class="bg-orange-100 text-orange-600 p-2 text-center font-bold rounded-t-lg">
        {{ t('estimatedCost') }}
      </div>

      <div class="border mb-4 rounded-b-lg">
        <div class="p-2 text-center font-bold border-b">
          <span v-if="construction.isPrimary">{{ t('mainConstructionCost') }}</span>
          <span v-else>{{ t('subConstructionCost') }}</span>
        </div>

        <div class="">
          <div class="grid grid-cols-2 border-b ">
            <div class="p-2 border-r ">
              {{ t('initialCost') }}
            </div>
            <div class="p-2 text-right">
              ¥{{ construction.estimatedCosts.totalEstimateCost.toLocaleString() }}
            </div>
          </div>

          <div v-for="(item, index) in construction.estimatedCosts.estimateCostItems" :key="index">
            <div class="grid grid-cols-2 border-b ">
              <div class="p-2 border-r ">
                第{{ item.sequenceNumber }}回
              </div>
              <div class="p-2 text-right">
                {{ item.amount.toLocaleString() }}
              </div>
            </div>
          </div>

          <div class="grid grid-cols-2">
            <div class="p-2 border-r  font-semibold">
              {{ t('subtotal') }}
            </div>
            <div class="p-2 text-right font-semibold">
              ¥{{ construction.estimatedCosts.totalEstimateCost.toLocaleString() }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 実績原価 (Third Column) -->
    <!-- <div>
        <div class="bg-green-100 p-2 text-center font-bold mb-2">
          実績原価
        </div>

        <div class="border border-blue-300 mb-4">
          <div class="bg-blue-100 text-blue-800 p-2 text-center">
            カテゴリー別原価
          </div>

          <div>
            <div class="grid grid-cols-2 border-b border-blue-200">
              <div class="p-2 border-r border-blue-200 font-semibold">
                カテゴリー
              </div>
              <div class="p-2 text-right font-semibold">
                金額
              </div>
            </div>

            <template v-if="construction.accumulatedCosts">
              <div class="grid grid-cols-2 border-b border-blue-200">
                <div class="p-2 border-r border-blue-200">
                  主要工事
                </div>
                <div class="p-2 text-right">
                  ¥{{ construction.accumulatedCosts.totalAccumulatedCost.toLocaleString() }}
                </div>
              </div>

              <div class="grid grid-cols-2 border-b border-blue-200">
                <div class="p-2 border-r border-blue-200">
                  リスク金額
                </div>
                <div class="p-2 text-right">
                  ¥{{ construction.accumulatedCosts.riskAmount.toLocaleString() }}
                </div>
              </div>
            </template>

            <div class="grid grid-cols-2 border-b border-blue-200">
              <div class="p-2 border-r border-blue-200 font-semibold">
                合計
              </div>
              <div class="p-2 text-right font-semibold">
                ¥{{ construction.accumulatedCosts.totalAccumulatedCost.toLocaleString() }}
              </div>
            </div>
          </div>
        </div>
      </div> -->
  </div>
  <div class="flex justify-end">
    <div class="p-2 font-semibold">
      {{ t('profitRatio') }}:
    </div>
    <div class="p-2 text-right font-semibold text-blue-600">
      {{ profitRatio }}%
    </div>
  </div>
</template>
