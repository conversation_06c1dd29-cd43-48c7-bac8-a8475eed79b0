import * as XLSX from 'xlsx'
import type { CostAmountItem, EstimateBudgetItem } from '~@/api/construction-cost'

interface ExportOptions {
  mainCostAmount?: CostAmountItem
  subCostAmount?: CostAmountItem
  overallCostAmount?: CostAmountItem
  mainEstimateBudget?: EstimateBudgetItem
  subEstimateBudget?: EstimateBudgetItem
  overallEstimateBudget?: EstimateBudgetItem
}

function formatNumber(value: number): string {
  return new Intl.NumberFormat('ja-JP').format(value)
}

function createCostAmountRows(costAmount?: CostAmountItem): string[][] {
  if (!costAmount)
    return []

  return [
    ['前回まで', '進捗率', '今回', '進捗率', '累計', '進捗率', '残金'],
    [`¥${formatNumber(costAmount.previousAmount)}`, `${costAmount.previousProgressPercentage}%`, `¥${formatNumber(costAmount.currentAmount)}`, `${costAmount.currentProgressPercentage}%`, `¥${formatNumber(costAmount.accumulatedAmount)}`, `${costAmount.accumulatedProgressPercentage}%`, `¥${formatNumber(costAmount.remainingBalance)}`],
  ]
}

function createEstimateBudgetRows(estimateBudget?: EstimateBudgetItem): string[][] {
  if (!estimateBudget)
    return []
  return [
    ['工事進捗に応じた予算', '予算対比', '実績利益率', '予測利益率'],
    [`¥${formatNumber(estimateBudget.budgetAccordingToProgress)}`, `¥${formatNumber(estimateBudget.againstBudget)}`, `${estimateBudget.actualProfitMargin}%`, `${estimateBudget.estimatedProfitMargin}%`],
  ]
}

export function createConstructionCostWorkSheet({
  mainCostAmount,
  subCostAmount,
  overallCostAmount,
  mainEstimateBudget,
  subEstimateBudget,
  overallEstimateBudget,
}: ExportOptions): XLSX.WorkSheet {
  // Create data array for the worksheet
  const wsData: string[][] = [
    [
      '出来高書 (本工事費) 金額',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '対予算（本工事費）集計',
      '',
      '',
      '',
    ], // Title row
  ]

  // Add main construction data
  createCostAmountRows(mainCostAmount).forEach((row, index) => {
    wsData.push([...row, '', ...createEstimateBudgetRows(mainEstimateBudget)[index]])
  })
  wsData.push(['', '', '', '', '', '', '', '', '', '', '', '']) // Spacing

  // Add sub construction data
  wsData.push([
    '出来高書 (別途工事費) 金額',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '対予算 (別途工事費) 集計',
    '',
    '',
    '',
  ]) // Title row
  createCostAmountRows(subCostAmount).forEach((row, index) => {
    wsData.push([...row, '', ...createEstimateBudgetRows(subEstimateBudget)[index]])
  })
  wsData.push(['', '', '', '', '']) // Spacing

  // Add overall data
  wsData.push([
    '出来高書 (総合計) 金額',
    '',
    '',
    '',
    '',
    '',
    '',
    '',
    '対予算 (総合計) 集計',
    '',
    '',
    '',
  ])
  createCostAmountRows(overallCostAmount).forEach((row, index) => {
    wsData.push([...row, '', ...createEstimateBudgetRows(overallEstimateBudget)[index]])
  })

  // Create worksheet
  const ws = XLSX.utils.aoa_to_sheet(wsData)

  // Set column widths
  ws['!cols'] = [
    { wch: 10 }, // A
    { wch: 10 }, // B
    { wch: 10 }, // C
    { wch: 10 }, // D
    { wch: 10 }, // E
    { wch: 10 }, // F
    { wch: 10 }, // G
    { wch: 5 }, // H (spacing)
    { wch: 10 }, // I
    { wch: 10 }, // J
    { wch: 10 }, // K
    { wch: 10 }, // L
  ]
  ws['!rows'] = [
    { hpt: 20 }, // Title row
    { hpt: 20 }, // Data rows
    { hpt: 20 }, // Spacing
    { hpt: 20 }, // Data rows
    { hpt: 20 }, // Spacing
    { hpt: 20 }, // Data rows
    { hpt: 20 }, // Spacing
    { hpt: 20 }, // Data rows
    { hpt: 20 }, // Data rows
    { hpt: 20 }, // Data rows
    { hpt: 20 }, // Data rows
    { hpt: 20 }, // Data rows
  ]

  // Add styles
  ws['!merges'] = [
    // Merge title cells
    { s: { r: 0, c: 0 }, e: { r: 0, c: 6 } }, // 出来高書 (本工事費) 金額
    { s: { r: 4, c: 0 }, e: { r: 4, c: 6 } }, // 出来高書 (別途工事費) 金額
    { s: { r: 8, c: 0 }, e: { r: 8, c: 6 } }, // 出来高書 (総合計) 金額
    { s: { r: 0, c: 8 }, e: { r: 0, c: 11 } }, // 対予算（本工事費）集計
    { s: { r: 4, c: 8 }, e: { r: 4, c: 11 } }, // 対予算 (別途工事費) 集計
    { s: { r: 8, c: 8 }, e: { r: 8, c: 11 } }, // 対予算 (総合計) 集計
  ]

  return ws
}
