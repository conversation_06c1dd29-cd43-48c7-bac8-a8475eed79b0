import type { StructureQueryParams } from '~@/api/company/struct'
import { getStructureListApi } from '~@/api/company/struct'

export function useStructure() {
  const messageNotification = useMessage()
  const fetchStructureList = async (params?: StructureQueryParams) => {
    const { data, status, message } = await getStructureListApi(params)
    if (status === 200)
      return data?.items ?? []

    else
      messageNotification.error(message ?? 'Fetch structure list failed!')
  }

  return {
    fetchStructureList,
  }
}
