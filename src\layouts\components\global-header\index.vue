<script setup lang="ts">
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue'
import { useLayoutState } from '../../basic-layout/context'
import GlobalHeaderLogo from './global-header-logo.vue'

const { layout, isMobile, handleMobileCollapsed, theme, menuHeader, collapsed, handleCollapsed, leftCollapsed }
  = useLayoutState()
const prefixCls = shallowRef('ant-pro-global-header')
const cls = computed(() => ({
  [prefixCls.value]: true,
  [`${prefixCls.value}-layout-${layout.value}`]: !!layout.value,
  [`${prefixCls.value}-inverted`]: theme.value === 'inverted' && layout.value === 'top',
}))
</script>

<template>
  <div :class="[cls]">
    <span
      v-if="layout === 'side' && !isMobile && !leftCollapsed" class="ml-0 text-18px"
      @click="handleCollapsed?.(!collapsed)"
    >
      <MenuUnfoldOutlined v-if="collapsed" />
      <MenuFoldOutlined v-else />
    </span>
    <template v-if="menuHeader">
      <GlobalHeaderLogo v-if="layout !== 'side' || isMobile" />
    </template>
    <span v-if="isMobile" class="ant-pro-global-header-collapsed-button" @click="handleMobileCollapsed">
      <MenuFoldOutlined />
    </span>
    <div
      class="flex-1 overflow-x-auto" :class="layout === 'top' ? `${prefixCls}-top` : ''" />
    <a-space flex-shrink-0 :size="24" class="header-actions">
      <slot name="headerActions" class="h-full" />
    </a-space>
  </div>
</template>

<style lang="less">
@import './index.less';
</style>

