import type { QueryParams } from '~@/api/common-params'
import { getPositionListApi } from '~@/api/company/position'

export function usePosition() {
  const messageNotification = useMessage()
  const fetchPositionList = async (params: QueryParams) => {
    try {
      const { data, status, message } = await getPositionListApi(params)
      if (status === 200)
        return data?.items
      else
        messageNotification.error(message ?? 'Fetch position list failed!')
    }
    catch (error) {
      throw new Error(error as string)
    }
  }

  return {
    fetchPositionList,
  }
}
