import { type ProjectScheduleParams, getProjectScheduleApi } from '~@/api/company/schedule'
import logger from '~@/utils/logger'

export async function getSchedules(queryParams: ProjectScheduleParams) {
  try {
    const { data, status } = await getProjectScheduleApi(queryParams)
    if (status === 200)
      return data?.items ?? []

    else
      return []
  }
  catch (e) {
    logger.error(e)
  }
}
