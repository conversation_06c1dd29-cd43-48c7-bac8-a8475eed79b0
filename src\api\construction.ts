// Interface cho cost items (các mục chi phí)
export enum CategoryCode {
  MATERIAL = 'MATERIAL',
  FUEL = 'FUEL',
  EQUIPMENT = 'EQUIPMENT',
  LEASE = 'LEASE',
  LABOR = 'LABOR',
  SERVICE = 'SERVICE',
  OTHER = 'OTHER',
  OVERTIME = 'OVERTIME',
  OUTSOURCE_CONTRACT = 'OUTSOURCE_CONTRACT',
  EMPLOYEE = 'EMPLOYEE',
  OUTSOURCE_DAILY = 'OUTSOURCE_DAILY',

}

export interface CostItem {
  sequenceNumber: number
  amount: number
}

// Interface cho một mục cost (có thể là main construction, sub construction hoặc overall construction)
export interface ConstructionCost {
  initialCostItems: CostItem[]
  totalInitialCost: number
  modifiedCostItems: CostItem[]
  totalModifiedCost: number
}

// Interface cho một cost category (phân loại chi phí)
export interface CategorizedCost {
  categoryId: string
  categoryName: string
  categoryCode: string
  totalAmount: number
  totalAvgAmount: number
  subCategories: string[]
}

// Interface cho một mục tổng chi phí (accumulated cost)
export interface AccumulatedCost {
  categorizedCosts: CategorizedCost[]
  riskAmount: number
  totalAccumulatedCost: number
  totalAccumulatedAvgCost: number
}

// Interface cho mỗi loại chi phí ước tính (estimate cost)
export interface EstimateCost {
  estimateCostItems: CostItem[]
  totalEstimateCost: number
}

// Interface cho các chi phí liên quan đến một dự án (contract costs, estimate costs, accumulated costs)
export interface ContractCosts {
  mainConstructionContractCost: ConstructionCost
  totalMainConstructionCost: number
  subConstructionContractCost: ConstructionCost
  totalSubConstructionCost: number
  overallConstructionContractCost: ConstructionCost
  totalOverallConstructionCost: number
}

export interface EstimateCosts {
  mainConstructionEstimateCost: EstimateCost
  subConstructionEstimateCost: EstimateCost
  overallConstructionEstimateCost: EstimateCost
}

export interface AccumulatedCosts {
  mainConstructionAccumulatedCost: AccumulatedCost
  subConstructionAccumulatedCost: AccumulatedCost
  overallConstructionAccumulatedCost: AccumulatedCost
}

// Interface chính cho dự án
export interface ConstructionProject {
  projectId: string
  projectName: string
  contractorName: string
  customerName: string
  contractualStartDate: string
  contractualEndDate: string
  contractCosts: ContractCosts
  estimateCosts: EstimateCosts
  accumulatedCosts: AccumulatedCosts
}
export interface ConstructionItem {
  constructionId: string
  constructionName: string
  description: string
  isPrimary: boolean
  contractualCosts: ContractualCosts
  estimatedCosts: EstimatedCosts
  accumulatedCosts: AccumulatedCosts
}

interface ContractualCosts {
  initialCostItems: CostItem[]
  totalInitialCost: number
  modifiedCostItems: CostItem[]
  totalModifiedCost: number
  constructionId: string
  isPrimary: boolean
}

interface EstimatedCosts {
  estimateCostItems: CostItem[]
  totalEstimateCost: number
  constructionId: string
  isPrimary: boolean
}

export interface ConstructionResponse {
  constructions: ConstructionItem[]
}

export async function getConstructionByProjectIdApi(projectId: string) {
  return useGet<ConstructionResponse>(`v1/construction/project/${projectId}`)
}

export async function getConstructionByIdApi(constructionId: string) {
  return useGet<ConstructionItem>(`v1/construction/${constructionId}`)
}
