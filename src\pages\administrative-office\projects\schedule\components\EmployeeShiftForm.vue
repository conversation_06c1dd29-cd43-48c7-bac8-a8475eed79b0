<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
import { DeleteOutlined } from '@ant-design/icons-vue'
// import type { Dayjs } from 'dayjs'
import { NSelect } from 'naive-ui'
import { usePagination } from 'vue-request'
import type { QueryParams } from '~@/api/common-params'
import type { EmployeeShift, OutsourceShift, UpdateScheduleShiftParams } from '~@/api/company/schedule'
import type { WorkshiftItem } from '~@/api/company/work-shift'
import { getWorkshiftApi } from '~@/api/company/work-shift'
import { formatTimeToHHMMSS } from '~@/utils/apiTimer'
import { Skills } from '~@/utils/constant'

const props = defineProps({
  projectId: {
    type: String,
    required: true,
  },
  selectedShift: {
    type: Object as PropType<EmployeeShift>,
    required: false,
  },
  selectedOutsourceShift: {
    type: Object as PropType<OutsourceShift>,
    required: false,
  },
})

const emit = defineEmits<{
  (event: 'closeShiftInfo'): void
  (event: 'updateEmployeeShift', employeeShiftId: string, projectId: string, params: UpdateScheduleShiftParams): void
  (event: 'deleteEmployeeShift', employeeShiftId: string): void
}>()

const { t } = useI18n()
const workshiftId = ref<string | null>(null)
const workshiftCombo = ref<WorkshiftItem[]>([])
const noMore = ref(false)

interface ShiftFormState {
  workshiftId?: string
  outsourceId?: string
  scheduledStartTime?: string
  scheduledEndTime?: string
  assignedWorkload?: number
  workingRole: string
  totalScheduledWorkTime: number

}

const shiftFormState = reactive<ShiftFormState>({
  outsourceId: undefined,
  scheduledStartTime: undefined,
  scheduledEndTime: undefined,
  assignedWorkload: undefined,
  workingRole: '',
  totalScheduledWorkTime: 0,
})

async function queryData(params: QueryParams) {
  const { data, status } = await getWorkshiftApi(params)
  if (status !== 200 || !data) {
    noMore.value = true
    return data
  }
  workshiftCombo.value.push(...data?.items ?? [])
  return data
}

const params: QueryParams = {
  pageNum: 1,
  pageSize: 8,
}

const {
  run,
} = usePagination(queryData, {
  defaultParams: [params],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

function closeShiftInfo() {
  emit('closeShiftInfo')
}

function updateEmployeeShift() {
  if (!props.selectedShift?.employeeShiftId)
    return

  const params: UpdateScheduleShiftParams = {
    startTime: shiftFormState.scheduledStartTime ?? '00:00',
    endTime: shiftFormState.scheduledEndTime ?? '00:00',
    assignedRole: shiftFormState.workingRole,
    totalScheduledWorkTime: shiftFormState.totalScheduledWorkTime,
  }
  emit('updateEmployeeShift', props.selectedShift.employeeShiftId, props.projectId, params)
}

function deleteEmployeeShift() {
  if (!props.selectedShift?.employeeShiftId)
    return
  emit('deleteEmployeeShift', props.selectedShift.employeeShiftId)
}

watch(() => props.selectedShift, () => {
  if (props.selectedShift) {
    workshiftId.value = null
    shiftFormState.scheduledStartTime = formatTimeToHHMMSS(props.selectedShift.scheduledStartTime)
    shiftFormState.scheduledEndTime = formatTimeToHHMMSS(props.selectedShift.scheduledEndTime)
    shiftFormState.workingRole = props.selectedShift.workingRole
    shiftFormState.assignedWorkload = props.selectedShift.assignedWorkload
    shiftFormState.totalScheduledWorkTime = props.selectedShift.totalScheduledWorkTime
  }
  else {
    shiftFormState.workshiftId = undefined
    shiftFormState.scheduledStartTime = undefined
    shiftFormState.scheduledEndTime = undefined
    shiftFormState.workingRole = ''
    shiftFormState.assignedWorkload = 0
    shiftFormState.totalScheduledWorkTime = 0
  }
}, { immediate: true })

function handleScroll(e: Event) {
  if (noMore)
    return
  const currentTarget = e.currentTarget as HTMLElement
  if (
    currentTarget.scrollTop + currentTarget.offsetHeight
      >= currentTarget.scrollHeight
  ) {
    params.pageNum = (params.pageNum ?? 1) + 1
    run(params)
  }
}

function handleUpdateValue(value: string) {
  const workshift = workshiftCombo.value.find((item: WorkshiftItem) => item.workShiftId === value)
  if (!workshift)
    return
  shiftFormState.scheduledStartTime = formatTimeToHHMMSS(workshift?.checkInTime)
  shiftFormState.scheduledEndTime = formatTimeToHHMMSS(workshift?.checkOutTime)
  shiftFormState.totalScheduledWorkTime = workshift?.totalRequiredTime
}

// const handleSelectTime = (time: Dayjs, type: 'scheduledStartTime' | 'scheduledEndTime') => {
//   shiftFormState[type] = time.format('HH:mm')
// }

onMounted(() => {
})
</script>

<template>
  <div class="flex justify-end items-center cursor-pointer" @click="closeShiftInfo">
    <CarbonClose />
  </div>
  <div class="mt-12">
    <div class="border-b-1 border-t-0 border-l-0 border-r-0 border-gray-300 border-solid mb-4">
      <a-typography-title :level="5">
        {{ t('title.userShiftInfo') }}
      </a-typography-title>
    </div>
    <a-form
      layout="vertical"
      name="shiftFormState"
    >
      <div class="flex flex-col border-b-1 border-t-0 border-l-0 border-r-0 border-gray-300 border-solid pt-4 mb-4">
        <a-form-item :label="t('workshift')" name="workshift">
          <NSelect
            v-model:value="workshiftId"
            :clearable="true"
            :options="workshiftCombo.map((item) => ({ label: item.workShiftName, value: item.workShiftId }))"
            :placeholder="t('Please Select')"
            @scroll="handleScroll"
            @update:value="handleUpdateValue"
          />
        </a-form-item>
        <a-form-item :label="t('startTime')" name="scheduledStartTime'">
          <!-- <a-time-picker
            v-model:value="shiftFormState.scheduledStartTime"
            format="HH:mm"
            value-format="HH:mm"
            class="w-full"
            @select="(time: Dayjs) => handleSelectTime(time, 'scheduledStartTime')"
          /> -->
          <TimePicker 
            v-model:value="shiftFormState.scheduledStartTime"
            value-type="string"
            value-format="HH:mm"
          />
        </a-form-item>
        <a-form-item :label="t('endTime')" name="scheduledEndTime'">
          <!-- <a-time-picker
            v-model:value="shiftFormState.scheduledEndTime"
            format="HH:mm"
            value-format="HH:mm"
            class="w-full"
            @select="(time: Dayjs) => handleSelectTime(time, 'scheduledEndTime')"
          /> -->
          <TimePicker 
            v-model:value="shiftFormState.scheduledEndTime"
            value-type="string"
            value-format="HH:mm"
          />
        </a-form-item>
        <a-form-item :label="t('workload')" name="assignedWorkload'">
          <a-input v-model:value="shiftFormState.assignedWorkload" disabled class="w-full" />
        </a-form-item>
      </div>
      <div class="flex gap-x-12 mb-4">
        <a-radio-group v-model:value="shiftFormState.workingRole" name="roleRadioGroup" class="flex">
          <div class="flex flex-col gap-y-2">
            <a-radio :value="Skills.SUPERVISOR">
              {{ t('supervisor') }}
            </a-radio>
            <a-radio :value="Skills.FOREMAN">
              {{ t('foreman') }}
            </a-radio>
            <a-radio :value="Skills.WORKER">
              {{ t('worker') }}
            </a-radio>
            <a-radio :value="Skills.OFFICE_OPERATION">
              {{ t('officeOperation') }}
            </a-radio>
          </div>
          <div class="flex flex-col gap-y-2">
            <a-radio :value="Skills.HEAVY_MACHINERY_OPERATOR">
              {{ t('heavyMachineryOperator') }}
            </a-radio>
            <a-radio :value="Skills.FORMWORK_WORKER">
              {{ t('formworkWorker') }}
            </a-radio>
            <a-radio :value="Skills.REBAR_WORKER">
              {{ t('rebarWorker') }}
            </a-radio>
          </div>
        </a-radio-group>
      </div>
      <a-form-item>
        <div class="flex justify-between gap-x-4">
          <a-button danger type="primary" class="flex items-center" @click="deleteEmployeeShift">
            <template #icon>
              <DeleteOutlined />
            </template>
            {{ t('button.delete') }}
          </a-button>
          <a-button type="primary" @click="updateEmployeeShift">
            {{ t('button.update') }}
          </a-button>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>
