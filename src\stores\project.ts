import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { type ProjectComboItem, getProjectComboApi } from '~@/api/company/project'
import { useApiRequest } from '~@/composables/useApiRequest'

const apiRequest = useApiRequest(getProjectComboApi, { immediate: false, showNotify: false })

export const useProjectStore = defineStore('project', () => {
  // state
  const projects = ref<ProjectComboItem[]>([])
  const isLoading = ref(false)

  // getters
  const projectCount = computed(() => projects.value.length)
  const hasProjects = computed(() => projects.value.length > 0)

  // actions
  async function fetchProjects() {
    try {
      await apiRequest.execute()
      projects.value = apiRequest.data.value?.items ?? []
    }
    catch (error) {
      console.error('Error fetching projects:', error)
    }
    finally {
      isLoading.value = false
    }
  }

  return {
    // state
    projects,
    isLoading,
    // getters
    projectCount,
    hasProjects,
    // actions
    fetchProjects,
  }
})
