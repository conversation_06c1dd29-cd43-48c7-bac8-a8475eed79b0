import { getRankingListApi } from '~@/api/company/ranking'

export function useRanking() {
  const messageNotification = useMessage()
  const fetchRankingList = async () => {
    try {
      const { data, status, message } = await getRankingListApi()
      if (status === 200)
        return data?.items
      else
        messageNotification.error(message ?? 'Fetch ranking list failed!')
    }
    catch (error) {
      throw new Error(error as string)
    }
  }

  return {
    fetchRankingList,
  }
}
