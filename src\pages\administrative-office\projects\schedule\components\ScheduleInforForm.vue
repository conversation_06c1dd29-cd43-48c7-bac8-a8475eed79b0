<!-- eslint-disable curly -->
<!-- eslint-disable antfu/top-level-function -->
<script setup lang="ts">
import { DeleteOutlined } from '@ant-design/icons-vue'
import EmployeeTab from './EmployeeTab.vue'
import OutsourceTab from './OutsourceTab.vue'
import EmployeeShiftForm from './EmployeeShiftForm.vue'
import OutsourceShiftForm from './OutsourceShiftForm.vue'
import type { EmployeeShift, OutsourceShift, ScheduleItem, ScheduleParams, UpdateScheduleParams, UpdateScheduleShiftParams } from '~@/api/company/schedule'
import type { EmployeeItem } from '~@/api/employee/employee'
import type { OutSourceItem } from '~@/api/outsource'

const props = defineProps<{
  selectedShift?: EmployeeShift
  selectedOutsourceShift?: OutsourceShift
  selectedSchedule?: ScheduleItem
  isShiftInfoVisible: boolean
  isOutsourceShiftInfoVisible: boolean
  isScheduleInfoVisible: boolean
  selectedWorkingDate: string
  projectId: string
}>()

const emit = defineEmits<{
  (event: 'handleClickOutScheduleInfoForm'): void
  (event: 'createSchedule', params: ScheduleParams): void
  (event: 'updateSchedule', scheduleId: string, params: UpdateScheduleParams): void
  (event: 'updateEmployeeShift', scheduleId: string, projectId: string, params: UpdateScheduleShiftParams): void
  (event: 'updateOutsourceShift', scheduleId: string, projectId: string, params: UpdateScheduleShiftParams): void
  (event: 'showAllEmployeeShift'): void
  (event: 'updateIsEmployeeDragging', isDragging: boolean): void
  (event: 'deleteEmployeeShift', employeeShiftId: string): void
  (event: 'deleteOutsourceShift', outsourceShiftId: string): void
  (event: 'deleteSchedule', scheduleId: string): void

}>()

const { t } = useI18n()

const activeKey = ref('1')
const userInfoRef = ref<HTMLElement | null>(null)

const scheduleFormState = reactive<ScheduleParams>({
  projectId: '',
  workingDate: '',
  plannedWorkload: undefined,
  estimatedWorkload: undefined,
})

const handleTabChange = (key: any) => {
  activeKey.value = key
}

// const handleClickOutside = (event: any) => {
//   // Kiểm tra thành phần cha của target có chứa class .ant-picker-panel-container không?
//   if (event.target.closest('.ant-picker-panel-container'))
//     return
//   emit('handleClickOutScheduleInfoForm')
// }

// onClickOutside(userInfoRef, handleClickOutside)

const updateSchedule = () => {
  const scheduleId = props.selectedSchedule?.scheduleId
  if (!scheduleId) {
    return
  }
  const params: UpdateScheduleParams = {
    plannedWorkload: Number(scheduleFormState.plannedWorkload),
    estimatedWorkload: Number(scheduleFormState.estimatedWorkload),
  }
  emit('updateSchedule', scheduleId, params)
}

const createSchedule = () => {
  const params: ScheduleParams = {
    projectId: scheduleFormState.projectId,
    workingDate: scheduleFormState.workingDate,
    plannedWorkload: scheduleFormState.plannedWorkload,
    estimatedWorkload: scheduleFormState.estimatedWorkload,
  }
  emit('createSchedule', params)
}

const deleteSchedule = () => {
  const scheduleId = props.selectedSchedule?.scheduleId
  if (!scheduleId) {
    return
  }
  emit('deleteSchedule', scheduleId)
}

const dragEmployeeStart = (event: DragEvent, employee: EmployeeItem) => {
  if (!event.dataTransfer)
    return
  emit('showAllEmployeeShift')
  emit('updateIsEmployeeDragging', true)
  event.dataTransfer.effectAllowed = 'move'
  event.dataTransfer.setData('employeeId', employee.employeeId)
  if (event.target instanceof HTMLElement)
    event.target.style.opacity = '0.5'
}

const dragOutsourceStart = (event: DragEvent, outsource: OutSourceItem) => {
  if (!event.dataTransfer)
    return
  emit('showAllEmployeeShift')
  emit('updateIsEmployeeDragging', true)
  event.dataTransfer.effectAllowed = 'move'
  event.dataTransfer.setData('outsourceId', outsource.outSourceId)
  if (event.target instanceof HTMLElement)
    event.target.style.opacity = '0.5'
}

const dragEmployeeEnd = (event: DragEvent) => {
  if (event.target instanceof HTMLElement)
    event.target.style.opacity = '1'

  if (!event.dataTransfer)
    return
  event.dataTransfer.clearData()
  emit('updateIsEmployeeDragging', false)
}

const closeScheduleInfo = () => {
  emit('handleClickOutScheduleInfoForm')
}

const closeShiftInfo = () => {
  emit('handleClickOutScheduleInfoForm')
}

function updateEmployeeShift(employeeShiftId: string, projectId: string, params: UpdateScheduleShiftParams) {
  emit('updateEmployeeShift', employeeShiftId, projectId, params)
}

function updateOutsourceShift(outsourceShiftId: string, projectId: string, params: UpdateScheduleShiftParams) {
  emit('updateOutsourceShift', outsourceShiftId, projectId, params)
}

function deleteEmployeeShift(employeeShiftId: string) {
  emit('deleteEmployeeShift', employeeShiftId)
}

function deleteOutsourceShift(outsourceShiftId: string) {
  emit('deleteOutsourceShift', outsourceShiftId)
}

watch(() => props.selectedSchedule, () => {
  scheduleFormState.estimatedWorkload = props.selectedSchedule?.totalEstimatedWorkload ?? 0
  scheduleFormState.plannedWorkload = props.selectedSchedule?.totalPlannedWorkload ?? 0
  scheduleFormState.workingDate = props.selectedSchedule?.workingDate ?? ''
})

watch(() => props.selectedWorkingDate, () => {
  scheduleFormState.workingDate = props.selectedWorkingDate
})

watch(() => props.projectId, () => {
  scheduleFormState.projectId = props.projectId
})

watch(() => props.isOutsourceShiftInfoVisible, () => {
})

onMounted(async () => {
})
</script>

<template>
  <!-- Phần thông tin employee bên phải -->
  <div
    ref="userInfoRef"
    class="schedule-infor-form flex-none xl:w-72 bg-white p-3 rounded-lg xl:h-[calc(100vh-8rem)] sticky top-20"
  >
    <!-- Phần thông tin employee -->
    <div
      v-show="!isShiftInfoVisible && !isScheduleInfoVisible && !isOutsourceShiftInfoVisible"
    >
      <a-tabs
        v-model:activeKey="activeKey" :tab-bar-gutter="60"
        :tab-bar-style="{
          'border-bottom': '1px solid #e0e0e0',
          'margin-left': '10px',
        }"
        @change="handleTabChange"
      >
        <a-tab-pane key="1">
          <template #tab>
            <span class="flex items-center gap-x-2">
              <CarbonUser />
              {{ t('employee.internal') }}
            </span>
          </template>
          <div class="flex-1 flex-col gap-y-4 mt-4">
            <KeepAlive>
              <EmployeeTab
                :employee-type="true"
                @drag-employee-start="dragEmployeeStart"
                @drag-employee-end="dragEmployeeEnd"
              />
            </KeepAlive>
          </div>
        </a-tab-pane>
        <!-- <a-tab-pane key="2">
          <template #tab>
            <span class="flex items-center gap-x-2">
              <CarbonGroupUser />
              {{ t('employee.outsource') }}
            </span>
          </template>
          <KeepAlive>
            <EmployeeTab
              :employee-type="false"
              @drag-employee-start="dragEmployeeStart"
              @drag-employee-end="dragEmployeeEnd"
            />
          </KeepAlive>
        </a-tab-pane> -->
        <a-tab-pane key="2">
          <template #tab>
            <span class="flex items-center gap-x-2">
              <CarbonGroupUser />
              {{ t('employee.outsource') }}
            </span>
          </template>
          <KeepAlive>
            <OutsourceTab
              @drag-outsource-start="dragOutsourceStart"
              @drag-outsource-end="dragEmployeeEnd"
            />
          </KeepAlive>
        </a-tab-pane>
      </a-tabs>
    </div>
    <!-- Phần thông tin lịch phân công -->
    <div v-if="isScheduleInfoVisible">
      <div class="flex justify-end items-center cursor-pointer" @click="closeScheduleInfo">
        <CarbonClose />
      </div>
      <div class="mt-5">
        <div class="border-b-1 border-t-0 border-l-0 border-r-0 border-gray-300 border-solid mb-4">
          <a-typography-title :level="5">
            {{ t('title.scheduleInfo') }}
          </a-typography-title>
        </div>
        <a-form
          :model="scheduleFormState"
          layout="vertical"
        >
          <div class="flex flex-col gap-y-2">
            <a-form-item
              :label="t('schedule.estimate')"
              name="estimated"
              :rules="[{ required: false, message: t('form.required') }]"
              class="w-full mb-0"
            >
              <a-input v-model:value="scheduleFormState.estimatedWorkload" type="number" class="w-full" />
            </a-form-item>
            <a-form-item
              :label="t('schedule.plan')"
              name="planned"
              :rules="[{ required: false, message: t('form.required') }]"
            >
              <a-input v-model:value="scheduleFormState.plannedWorkload" type="number" />
            </a-form-item>
          </div>
          <a-form-item>
            <div v-if="!selectedSchedule?.scheduleId">
              <a-button type="primary" @click="createSchedule">
                {{ t('button.create') }}
              </a-button>
            </div>
            <div v-else class="flex justify-between gap-x-4">
              <a-button danger type="primary" class="flex items-center justify-center" @click="deleteSchedule">
                <template #icon>
                  <DeleteOutlined />
                </template>
                {{ t('button.delete') }}
              </a-button>
              <a-button type="primary" @click="updateSchedule">
                {{ t('button.update') }}
              </a-button>
            </div>
          </a-form-item>
        </a-form>
      </div>
    </div>

    <!-- Phần hiển thị thông tin lịch phân công của 1 người dùng -->
    <keep-alive>
      <EmployeeShiftForm
        v-if="isShiftInfoVisible"
        :project-id="projectId"
        :selected-shift="selectedShift"
        @close-shift-info="closeShiftInfo"
        @update-employee-shift="updateEmployeeShift"
        @delete-employee-shift="deleteEmployeeShift"
        @delete-outsource-shift="deleteOutsourceShift"
      />
    </keep-alive>
    <keep-alive>
      <OutsourceShiftForm
        v-if="isOutsourceShiftInfoVisible"
        :project-id="projectId"
        :selected-outsource-shift="selectedOutsourceShift!"
        @close-shift-info="closeShiftInfo"
        @update-outsource-shift="updateOutsourceShift"
        @delete-outsource-shift="deleteOutsourceShift"
      />
    </keep-alive>
  </div>
</template>

<style scoped>
.employee-card {
  display: flex;
  gap: 0.5rem;
  border-radius: 0.5rem;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  background-color: white;
  transition: box-shadow 0.2s, transform 0.2s, background-color 0.2s, color 0.0s;
}
.employee-card:hover {
  /* box-shadow: 0 0px 1px rgba(0, 0, 0, 0.3); */
  transform: scale(1.05);
  color: #1a73e8;
}
</style>
