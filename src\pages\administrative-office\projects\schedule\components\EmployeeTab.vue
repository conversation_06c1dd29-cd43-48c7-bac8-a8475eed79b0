<script setup lang="ts">
import { usePagination } from 'vue-request'
import type { EmployeeItem, EmployeeQueryParams } from '~@/api/employee/employee'
import { useEmployee } from '~@/composables/employee/useEmployee'
import { useAvatarStore } from '~@/stores/avatar'

const props = defineProps({
  employeeType: {
    type: Boolean,
    required: true,
  },
})

const emits = defineEmits<{
  (event: 'dragEmployeeStart', evt: DragEvent, employee: EmployeeItem): void
  (event: 'dragEmployeeEnd', evt: DragEvent): void
}>()

const { t } = useI18n()
const { employeeData, fetchEmployee } = useEmployee()
const { height } = useWindowScrollSize()
const avatarStore = useAvatarStore()

const params = reactive<EmployeeQueryParams>({
  name: undefined,
  code: undefined,
  employeeType: props.employeeType,
  pageNum: 1,
  pageSize: 100,
})

const searchKey = ref<string>('')

const {
  loading,
} = usePagination(fetchEmployee, {
  defaultParams: [params],
  pagination: {
    currentKey: 'pageIndex',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

function dragEmployeeStart(evt: DragEvent, employee: EmployeeItem) {
  emits('dragEmployeeStart', evt, employee)
}

function dragEmployeeEnd(evt: DragEvent) {
  emits('dragEmployeeEnd', evt)
}

async function onSearch() {
  if (searchKey.value === '') {
    params.code = undefined
    params.pageNum = 1
    params.pageSize = 8
    const res = await fetchEmployee(params)
    employeeData.value = res?.items ?? []
  }
  else {
    params.code = searchKey.value
    params.pageNum = 1
    params.pageSize = 7
    const res = await fetchEmployee(params)
    employeeData.value = res?.items ?? []
  }
}

onMounted(() => {
})
</script>

<template>
  <div class="flex-none h-10">
    <a-input v-model:value="searchKey" :placeholder="t('search')" @press-enter="onSearch" />
  </div>
  <div class="overflow-y-auto" :style="{ height: `${height - 300}px` }">
    <div class="flex flex-col flex-1 gap-y-4 snap-y snap-mandatory">
      <div
        v-for="employee in employeeData" :key="employee.employeeId"
        class="flex gap-x-2 rounded-lg border-1 border-gray-300 border-solid p-2 bg-white snap-start"
      >
        <div
          class="cursor-move"
          :draggable="true"
          @dragstart="dragEmployeeStart($event, employee)"
          @dragend="dragEmployeeEnd($event)"
        >
          <a-avatar :size="50" :src="avatarStore.getImageSrcByEmployeeId(employee.employeeId) ?? ''" class="shadow-lg" />
        </div>
        <div>
          <span class="font-bold">{{ employee.employeeName }}</span>
          <div class="text-[#74797A]">
            {{ t('code') }}: {{ employee.employeeCode }}
          </div>
          <span class="flex gap-x-2 items-center text-[#74797A]"><CarbonObservation />{{ employee.positionName }}</span>
        </div>
      </div>
      <div
        v-if="loading"
        class="flex justify-center items-center"
      >
        <a-spin />
      </div>
    </div>
  </div>
</template>
