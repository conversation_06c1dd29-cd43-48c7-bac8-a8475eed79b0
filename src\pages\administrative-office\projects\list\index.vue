<!-- eslint-disable curly -->
<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
import {
  ArrowRightOutlined,
  EditOutlined,
  FormOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue'
import { usePagination } from 'vue-request'
import dayjs from 'dayjs'
import type { Rule } from 'ant-design-vue/es/form'
import { isEmpty } from 'lodash'
import type {
  FilterParams,
  ManagerInfo,
  ProjectItem,
  ProjectParams,
  ProjectType,
} from '~@/api/company/project'
import {
  createProjectApi,
  getPaginatedProjectListApi,
  getProjectTypeComboApi,
  updateProjectApi,
} from '~@/api/company/project'
import type { SimpleStructureInfo } from '~@/api/company/struct'
import { getStructureComboApi } from '~@/api/company/struct'
import logger from '~@/utils/logger'
import type { StatusItem } from '~@/api/common/common'
import { getProjectStatusListApi } from '~@/api/common/common'
import type { QueryParams } from '~@/api/common-params'
import type { EmployeeCombo } from '~@/api/employee/employee'
import { getSimpleEmployeeInfoApi } from '~@/api/employee/employee'
import type { GetContractorParams } from '~@/api/company/contractor'
import { getContractor } from '~@/api/company/contractor'
import type { GetCustomerParams } from '~@/api/company/customer'
import { getCustomer } from '~@/api/company/customer'
import type { QueryWorkshiftParams, WorkshiftItem } from '~@/api/company/work-shift'
import { getWorkshiftApi } from '~@/api/company/work-shift'

const messageSystem = useMessage()
const isEdit = ref<boolean>(false)
const isFilter = shallowRef(false)
const isLoading = ref(false)

const isShowActiveProject = ref(true)
const isModalOpen = ref(false)
const isFilterVisible = shallowRef<boolean>(false)
const modalFormRef = ref()
const { t } = useI18n()
const structureListCombo = ref<SimpleStructureInfo[]>([])
const sortedProject = ref<ProjectItem[]>([])
const userListCombo = ref<any[]>([])
const projectTypeCombo = ref<ProjectType[]>([])

const systemStatusOptions = ref<StatusItem[]>([])

// const systemStatusOptions = ref<any[]>([])
//   {
//     'statusCode': 5,
//     'statusCode': 'PLANNED',
//     'statusName': '計画済み',
//     'description': null,
//   },
//   {
//     'statusCode': 6,
//     'statusCode': 'STARTED',
//     'statusName': '開始済み',
//     'description': null,
//   },
//   {
//     'statusCode': 7,
//     'statusCode': 'ENDED',
//     'statusName': '完了',
//     'description': null,
//   },
// ]

const formState = reactive<any>({
  projectId: undefined,
  projectCode: undefined,
  projectName: '',
  projectTypeId: '',
  description: '',
  statusCode: undefined,
  expectedDateRange: ['', ''],
  actualDateStart: undefined,
  actualDateEnd: undefined,
  actualBudget: undefined,
  initialBudget: undefined,
  primaryManagerEmployeeIds: [],
  subManagerEmployeeIds: [],
  address: undefined,
  addressTypeId: undefined,
  workShiftIds: [],
  defaultWorkShift: undefined,
  customerId: undefined,
  contractorId: undefined,
})
interface FormFilterState {
  projectName: string | undefined
  projectTypeId: string | undefined
  statusCode: string | undefined
  expectedDateRange: [string, string]
  actualDateRange: [string, string]
  budgetRange: [number, number]
  costRange: [number, number]
  primaryManagerEmployeeIds: number[]
  subManagerEmployeeIds: number[]
  address: string | undefined
}
const formFilterState = reactive<FormFilterState>({
  projectName: undefined,
  address: undefined,
  projectTypeId: undefined,
  statusCode: 'STARTED',
  expectedDateRange: ['', ''],
  actualDateRange: ['', ''],
  budgetRange: [0, 10000000],
  costRange: [0, 10000000],
  primaryManagerEmployeeIds: [],
  subManagerEmployeeIds: [],
})

const queryData = async (params: FilterParams) => {
  try {
    params.projectName = formFilterState.projectName ?? undefined
    params.statusCode = formFilterState.statusCode ?? undefined
    if (isFilter.value) {
      params.typeId = formFilterState.projectTypeId ?? undefined
      params.exStartDate = formFilterState.expectedDateRange?.[0]
        ? formFilterState.expectedDateRange?.[0]
        : undefined
      params.exEndDate = formFilterState.expectedDateRange?.[1]
        ? formFilterState.expectedDateRange?.[1]
        : undefined
      params.actStartDate = formFilterState.actualDateRange[0] ?? undefined
      params.actEndDate = formFilterState.actualDateRange[1] ?? undefined
      params.budgetMin = formFilterState.budgetRange?.[0] ?? undefined
      params.budgetMax = formFilterState.budgetRange?.[1] ?? undefined
      params.costMin = formFilterState.costRange?.[0] ?? undefined
      params.costMax = formFilterState.costRange?.[1] ?? undefined
    }
    const { data, status } = await getPaginatedProjectListApi(params)
    if (status === 200) {
      sortedProject.value = data?.items?.sort((a, b) => a.projectCode.localeCompare(b.projectCode)) ?? []
      return data ?? []
    }
    else {
      return []
    }
  }
  catch (e) {
    logger.error(e)
  }
  finally {
    isFilter.value = false
  }
}

const { refresh, current, changeCurrent, total, pageSize }
  = usePagination(queryData, {
    defaultParams: [
      {
        pageNum: 1,
        pageSize: 20,
      },
    ],
    pagination: {
      currentKey: 'pageNum',
      pageSizeKey: 'pageSize',
      totalKey: 'totalRecords',
    },
  })

// const dataSource = computed(() => {
//   return data
// })

async function getStructureListCombo() {
  const params: QueryParams = {
    pageSize: 1000,
    pageNum: 1,
  }
  try {
    const { data, status, code } = await getStructureComboApi(params)
    if (status === 200) {
      structureListCombo.value = data?.items ?? []
      logger.log('structureListCombo', structureListCombo.value)
    }
    else {
      logger.error('status: ', status)
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}
// async function getWorksiteCombo() {
//   try {
//     const { data, status, code } = await getWorksiteComboApi()
//     if (status === 200) {
//       worksiteListCombo.value = data?.items ?? []
//       logger.log('worksiteListCombo', worksiteListCombo.value)
//     }
//     else {
//       logger.error('status: ', status)
//       logger.error(t(code))
//     }
//   }
//   catch (e) {
//     logger.error(e)
//   }
// }
async function getEmployeeListCombo() {
  try {
    const { data, status, code } = await getSimpleEmployeeInfoApi()
    if (status === 200) {
      if (data?.items?.length) {
        userListCombo.value = data?.items.map((item: EmployeeCombo) => (
          {
            id: item.employeeId,
            label: `${item.employeeCode} ${item.employeeName}`,
          }
        ))
        userListCombo.value.sort((a, b) => a.label.localeCompare(b.label))
      }
    }
    else {
      logger.error('status: ', status)
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}
async function getProjectTypeCombo() {
  try {
    const { data, status, code } = await getProjectTypeComboApi()
    if (status === 200) {
      projectTypeCombo.value = data?.items ?? []
    }
    else {
      logger.error('status: ', status)
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}
function openModal(item: ProjectItem, isAddNew?: boolean) {
  logger.log('openModal')
  if (isAddNew) {
    isEdit.value = false
    isModalOpen.value = true
  }
  else {
    isEdit.value = true
    isModalOpen.value = true
    formState.address = item.address
    formState.projectId = item.projectId
    formState.projectCode = item.projectCode
    formState.projectName = item.projectName
    formState.projectTypeId = item.projectTypeId
    formState.address = item.address
    formState.description = item.description
    formState.statusCode = item.statusCode
    const actualStartDate = item.actualStartDate
      ? dayjs(item.actualStartDate).format('YYYY-MM-DD')
      : undefined
    const actualEndDate = item.actualEndDate
      ? dayjs(item.actualEndDate).format('YYYY-MM-DD')
      : undefined
    const expectedStartDate = item.expectedStartDate
      ? dayjs(item.expectedStartDate).format('YYYY-MM-DD')
      : undefined
    const expectedEndDate = item.expectedEndDate
      ? dayjs(item.expectedEndDate).format('YYYY-MM-DD')
      : undefined
    formState.actualDateStart = actualStartDate
    formState.actualDateEnd = actualEndDate
    formState.expectedDateRange = [expectedStartDate, expectedEndDate]
    formState.actualBudget = item.actualBudget ?? undefined
    formState.initialBudget = item.initialBudget ?? undefined
    formState.primaryManagerEmployeeIds = item.managersInfo
      .filter((item: ManagerInfo) => item.isPrimaryManager)
      .map((item: ManagerInfo) => item.managerId)
    formState.subManagerEmployeeIds = item.managersInfo
      .filter((item: ManagerInfo) => !item.isPrimaryManager)
      .map((item: ManagerInfo) => item.managerId)
    if (item.isOffice) {
      formState.addressTypeId = 1
    }
    else if (item.isHeadOffice) {
      formState.addressTypeId = 2
    }
    else {
      formState.addressTypeId = 3
    }

    formState.customerId = item.customerId
    formState.contractorId = item.contractorId
    formState.workShiftIds = item.workShifts.map((item: WorkshiftItem) => item.workShiftId)
    formState.defaultWorkShift = item.workShifts.find((item: WorkshiftItem) => item.isDefault)?.workShiftId

    logger.log('formState: ', formState)
  }
}

async function createProject(newProject: ProjectParams) {
  if (isLoading.value)
    return
  isLoading.value = true
  const { status, message } = await createProjectApi(newProject)
  if (status === 200) {
    messageSystem.success(message)
  }
  else {
    messageSystem.error(message)
  }
  isLoading.value = false
}

async function updateProject(projectId: string, params: ProjectParams) {
  if (isLoading.value)
    return

  isLoading.value = true
  const { status, message } = await updateProjectApi(projectId, params)
  if (status === 200) {
    messageSystem.success(message)
  }
  else {
    messageSystem.error(message)
  }
  isLoading.value = false
}

const closeModal = () => {
  isModalOpen.value = false
  modalFormRef.value.resetFields()
  formState.projectCode = ''
  formState.projectName = undefined
  formState.description = ''
  formState.statusCode = undefined
  formState.actualDateStart = ''
  formState.actualDateEnd = ''
  formState.expectedDateRange = ['', '']
  formState.actualBudget = undefined
  formState.initialBudget = undefined
  formState.primaryManagerEmployeeIds = []
  formState.subManagerEmployeeIds = []
  formState.address = undefined
  formState.projectTypeId = undefined
  formState.projectId = undefined
  formState.workShiftIds = []
  formState.defaultWorkShift = undefined
  formState.addressTypeId = undefined
  formState.customerId = undefined
  formState.contractorId = undefined
  isEdit.value = false
}

const handleOk = async () => {
  logger.log('handleOk')
  modalFormRef.value.validate().then(async () => {
    const project: ProjectParams = {
      projectCode: formState.projectCode,
      projectName: formState.projectName,
      workShiftIds: formState.workShiftIds,
      workShifts: [],
      contractorId: formState.contractorId,
      customerId: formState.customerId,
      description: formState.description,
      statusCode: formState.statusCode,
      actualStartDate: formState.actualDateStart,
      actualEndDate: formState.actualDateEnd,
      expectedStartDate: formState.expectedDateRange
        ? formState.expectedDateRange[0]
        : undefined,
      expectedEndDate: formState.expectedDateRange
        ? formState.expectedDateRange[1]
        : undefined,
      actualBudget: formState.actualBudget,
      initialBudget: formState.initialBudget,
      primaryManagerEmployeeIds: formState.primaryManagerEmployeeIds ?? [],
      subManagerEmployeeIds: formState.subManagerEmployeeIds ?? [],
      address: formState.address,
      projectTypeId: formState.projectTypeId,
    }

    if (formState.defaultWorkShift) {
      project.workShifts = formState.workShiftIds.map((id: string) => ({
        workShiftId: id,
        isDefault: id === formState.defaultWorkShift,
      }))
    }

    switch (formState.addressTypeId) {
      case 1:
        project.isOffice = true
        project.isHeadOffice = false
        break
      case 2:
        project.isHeadOffice = true
        project.isOffice = false
        break
      default:
        project.isOffice = false
        project.isHeadOffice = false
        break
    }
    if (!isEdit.value) {
      logger.log('project: ', project)
      await createProject(project)
      closeModal()
    }
    else {
      const projectId = formState.projectId
      if (!projectId) {
        return
      }
      await updateProject(projectId, project)
      closeModal()
    }
    isModalOpen.value = false
    refresh()
    // tableKey.value += 1
  })
}

// async function onDelete(projectKey: string) {
//   try {
//     const projectData: any = {
//       ...formState,
//       status: formState.status ? 1 : 0,
//     }
//     const { status, code } = await deleteProjectApi(projectData)
//     if (status === 200) {
//       message.success('削除成功')
//     }
//     else {
//       logger.error('status', status)
//       logger.error(t(code))
//     }
//   }
//   catch (e) {
//     logger.error(e)
//   }
// }

// function showConfirm(item: any) {
//   Modal.confirm({
//     title: `${t('button.delete')}`,
//     icon: createVNode(ExclamationCircleOutlined),
//     content: createVNode('div', {}, `${t('alert.confirmDelete')}`),
//     cancelText: `${t('button.cancel')}`,
//     okText: `${t('button.ok')}`,
//     async onOk() {
//       await onDelete(item.ProjectKey)
//       refresh()
//     },
//     onCancel() {
//       Modal.destroyAll()
//     },
//     class: 'test',
//   })
// }

// async function getSystemStatus() {
//   try {
//     const { data, status, code } = await getStatusList()
//     if (status === 200) {
//       logger.log('systemStatusOptions', data?.items)
//       systemStatusOptions.value = data?.items ?? []
//     }
//     else {
//       logger.error(t(code))
//     }
//   }
//   catch (e) {
//     logger.error(e)
//   }
// }

const openAddNewModel = () => {
  isModalOpen.value = true
  isEdit.value = false
  logger.log('isEdit', isEdit.value)
}
const formatter = (value?: number) => {
  return `${value}￥`
}

const handleFilter = () => {
  isFilter.value = true
  changeCurrent(1)
  isFilterVisible.value = false
}

const handleSwitchChange = () => {
  isFilterVisible.value = false
  const activeStatusCode = systemStatusOptions.value.find((item: StatusItem) => item.statusCode === 'STARTED')?.statusCode
  if (isShowActiveProject.value === true) {
    formFilterState.statusCode = activeStatusCode
  }
  else {
    formFilterState.statusCode = undefined
  }
  changeCurrent(1)
}

const handlePageSizeChange = () => {
  changeCurrent(1)
}

const pageSizeOptions = [
  {
    label: '10',
    value: 10,
  },
  {
    label: '20',
    value: 20,
  },
  {
    label: '50',
    value: 50,
  },
  {
    label: '100',
    value: 100,
  },
]

const addressTypeCombo = computed(() => {
  return [
    {
      label: t('isOffice'),
      value: 1,
    },
    {
      label: t('isHeadOffice'),
      value: 2,
    },
    {
      label: t('isWorksite'),
      value: 3,
    },
  ]
})
const resetFormFilter = () => {
  formFilterState.projectName = undefined
  formFilterState.address = undefined
  formFilterState.projectTypeId = undefined
  formFilterState.statusCode = undefined
  formFilterState.expectedDateRange = ['', '']
  formFilterState.budgetRange = [0, 10000000]
  formFilterState.primaryManagerEmployeeIds = []
  formFilterState.subManagerEmployeeIds = []
}

const handleClearFilter = () => {
  resetFormFilter()
  refresh()
  isFilterVisible.value = false
}

const getTagClass = (statusCode: string) => {
  switch (statusCode) {
    case 'PLANNED':
      return 'bg-gray-200 text-gray-700'
    case 'STARTED':
      return 'bg-[#DCF6E0] text-[#106B1E]'
    case 'ENDED':
      return 'bg-[#FFCDD2] text-[#C62828]'
    default:
      return 'bg-gray-200 text-gray-700'
  }
}

const validateMainManager = (_rule: Rule, value: number[]) => {
  if (value.length === 0) {
    return Promise.reject(new Error(t('placeholder.select-data', { msg: t('mainManager') })))
  }
  if (formState.subManagerEmployeeIds) {
    const len = value.length
    const index = formState.subManagerEmployeeIds.findIndex((item: number) => item === value[len - 1])
    if (index !== -1) {
      return Promise.reject(new Error(t('validate.mainManager')))
    }
  }
  return Promise.resolve()
}

const validateSubManager = (_rule: Rule, value: number[]) => {
  logger.log('value', value)
  if (formState.primaryManagerEmployeeIds) {
    const len = value.length
    const index = formState.primaryManagerEmployeeIds.findIndex((item: number) => item === value[len - 1])
    if (index !== -1) {
      return Promise.reject(new Error(t('validate.subManager')))
    }
  }
  return Promise.resolve()
}

async function getProjectStatus() {
  const { data, status, code } = await getProjectStatusListApi()
  if (status === 200) {
    systemStatusOptions.value = data?.items ?? []
  }
  else {
    logger.error(t(code))
  }
}

// Get customer info
const customerParams: GetCustomerParams = reactive({
  pageNum: 1,
  pageSize: 100,
})

async function queryCustomer(params: GetCustomerParams) {
  const { data, status, code } = await getCustomer(params)
  if (status === 200) {
    return data
  }
  else {
    logger.error(t(code))
  }
}

const {
  data: customerData,
} = usePagination(queryCustomer, {
  defaultParams: [customerParams],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const customerOptions = computed(() => {
  return customerData?.value?.items ?? []
})

// Get customer info
const contractorParams: GetContractorParams = reactive({
  pageNum: 1,
  pageSize: 100,
})

async function queryConstractor(params: GetContractorParams) {
  const { data, status, code } = await getContractor(params)
  if (status === 200) {
    return data
  }
  else {
    logger.error(t(code))
  }
}

const {
  data: contractorData,
} = usePagination(queryConstractor, {
  defaultParams: [contractorParams],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const contractorOptions = computed(() => {
  return contractorData?.value?.items ?? []
})

// Get workshift info
const workshiftParams: QueryWorkshiftParams = reactive({
  pageNum: 1,
  pageSize: 100,
})

async function queryWorkshift(params: QueryWorkshiftParams) {
  const { data, status, code } = await getWorkshiftApi(params)
  if (status === 200) {
    return data
  }
  else {
    logger.error(t(code))
  }
}

const {
  data: workshiftData,
} = usePagination(queryWorkshift, {
  defaultParams: [workshiftParams],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const workshiftOptions = computed(() => {
  return workshiftData?.value?.items ?? []
})

const defaultWorkShiftOptions = computed(() => {
  return workshiftData?.value?.items?.filter((item: WorkshiftItem) => {
    return formState.workShiftIds.includes(item.workShiftId)
  }) ?? []
})

onMounted(async () => {
  const promises = [
    getProjectStatus(),
    getProjectTypeCombo(),
    getEmployeeListCombo(),
    getStructureListCombo(),
  ]
  Promise.all(promises)
})
</script>

<template>
  <page-container class="font-size-custom">
    <a-row :gutter="[24, 24]">
      <a-col :xxl="24" :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
        <div class="flex justify-between">
          <div class="flex gap-x-[8px] items-center">
            <div class="2xl:w-104 w-80">
              <a-input
                v-model:value="formFilterState.projectName"
                :placeholder="t('projectName')"
                @press-enter="handleFilter"
              >
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
            </div>
            <div>
              <a-switch
                v-model:checked="isShowActiveProject"
                checked-children="active"
                un-checked-children="all"
                @click="handleSwitchChange"
              />
            </div>
          </div>
          <div class="flex gap-x-[8px]">
            <a-popover
              v-model:open="isFilterVisible"
              placement="bottomRight"
              trigger="click"
            >
              <template #title>
                <div class="flex justify-between items-center h-[36px]">
                  <div class="flex gap-x-[8px] items-center">
                    <CarbonProjectFilter />
                    <span>{{ t("button.filter") }}</span>
                  </div>
                  <div>
                    <CarbonClose />
                  </div>
                </div>
                <a-divider class="m-0" />
              </template>
              <template #content>
                <a-form
                  :model="formFilterState"
                  layout="vertical"
                  class="flex flex-col w-[21rem]"
                >
                  <div class="h-[5rem] flex">
                    <a-form-item
                      :label="t('projectType')"
                      name="type"
                      class="w-[21rem]"
                    >
                      <a-select
                        v-model:value="formFilterState.projectTypeId"
                        :options="projectTypeCombo"
                        :field-names="{
                          label: 'projectTypeName',
                          value: 'projectTypeId',
                        }"
                        allow-clear
                      />
                    </a-form-item>
                  </div>
                  <div class="flex gap-x-[12px] h-[4.5rem]">
                    <!-- <a-form-item
                      :label="t('projectStructure')"
                      name="structureId"
                      class="w-[10.5rem]"
                    >
                      <a-select
                        v-model:value="formFilterState.structureId"
                        :options="structureListCombo"
                        :field-names="{ label: 'name', value: 'id' }"
                        allow-clear
                      />
                    </a-form-item> -->
                    <a-form-item
                      :label="t('status')"
                      name="status"
                      class="w-[10.5rem]"
                    >
                      <a-select
                        v-model:value="formFilterState.statusCode"
                        :options="systemStatusOptions"
                        :field-names="{
                          label: 'statusName',
                          value: 'statusCode',
                        }"
                        allow-clear
                      />
                    </a-form-item>
                  </div>
                  <div class="flex items-center">
                    <a-tabs>
                      <a-tab-pane key="1" :tab="t('expectedPlan')">
                        <a-form-item name="expectedDateRange">
                          <a-range-picker
                            v-model:value="formFilterState.expectedDateRange"
                            class="w-[21rem]"
                            value-format="YYYY-MM-DD"
                            format="YYYY-MM-DD"
                          >
                            <template #suffixIcon>
                              <CarbonProjectCalendar />
                            </template>
                          </a-range-picker>
                        </a-form-item>
                        <div class="flex flex-col gap-y-[8px]">
                          <div class="flex flex-col gap-y-[0.5rem]">
                            <span>{{ t("budgetRange") }}</span>
                            <a-slider
                              v-model:value="formFilterState.budgetRange"
                              range
                              :tip-formatter="formatter"
                              :min="0"
                              :max="10000000"
                              :step="1000"
                            />
                          </div>
                          <div class="flex gap-x-[16px]">
                            <a-input
                              v-model:value="formFilterState.budgetRange[0]"
                            >
                              <template #suffix>
                                <CarbonYen class="w-[1rem] h-[1rem]" />
                              </template>
                            </a-input>
                            <a-input
                              v-model:value="formFilterState.budgetRange[1]"
                            >
                              <template #suffix>
                                <CarbonYen class="w-[1rem] h-[1rem]" />
                              </template>
                            </a-input>
                          </div>
                        </div>
                      </a-tab-pane>
                      <a-tab-pane key="2" :tab="t('actualPlan')">
                        <a-form-item name="actualDateRange">
                          <a-range-picker
                            v-model:value="formFilterState.actualDateRange"
                            class="w-[21rem]"
                            value-format="YYYY-MM-DD"
                            format="YYYY-MM-DD"
                          >
                            <template #suffixIcon>
                              <CarbonProjectCalendar />
                            </template>
                          </a-range-picker>
                        </a-form-item>
                        <div class="flex flex-col gap-y-[8px]">
                          <div class="flex flex-col gap-y-[0.5rem]">
                            <span>{{ t("costRange") }}</span>
                            <a-slider
                              v-model:value="formFilterState.costRange"
                              range
                              :tip-formatter="formatter"
                              :min="0"
                              :max="10000000"
                              :step="1000"
                            />
                          </div>
                          <div class="flex gap-x-[16px]">
                            <a-input
                              v-model:value="formFilterState.costRange[0]"
                            >
                              <template #suffix>
                                <CarbonYen class="w-[1rem] h-[1rem]" />
                              </template>
                            </a-input>
                            <a-input
                              v-model:value="formFilterState.costRange[1]"
                            >
                              <template #suffix>
                                <CarbonYen class="w-[1rem] h-[1rem]" />
                              </template>
                            </a-input>
                          </div>
                        </div>
                      </a-tab-pane>
                    </a-tabs>
                  </div>
                  <div class="flex flex-col h-[4.5rem]">
                    <a-divider class="m-3" />
                    <div class="flex justify-between items-center">
                      <a-button
                        class="bg-[#E4E4E2] text-[#74797A]"
                        @click="resetFormFilter"
                      >
                        {{ t("button.reset") }}
                      </a-button>
                      <a-button type="primary" @click="handleFilter">
                        {{
                          t("button.apply")
                        }}
                      </a-button>
                    </div>
                  </div>
                </a-form>
              </template>
              <a-button class="flex items-center gap-x-[5px] text-[1rem]">
                <template #icon>
                  <CarbonFilter class="w-[1rem] h-[1rem]" />
                </template>
                {{ t("button.filter") }}
                <DownOutlined />
              </a-button>
            </a-popover>
            <a-button
              class="text-[1rem] flex items-center"
              @click="handleClearFilter"
            >
              {{ t("button.resetFilter") }}
            </a-button>
            <a-button
              type="primary"
              class="text-[1rem] flex items-center"
              @click="openAddNewModel"
            >
              {{ t("button.new") }}
            </a-button>
          </div>
        </div>
      </a-col>
      <a-col :xxl="24" :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
        <a-row
          :gutter="[24, 24]"
          style="overflow: auto; max-height: calc(100vh - 15rem)"
        >
          <template v-if="sortedProject">
            <template
              v-for="item in sortedProject"
              :key="item"
            >
              <a-col :xxl="8" :xl="12" :lg="12" :md="24" :sm="24" :xs="24">
                <div
                  class="flex flex-col w-full h-full bg-white p-4 text-[0.875rem] font-400 font-noto-sans-jp rounded-[8px] shadow-md gap-y-[4px] overflow-hidden"
                >
                  <div class="flex justify-between">
                    <div class="flex gap-x-[20px]">
                      <div class="flex">
                        <span class="text-[1.25rem] font-500">
                          {{ item.projectCode }}&nbsp;-&nbsp;
                        </span>
                        <span class="text-[1.25rem] font-500">
                          {{ item.projectName }}
                        </span>
                      </div>
                    </div>
                    <div class="flex max-h-[30px]">
                      <a-tag
                        :class="getTagClass(item.statusCode)"
                        class="flex items-center"
                      >
                        {{ item.statusName }}
                      </a-tag>
                      <div class="flex items-center">
                        <a-dropdown>
                          <CarbonMenuDotsVertical />
                          <template #overlay>
                            <a-menu>
                              <a-menu-item @click="openModal(item, false)">
                                <EditOutlined />
                                {{ t("button.edit") }}
                              </a-menu-item>
                              <!-- <a-menu-item>
                                <DeleteOutlined />
                                {{ t('button.delete') }}
                              </a-menu-item> -->
                            </a-menu>
                          </template>
                        </a-dropdown>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center gap-x-[20px]">
                    <div class="flex gap-x-[5px] items-center">
                      <a-avatar
                        src="/icon/description_icon.svg"
                        shape="square"
                        class="w-[0.94rem] h-[0.94rem]"
                      />
                      <a-tooltip>
                        <template #title>
                          {{ item.description }}
                        </template>
                        <span class="text-[#256CB5]">{{
                          t("button.description")
                        }}</span>
                      </a-tooltip>
                    </div>
                    <!-- <div class="text-[#74797A]">
                      <span>{{ t("projectCode") }}:&nbsp;</span>
                      <span>{{ item.projectCode }}</span>
                    </div> -->
                    <div class="text-[#74797A]">
                      <span>{{ t("projectType") }}:&nbsp;</span>
                      <span>{{ item.projectTypeName }}</span>
                    </div>
                  </div>
                  <a-divider class="m-2" />

                  <div class="flex items-center">
                    <span class="text-[#74797A]">{{ t("address") }}:&nbsp;</span>
                    <span>{{ item.address }}</span>
                  </div>

                  <div class="flex">
                    <div class="flex flex-col gap-y-[4px] w-full">
                      <div class="flex gap-x-[8px] text-[#74797A] w-full">
                        <div class="w-1/2">
                          <span>{{ t("expectedPlan") }}</span>
                        </div>
                        <div class="w-1/2">
                          <span>{{ t("actualPlan") }}</span>
                        </div>
                      </div>
                      <div class="flex gap-x-[8px]">
                        <div class="flex items-center gap-x-[4px] w-1/2">
                          <CarbonProjectCalendar2 />
                          <span v-if="item.expectedStartDate">{{
                            item.expectedStartDate
                          }}</span>
                          <span v-else class="text-[#74797A]">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                          <a-avatar
                            v-if="item.expectedEndDate"
                            src="/icon/project_arrow_right_icon.svg"
                            shape="square"
                            class="w-[1.125rem] h-[1.125rem]"
                          />
                          <span v-if="item.expectedEndDate">{{
                            item.expectedEndDate
                          }}</span>
                          <span v-else class="text-[#74797A]">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                        </div>
                        <div class="flex items-center gap-x-[4px] w-1/2">
                          <CarbonProjectCalendar2 />
                          <span v-if="item.actualStartDate">{{
                            item.actualStartDate
                          }}</span>
                          <span v-else class="text-[#74797A]">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                          <a-avatar
                            v-if="item.actualEndDate"
                            src="/icon/project_arrow_right_icon.svg"
                            shape="square"
                            class="w-[1.125rem] h-[1.125rem]"
                          />
                          <span v-if="item.actualEndDate">{{
                            item.actualEndDate
                          }}</span>
                          <span v-else class="text-[#74797A]">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                        </div>
                      </div>
                      <div class="flex gap-x-[8px]">
                        <div class="flex items-center gap-x-[4px] w-1/2">
                          <CarbonCoinYen class="m-[1px]" />
                          <span>{{ item.initialBudget ?? "0" }}</span>
                          <a-avatar
                            src="/icon/project_yen_icon.svg"
                            shape="square"
                            class="w-[1rem] h-[1rem]"
                          />
                        </div>
                        <div class="flex items-center gap-x-[4px] w-1/2">
                          <CarbonCoinYen class="ml-[1px]" />
                          <span>{{ item.actualBudget ?? "0" }}</span>
                          <a-avatar
                            src="/icon/project_yen_icon.svg"
                            shape="square"
                            class="w-[1rem] h-[1rem]"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    class="flex flex-between text-[0.75rem] items-center mt-[18px]"
                  >
                    <div
                      class="flex gap-x-[12px] items-center w-[90%] overflow-x-auto"
                    >
                      <div class="flex items-center gap-x-[8px]">
                        <a-avatar
                          src="/icon/project_user_icon.svg"
                          shape="square"
                          class="w-[1.25rem] h-[1.25rem]"
                        />
                        <span class="text-[#24598E] w-fit">{{ t("button.manager") }}:&nbsp;</span>
                      </div>
                      <div class="flex items-center overflow-hidden">
                        <template
                          v-for="index in Math.min(
                            3,
                            item.managersInfo?.length,
                          )"
                          :key="index"
                        >
                          <a-tag
                            v-if="item.managersInfo[index - 1].isPrimaryManager"
                            class="text-[#DC6000] bg-[#FCE9D2]"
                          >
                            {{ item.managersInfo[index - 1].managerName }}
                          </a-tag>
                          <a-tag v-else class="text-[#256CB5] bg-[#DEF0FF]">
                            {{ item.managersInfo[index - 1].managerName }}
                          </a-tag>
                        </template>
                        <template v-if="item.managersInfo?.length > 3">
                          <a-tooltip color="white">
                            <template #title>
                              <template
                                v-for="(manager, index) in item.managersInfo"
                                :key="index"
                              >
                                <template v-if="index >= 3">
                                  <span
                                    v-if="manager.isPrimaryManager"
                                    class="text-[#DC6000]"
                                  >{{ manager.managerName }}</span>
                                  <span v-else class="text-[#256CB5]">{{
                                    manager.managerName
                                  }}</span>
                                  <span
                                    v-if="index < item.managersInfo.length - 1"
                                    class="text-[#256CB5]"
                                  >,&nbsp;</span>
                                </template>
                              </template>
                            </template>
                            <span class="text-[#74797A] cursor-pointer">+{{ item.managersInfo.length - 3 }}</span>
                          </a-tooltip>
                        </template>
                      </div>
                    </div>
                    <div class="flex items-center">
                      <a-avatar
                        src="/icon/project_view_icon.svg"
                        shape="square"
                        class="w-[1.25rem] h-[1.25rem]"
                      />
                    </div>
                  </div>
                </div>
              </a-col>
            </template>
          </template>
        </a-row>
      </a-col>
      <a-col :xxl="24" :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
        <template
          v-if="sortedProject.length > 0"
        >
          <div class="flex justify-between w-full">
            <a-pagination
              v-model:current="current"
              :page-size="pageSize"
              :total="total"
              class="text-[0.875rem] project-pagination"
            />
            <div
              class="flex items-center gap-x-[8px] text-[#74797A] project-select-page-size"
            >
              <span>{{ t("button.show") }}</span>
              <a-select
                v-model:value="pageSize"
                :options="pageSizeOptions"
                @change="handlePageSizeChange"
              >
                <template #suffixIcon>
                  <CarbonPagninationArrowDown />
                </template>
              </a-select>
              <span>{{ t("button.entries") }}</span>
            </div>
          </div>
        </template>
      </a-col>
    </a-row>
    <a-modal
      v-model:open="isModalOpen"
      width="52rem"
      class="font-noto-sans-jp font-400 text-[0.875rem]"
      @cancel="closeModal"
    >
      <template #title>
        <div
          class="flex items-center justify-center font-500 text-[1.25rem] text-[#1C4771]"
        >
          <span>{{
            isEdit ? t("title.edit-project") : t("title.add-project")
          }}</span>
        </div>
      </template>
      <a-form
        ref="modalFormRef"
        :model="formState"
        layout="vertical"
        class="flex flex-col"
      >
        <div class="flex gap-x-[12px]">
          <a-form-item
            :label="t('projectCode')"
            name="projectCode"
            class="w-[25rem]"
            :rules="[
              {
                required: true,
                message: t('placeholder.enter-data', { msg: t('projectCode') }),
              },
            ]"
          >
            <a-input v-model:value="formState.projectCode" />
          </a-form-item>
          <a-form-item
            :label="t('form.projectName')"
            name="projectName"
            class="w-[25rem]"
            :rules="[
              {
                required: true,
                message: t('placeholder.enter-data', {
                  msg: t('form.projectName'),
                }),
              },
            ]"
          >
            <a-input v-model:value="formState.projectName" />
          </a-form-item>
        </div>
        <div class="flex gap-x-[12px]">
          <a-form-item
            :label="t('form.addressType')"
            name="addressTypeId"
            class="w-[25rem]"
            :rules="[
              {
                required: true,
                message: t('placeholder.select-data', {
                  msg: t('form.addressType'),
                }),
              },
            ]"
          >
            <a-select
              v-model:value="formState.addressTypeId"
              :options="addressTypeCombo"
              allow-clear
            />
          </a-form-item>
          <a-form-item
            :label="t('projectType')"
            name="projectTypeId"
            class="w-[25rem]"
            :rules="[
              {
                required: true,
                message: t('placeholder.select-data', {
                  msg: t('projectType'),
                }),
              },
            ]"
          >
            <a-select
              v-model:value="formState.projectTypeId"
              :options="projectTypeCombo"
              :field-names="{
                label: 'projectTypeName',
                value: 'projectTypeId',
              }"
              allow-clear
            />
          </a-form-item>
        </div>
        <div class="grid grid-cols-2 gap-x-[12px]">
          <a-form-item
            :label="t('form.contractor')"
            name="contractorId"
          >
            <a-select
              v-model:value="formState.contractorId"
              :options="contractorOptions"
              :field-names="{ label: 'contractorName', value: 'contractorId' }"
            />
          </a-form-item>
          <a-form-item
            :label="t('customer')"
            name="customerId"
          >
            <a-select
              v-model:value="formState.customerId"
              :options="customerOptions"
              :field-names="{ label: 'customerName', value: 'customerId' }"
            />
          </a-form-item>
        </div>
        <div class="flex gap-x-[12px]">
          <a-form-item
            :label="t('workshift')"
            name="workShiftIds"
            class="w-[52rem]"
            :rules="[{ required: true }]"
          >
            <a-select
              v-model:value="formState.workShiftIds"
              :options="workshiftOptions"
              :field-names="{ label: 'workShiftName', value: 'workShiftId' }"
              mode="multiple"
              allow-clear
              @change="(value) => {
                if (isEmpty(value)) formState.defaultWorkShift = undefined
              }"
            />
          </a-form-item>
        </div>
        <div class="flex gap-x-[12px]">
          <a-form-item
            :label="t('default-workshift')"
            name="defaultWorkShift"
            class="w-[52rem]"
            :rules="[{ required: true }]"
          >
            <a-select
              v-model:value="formState.defaultWorkShift"
              :options="defaultWorkShiftOptions"
              :field-names="{ label: 'workShiftName', value: 'workShiftId' }"
              allow-clear
            />
          </a-form-item>
        </div>
        <div class="flex gap-x-[12px]">
          <a-form-item
            :label="t('address')"
            name="address"
            class="w-[52rem]"
            :rules="[
              {
                required: true,
                message: t('placeholder.enter-data', {
                  msg: t('address'),
                }),
              },
            ]"
          >
            <a-input
              v-model:value="formState.address"
              allow-clear
            />
          </a-form-item>
          <!-- <a-form-item
            :label="t('projectStructure')"
            name="structureId"
            class="w-[25rem]"
            :rules="[
              {
                required: true,
                message: t('placeholder.select-data', {
                  msg: t('projectStructure'),
                }),
              },
            ]"
          >
            <a-select
              v-model:value="formState.structureId"
              :options="structureListCombo"
              :field-names="{
                label: 'name',
                value: 'id',
              }"
              allow-clear
            />
          </a-form-item> -->
        </div>
        <div class="flex gap-x-[12px]">
          <a-form-item :label="t('expectedDate')" name="expectedDateRange">
            <a-range-picker
              v-model:value="formState.expectedDateRange"
              class="w-[24rem]"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            >
              <template #suffixIcon>
                <a-avatar
                  src="/icon/calendar_project_icon.svg"
                  shape="square"
                  class="w-[1.25rem] h-[1.25rem]"
                />
              </template>
            </a-range-picker>
          </a-form-item>
          <!-- <a-form-item :label="t('actualDate')" name="actualDateRange">
            <a-range-picker
              v-model:value="formState.actualDateRange"
              class="w-[24rem]"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            >
              <template #suffixIcon>
                <a-avatar
                  src="/icon/calendar_project_icon.svg"
                  shape="square"
                  class="w-[1.25rem] h-[1.25rem]"
                />
              </template>
            </a-range-picker>
            <div class="flex w-[25rem] justify-between">
              <a-date-picker />
              <a-date-picker />
            </div>
          </a-form-item> -->
          <a-form-item :label="t('actualDate')" name="actualDateStart">
            <!-- <a-range-picker
              v-model:value="formState.actualDateRange"
              class="w-[24rem]"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            >
              <template #suffixIcon>
                <a-avatar
                  src="/icon/calendar_project_icon.svg"
                  shape="square"
                  class="w-[1.25rem] h-[1.25rem]"
                />
              </template>
            </a-range-picker> -->
            <div class="flex w-[24.2rem] gap-1">
              <a-date-picker
                v-model:value="formState.actualDateStart"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full"
              />
              <ArrowRightOutlined />
              <a-date-picker
                v-model:value="formState.actualDateEnd"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full"
              />
            </div>
          </a-form-item>
        </div>
        <div class="flex gap-x-[12px]">
          <a-form-item
            :label="t('initialBudget')"
            name="initialBudget"
            class="w-[25rem]"
          >
            <a-input v-model:value="formState.initialBudget">
              <template #suffix>
                <a-avatar
                  src="/icon/project_yen_icon.svg"
                  shape="square"
                  class="w-[1rem] h-[1rem]"
                />
              </template>
            </a-input>
          </a-form-item>
          <a-form-item
            :label="t('actualBudget')"
            name="actualBudget"
            class="w-[25rem]"
          >
            <a-input v-model:value="formState.actualBudget">
              <template #suffix>
                <a-avatar
                  src="/icon/project_yen_icon.svg"
                  shape="square"
                  class="w-[1rem] h-[1rem]"
                />
              </template>
            </a-input>
          </a-form-item>
        </div>
        <div class="flex gap-x-[12px]">
          <a-form-item
            :label="t('mainManager')"
            name="primaryManagerEmployeeIds"
            class="w-[25rem]"
            :rules="[
              {
                required: true,
                validator: validateMainManager,
              },
            ]"
          >
            <a-select
              v-model:value="formState.primaryManagerEmployeeIds"
              mode="multiple"
              :options="userListCombo"
              allow-clear
              :field-names="{
                label: 'label',
                value: 'id',
              }"
              option-label-prop="label"
            >
              <template #tagRender="{ label, closable, onClose }">
                <a-tag
                  :closable="closable"
                  class="text-[#DC6000] bg-[#FCE9D2] h-[1.5rem] text-[0.875rem] project-tag"
                  :field-names="{
                    label: 'label',
                    value: 'id',
                  }"
                  option-label-prop="label"
                  @close="onClose"
                >
                  {{ label }}&nbsp;&nbsp;
                </a-tag>
              </template>
            </a-select>
          </a-form-item>
          <a-form-item
            :label="t('subManager')"
            name="subManagerEmployeeIds"
            class="w-[25rem]"
            :rules="[
              {
                required: false,
                validator: validateSubManager,
              },
            ]"
          >
            <!-- <a-select
              v-model:value="formState.subManagerEmployeeIds"
              mode="multiple"
              :options="userListCombo"
              allow-clear
              option-label-prop="label"
            >
              <template #tagRender="{ label, closable, onClose }">
                <a-tag
                  :closable="closable"
                  class="text-[#256CB5] bg-[#DEF0FF] h-[1.5rem] text-[0.875rem] project-tag"
                  :field-names="{
                    label: 'label',
                    value: 'id',
                  }"
                  option-label-prop="label"
                  @close="onClose"
                >
                  {{ label }}&nbsp;&nbsp;
                </a-tag>
              </template>
            </a-select> -->

            <a-select
              v-model:value="formState.subManagerEmployeeIds"
              mode="multiple"
              :options="userListCombo"
              allow-clear
              :field-names="{
                label: 'label',
                value: 'id',
              }"
              option-label-prop="label"
            >
              <template #tagRender="{ label, closable, onClose }">
                <a-tag
                  :closable="closable"
                  class="text-[#256CB5] bg-[#DEF0FF] h-[1.5rem] text-[0.875rem] project-tag"
                  :field-names="{
                    label: 'label',
                    value: 'id',
                  }"
                  option-label-prop="label"
                  @close="onClose"
                >
                  {{ label }}&nbsp;&nbsp;
                </a-tag>
              </template>
            </a-select>
          </a-form-item>
        </div>
        <div class="flex gap-x-[12px]">
          <div class="flex w-[25rem] gap-x-[10px]">
            <span>{{ t("project.status") }}</span>
            <a-form-item
              name="statusCode"
              :rules="[
                {
                  required: true,
                  message: t('placeholder.select-data', { msg: t('status') }),
                },
              ]"
            >
              <a-radio-group
                v-model:value="formState.statusCode"
                class="flex flex-col gap-y-[6px] project-radio"
              >
                <a-radio value="PLANNED">
                  {{ t("status.planed") }}
                </a-radio>
                <a-radio value="STARTED">
                  {{ t("status.started") }}
                </a-radio>
                <a-radio value="ENDED">
                  {{ t("status.ended") }}
                </a-radio>
              </a-radio-group>
            </a-form-item>
          </div>
          <a-form-item
            :label="t('description')"
            name="description"
            class="w-[25rem]"
            :rules="[
              {
                required: false,
                message: t('placeholder-enter-data', { msg: t('description') }),
              },
            ]"
          >
            <a-textarea v-model:value="formState.description" />
          </a-form-item>
        </div>
      </a-form>
      <template #footer>
        <a-button @click="closeModal">
          {{ t("button.cancel") }}
        </a-button>
        <a-button type="primary" @click="handleOk">
          <PlusOutlined v-if="!isEdit" />
          <FormOutlined v-else />
          {{ isEdit ? t("button.update") : t("button.create") }}
        </a-button>
      </template>
    </a-modal>
  </page-container>
</template>

<style scoped></style>
