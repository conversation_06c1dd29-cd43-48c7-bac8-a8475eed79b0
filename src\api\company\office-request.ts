export interface EmployeeAttendanceItem {
  employeeId: string
  employeeName: string
  code: string
  workTime: number
  overtime: number
  workDays: number
  offdays: number
  usedLeaves: number
  remainLeaves: number
  comment: string | null
  isRequested: boolean
  status: string | null
}

export interface MonthlyAttendancePaginatedResponse {
  items: EmployeeAttendanceItem[]
  pageNum: number
  pageSize: number
  totalRecords: number
}

export interface MonthlyAttendanceParams {
  dateFrom: string
  dateTo: string
  pageNum: number
  pageSize: number
  keyword?: string
}

export interface TimePeriodParams {
  dateFrom: string
  dateTo: string
}

export interface UserShiftItem {
  projectName: string
  workingLocation: string
  checkInTime: string
  checkOutTime: string
  workHours: number
  overtime: number
  description: string | null
  isRequested: boolean
  isApproved: boolean | null
}
export interface CalendarInfoItem {
  date: string
  shiftInfos: UserShiftItem[]
  leaveInfos: any[]
}

export interface UserAttendanceData {
  employeeId: string
  employeeName: string
  code: string
  calendar: CalendarInfoItem[]
}

export async function getMonthlyAttendanceApi(params: MonthlyAttendanceParams) {
  return useGet<MonthlyAttendancePaginatedResponse>('v1/monthlyattendance', params)
}

export async function getMonthlyAttendanceByUserApi(employeeId: string, params: TimePeriodParams) {
  return useGet<UserAttendanceData>(`v1/monthlyattendance/${employeeId}`, params)
}
