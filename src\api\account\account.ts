import type {
  AcceptInvitationParams,
  AccountResponse,
  InvitationResponse,
  UpdateAccountParams,
  UserAccountParams,
} from '~@/types/account'

const accountApi = {
  getAccountByEmail: 'v1/account/email',
  checkAccountExistByIdentity: 'v1/account/identity',
  createAccount: 'v1/account',
  updateAccount: 'v1/account',
  getOTP: 'v1/auth/verification/email',
  getInvitations: 'v1/account/invitation',
  acceptInvitation: 'v1/account/invitation/accept',
}

export async function getAccountByEmail(params: { email: string }) {
  return useGet<AccountResponse>(accountApi.getAccountByEmail, params)
}

// Check account exist by identity (email | loginId)
export async function checkAccountExistByIdentity(params: { identityInfo: string }) {
  return useGet<any>(accountApi.checkAccountExistByIdentity, params)
}

export async function createAccount(params: UserAccountParams) {
  return usePost<any>(accountApi.createAccount, params)
}

export async function updateAccount(params: UpdateAccountParams) {
  return usePut<any>(accountApi.updateAccount, params)
}

export async function getOTP(params: { email: string }) {
  return useGet<any>(accountApi.getOTP, params)
}

export async function getInvitationsApi() {
  return useGet<InvitationResponse>(accountApi.getInvitations)
}

export async function acceptInvitationApi(params: AcceptInvitationParams) {
  return usePost<boolean>(accountApi.acceptInvitation, params)
}
