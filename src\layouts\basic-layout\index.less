:root {
  --row-height: 32px;
  --font-size: 14px;
  --table-header-bg-color: #f2f8fd;
  --table-header-text-color: #24598E;
}

.ant-pro-basicLayout {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100%;

  // .ant-layout {
  //   background: #153554;
  // }

  &-content {
    padding: 1rem;
    // border-top-left-radius: 16px;
    // background-color: var(--bg-color-container);
  }
  .ant-card-head {
    font-size: 12px;
    min-height: 38px;
    padding: 12px;
  }
  .ant-card .ant-card-body {
    padding: 12px;
  }
  .ant-typography strong {
    font-size: 12px;
  }
  // .ant-table-wrapper .ant-table-thead >tr>th,
  // .ant-table-wrapper .ant-table-thead >tr>th:hover{
  //   background-color: #DEF0FF;
  //   color:#24598E;
  // }

  .ant-table-thead > tr{
    th {
      padding: 12px;
      font-size: var(--font-size);
      font-weight: 600;
      background-color: var(--table-header-bg-color);
      color: var(--table-header-text-color);
    }
  }

  // Custom attendance table table
  .custom-antd-table {
    .ant-table-tbody > tr{
      height: var(--row-height);
  
      td {
        padding: 4px 8px;
        font-size: var(--font-size);
      }
    }
  
    .ant-table-thead > tr{
      th {
        padding: 12px;
        font-size: var(--font-size);
        font-weight: 600;
        background-color: var(--table-header-bg-color);
        color: var(--table-header-text-color);
      }
    }
  
    .ant-table-tbody > tr.row-active{
      background: #f2f8fd;
      td:first-child {
        border-left: 2px solid #b7d7f2;
      }
      td[rowspan] + td {
        border-left: 2px solid #b7d7f2;
      }
      td[rowspan] ~ td {
        border-top: 2px solid #b7d7f2 !important;
      }
      td[rowspan] {
        border-top: 2px solid #b7d7f2 !important;
      }
      td {
        background: #f2f8fd;
        border-bottom: 2px solid #b7d7f2 !important;
      }
    }
    .ant-table-tbody > tr.row-active.row-alone{
      td {
        border-top: 2px solid #b7d7f2 !important;
      }
    }
  }
  // Custom table header in role page
  .role-custom-table {
    .ant-table.ant-table-bordered
      > .ant-table-container  
      > .ant-table-content
      > table {
      // width: 100% !important;
      text-align: center;
      font-size: 12px;
      > thead > tr > th,
      > tbody > tr > td {
        border-inline-end: 1px solid #dedede;
        min-width: 80px;
      }
    }
    .ant-table.ant-table-bordered .ant-table-tbody > tr > td {
      border-bottom: 1px solid #dedede;
      padding: 12px;
      &:first-child {
        border-inline-start: 1px solid #dedede;
      }
    }

    // Custom table header in role page
    .ant-table-thead > tr > th {
      background-color: #E4E4E2;
      color: #74797A;
      font-size: 1rem;
      border-right: 1px solid #dedede;
      border-top: 1px solid #dedede;
      min-width: 80px;
      text-align: center;

      &:first-child {
        border-left: 1px solid #dedede;
      }
    }
    .ant-table:not(.ant-table-bordered) .ant-table-tbody > tr > td {
      border-right: 1px solid #dedede;
      border-bottom: 1px solid #dedede;
      border-top: none;

      &:first-child {
        border-left: 1px solid #dedede;
      }
    }
  }

  @media screen and (max-width: 480px) {
    .ant-layout .ant-layout-content {
      margin: 0;
    }
    .ant-card .ant-card-body {
      padding: 12px;
    }
    .ant-table-wrapper {
      .ant-table.ant-table-bordered
        > .ant-table-container
        > .ant-table-content
        > table {
        > thead > tr > th,
        > tbody > tr > td {
          padding: 5px;
          text-align: center;
          min-width: 65px;
        }
      }
    }
    .ant-table-wrapper .ant-table-thead > tr > th {
      padding: 5px;
      min-width: 65px;
    }
  }
}

[data-theme="dark"] {
  .ant-layout {
    background: rgb(42, 44, 44);
  }
}

