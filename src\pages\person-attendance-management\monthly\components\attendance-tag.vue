<!-- eslint-disable antfu/top-level-function -->
<script setup lang="ts">
import type { CSSProperties } from 'vue'

const props = defineProps({
  workingStatusType: {
    type: String,
  },
  workingCount: {
    type: Number,
  },
})

const { t } = useI18n()

enum AttendanceTagColorEnum {
  BACKGROUND_COLOR = '#a9d3b0',
  HEAD_COLOR = '#106b1e',
  FONT_COLOR = '#438e4e',
}

enum RequestTagColorEnum {
  BACKGROUND_COLOR = '#fce9d2',
  HEAD_COLOR = '#dc6000',
  FONT_COLOR = '#dc6001',
}

enum WorkingDayTagColorEnum {
  BACKGROUND_COLOR = '#d6edff',
  HEAD_COLOR = '#256cb5',
  FONT_COLOR = '#256cb6',
}

enum AbsentTagColorEnum {
  BACKGROUND_COLOR = '#e6e6e6',
  HEAD_COLOR = '#3a3b3c',
  FONT_COLOR = '#3a3b3b',
}

const getAttendanceTagStyle = () => {
  const style: CSSProperties = {}
  style.backgroundClip = 'border-box'
  if (props.workingStatusType === 'attendance')
    style.backgroundColor = AttendanceTagColorEnum.BACKGROUND_COLOR
  if (props.workingStatusType === 'request')
    style.backgroundColor = RequestTagColorEnum.BACKGROUND_COLOR
  if (props.workingStatusType === 'working-day')
    style.backgroundColor = WorkingDayTagColorEnum.BACKGROUND_COLOR
  if (props.workingStatusType === 'absent')
    style.backgroundColor = AbsentTagColorEnum.BACKGROUND_COLOR
  return style
}

const getAttendanceTagHeadStyle = () => {
  const style: CSSProperties = {}
  if (props.workingStatusType === 'attendance')
    style.backgroundColor = AttendanceTagColorEnum.HEAD_COLOR
  if (props.workingStatusType === 'request')
    style.backgroundColor = RequestTagColorEnum.HEAD_COLOR
  if (props.workingStatusType === 'working-day')
    style.backgroundColor = WorkingDayTagColorEnum.HEAD_COLOR
  if (props.workingStatusType === 'absent')
    style.backgroundColor = AbsentTagColorEnum.HEAD_COLOR
  style.width = '3%'
  return style
}

const getAttendanceTagTailStyle = () => {
  const style: CSSProperties = {}
  style.display = 'flex'
  style.justifyContent = 'center'
  style.alignItems = 'center'
  style.width = '97%'
  style.height = '100%'
  return style
}

const getAttendanceTagSpanStyle = () => {
  const style: CSSProperties = {}
  if (props.workingStatusType === 'attendance')
    style.color = AttendanceTagColorEnum.FONT_COLOR
  if (props.workingStatusType === 'request')
    style.color = RequestTagColorEnum.FONT_COLOR
  if (props.workingStatusType === 'working-day')
    style.color = WorkingDayTagColorEnum.FONT_COLOR
  if (props.workingStatusType === 'absent')
    style.color = AbsentTagColorEnum.FONT_COLOR
  style.fontSize = 'clamp(13px, 0.8vw, 15px)'
  style.fontWeight = '400'
  style.lineHeight = '24px'
  return style
}
</script>

<template>
  <div class="attendance-tag" :style="getAttendanceTagStyle()">
    <div :style="getAttendanceTagHeadStyle()" />
    <div :style="getAttendanceTagTailStyle()">
      <span :style="getAttendanceTagSpanStyle()">{{ t(`calendar.${workingStatusType}`) }} {{ workingCount && `(${workingCount})` }}</span>
    </div>
  </div>
</template>

<style scoped>
.attendance-tag {
  display: flex;
  height: calc(0.5vw + 15px);
}

.attendance-tag:hover {
  background-color: #ccc;
}
</style>
