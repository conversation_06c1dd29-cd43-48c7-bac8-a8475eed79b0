<script setup lang="ts">
import { Empty } from 'ant-design-vue';
import dayjs from 'dayjs';
import calendar from 'dayjs/plugin/calendar';
import updateLocale from 'dayjs/plugin/updateLocale';
import {
  getNotificationEmployee,
  NotificationEmployeeItem,
  readAllNotifications,
  readNotification,
} from '~@/api/company/notification';
import logger from '~@/utils/logger';

dayjs.extend(calendar);
dayjs.extend(updateLocale);

dayjs.updateLocale('en', {
  calendar: {
    sameDay: '[Today]',
    lastDay: '[Yesterday]',
    nextDay: '[Tomorrow]',
    nextWeek: 'dddd',
    lastWeek: 'dddd',
    sameElse: 'DD/MM/YYYY',
  },
});

dayjs.updateLocale('ja', {
  calendar: {
    sameDay: '[今日]',
    lastDay: '[昨日]',
    nextDay: '[明日]',
    nextWeek: 'dddd',
    lastWeek: 'dddd',
    sameElse: 'YYYY/MM/DD',
  },
});

const { t } = useI18n();
const notificationGroup = ref<Record<string, NotificationEmployeeItem[]>>({});
const isRead = ref();
const isLoading = ref(false);
const listRef = ref();
const page = ref(1);
const limit = ref(10);
const noMoreContent = ref(false);
const loadInitialData = ref(false);
const hasNotification = ref(false);

useInfiniteScroll(
  listRef,
  async () => {
    if (!loadInitialData.value) return;
    await loadData(page.value + 1, limit.value);
  },
  {
    distance: 50,
    canLoadMore: () => {
      if (noMoreContent.value) return false;
      return true;
    },
  }
);

const loadData = async (pageNum: number, pageSize: number) => {
  const res = await getNotificationEmployee({
    pageNum,
    pageSize,
    isRead: isRead.value,
  });
  notificationGroup.value = (res.data?.items ?? []).reduce((acc, item) => {
    const dateKey = dayjs(item.createdTime).format('YYYY-MM-DD');
    if (!acc[dateKey]) {
      acc[dateKey] = [];
    }
    acc[dateKey].push(item);
    return acc;
  }, notificationGroup.value);
  notificationGroup.value = Object.fromEntries(
    Object.entries(notificationGroup.value).sort(
      ([keyA], [keyB]) => dayjs(keyB).unix() - dayjs(keyA).unix()
    )
  );
  noMoreContent.value = (res.data?.items || []).length < pageSize;
  page.value = pageNum;
  limit.value = pageSize;
  loadInitialData.value = true;
  hasNotification.value = Object.values(notificationGroup.value).some((items) =>
    items.some((item) => !item.isRead)
  );
};

const handleReadNotification = async (item: NotificationEmployeeItem) => {
  try {
    await readNotification(item.notificationId);
    item.isRead = true;
  } catch (error) {
    logger.error(error);
  }
};

const handleChange = async (visible: boolean) => {
  if (visible) {
    notificationGroup.value = {};
    noMoreContent.value = false;
    isLoading.value = true;
    await loadData(1, limit.value);
    isLoading.value = false;
  } else {
    loadInitialData.value = false;
  }
};

const handleMarkAllAsRead = async () => {
  try {
    isLoading.value = true;
    await readAllNotifications();
    notificationGroup.value = {};
    await loadData(1, limit.value);
    isLoading.value = false;
  } catch (error) {
    logger.error(error);
  }
};

const handleAllRead = async () => {
  isRead.value = undefined;
  isLoading.value = true;
  notificationGroup.value = {};
  noMoreContent.value = false;
  loadInitialData.value = false;
  await loadData(1, limit.value);
  isLoading.value = false;
};

const handleUnread = async () => {
  isRead.value = false;
  isLoading.value = true;
  notificationGroup.value = {};
  noMoreContent.value = false;
  loadInitialData.value = false;
  await loadData(1, limit.value);
  isLoading.value = false;
};

onMounted(async () => {
  await loadData(1, limit.value);
});
</script>

<template>
  <span
    hover="bg-[var(--hover-color)]"
    flex
    justify-center
    cursor-pointer
    class="transition-all-300"
  >
    <a-popover
      :overlay-inner-style="{ padding: 0 }"
      trigger="click"
      @open-change="handleChange"
    >
      <template #content>
        <div class="w-[360px]">
          <div class="p-4">
            <span class="text-base font-normal">
              {{ $t('notification') }}
            </span>
          </div>
          <div class="px-4 pb-4">
            <div class="flex justify-between items-center">
              <div class="flex gap-2">
                <a-button
                  :type="isRead === undefined ? 'primary' : 'default'"
                  size="small"
                  @click="handleAllRead"
                >
                  {{ t('all') }}
                </a-button>
                <a-button
                  :type="isRead === false ? 'primary' : 'default'"
                  size="small"
                  @click="handleUnread"
                >
                  {{ t('unread') }}
                </a-button>
              </div>
              <div
                class="flex gap-1 cursor-pointer"
                @click="handleMarkAllAsRead"
              >
                <img src="/icon/check-outline.svg" /> {{ t('mark-all-as-read') }}
              </div>
            </div>
          </div>
          <a-spin :spinning="isLoading">
            <div class="h-[32rem] overflow-y-auto" ref="listRef">
              <a-empty
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
                v-if="Object.keys(notificationGroup).length === 0"
              />
              <div
                class="px-4"
                :class="{ 'py-4': index !== 0 }"
                v-for="([key, items], index) in Object.entries(
                  notificationGroup
                )"
                v-else
              >
                <span class="text-sm font-normal text-[#74797A]">
                  {{ dayjs(key).calendar() }}
                </span>
                <div
                  v-for="item in items"
                  class="p-2 hover:bg-[#E4E4E2] rounded-md cursor-pointer"
                  @click="handleReadNotification(item)"
                >
                  <div class="flex justify-between items-center">
                    <div>
                      <div class="text-sm font-bold">{{ item.title }}</div>
                      <div
                        class="flex items-center justify-center gap-2 text-xs font-normal text-[#74797A]"
                      >
                        <span>
                          {{ dayjs(item.createdTime).format('DD MMM, 2025') }}
                        </span>
                        <div class="flex items-center justify-center gap-1">
                          <img class="w-3.5 h-3.5" src="/icon/clock.svg" />
                          <span>
                            {{ dayjs(item.createdTime).format('HH:mm') }}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <img
                        v-if="item.isRead"
                        src="/icon/check-outline-blue.svg"
                        class="w-3.5 h-3.5"
                      />
                      <div v-else class="bg-[#0e78d3] w-3 h-3 rounded-full" />
                    </div>
                  </div>
                </div>
              </div>
              <a-skeleton-button
                class="p-4"
                active
                block
                v-if="noMoreContent === false && !isLoading"
              />
            </div>
          </a-spin>
        </div>
      </template>
      <a-avatar :size="48" shape="square">
        <template #icon>
          <img
            v-if="hasNotification"
            src="/noti-yes.svg"
            alt="Flag"
            class="flag-icon"
          />
          <img v-else src="/noti-no.svg" alt="Flag" class="flag-icon" />
        </template>
      </a-avatar>
    </a-popover>
  </span>
</template>

<style lang="less" scoped>
.popover-noti {
  :deep(.ant-popover-inner) {
    padding: 0;
  }
}
</style>
