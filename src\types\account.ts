export interface UserInfoItem {
  name?: string
  address?: string
  phone?: string
  gender?: boolean // true for male, false for female, or you can define as per your requirements
  birthday?: string
}

export interface UserAccountParams {
  email: string
  loginId: string
  password: string
  hashedPassword?: string // KO CẦN THIẾT PHẢI GỬI LÊN API
  otp: string
  userInfo?: UserInfoItem
}

export interface UpdateAccountParams {
  email: string
  loginId: string
}

export interface InviteAccountParams {
  email: string
  employeeCode: string
  roleIds: string[]
  invitationDescription?: string
}

export interface AccountResponse {
  accountUid: string
  userName: string
}

export interface InvitationItem {
  invitationId: string
  orgName: string
  orgId: string
  expireTime: string
  description: string
}
export interface InvitationResponse {
  items: InvitationItem[]
}

export interface AcceptInvitationParams {
  invitationId: string
  isAccept: boolean
}
