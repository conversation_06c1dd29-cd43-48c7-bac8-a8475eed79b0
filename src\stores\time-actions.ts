import { defineStore } from 'pinia'
import logger from '~@/utils/logger'

export interface TimeActionState {
  workplaceId: string | undefined
  projectId: string | undefined
  checkInState: boolean
  checkOutState: boolean
  breakInState: boolean
  breakOutState: boolean
  // Thêm các thuộc tính khác nếu cần
}

export const useTimeActionStore = defineStore('time-actions', {
  state: (): TimeActionState => {
    const savedState = localStorage.getItem('time-action-state')
    logger.log('savedState', savedState)
    if (savedState)
      return JSON.parse(savedState)
    return {
      workplaceId: undefined,
      projectId: undefined,
      checkInState: false,
      checkOutState: false,
      breakInState: false,
      breakOutState: false,
    }
  },

  actions: {
    setWorkplaceId(payload: string | undefined) {
      this.workplaceId = payload
    },
    setProjectId(payload: string | undefined) {
      this.projectId = payload
    },
    setCheckInState(payload: boolean) {
      this.checkInState = payload
    },
    setCheckOutState(payload: boolean) {
      this.checkOutState = payload
    },
    setBreakInState(payload: boolean) {
      this.breakInState = payload
    },
    setBreakOutState(payload: boolean) {
      this.breakOutState = payload
    },
    // Thêm các phương thức khác nếu cần
  },
})
