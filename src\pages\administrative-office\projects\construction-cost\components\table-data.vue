<script setup lang="ts">
import FormulaTooltip from './fomula-tooltip.vue'
import type { CostAmountItem } from '~@/api/construction-cost'
import type { ConstructionType } from '~@/utils/constant'

defineProps({
  costAmount: {
    type: Object as () => CostAmountItem,
    required: false,
  },
})

const { t } = useI18n()

onMounted(() => {
  // Cần 1 API call đến -> construction/{id}
})

// Format number to Japanese currency format
function formatCurrency(value: number): string {
  if (!value)
    return '¥0'
  return new Intl.NumberFormat('ja-JP', {
    style: 'currency',
    currency: 'JPY',
    currencyDisplay: 'symbol',
    maximumFractionDigits: 0,
  }).format(value).replace('¥', '¥')
}

// Format percentage
function formatPercentage(value: number): string {
  if (!value)
    return '0.00 %'
  return `${value.toFixed(2)} %`
}

function getTitle(timeState: 'LAST' | 'CURRENT' | 'ACCUMULATED', type?: ConstructionType) {
  const key = `${timeState}_${type}`
  let result = ''
  switch (key) {
    case 'LAST_MAIN':
      result = '前回までの進捗率(本工）'
      break
    case 'LAST_SUB':
      result = '前回までの進捗率(分工）'
      break
    case 'LAST_OVERALL':
      result = '前回までの進捗率(総合）'
      break
    case 'CURRENT_MAIN':
      result = '今回の進捗率(本工）'
      break
    case 'CURRENT_SUB':
      result = '今回の進捗率(分工）'
      break
    case 'CURRENT_OVERALL':
      result = '今回の進捗率(総合）'
      break
    case 'ACCUMULATED_MAIN':
      result = '累計進捗率(本工）'
      break
    case 'ACCUMULATED_SUB':
      result = '累計進捗率(分工）'
      break
    case 'ACCUMULATED_OVERALL':
      result = '累計進捗率(総合）'
      break
    default:
      break
  }
  return result
}

function getNumberator(timeState: 'LAST' | 'CURRENT' | 'ACCUMULATED', type?: ConstructionType) {
  const key = `${timeState}_${type}`
  let result = ''
  switch (key) {
    case 'LAST_MAIN':
      result = '前回までの出来高金額(本工）'
      break
    case 'LAST_SUB':
      result = '前回までの出来高金額(分工）'
      break
    case 'LAST_OVERALL':
      result = '前回までの出来高金額(総合）'
      break
    case 'CURRENT_MAIN':
      result = '今回の出来高金額(本工）'
      break
    case 'CURRENT_SUB':
      result = '今回の出来高金(分工）'
      break
    case 'CURRENT_OVERALL':
      result = '今回の出来高金額(総合）'
      break
    case 'ACCUMULATED_MAIN':
      result = '累計出来高金額(本工）'
      break
    case 'ACCUMULATED_SUB':
      result = '累計出来高金額(分工）'
      break
    case 'ACCUMULATED_OVERALL':
      result = '累計出来高金額(総合）'
      break
    default:
      break
  }
  return result
}

function getDenominator(type?: ConstructionType) {
  let result = ''
  switch (type) {
    case 'MAIN':
      result = '契約金額の本工事費'
      break
    case 'SUB':
      result = '契約金額の別途工事費'
      break
    case 'OVERALL':
      result = '契約金額の総合工事費'
      break
    default:
      break
  }
  return result
}
</script>

<template>
  <div class="w-full">
    <table class="w-full border-collapse border border-gray-400 border-solid">
      <!-- Header -->
      <thead>
        <tr>
          <th colspan="7" class="bg-gray-100 border border-gray-400 border-solid p-2 text-center font-bold">
            <span v-if="costAmount?.type === 'MAIN'"> {{ t('mainConstructionCostAmount') }}</span>
            <span v-else-if="costAmount?.type === 'SUB'"> {{ t('subConstructionCostAmount') }} </span>
            <span v-else-if="costAmount?.type === 'OVERALL' "> {{ t('overallConstructionCostAmount') }} </span>
          </th>
        </tr>
      </thead>

      <!-- Column Headers -->
      <thead>
        <tr>
          <th class="p-2 bg-gray-50 border border-gray-400 border-solid">
            {{ t('previousAmount') }}
          </th>
          <th class="p-2 bg-gray-50 border border-gray-400 border-solid">
            {{ t('processRate') }}
          </th>
          <th class="p-2 bg-blue-100 border border-gray-400 border-solid">
            {{ t('currentAmount') }}
          </th>
          <th class="p-2 bg-gray-50 border border-gray-400 border-solid">
            {{ t('processRate') }}
          </th>
          <th class="p-2 bg-green-100 border border-gray-400 border-solid">
            {{ t('accumulatedAmount') }}
          </th>
          <th class="p-2 bg-gray-50 border border-gray-400 border-solid">
            {{ t('processRate') }}
          </th>
          <th class="p-2 bg-orange-100 border border-gray-400 border-solid">
            {{ t('remainingBalance') }}
          </th>
        </tr>
      </thead>

      <!-- Data Row -->
      <tbody>
        <tr>
          <td class="p-2 bg-gray-50 border border-gray-400 border-solid text-center">
            {{ formatCurrency(costAmount?.previousAmount ?? 0) }}
          </td>
          <td class="p-2 bg-gray-50 border border-gray-400 border-solid text-center">
            <FormulaTooltip
              :title="getTitle('LAST', costAmount?.type)"
              :numerator="getNumberator('LAST', costAmount?.type)"
              :denominator="getDenominator(costAmount?.type)"
              :value-percentage="formatPercentage(costAmount?.previousProgressPercentage ?? 0)"
            />
          </td>
          <td class="p-2 bg-blue-100 text-red-600 font-bold border border-gray-400 border-solid text-center">
            {{ formatCurrency(costAmount?.currentAmount ?? 0) }}
          </td>
          <td class="p-2 bg-gray-50 border border-gray-400 border-solid text-center">
            <FormulaTooltip
              :title="getTitle('CURRENT', costAmount?.type)"
              :numerator="getNumberator('CURRENT', costAmount?.type)"
              :denominator="getDenominator(costAmount?.type)"
              :value-percentage="formatPercentage(costAmount?.currentProgressPercentage ?? 0)"
            />
          </td>
          <td class="p-2 bg-green-100 border border-gray-400 border-solid text-center">
            {{ formatCurrency(costAmount?.accumulatedAmount ?? 0) }}
          </td>
          <td class="p-2 bg-gray-50 border border-gray-400 border-solid text-center">
            <FormulaTooltip
              :title="getTitle('ACCUMULATED', costAmount?.type)"
              :numerator="getNumberator('ACCUMULATED', costAmount?.type)"
              :denominator="getDenominator(costAmount?.type)"
              :value-percentage="formatPercentage(costAmount?.accumulatedProgressPercentage ?? 0)"
            />
          </td>
          <td class="p-2 bg-orange-100 text-red-600 font-bold border border-gray-400 border-solid text-center">
            {{ formatCurrency(costAmount?.remainingBalance ?? 0) }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
