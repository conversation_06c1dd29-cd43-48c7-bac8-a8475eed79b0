import type { EmployeeItem, EmployeeQueryParams } from '~@/api/employee/employee'
import { getEmployeeDataApi } from '~@/api/employee/employee'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'

export function useEmployee() {
  const employeeData = ref<EmployeeItem[]>([])
  const fetchEmployee = async (params: EmployeeQueryParams) => {
    const { data, status } = await getEmployeeDataApi(params)
    if (status === ResponseStatusEnum.SUCCESS) {
      employeeData.value.push(...data?.items ?? [])
      return data
    }
    return null
  }

  return {
    employeeData,
    fetchEmployee,
  }
}
