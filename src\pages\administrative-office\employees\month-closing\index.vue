<!-- eslint-disable antfu/top-level-function -->
<!-- eslint-disable curly -->
<script lang="ts" setup>
import { usePagination } from 'vue-request'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'
import type { EmployeeAttendanceItem, MonthlyAttendanceParams } from '~@/api/company/office-request'
import { getMonthlyAttendanceApi } from '~@/api/company/office-request'
import logger from '~@/utils/logger'
import { useAvatarStore } from '~@/stores/avatar'

const { t } = useI18n()
const avatarStore = useAvatarStore()
const router = useRouter()

const currentEmployeeInfo = ref<EmployeeAttendanceItem>()
const selectedMonth = ref<Dayjs>(dayjs())
const searchValue = ref('')
const REDIRECT_NAME = 'MonthClosingDetail'

// interface Params {
//   pageNum?: number
//   pageSize?: number
//   [key: string]: any
// }
// const searchForm = ref<Params>({
//   pageSize: 100,
//   pageNum: 1,
//   status: true,
// })

const isRouteExist = computed(() => {
  return router.getRoutes().some(route => route.name === REDIRECT_NAME)
})

async function queryData(params: MonthlyAttendanceParams) {
  try {
    const { data, status, code } = await getMonthlyAttendanceApi(params)
    if (status === ResponseStatusEnum.SUCCESS) {
      return data
    }
    else {
      logger.error(code)
      return undefined
    }
  }
  catch (e) {
    logger.error(e)
  }
}

// async function getMonthlyAttendanceByUser(userId: string) {
//   const params: TimePeriodParams = {
//     dateFrom: selectedMonth.value.startOf('month').format('YYYY-MM-DD'),
//     dateTo: selectedMonth.value.endOf('month').format('YYYY-MM-DD'),
//   }
//   try {
//     const { data, status, code } = await getMonthlyAttendanceByUserApi(userId, params)
//     if (status === ResponseStatusEnum.SUCCESS) {
//       calendarData.value = data?.calendar ?? []
//       logger.log('calendarData', calendarData)
//       return data
//     }
//     else {
//       logger.error(code)
//       return undefined
//     }
//   }
//   catch (e) {
//     logger.error(e)
//   }
// }

const {
  data: dataSource,
  loading,
  total,
  current,
  pageSize,
  run,
  changeCurrent,
} = usePagination(queryData, {
  defaultParams: [{
    dateFrom: selectedMonth.value.startOf('month').format('YYYY-MM-DD'),
    dateTo: selectedMonth.value.endOf('month').format('YYYY-MM-DD'),
    pageNum: 1,
    pageSize: 20,
  }],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const columns: any = computed(() => {
  return [
    {
      title: t('employeeName'),
      dataIndex: 'employeeName',
      key: 'employeeName',
      width: 200,
      fixed: 'left',
      // sorter: (a: UserItem, b: UserItem) => a.name.length - b.name.length,
    },
    {
      title: t('workTime'),
      dataIndex: 'workTime',
      key: 'workTime',
      width: 100,
    },
    {
      title: t('overtime'),
      dataIndex: 'overtime',
      key: 'overtime',
      sorter: true,
      width: 100,
    },
    {
      title: t('workDays'),
      dataIndex: 'workDays',
      key: 'workDays',
      width: 100,
      // customRender: ({ record }: any) => {
      //   if (!record.birthday)
      //     return t('no-data')
      //   return dayjs(record.birthday, 'YYYY-MM-DD').format('YYYY-MM-DD')
      // },
    },
    {
      title: t('offdays'),
      dataIndex: 'offdays',
      key: 'offdays',
      align: 'center',
      width: 100,
    },

    {
      title: t('usedLeaves'),
      dataIndex: 'usedLeaves',
      key: 'usedLeaves',
      align: 'center',
      width: 100,
    },
    {
      title: t('remainLeaves'),
      dataIndex: 'remainLeaves',
      key: 'remainLeaves',
      align: 'center',
      width: 100,
    },
    {
      title: t('comment'),
      dataIndex: 'comment',
      key: 'comment',
      align: 'center',
      width: 100,
    },
    {
      title: t('isRequested'),
      dataIndex: 'isRequested',
      key: 'isRequested',
      align: 'center',
      width: 100,
    },
    {
      title: t('status'),
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: 100,
    },
    {
      title: t('action'),
      dataIndex: 'actions',
      key: 'actions',
      align: 'center',
      width: 100,
      fixed: 'right',
    },
  ]
})

// const onSearch = () => {
//   const filter: any = {
//     ...searchForm.value,
//   }

//   if (filter.gender != null)
//     filter.gender = filter.gender === 1

//   if (!filter.status)
//     delete filter.status

//   // if (searchForm.value.type != null)
//   //   filter.type = searchForm.value.type == 0 ? false : true;
//   logger.log(filter)
//   handleTableChange(
//     {
//       pageSize: 10,
//       current: 1,
//     },
//     filter,
//   )
// }

const pageSizeOptions = [
  {
    label: '10',
    value: 10,
  },
  {
    label: '20',
    value: 20,
  },
  {
    label: '50',
    value: 50,
  },
  {
    label: '100',
    value: 100,
  },
]

const onChangeWeek = (changeType: 'prev' | 'next') => {
  currentEmployeeInfo.value = undefined
  if (changeType === 'prev') {
    selectedMonth.value = selectedMonth.value.subtract(1, 'month')
  }
  else {
    selectedMonth.value = selectedMonth.value.add(1, 'month')
  }
  changeCurrent(1)
}

const handlePageSizeChange = () => {
  run({
    pageNum: 1,
    pageSize: pageSize.value,
    keyword: searchValue.value,
    dateFrom: selectedMonth.value.startOf('month').format('YYYY-MM-DD'),
    dateTo: selectedMonth.value.endOf('month').format('YYYY-MM-DD'),
  })
}

// const onViewDetail = async (record: EmployeeAttendanceItem) => {
//   currentEmployeeInfo.value = {
//     ...record,
//   }
//   logger.log('currentEmployeeInfo: ', currentEmployeeInfo.value)
//   if (!record.employeeId)
//     return
//   calendarData.value = []
//   selectedCalendarInfoItem.value = undefined
//   selectedDate.value = 0
//   await getMonthlyAttendanceByUser(record.employeeId)
// }

// const onSelectDate = (date: number) => {
//   selectedDate.value = date
//   logger.log('date: ', date)
//   selectedCalendarInfoItem.value = calendarData.value[date - 1]
//   logger.log('selectedCalendarInfoItem: ', selectedCalendarInfoItem.value)
// }

// const customDateCellClass = (date: number) => {
//   let style = 'bg-[#CDCECD] text-white'

//   if (calendarData.value.length > 0) {
//     if (calendarData.value[date - 1].leaveInfos.length || calendarData.value[date - 1].shiftInfos.length) {
//       style = 'bg-[#B7D7F2] text-black'
//     }
//   }
//   if (date === selectedDate.value) {
//     style = 'bg-blue text-white'
//   }
//   if (date === selectedMonth.value.date()) {
//     style = 'bg-orange text-white'
//   }
//   return style
// }

const onSearch = (value: string) => {
  run({
    pageNum: 1,
    pageSize: pageSize.value,
    keyword: value,
    dateFrom: selectedMonth.value.startOf('month').format('YYYY-MM-DD'),
    dateTo: selectedMonth.value.endOf('month').format('YYYY-MM-DD'),
  })
}

onMounted(async () => {
})
</script>

<!-- class="flex lg:flex-row gap-x-[20px]" -->
<!--    -->
<template>
  <page-container>
    <div class="flex flex-col xl:flex-row gap-x-[20px] gap-y-[20px] xl-h-[calc(100vh-8rem)]">
      <div class="flex flex-col justify-between min-w-[400px] bg-white shadow-lg rounded-lg p-4">
        <div class="flex flex-col gap-y-4">
          <div class="flex justify-between items-center">
            <a-space>
              <a-input-search
                v-model:value="searchValue"
                :placeholder="t('input.placeholder')"
                style="width: 270px"
                @search="onSearch"
              />
            </a-space>
            <div class="flex">
              <a-space class="gap-x-2">
                <div class="flex justify-between items-center gap-x-4">
                  <CarbonArrowLeft class="hover:bg-gray-200 cursor-pointer" @click="onChangeWeek('prev')" />
                  <div class="w-[90px] flex justify-center">
                    <span class="text-lg">
                      {{ dayjs(selectedMonth).format("MMM YYYY") }}
                    </span>
                  </div>
                  <CarbonArrowRight class="hover:bg-gray-200 cursor-pointer" @click="onChangeWeek('next')" />
                </div>
              </a-space>
            </div>
          </div>
          <a-table
            :columns="columns"
            :data-source="dataSource?.items ?? []"
            :loading="loading"
            :pagination="false"
            class="office-request-table"
            :bordered="true"
            row-key="employeeId"
            :scroll="{ x: 1500, y: 'calc(100vh - 350px)' }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'employeeName'">
                <div class="flex">
                  <div class="mr-2">
                    <a-avatar :size="48" :src="avatarStore.getImageSrcByEmployeeId(record.employeeId) ?? ''" class="shadow-lg" />
                  </div>
                  <div class="text-left flex flex-col">
                    <span class="text-md font-bold">
                      {{ record.employeeName }}
                    </span>
                    <span class="text-gray-500">
                      {{ `${t('code')}: ${record.code}` }}
                    </span>
                  </div>
                </div>
              </template>
              <template v-if="column.dataIndex === 'actions'">
                <router-link
                  v-if="isRouteExist "
                  :to="{
                    name: REDIRECT_NAME,
                    params: {
                      id: record.employeeId,
                    },
                    query: {
                      month: selectedMonth.format('YYYY-MM'),
                      employeeCode: record.code,
                      employeeName: record.employeeName,
                    },
                  }"
                >
                  <a-button type="primary">
                    {{ t('button.view-detail') }}
                  </a-button>
                </router-link>
                <a-button v-else type="primary" :disabled="true">
                  {{ t('button.view-detail') }}
                </a-button>
              </template>
            </template>
          </a-table>
        </div>

        <!-- Pagination Section -->
        <div class="flex justify-between">
          <a-pagination
            v-model:current="current"
            :page-size="pageSize"
            :total="total"
            class="text-[0.875rem] project-pagination"
          />
          <div
            class="flex items-center gap-x-[8px] text-[#74797A] project-select-page-size"
          >
            <span>{{ t("button.show") }}</span>
            <a-select
              v-model:value="pageSize"
              :options="pageSizeOptions"
              @change="handlePageSizeChange"
            >
              <template #suffixIcon>
                <CarbonPagninationArrowDown />
              </template>
            </a-select>
            <span>{{ t("button.entries") }}</span>
          </div>
        </div>
      </div>
    </div>
  </page-container>
</template>

<style lang="less">
.custom-layout {
  > div {
    gap: 12px;
    .ant-col {
      flex: 0 0 auto;
      padding-bottom: 0;
    }
  }
}
.user-modal {
  .ant-modal-header {
    text-align: center;
    .ant-modal-title {
      font-size: 20px;
      color: #1c4771;
    }
  }
}
// .filter-popover {
//   .ant-popover-inner {
//     padding: 0;
//   }
// }

.office-request-table .ant-table-pagination.ant-pagination {
  display: none;
}

.custom-height {
  height: calc(100vh - 140px);
}
</style>
