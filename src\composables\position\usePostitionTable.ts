import { usePagination } from 'vue-request'
import { getPositionListApi } from '~@/api/company/position'

export function usePositionTable() {
  const {
    data: positionDataSource,
    current,
    totalPage,
    loading,
    pageSize,
    run: fetchData,
  } = usePagination(getPositionListApi, {
    pagination: {
      currentKey: 'pageNum',
      pageSizeKey: 'pageSize',
      totalKey: 'totalRecords',
    },
  })

  const pagination = computed(() => ({
    total: totalPage.value,
    current: current.value,
    pageSize: pageSize.value,
  }))

  return {
    positionDataSource,
    loading,
    pagination,
    fetchData,
  }
}
