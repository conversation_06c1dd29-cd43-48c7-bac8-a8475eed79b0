interface NotificationDevice {
  deviceId: string;
  platform: string;
  osVersion: string;
  appVersion: string;
  firebaseToken: string;
}

export interface TargetItem {
  targetIds?: string[];
  targetType?: string;
  notificationTargetId?: string;
  isDeleted?: boolean;
  publishStatus?: string;
  publishAt?: string;
}

export interface NotificationItem {
  notificationId: string;
  title: string;
  body: string;
  notificationType: string;
  targets: TargetItem[];
}

export interface NotificationEmployeeItem {
  notificationId: string;
  title: string;
  body: string;
  isRead: boolean;
  createdTime: string;
}

interface GetNotificationParams {
  keyword?: string;
  pageNum?: number;
  pageSize?: number;
}

interface GetNotificationEmployeeParams {
  keyword?: string;
  pageNum?: number;
  pageSize?: number;
  dateFrom?: string;
  dateTo?: string;
  isRead?: boolean;
}

export interface NotificationItemResponse {
  items: NotificationItem[];
  pageNum: number;
  pageSize: number;
}

export interface NotificationEmployeeItemResponse {
  items: NotificationEmployeeItem[];
  pageNum: number;
  pageSize: number;
}

export type NotificationItemParams = Partial<NotificationItem>;

export async function createNotificationDevice(
  data: Partial<NotificationDevice>
) {
  return usePost('v1/notification/device/register', data);
}

export async function updateNotification(
  id: string,
  data: Partial<NotificationItem>
) {
  return usePut(`v1/notification/${id}`, data);
}

export async function createNotification(data?: NotificationItemParams) {
  return usePost<NotificationItem>('v1/notification', data);
}

export async function getNotification(params?: GetNotificationParams) {
  return useGet<NotificationItemResponse>('v1/notification', params);
}

export async function getOneNotification(id: string) {
  return useGet<NotificationItem>(`v1/notification/${id}`);
}

export async function deleteNotification(id: string) {
  return useDelete(`v1/notification/${id}`);
}

export async function getNotificationEmployee(
  params?: GetNotificationEmployeeParams
) {
  return useGet<NotificationEmployeeItemResponse>(
    `v1/notification/employee`,
    params
  );
}

export async function sendNotificationTest(title: string, body: string) {
  const token = useFcmToken().value;
  return usePost(`v1/notification/token/${token}`, {
    title,
    body,
  });
}

export async function sendNotification(
  notificationId: string,
  targetId: string
) {
  return usePost(`v1/notification/${notificationId}/publish/${targetId}`);
}

export async function readNotification(id: string) {
  return usePut(`v1/notification/${id}/read`, {});
}

export async function readAllNotifications() {
  return usePut(`v1/notification/notification/read`, {});
}

export async function unReadNotification(id: string) {
  return usePut(`v1/notification/${id}/unread`, {});
}
