import type { QueryParams } from './common-params'
import type { ManagerInfo } from './company/project'

export enum PROJECT_MENU_TYPE {
  CURRENT_PROJECT = 'current_project',
  OTHER_PROJECT = 'other_project',
  BUSINESS = 'business_trip',
  REMOTE = 'remote',
  CUSTOM_WORKPLACE = 'custom_workplace',
}

export enum ATTENDANCE_TYPE {
  CURRENT_PROJECT = 1,
  OTHER_PROJECT = 2,
  BUSINESS = 3,
  REMOTE = 4,
  CUSTOM_WORKPLACE = 5,
}

// Utility function to get display text for working location
export function getWorkingLocationDisplayText(workingLocation: string | undefined | null, t: (key: string) => string): string {
  if (!workingLocation)
    return '---'

  switch (workingLocation) {
    case PROJECT_MENU_TYPE.BUSINESS:
      return t('select.business')
    case PROJECT_MENU_TYPE.REMOTE:
      return t('select.remote')
    case PROJECT_MENU_TYPE.CURRENT_PROJECT:
      return t('select.current-project')
    case PROJECT_MENU_TYPE.OTHER_PROJECT:
      return t('select.other-project')
    case PROJECT_MENU_TYPE.CUSTOM_WORKPLACE:
      return t('enterWorkplace')
    default:
      // For actual addresses or custom workplace names, return as-is
      return workingLocation
  }
}

export interface BreakTimeItem {
  breakInTime?: string
  breakOutTime?: string
}

export interface ModifiedBreakTime {
  breakInTime?: string
  breakOutTime?: string
}

export interface InlineAttendanceItem {
  employeeId?: string | null
  employeeName?: string | null
  projectNames?: string[] | null
  projectCodes?: string[] | null
  workingLocations?: string[] | null
  workingDate?: string
  checkInTimes?: string[]
  checkOutTimes?: string[]
  breakList?: string[]
  totalWorkTime?: number | null
  totalOverTime?: number | null
  totalBreakTime?: number | null
}

export interface AttendanceItem {
  employeeShiftId?: string
  employeeId?: string | null
  employeeName?: string | null
  projectScheduleId?: string | null
  projectId?: string | null
  projectName?: string | null
  projectCode?: string | null
  workingLocation?: string | null
  holidayId?: string | null
  isOnHoliday?: boolean | null
  isApproved?: boolean | null
  isRequested?: boolean | null
  workingDate?: string
  checkInTime?: string
  checkInLocation?: string | null
  checkOutTime?: string | null
  checkOutLocation?: string | null
  scheduledStartTime?: string | null
  scheduledEndTime?: string | null
  breakList?: BreakTimeItem[]
  totalWorkTime?: number | null
  totalOverTime?: number | null
  totalBreakTime?: number | null
  description?: string | null
  attendanceStatus?: string | null
  modifiedCheckInTimeLastModifierType?: 'SYSTEM' | 'AUTHOR' | 'MANAGER' | 'AUTO' | null
  modifiedCheckOutTimeLastModifierType?:
    | 'SYSTEM'
    | 'AUTHOR'
    | 'MANAGER'
    | 'AUTO'
    | null
  modifiedBreakListLastModifierType?: 'SYSTEM' | 'AUTHOR' | 'MANAGER' | 'AUTO' | null
  isScheduled?: boolean | null
  approvedBy?: string | null
  isCanceled?: boolean | null
  createTime?: string | null
}

export interface AttendanceItemParams {
  projectId?: string
  workplaceId?: string
  workingDate?: string
  latitude?: string
  longtitude?: string
  checkInTime?: string
  checkOutTime?: string
  breakInTime?: string
  breakOutTime?: string
  description?: string
}

export interface AttendanceCreateParams {
  projectId?: string
  checkInTime?: string
  checkOutTime?: string
  breakList?: BreakTimeItem[]
  description?: string
  workingLocation?: string
  attendanceType?: number
}

export interface AttendanceUpdateParams {
  checkInTime?: string
  checkOutTime?: string
  breakList?: BreakTimeItem[]
  description?: string
  totalOverTime?: number
  workingLocation?: string
  projectId?: string
  workingDate?: string
  employeeIds?: string[]
  attendanceType?: number
}

export interface CheckInParams {
  projectId?: string
  workingLocation?: string
  latitude?: string
  longitude?: string
}

export interface FastCheckInParams {
  latitude?: string
  longitude?: string
}

export interface CheckOutParams {
  latitude?: string
  longitude?: string
}

export interface AttendanceDataResponse {
  items: AttendanceItem[]
  totalRow: number
}

export interface AttendanceSearchParams {
  fromDate: string
  toDate: string
  pageNum?: number
  pageSize?: number
}
export interface AttendanceSummaryParams {
  userId: string
  fromDate: string
  toDate: string
}

export interface AttendanceEmployeeSummaryParams {
  employeeId?: string // Nếu không truyền vào thì mặc định là user hiện tại
  fromDate: string
  toDate: string
}

export interface AttendanceTodayParams {
  projectId: string
  workplaceId: string
  queryDate: string
}

export interface AttendanceByWorksiteParams {
  projectId: string
  date: string
}

export interface AttendanceResponse {
  items: AttendanceItem[]
}

export interface ApproveRequestByAttendanceItemParams {
  employeeShiftId: string
  approvalStatus: boolean // true: approve, false: reject
  rejectReason?: string
}

export interface AttendanceWorksite {
  worksiteId: string
  worksiteName: string
  address: string
  managers: ManagerInfo[]
  attendanceData: AttendanceItem[]
  totalWorkTime: number
  totalOverTime: number
  totalBreakTime: number
  totalWorksDay: number
  leaveTaken: number
}

export interface EmployeeAttendanceSummaryResponse {
  totalWorkingDays: number
  totalAbsentDays: number
  totalWorkTime: number
  totalOverTime: number
  totalPaidLeaveUsed: number
  totalUnpaidLeaveUsed: number
}

export interface AttendanceSummaryResponse {
  userId: string
  userName: string
  items: AttendanceWorksite[]
}

export interface AttendanceEmployeeSummaryResponse {
  totalWorkingDays: number
  totalAbsentDays: number
  totalWorkTime: number
  totalOverTime: number
  totalPaidLeaveUsed: number
  totalUnpaidLeaveUsed: number
}

export interface WorksiteReport {
  worksiteId: string
  hasReport: boolean
  items: AttendanceItem[]
}

export async function getAttendanceByDateApi(params: AttendanceSearchParams) {
  return useGet<AttendanceResponse>('v1/attendance', params)
}

export async function createAttendanceApi(body: AttendanceCreateParams) {
  return usePost<AttendanceItem>('v1/attendance', body)
}

export async function getAttendanceEmployeeApi(
  employeeId: string,
  params: QueryParams,
) {
  return useGet<AttendanceResponse>(
    `v1/attendance/employee/${employeeId}`,
    params,
  )
}

export async function getAttendanceApi(shiftId: string) {
  return useGet<AttendanceItem>(`v1/attendance/${shiftId}`)
}

export async function getAttendanceTodayApi(params: AttendanceTodayParams) {
  return useGet<any>('v1/attendance/today', params)
}

export async function getAttendanceByWorksiteApi(
  params: AttendanceByWorksiteParams,
) {
  return useGet<WorksiteReport>(`v1/attendance/projects/${params.projectId}`, params)
}

export async function checkInByShiftIdApi(
  shiftId: string,
  params: FastCheckInParams,
) {
  return usePut<any>(`v1/attendance/${shiftId}/checkin`, params)
}

export async function checkInApi(params: CheckInParams) {
  return usePost<AttendanceItem>('v1/attendance/checkin', params)
}

export async function updateCheckoutApi(
  shiftId: string,
  params: CheckOutParams,
) {
  return usePut<any>(`v1/attendance/${shiftId}/checkout`, params)
}

export async function updateBreakInApi(shiftId: string, params: any) {
  return usePut<any>(`v1/attendance/${shiftId}/breakin`, params)
}

export async function updateBreakOutApi(shiftId: string, params: any) {
  return usePut<any>(`v1/attendance/${shiftId}/breakout`, params)
}

export async function updateAttendanceApi(
  employeeShiftId: string,
  params: AttendanceUpdateParams,
) {
  return usePut<any>(`v1/attendance/${employeeShiftId}`, params)
}

export async function deleteAttendanceApi(employeeShiftId: string) {
  return useDelete(`v1/attendance/${employeeShiftId}`)
}

export async function sendRequestByAttendanceItem(employeeShiftId: string) {
  return usePost<any>(`v1/attendance/${employeeShiftId}/request`)
}

export async function approveRequestByAttendanceItem(
  shiftId: string,
  params: ApproveRequestByAttendanceItemParams,
) {
  return usePut<any>(`v1/attendance/${shiftId}/approve`, params)
}

export async function getAttendanceEmployeeSummaryApi(params: AttendanceEmployeeSummaryParams) {
  return useGet<AttendanceEmployeeSummaryResponse>(`v1/attendance/employee/summary`, params)
}
