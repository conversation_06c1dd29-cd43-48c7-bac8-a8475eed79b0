// import * as XLSX from 'xlsx'
// import type { Dayjs } from 'dayjs'
// import type { InputCostItem } from '~@/api/input-cost-item'
// import type { ConstructionCostItem, CostAmountItem, EstimateBudgetItem } from '~@/api/construction-cost'
// import type { ProjectItem } from '~@/api/company/project'
// import type { ConstructionItem } from '~@/api/construction'
// import type { CostCategoryItem } from '~@/api/company/cost-category'

// interface ExportConstructionCostOptions {
//   currentMonth: Dayjs
//   projectInfo?: ProjectItem
//   categoryData?: CostCategoryItem[]
//   mainCostAmount?: CostAmountItem
//   subCostAmount?: CostAmountItem
//   overallCostAmount?: CostAmountItem
//   mainEstimateBudget?: EstimateBudgetItem
//   subEstimateBudget?: EstimateBudgetItem
//   overallEstimateBudget?: EstimateBudgetItem
//   constructionCosts?: ConstructionCostItem[]
//   projectConstructions?: ConstructionItem[]
// }

// const costData = ref<InputCostItem[]>([])

// // Format number with Japanese yen formatting
// function formatNumber(value: number) {
//   return value.toLocaleString('ja-JP')
// }
// // Map category names to their respective codes
// const categoryMap: Record<string, string> = {}
// function getCategoryItems(categoryName: string, position: number) {
//   const categoryCode = categoryMap[categoryName]

//   if (!categoryCode)
//     return []

//   // Filter items by category and get items at specific position
//   const itemsInCategory = costData.value.filter(
//     item => item.categoryCode === categoryCode,
//   )

//   // Return item at position or empty array if not exists
//   return position < itemsInCategory.length ? [itemsInCategory[position]] : []
// }

// // Calculate total for a specific category
// function getCategoryTotal(categoryName: string) {
//   const categoryCode = categoryMap[categoryName]

//   if (!categoryCode)
//     return 0

//   return costData.value
//     .filter(item => item.categoryCode === categoryCode)
//     .reduce((sum, item) => sum + item.totalTaxed, 0)
// }

// function createProjectInfoSheet(projectInfo?: ProjectItem): XLSX.WorkSheet {
//   const projectInfoData = [
//     ['工事名称', projectInfo?.projectName || ''],
//     ['工事場所', projectInfo?.address || ''],
//     ['発注者', projectInfo?.customerName || ''],
//     ['工期', `${projectInfo?.expectedStartDate || ''} ～ ${projectInfo?.expectedEndDate || ''}`],
//   ]
//   return XLSX.utils.aoa_to_sheet(projectInfoData)
// }

// interface CreateCostSheetOptions {
//   categoryData: CostCategoryItem[]
// }

// function createCostSheet({
//   categoryData,
// }: CreateCostSheetOptions): XLSX.WorkSheet {
//   const costData = []

//   // Header row
//   const headers = ['']
//   categoryData.forEach((category, index) => {
//     if (index >= 4)
//       headers.push(category.categoryName)
//   })
//   costData.push(headers)

//   // Data rows
//   for (let i = 0; i < 8; i++) {
//     const row = [''] // First column empty
//     categoryData.forEach((category, index) => {
//       if (index >= 4) {
//         const items = getCategoryItems(category.categoryName, i)
//         let cellValue = ''
//         if (items.length > 0) {
//           cellValue = items.map(item =>
//             `${item.itemName}\n${item.quantity} ${item.unit}\n¥${formatNumber(item.totalTaxed)}`,
//           ).join('\n')
//         }
//         row.push(cellValue)
//       }
//     })
//     costData.push(row)
//   }

//   // Subtotal row
//   const subtotalRow = ['小計']
//   categoryData.forEach((category, index) => {
//     if (index >= 4)
//       subtotalRow.push(`¥${formatNumber(getCategoryTotal(category.categoryName))}`)
//   })
//   costData.push(subtotalRow)

//   // Create worksheet
//   const ws = XLSX.utils.aoa_to_sheet(costData)

//   // Set column widths
//   const colWidths = [{ wch: 15 }] // First column
//   categoryData.forEach((_, index) => {
//     if (index >= 4)
//       colWidths.push({ wch: 20 })
//   })
//   ws['!cols'] = colWidths

//   return ws
// }

// export function exportConstructionCostToExcel({
//   currentMonth,
//   projectInfo,
//   categoryData,
//   mainCostAmount,
//   subCostAmount,
//   overallCostAmount,
//   mainEstimateBudget,
//   subEstimateBudget,
//   overallEstimateBudget,
//   constructionCosts,
//   projectConstructions,
// }: ExportConstructionCostOptions): void {
//   // Tạo workbook mới
//   const wb = XLSX.utils.book_new()

//   // Tạo worksheet thông tin dự án
//   const projectInfoSheet = createProjectInfoSheet(projectInfo)
//   XLSX.utils.book_append_sheet(wb, projectInfoSheet, '工事情報')

//   // Tạo worksheet chi phí
//   const costSheet = createCostSheet({
//     categoryData: categoryData || [],
//   })
//   XLSX.utils.book_append_sheet(wb, costSheet, '工事原価')

//   // Export file
//   const fileName = `工事原価表_${currentMonth.format('YYYYMM')}.xlsx`
//   XLSX.writeFile(wb, fileName)
// }
