export interface ListResultModel {
  EmpContractCode: string;
  EmpContractName: string;
  Notes: string;
  OrgStructureKey: number | undefined;
  OrgPositionKey: number | undefined;
  OfficeKey: number | undefined;
  EndUserKey: number | undefined;
  FromDate: string;
  ToDate: string;
  Active: boolean;
  SearchParams?: string;
  PageSize?: number;
  PageIndex?: number;
}

export type ListResultParams = Partial<ListResultModel>;

export async function getListApi(params?: ListResultParams) {
  return useGet<ListResultModel[]>("/EmpContract/SearchPaging", params);
}

export async function onCreate(data?: ListResultParams) {
  return usePost<ListResultModel[]>("/EmpContract/Create", data);
}

export async function onUpdate(id: number, data?: ListResultParams) {
  return usePut<ListResultModel[]>(`v1/EmpContract/Update/${id}`, data);
}

export async function onDelete(id: number) {
  return useDelete<ListResultModel[]>(`v1/EmpContract/Delete/${id}`);
}
