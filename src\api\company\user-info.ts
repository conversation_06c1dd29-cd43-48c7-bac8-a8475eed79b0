import type { UserAvatar, UserInfoItem, UserInfoParams } from '~/types/company/user-info'
import { USER_INFO_ENDPOINTS } from '~/constants/api/company/user-info'

/**
 * Get user information
 * @returns Promise<UserInfoResponse>
 */
export async function getUserInfoApi() {
  return useGet<UserInfoItem>(USER_INFO_ENDPOINTS.BASE)
}

/**
 * Update user information
 * @param data UserInfoParams
 * @returns Promise<UserInfoResponse>
 */
export async function updateUserInfoApi(data: UserInfoParams) {
  return usePut<UserInfoItem>(USER_INFO_ENDPOINTS.BASE, data)
}

export async function getUserInfoByIdApi(id: string) {
  return useGet<UserInfoItem>(USER_INFO_ENDPOINTS.BY_ID(id))
}

export async function getUserInfoLogsApi(id: string) {
  return useGet<UserInfoItem>(USER_INFO_ENDPOINTS.BY_ID_LOGS(id))
}

export async function getUserInfoAvatarApi() {
  return useGet<UserAvatar>(USER_INFO_ENDPOINTS.AVATAR)
}
