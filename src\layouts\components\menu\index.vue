<script setup lang="ts">
import type { CSSProperties } from 'vue'
import { useLayoutState } from '../../basic-layout/context'
import SubMenu from './sub-menu.vue'

const { theme, collapsed, layout, isMobile, selectedKeys, openKeys, handleOpenKeys, handleSelectedKeys, handleMenuSelect, menuData } = useLayoutState()
// const menuTheme = computed(() => {
//   if (theme.value === 'inverted')
//     return 'dark'
//   return 'dark'
//   // return theme.value
// })
const menuStyle = computed<CSSProperties>(() => {
  return {
    fontFamily: 'Noto Sans JP',
    fontSize: '1rem',
    backgroundColor: theme.value === 'inverted' ? '#000' : '#1c4771',
    color: 'white',
  }
})
</script>

<template>
  <a-menu
    :selected-keys="selectedKeys" :open-keys="collapsed ? [] : openKeys"
    :mode="(layout === 'top' && !isMobile) ? 'horizontal' : 'inline'" :collapsed="collapsed"
    :style="menuStyle" @update:open-keys="handleOpenKeys"
    @update:selected-keys="handleSelectedKeys"
    @select="handleMenuSelect"
  >
    <template v-for="item in menuData">
      <template v-if="!item.hideInMenu">
        <SubMenu :key="item.path" :item="item" />
      </template>
    </template>
  </a-menu>
</template>
