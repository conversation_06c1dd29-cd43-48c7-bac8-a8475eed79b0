<script lang="ts" setup>
import {
  CloseOutlined,
  DownOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import type {
  ColumnGroupType,
  ColumnType,
  TablePaginationConfig,
} from 'ant-design-vue/es/table';
import type { FilterValue } from 'ant-design-vue/es/table/interface';
import type { FileType } from 'ant-design-vue/es/upload/interface';
import _, { cloneDeep } from 'lodash';
import { usePagination } from 'vue-request';
import ItemDetail from './item-detail.vue';
import type { CostCategoryItem } from '~@/api/company/cost-category';
import { getCostCategory } from '~@/api/company/cost-category';
import type { CostItem, GetCostItemParams } from '~@/api/company/cost-item';
import {
  createCostItem,
  deleteCostItem,
  getCostItem,
  getCostItemLogo,
  getOneCostItem,
  updateCostItem,
} from '~@/api/company/cost-item';
import type { ManufacturerItem } from '~@/api/company/manufacturer';
import { getManufacturer } from '~@/api/company/manufacturer';
import { ResponseStatusEnum } from '~@/enums/response-status-enum';
import { ModalType } from '~@/enums/system-status-enum';

type ColumnItemType<T> = ColumnGroupType<T> | ColumnType<T>;

type FormState = CostItem & {
  category?: {
    value: string;
    label: string;
  };
  manufacturer?: {
    value: string;
    label: string;
  };
};

const initFormState: FormState = {
  image: undefined,
  imageUrl: '',
  itemId: '',
  itemCode: '',
  itemName: '',
  itemSubName: '',
  serialNumber: '',
  categoryId: '',
  categoryCode: '',
  categoryName: '',
  manufacturerId: '',
  manufacturerCode: '',
  manufacturerName: '',
  size: '',
  description: '',
  category: undefined,
  manufacturer: undefined,
};

const initSearchForm: GetCostItemParams = {
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  categoryId: undefined,
  manufacturerId: undefined,
  size: undefined,
};

const modalType = ref<ModalType>(ModalType.ADD);
const formRef = ref();
const uploadRef = ref();
const tableLeft = ref();
const transactionRef = ref();
const { height: tableLeftHeight } = useElementSize(tableLeft);
const categories = ref<CostCategoryItem[]>([]);
const manufacturers = ref<ManufacturerItem[]>([]);
const modalLoading = ref<boolean>(false);
const isOpenDetail = ref<boolean>(false);
const isOpenModalDetail = ref<boolean>(false);
const { t } = useI18n();
const formState = reactive<FormState>({ ...cloneDeep(initFormState) });
const isOpenModal = ref<boolean>(false);
const isOpenFilter = ref<boolean>(false);
const idDetail = ref<string>('');
const searchForm = ref<GetCostItemParams>({ ...cloneDeep(initSearchForm) });
const { width: windowWidth } = useWindowSize();

async function queryData(params?: GetCostItemParams) {
  const { data } = await getCostItem(params);
  return data;
}

const {
  data: dataSource,
  loading,
  refresh,
  total,
  current,
  run,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [searchForm.value],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
});

const pagination = computed(() => ({
  total: total.value ?? 10,
  current: current.value,
  pageSize: pageSize.value,
  showSizeChanger: true,
}));

const columns = computed<ColumnItemType<CostItem>[]>(() => [
  { dataIndex: 'image', width: 120, align: 'center' },
  { dataIndex: 'item', width: 250 },
  { dataIndex: 'category', width: 200 },
  { dataIndex: 'manufacturer', width: 250 },
  { dataIndex: 'action', width: 180, align: 'center', fixed: 'right' },
]);

async function onFinish() {
  try {
    await formRef.value.validate();

    switch (modalType.value) {
      case ModalType.ADD: {
        const create = await createCostItem({
          image: formState.image,
          itemCode: formState.itemCode,
          itemName: formState.itemName,
          serialNumber: formState.serialNumber,
          categoryId: formState.category?.value,
          manufacturerId: formState.manufacturer?.value,
          size: formState.size,
          description: formState.description,
        });
        if (create.status === ResponseStatusEnum.SUCCESS) {
          message.success(create.message);
        } else {
          message.error(create.message);
          return;
        }

        break;
      }
      case ModalType.EDIT: {
        const update = await updateCostItem(formState.itemId, {
          image: formState.image,
          itemCode: formState.itemCode,
          itemName: formState.itemName,
          serialNumber: formState.serialNumber,
          categoryId: formState.category?.value,
          manufacturerId: formState.manufacturer?.value,
          size: formState.size,
          description: formState.description,
        });
        if (update.status === ResponseStatusEnum.SUCCESS) {
          message.success(update.message);
        }
        break;
      }
      default:
        break;
    }

    isOpenModal.value = false;
    onReset();
    refresh();
  } catch (error) {
  }
}

function beforeUpload(file: FileType) {
  formState.image = file;
  formState.imageUrl = URL.createObjectURL(file);
  return false;
}

async function openModal(id: string, type: ModalType) {
  switch (type) {
    case ModalType.ADD:
      modalType.value = type;
      isOpenModal.value = true;
      break;
    case ModalType.COPY:
    case ModalType.EDIT: {
      isOpenModal.value = true;
      modalLoading.value = true;
      modalType.value = type;

      const update = await getOneCostItem(id);
      formState.imageUrl = update.data?.imageUrl && getCostItemLogo(id);
      formState.itemId = id;
      formState.itemCode = update.data?.itemCode ?? '';
      formState.itemName = update.data?.itemName ?? '';
      formState.serialNumber = update.data?.serialNumber ?? '';
      if (update.data?.categoryId) {
        formState.category = {
          value: update.data?.categoryId ?? '',
          label: update.data?.categoryName ?? '',
        };
      }
      if (update.data?.manufacturerId) {
        formState.manufacturer = {
          value: update.data?.manufacturerId ?? '',
          label: update.data?.manufacturerName ?? '',
        };
      }
      formState.size = update.data?.size ?? '';
      formState.description = update.data?.description ?? '';

      modalLoading.value = false;
      break;
    }
    default:
      break;
  }
}

async function handleDeleteCostItem(id: string) {
  try {
    const del = await deleteCostItem(id);
    if (del.status === ResponseStatusEnum.SUCCESS) message.success(del.message);
    else message.error(del.message);
  } catch (error) {
  } finally {
    refresh();
  }
}

function handleTableChange(
  pagination: TablePaginationConfig,
  filters: Record<string, FilterValue>
) {
  searchForm.value.pageSize = pagination.pageSize;
  searchForm.value.pageNum = pagination.current;
  run({ ...searchForm.value, ...filters });
}

function handlePaginationChange(page: number, pageSize: number) {
  searchForm.value.pageSize = pageSize;
  searchForm.value.pageNum = page;
  run(searchForm.value);
}

function onReset() {
  Object.assign(formState, cloneDeep(initFormState));
}

const renderTitle = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
    case ModalType.COPY:
      return _.startCase(_.toLower(`${t('button.add')} ${t('form.item')}`));
    case ModalType.EDIT:
      return `${t('button.edit')} ${t('form.item')}`;
    case ModalType.LOG:
      return t('log-item');
    default:
      return '';
  }
});

const renderOkConfirm = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
    case ModalType.COPY:
      return t('message.add-confirmation');
    case ModalType.EDIT:
      return t('message.edit-confirmation');
    default:
      return '';
  }
});

async function loadCategoryData() {
  const { data } = await getCostCategory();
  categories.value = data?.items ?? [];
}

async function loadManufacturerData() {
  const { data } = await getManufacturer();
  manufacturers.value = data?.items ?? [];
}

onMounted(async () => {
  await Promise.all([loadCategoryData(), loadManufacturerData()]);
});

function onResetSearch() {
  Object.assign(searchForm.value, cloneDeep(initSearchForm));
}

function onSearch() {
  handleTableChange(
    { pageSize: searchForm.value.pageSize ?? 10, current: 1 },
    {}
  );
}

const customRow = computed(() => (data: CostItem) => {
  const isLargeScreen = windowWidth.value >= 1536;

  return {
    onClick: () => {
      idDetail.value = data.itemId;
      if (isLargeScreen) isOpenDetail.value = true;
    },
    class:
      data.itemId === idDetail.value ? 'row-active shadow-lg' : 'shadow-lg',
  };
});

const handleOpenDetail = (record: CostItem) => {
  const isLargeScreen = windowWidth.value >= 1536;
  idDetail.value = record.itemId;
  if (!isLargeScreen) isOpenModalDetail.value = true;
};

onClickOutside(transactionRef, (event) => {
  if (document.getElementById('app')?.contains(event.target as Node))
    isOpenDetail.value = false;
});

watch(
  () => windowWidth.value,
  (newVal) => {
    if (newVal < 1536) {
      isOpenDetail.value = false;
    }
  }
);
</script>

<template>
  <page-container>
    <a-row ref="transactionRef" :gutter="[12, 12]">
      <a-col :span="isOpenDetail ? 12 : 24">
        <a-row
          ref="tableLeft"
          :wrap="false"
          :gutter="[12, 12]"
          class="h-[calc(100vh-110px)] flex-col"
        >
          <a-col flex="none" span="24">
            <a-row>
              <a-col span="24">
                <a-row :gutter="[12, 12]" :wrap="false" class="pt-4">
                  <a-col flex="auto">
                    <div>
                      <a-typography-title :level="4" ellipsis>
                        {{ t('form.item-list') }}
                      </a-typography-title>
                    </div>
                  </a-col>
                  <a-col flex="none">
                    <a-input
                      v-model:value="searchForm.keyword"
                      :placeholder="t('search')"
                      allow-clear
                      @press-enter="onSearch"
                      :class="{
                        'w-[240px]': isOpenDetail,
                        'w-[300px]': !isOpenDetail,
                      }"
                    >
                      <template #prefix>
                        <SearchOutlined class="text-gray-500" />
                      </template>
                    </a-input>
                  </a-col>
                  <a-col flex="none">
                    <a-popover
                      v-model:open="isOpenFilter"
                      trigger="click"
                      placement="bottomRight"
                      :arrow="false"
                      class="w-[6rem]"
                      :overlay-style="{ width: '300px' }"
                    >
                      <template #title>
                        <div class="flex items-center justify-between p-2">
                          <span class="flex items-center gap-1">
                            <CarbonFilterNew size="14" />
                            {{ t('button.filters') }}
                          </span>
                          <a-button
                            type="text"
                            size="small"
                            class="flex items-center justify-center"
                            @click="isOpenFilter = false"
                          >
                            <CloseOutlined />
                          </a-button>
                        </div>
                        <a-divider class="m-0" />
                      </template>
                      <template #content>
                        <div class="mx-auto p-2">
                          <a-form layout="vertical">
                            <a-row :gutter="[12, 12]">
                              <a-col span="24">
                                <a-form-item
                                  :label="t('form.main-category')"
                                  name="category"
                                  class="mb-0"
                                >
                                  <a-select
                                    v-model:value="searchForm.categoryId"
                                    :placeholder="t('form.main-category')"
                                    allow-clear
                                  >
                                    <template
                                      v-for="item in categories"
                                      :key="item.categoryId"
                                    >
                                      <a-select-option :value="item.categoryId">
                                        {{ item.categoryName }}
                                      </a-select-option>
                                    </template>
                                  </a-select>
                                </a-form-item>
                              </a-col>
                              <a-divider class="mb-0 mt-0" />
                              <a-col span="24">
                                <a-form-item
                                  :label="t('form.manufacturer')"
                                  name="manufacturer"
                                  class="mb-0"
                                >
                                  <a-select
                                    v-model:value="searchForm.manufacturerId"
                                    :placeholder="t('form.manufacturer')"
                                    allow-clear
                                    mode="multiple"
                                  >
                                    <template
                                      v-for="item in manufacturers"
                                      :key="item.manufacturerId"
                                    >
                                      <a-select-option
                                        :value="item.manufacturerId"
                                      >
                                        {{ item.manufacturerName }}
                                      </a-select-option>
                                    </template>
                                  </a-select>
                                </a-form-item>
                              </a-col>
                              <a-divider class="mb-0 mt-0" />
                              <a-col span="24">
                                <a-form-item
                                  :label="t('form.size')"
                                  name="size"
                                  class="mb-0"
                                >
                                  <a-input
                                    v-model:value="searchForm.size"
                                    :placeholder="t('form.size')"
                                  />
                                </a-form-item>
                              </a-col>
                              <a-divider class="mb-0 mt-0" />
                              <a-col span="24" class="p-2 text-right">
                                <div class="flex justify-between">
                                  <a-button @click="onResetSearch">
                                    {{ $t('button.reset') }}
                                  </a-button>
                                  <a-button
                                    type="primary"
                                    @click="
                                      () => {
                                        isOpenFilter = false;
                                        onSearch();
                                      }
                                    "
                                  >
                                    {{ $t('button.apply') }}
                                  </a-button>
                                </div>
                              </a-col>
                            </a-row>
                          </a-form>
                        </div>
                      </template>
                      <a-button class="w-[7rem]">
                        <div class="flex items-center gap-1">
                          <CarbonFilterNew size="14" />
                          {{ t('button.filters') }}
                          <DownOutlined />
                        </div>
                      </a-button>
                    </a-popover>
                  </a-col>
                  <a-col flex="none">
                    <a-button
                      class="flex items-center"
                      type="primary"
                      @click="openModal('', ModalType.ADD)"
                    >
                      <PlusOutlined />
                      {{ `${t('button.new')} ${t('form.item')}` }}
                    </a-button>
                  </a-col>
                </a-row>
              </a-col>
              <a-col span="24">
                <a-table
                  class="tableCostItem"
                  :scroll="{ x: 'max-content', y: 'calc(100vh - 225px)' }"
                  :columns="columns"
                  :data-source="dataSource?.items"
                  :loading="loading"
                  :pagination="false"
                  row-key="itemId"
                  :show-header="false"
                  :custom-row="customRow"
                  @change="handleTableChange"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'image'">
                      <img
                        v-if="record.imageUrl"
                        :src="getCostItemLogo(record.itemId)"
                        class="h-[80px] w-[80px] rounded-md"
                      />
                    </template>
                    <template v-if="column.dataIndex === 'item'">
                      <div class="flex flex-col justify-center gap-2">
                        <a-typography-title :level="5">
                          {{ record.itemName }}
                        </a-typography-title>
                        <div>
                          <span class="text-gray-500">
                            {{ t('form.code') }}:
                          </span>
                          <span class="font-medium">{{ record.itemCode }}</span>
                        </div>
                        <div>
                          <span class="text-gray-500">
                            {{ t('form.seri') }}:
                          </span>
                          <span class="font-medium">
                            {{ record.serialNumber }}
                          </span>
                        </div>
                      </div>
                    </template>
                    <template v-if="column.dataIndex === 'category'">
                      <div class="flex flex-col justify-center gap-2">
                        <div class="flex flex-col">
                          <div>
                            <span class="text-gray-500">
                              {{ t('form.category') }}:
                            </span>
                            <span class="font-medium">
                              {{ record.categoryName }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </template>
                    <template v-if="column.dataIndex === 'manufacturer'">
                      <div class="flex flex-col justify-center gap-2">
                        <div class="flex flex-col">
                          <div>
                            <span class="text-gray-500">
                              {{ t('form.manufacturer') }}:
                            </span>
                            <span class="font-medium">
                              {{ record.manufacturerName }}
                            </span>
                          </div>
                        </div>
                        <div>
                          <span class="text-gray-500">
                            {{ t('form.size') }}:
                          </span>
                          <span class="font-medium">
                            {{ record.size }}
                          </span>
                        </div>
                      </div>
                    </template>
                    <template v-if="column.dataIndex === 'action'">
                      <div class="flex flex-justify-center gap-2">
                        <a-button
                          class="flex items-center"
                          size="small"
                          type="text"
                          @click="() => handleOpenDetail(record as CostItem)"
                        >
                          <img src="/icon/detail.svg" class="w-[20px]" />
                        </a-button>
                        <a-button
                          class="flex items-center"
                          size="small"
                          type="text"
                          @click="openModal(record.itemId, ModalType.EDIT)"
                        >
                          <img src="/icon/edit.svg" class="w-[20px]" />
                        </a-button>
                        <a-popconfirm
                          :title="t('message.delete-confirmation')"
                          @confirm="() => handleDeleteCostItem(record.itemId)"
                        >
                          <a-button
                            class="flex items-center"
                            size="small"
                            type="text"
                          >
                            <img src="/icon/delete.svg" class="w-[20px]" />
                          </a-button>
                        </a-popconfirm>
                      </div>
                    </template>
                  </template>
                </a-table>
              </a-col>
            </a-row>
          </a-col>
          <a-col flex="auto" span="24">
            <div class="h-full flex items-end">
              <a-row justify="space-between" class="mt-4 w-full">
                <a-col>
                  <a-pagination
                    class="pagination"
                    :total="pagination.total"
                    :current="pagination.current"
                    :page-size="pagination.pageSize"
                    @change="handlePaginationChange"
                  />
                </a-col>
                <a-col>
                  <a-row :gutter="[12, 12]" justify="center" align="middle">
                    <a-col>{{ t('show') }}</a-col>
                    <a-col>
                      <a-pagination
                        class="pagination pagination-right"
                        :total="pagination.total"
                        :current="pagination.current"
                        :page-size="pagination.pageSize"
                        show-size-changer
                        :build-option-text="(props: any) => props.value"
                        @change="handlePaginationChange"
                      />
                    </a-col>
                    <a-col>{{ t('entries') }}</a-col>
                  </a-row>
                </a-col>
              </a-row>
            </div>
          </a-col>
        </a-row>
      </a-col>
      <a-col v-if="isOpenDetail" span="12">
        <ItemDetail :id="idDetail" :height="tableLeftHeight" />
      </a-col>
    </a-row>

    <a-modal
      v-model:open="isOpenModal"
      width="800px"
      :footer="false"
      :closable="false"
      :mask-closable="false"
      @cancel="onReset"
    >
      <template #title>
        <div class="flex justify-center items-center">
          <a-typography-title :level="4" class="!text-[#256CB5]">
            {{ renderTitle }}
          </a-typography-title>
        </div>
      </template>
      <a-card border-style="none" :loading="modalLoading" class="card">
        <a-form
          ref="formRef"
          :model="formState"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
          autocomplete="off"
          @finish="onFinish"
        >
          <a-row :gutter="[12, 12]" class="flex-nowrap">
            <a-col flex="none">
              <div class="w-32 flex flex-col justify-center items-center gap-2">
                <div>
                  <a-form-item :label="t('form.logo')" name="logo" no-style>
                    <a-upload
                      name="logo"
                      list-type="picture-card"
                      :show-upload-list="false"
                      :before-upload="beforeUpload"
                    >
                      <img
                        v-if="formState.imageUrl"
                        ref="uploadRef"
                        :src="formState.imageUrl"
                        class="h-full w-full rounded-md"
                      />
                      <div v-else ref="uploadRef">
                        <PlusOutlined />
                      </div>
                    </a-upload>
                  </a-form-item>
                </div>
                <a-row
                  v-if="formState.imageUrl"
                  :gutter="[4, 4]"
                  class="w-full justify-center items-center"
                >
                  <a-col>
                    <a-button
                      class="flex items-center"
                      type="text"
                      size="small"
                      @click="uploadRef.click()"
                    >
                      <img src="/icon/refresh.svg" class="w-[20px]" />
                    </a-button>
                  </a-col>
                  <a-col>
                    <a-popconfirm
                      :title="t('message.delete-confirmation')"
                      @confirm="
                        () => {
                          formState.imageUrl = '';
                          formState.image = undefined;
                        }
                      "
                    >
                      <a-button
                        class="flex items-center"
                        type="text"
                        size="small"
                      >
                        <img src="/icon/delete.svg" class="w-[20px]" />
                      </a-button>
                    </a-popconfirm>
                  </a-col>
                </a-row>
              </div>
            </a-col>
            <a-col flex="auto">
              <a-row :gutter="[12, 12]">
                <a-col span="24">
                  <a-form-item
                    :label="t('form.item-name')"
                    name="itemName"
                    :rules="[{ required: true }]"
                  >
                    <a-input
                      v-model:value="formState.itemName"
                      :placeholder="t('form.item-name')"
                    />
                  </a-form-item>
                </a-col>
                <a-col span="12">
                  <a-form-item
                    :label="t('form.code')"
                    name="itemCode"
                    :rules="[{ required: true }]"
                  >
                    <a-input
                      v-model:value="formState.itemCode"
                      :placeholder="t('form.code')"
                    />
                  </a-form-item>
                </a-col>
                <a-col span="12">
                  <a-form-item :label="t('form.seri')" name="serialNumber">
                    <a-input
                      v-model:value="formState.serialNumber"
                      :placeholder="t('form.seri')"
                    />
                  </a-form-item>
                </a-col>
                <a-col span="12">
                  <a-form-item
                    :label="t('form.main-category')"
                    name="category"
                    :rules="[{ required: true }]"
                  >
                    <a-select
                      v-model:value="formState.category"
                      :placeholder="t('form.main-category')"
                      allow-clear
                      label-in-value
                    >
                      <template
                        v-for="item in categories"
                        :key="item.categoryId"
                      >
                        <a-select-option :value="item.categoryId">
                          {{ item.categoryName }}
                        </a-select-option>
                      </template>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col span="12">
                  <a-form-item
                    :label="t('form.manufacturer')"
                    name="manufacturer"
                  >
                    <a-select
                      v-model:value="formState.manufacturer"
                      :placeholder="t('form.manufacturer')"
                      allow-clear
                      label-in-value
                    >
                      <template
                        v-for="item in manufacturers"
                        :key="item.manufacturerId"
                      >
                        <a-select-option :value="item.manufacturerId">
                          {{ item.manufacturerName }}
                        </a-select-option>
                      </template>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col span="24">
                  <a-form-item :label="t('form.size')" name="size">
                    <a-input
                      v-model:value="formState.size"
                      :placeholder="t('form.size')"
                    />
                  </a-form-item>
                </a-col>
                <a-col span="24">
                  <a-form-item
                    :label="t('form.description')"
                    name="description"
                  >
                    <a-input
                      v-model:value="formState.description"
                      :placeholder="t('form.description')"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-col>
          </a-row>

          <a-row justify="end">
            <a-row :gutter="[4, 4]">
              <a-col>
                <a-button
                  @click.stop="
                    () => {
                      isOpenModal = false;
                      onReset();
                    }
                  "
                >
                  {{ t('button.cancel') }}
                </a-button>
              </a-col>
              <a-col>
                <a-popconfirm :title="renderOkConfirm" @confirm.stop="onFinish">
                  <a-button type="primary">
                    {{ t('button.save') }}
                  </a-button>
                </a-popconfirm>
              </a-col>
            </a-row>
          </a-row>
        </a-form>
      </a-card>
    </a-modal>

    <a-modal v-model:open="isOpenModalDetail" width="1200px" :footer="false">
      <div class="mt-5">
        <ItemDetail :id="idDetail" :height="850" />
      </div>
    </a-modal>
  </page-container>
</template>

<style lang="less" scoped>
.log-cost-item {
  :deep(.ant-collapse-header) {
    padding: 0;
  }
}
.card {
  :deep(.ant-upload) {
    margin: 0 !important;
  }
}
.tableCostItem {
  :deep(.ant-table) {
    background: transparent;
  }
  :deep(table) {
    border-collapse: separate;
    border-spacing: 0 16px;
    margin-top: -16px;
    margin-right: 10px;
  }
  :deep(.ant-table-container) {
    padding-top: 16px;
  }
  :deep(.ant-table-tbody > tr.row-active) {
    background: #f2f8fd;
    td:first-child {
      border-left: 2px solid #b7d7f2;
    }
    td:last-child {
      border-right: 2px solid #b7d7f2;
    }
    td {
      background: #f2f8fd;
      border-top: 2px solid #b7d7f2 !important;
      border-bottom: 2px solid #b7d7f2 !important;
    }
  }
  :deep(.ant-table-tbody > tr) {
    background: #fff;
    box-shadow: 0px 2px 4px 0px #0000001a;
    border-radius: 8px;
    border-width: 1px;
  }
  :deep(.ant-table-tbody > tr > td:first-child) {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }
  :deep(.ant-table-tbody > tr > td:last-child) {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }
  :deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
    background: #f2f8fd;
  }
}
.pagination {
  :deep(.ant-pagination-item-active) {
    background: #f99649;
    border-color: #f99649;
    a {
      color: #fff;
    }
  }
  :deep(.ant-select-selector) {
    background: #f99649;
    border-color: #f99649 !important;
    .ant-select-selection-item {
      color: #fff;
    }
  }
  :deep(.ant-select-arrow) {
    color: #fff;
  }
}
.pagination-right {
  :deep(.ant-pagination-prev) {
    display: none;
  }
  :deep(.ant-pagination-next) {
    display: none;
  }
  :deep(.ant-pagination-item) {
    display: none;
  }
  :deep(.ant-pagination-options) {
    margin: 0;
  }
  :deep(.ant-pagination-jump-next) {
    display: none;
  }
  :deep(.ant-pagination-jump-prev) {
    display: none;
  }
}
</style>
