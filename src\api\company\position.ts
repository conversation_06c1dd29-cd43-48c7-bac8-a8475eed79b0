import type { QueryParams } from '../common-params'

export interface PositionItem {
  positionId: string
  positionCode: string
  positionName: string
  description: string | null
  createTime: string | null
  updateTime: string | null
}

export interface PositionParams {
  positionCode: string
  positionName: string
  description?: string
}

export interface PaginatedPositionResponse {
  items: PositionItem[] | null
  pageIndex: number
  pageSize: number
  totalRecords: number
}

export async function getPositionListApi(params: QueryParams) {
  return useGet<PaginatedPositionResponse>('v1/position', params)
}

export async function getPositionByIdApi(positionId: string) {
  return useGet<PositionItem>(`v1/position/${positionId}`)
}

export async function createPositionApi(params: PositionParams) {
  return usePost<PositionItem>('v1/position', params)
}

export async function updatePositionApi(positionId: string, data: PositionParams) {
  return usePut<PositionItem>(`v1/position/${positionId}`, data)
}

export async function deletePositionApi(positionId: string) {
  return useDelete(`v1/position/${positionId}`)
}
