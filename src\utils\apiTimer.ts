/* eslint-disable antfu/top-level-function */
/* eslint-disable curly */
/* eslint-disable style/quotes */
import { format, parse } from 'date-fns'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'

dayjs.extend(duration)

export const YYYYMMDDRegex = /^(19|20)\d{2}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$/
export const YYYYMMDDHHMMSSRegex = /^(19|20)\d{2}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01]) ([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/
export const YYYYMMDDHHMMRegex = /^(19|20)\d{2}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01]) ([01]\d|2[0-3]):([0-5]\d)$/
export const HHMMSSRegex = /^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/
export const HHMMRegex = /^([01]\d|2[0-3]):([0-5]\d)$/

/**
 *
 * @param dateString
 * @returns 'yyyy-MM-dd'
 */
export const parseDate = (dateString: string | null | undefined): string | undefined => {
  if (!dateString)
    return undefined
  const formats = [
    "yyyy-MM-dd HH:mm:ss",
    "yyyy-MM-dd'T'HH:mm:ss.SSSxxx",
    "yyyy-MM-dd'T'HH:mm:ssxxx",
    "yyyy-MM-dd'T'HH:mm:ss",
    "yyyy-MM-dd",
    "MM/dd/yyyy",
    "dd/MM/yyyy",
    "yyyy/MM/dd",
  ]

  for (const fmt of formats) {
    try {
      const parsedDate = parse(dateString, fmt, new Date())
      if (!Number.isNaN(parsedDate.getTime())) {
        return format(parsedDate, 'yyyy-MM-dd')
      }
    }
    catch (error) {
      // Ignore parsing errors and try the next format
      continue
    }
  }

  throw new Error(`Unable to parse date: ${dateString}`)
}

/**
 *
 * @param timeString
 * @returns 'HH:mm:ss'
 */
export const parseTime = (timeString: string): string => {
  const formats = [
    "HH:mm:ss",
    "HH:mm",
    "hh:mm:ss a",
    "hh:mm a",
    "HH:mm:ss.SSS",
    "HH:mm:ss.SSSXXX",
    // Add more formats as needed
  ]

  for (const fmt of formats) {
    try {
      const parsedTime = parse(timeString, fmt, new Date())
      if (!Number.isNaN(parsedTime.getTime())) {
        return format(parsedTime, 'HH:mm:ss')
      }
    }
    catch (error) {
      // Ignore parsing errors and try the next format
    }
  }

  throw new Error(`Unable to parse time: ${timeString}`)
}

export function formatTimeToHHMM(time?: string) {
  if (!time)
    return '00:00'
  const newTime = time.replace(/^(\d{2}):(\d{2})(:\d{2})?$/, '$1:$2')
  return newTime
}

export function formatTimeToHHMMSS(time?: string) {
  if (!time)
    return '00:00:00'
  const newTime = time.replace(/^(\d{2}):(\d{2})(:\d{2})?$/, '$1:$2:00')
  return newTime
}

export const convertTimeToSeconds = (timeString: string) => {
  if (!timeString)
    return 0
  if (HHMMRegex.test(timeString)) {
    const [hours, minutes] = timeString.split(':')
    return (Number.parseInt(hours) * 60 + Number.parseInt(minutes)) * 60
  }
  if (HHMMSSRegex.test(timeString)) {
    const [hours, minutes, _] = timeString.split(':')
    return (Number.parseInt(hours) * 60 + Number.parseInt(minutes)) * 60
  }
  return 0
}

export const convertSecondsToTime = (seconds?: number) => {
  if (!seconds)
    return '00:00'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds - hours * 3600) / 60)
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
}

export function getDurationTime(startTime: string | null | undefined, endTime: string | null | undefined) {
  if (!startTime || !endTime)
    return 0
  if (YYYYMMDDHHMMRegex.test(startTime) && YYYYMMDDHHMMRegex.test(endTime)) {
    const startDayJs = dayjs(startTime, 'YYYY-MM-DD HH:mm')
    const endDayJs = dayjs(endTime, 'YYYY-MM-DD HH:mm')
    return endDayJs.diff(startDayJs, 'second')
  }
  if (HHMMSSRegex.test(startTime) && HHMMSSRegex.test(endTime)) {
    const startSeconds = dayjs(startTime, 'HH:mm:ss')
    const endSeconds = dayjs(endTime, 'HH:mm:ss')
    return endSeconds.diff(startSeconds, 'second')
  }
  if (HHMMRegex.test(startTime) && HHMMRegex.test(endTime)) {
    const startSeconds = dayjs(startTime, 'HH:mm')
    const endSeconds = dayjs(endTime, 'HH:mm')
    return endSeconds.diff(startSeconds, 'second')
  }
  return 0
}

// Utility functions
export function formatNo(index: number) {
  return `00${index + 1}`.slice(-3)
}

export function formatCurrency(value?: number) {
  if (!value)
    return '0'
  return value.toLocaleString('ja-JP')
}

export function formatPercent(value: number) {
  return `${value}%`
}

/**
 * Combine date and time strings into datetime string
 * @param dateString Format: 'YYYY-MM-DD'
 * @param timeString Format: 'HH:mm:ss'
 * @returns Format: 'YYYY-MM-DD HH:mm:ss'
 */
export const combineDateAndTime = (dateString: string | null | undefined, timeString: string | null | undefined): string => {
  if (!dateString)
    return ''
  if (!timeString)
    return `${dateString} 00:00:00`

  // Ensure time has seconds
  const formattedTime = timeString.includes(':')
    ? timeString.split(':').length === 2
      ? `${timeString}:00`
      : timeString
    : `${timeString}:00:00`

  const baseDate = dayjs(`${dateString} 00:00:00`, 'YYYY-MM-DD HH:mm:ss')
  const [hours, minutes, seconds] = formattedTime.split(':').map(Number)
  const dur = dayjs.duration({ hours, minutes, seconds })
  const totalMs = dur.asMilliseconds()
  const result = baseDate.add(totalMs, 'ms')

  return result.format('YYYY-MM-DD HH:mm:ss')
}

export const convertToHHMM = (timeString: string | null | undefined): string => {
  if (!timeString)
    return '00:00'
  if (HHMMRegex.test(timeString))
    return timeString
  if (HHMMSSRegex.test(timeString)) {
    return timeString.split(':').slice(0, 2).join(':')
  }
  if (YYYYMMDDHHMMSSRegex.test(timeString)) {
    return timeString.split(' ')[1].split(':').slice(0, 2).join(':')
  }
  return '00:00'
}
