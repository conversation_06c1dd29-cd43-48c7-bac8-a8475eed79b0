<!-- eslint-disable antfu/top-level-function -->
<!-- eslint-disable curly -->
<script lang="ts" setup>
import type { Dayjs } from 'dayjs'
import type { UnwrapRef } from 'vue'
import { onClickOutside, useMagicKeys } from '@vueuse/core'
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons-vue'
import { NCollapseTransition } from 'naive-ui'
import ScheduleCell from './ScheduleCell.vue'
import ShiftCell from './ShiftCell.vue'
import type { EmployeeShift, OutsourceShift, OutsourceShiftParams, ProjectScheduleItem, ScheduleItem, ScheduleParams, ScheduleShiftParams, TargetInfo } from '~@/api/company/schedule'

const props = defineProps({
  projectScheduleData: {
    type: Array as () => ProjectScheduleItem[],
    default: () => [],
  },
  titleDate: {
    type: Object as () => Dayjs,
    required: true,
  },
  isShowAllEmployeeShift: {
    type: Boolean,
    required: true,
  },
  isEmployeeDragging: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits<{
  (event: 'saveClickedCellState', projectId: string, date: string): void
  (event: 'showShiftInfo', shift: EmployeeShift, projectId: string): void
  (event: 'showOutsourceShiftInfo', outsourceShift: OutsourceShift, projectId: string): void
  (event: 'showScheduleInfo', projectId: string, workingDate: string, type: 'EDIT' | 'ADD_NEW', schedule?: ScheduleItem): void
  (event: 'createEmployeeShift', projectId: string, scheduleId: string, params: ScheduleShiftParams): void
  (event: 'createOutsourceShift', projectId: string, scheduleId: string, params: OutsourceShiftParams): void
  (event: 'duplicateAnEmployeeShift', copiedEmployeeShiftId: string, targetCopyInfo: TargetInfo): void
  (event: 'duplicateAnOutsourceShift', copiedOutsourceshiftId: string, targetCopyInfo: TargetInfo): void
  (event: 'deleteSchedule', scheduleId: string): void
  (event: 'deleteEmployeeShift', employeeShiftId: string): void
  (event: 'deleteOutsourceShift', outsourceShiftId: string): void
  (event: 'updateIsEmployeeDragging', isDragging: boolean): void
  (event: 'createSchedule', params: ScheduleParams): void
}>()

const message = useMessage()
const keys = useMagicKeys()

// Ref Variables
const projectScheduleRef = ref<HTMLElement | null>(null)

// Data Variables
const copiedEmployeeShiftId = ref<string | undefined>(undefined)
const copiedOutsourceShiftId = ref<string | undefined>(undefined)
const targetCopyInfo = reactive<TargetInfo>({ projectId: undefined, workingDate: undefined })
const clickedEmployeeShift = ref<EmployeeShift | undefined>(undefined)
const clickedSchedule = ref<ScheduleItem | undefined>(undefined)
const clickedOutsourceShift = ref<OutsourceShift | undefined>(undefined)

// State Variables
const isEmployeeDragging = ref(false)
const isDraggingEnter = ref<Record<string, boolean>>({})
const isClickedEmployeeShift = ref<Record<string, boolean>>({})
const isDraggingOver = ref(false)
const isShowEmployeeShift: UnwrapRef<Record<string, boolean>> = reactive({})
const clickedCells: UnwrapRef<Record<string, boolean>> = reactive({})

// Methods
const handleProjectClicked = async (projectId: string) => {
  if (!props.isShowAllEmployeeShift) {
    isShowEmployeeShift[projectId] = !isShowEmployeeShift?.[projectId]
  }
}

const getScheduleId = (projectId: string, workingDate: string) => {
  if (!props.projectScheduleData)
    return undefined
  const idx = props.projectScheduleData.findIndex((project: ProjectScheduleItem) => project.projectId === projectId)
  if (idx === -1) {
    return undefined
  }
  const scheduleIdx = props.projectScheduleData[idx].schedules.findIndex((schedule: ScheduleItem) => schedule.workingDate === workingDate)
  if (scheduleIdx === -1) {
    return undefined
  }
  return props.projectScheduleData[idx].schedules[scheduleIdx].scheduleId
}

const onEmployeeDrop = (evt: DragEvent, projectId: string, workingDate: string, key?: string) => {
  isDraggingEnter.value[key ?? ''] = false
  const scheduleId = getScheduleId(projectId, workingDate)
  if (!evt.dataTransfer) {
    return
  }
  evt.preventDefault()
  if (!scheduleId) {
    message.error('Schedule is not found')
    // Tạo mới schedule
    const params: ScheduleParams = {
      projectId,
      workingDate,
      plannedWorkload: 1,
      estimatedWorkload: 1,
      description: '',
      shifts: [],
      outsourceShifts: [],
    }

    params.shifts!.push({
      employeeId: evt.dataTransfer.getData('employeeId'),
      startTime: '00:00:00',
      endTime: '00:00:00',
      assignedRole: 'UNASSIGNED',
    })
    emit('createSchedule', params)
  }
  else {
    const employeeId = evt.dataTransfer.getData('employeeId') || undefined
    const outsourceId = evt.dataTransfer.getData('outsourceId') || undefined
    if (employeeId) {
      const params: ScheduleShiftParams = {
        employeeId,
        startTime: '00:00:00',
        endTime: '00:00:00',
        assignedRole: 'UNASSIGNED',
      }
      emit('createEmployeeShift', projectId, scheduleId, params)
    }
    else if (outsourceId) {
      const params: OutsourceShiftParams = {
        outSourceId: outsourceId,
        startTime: '00:00:00',
        endTime: '00:00:00',
        assignedWorkload: 0,
        assignedRole: 'UNASSIGNED',
      }
      emit('createOutsourceShift', projectId, scheduleId, params)
    }
  }
  emit('updateIsEmployeeDragging', false)
}

const onOutsourceDrop = (evt: DragEvent, projectId: string, workingDate: string, key?: string) => {
  isDraggingEnter.value[key ?? ''] = false
  const scheduleId = getScheduleId(projectId, workingDate)
  if (!evt.dataTransfer || !scheduleId) {
    if (!scheduleId)
      message.error('Schedule is not found')
    return
  }
  evt.preventDefault()
  const outsourceId = evt.dataTransfer.getData('outsourceId')
  if (outsourceId)
    return
  const params: OutsourceShiftParams = {
    outSourceId: outsourceId,
    startTime: '00:00:00',
    endTime: '00:00:00',
    assignedWorkload: 0,
    assignedRole: 'UNASSIGNED',
  }
  emit('createOutsourceShift', projectId, scheduleId, params)
  emit('updateIsEmployeeDragging', false)
}

const onEmployeeDragLeave = (evt: DragEvent, key: string) => {
  if (!evt.dataTransfer)
    return
  isDraggingEnter.value[key] = false
}

const onEmployeeDragEnter = (evt: DragEvent, key: string) => {
  evt.preventDefault()
  // Object.keys(isDraggingEnter.value).forEach((key: string) => {
  //   isDraggingEnter.value[Number(key)] = false
  // })
  isDraggingEnter.value[key] = true
}

const onEmployeeDragOver = (evt: DragEvent) => {
  if (!evt.dataTransfer)
    return
  evt.preventDefault()
  isDraggingOver.value = true
  evt.dataTransfer.dropEffect = 'move'
}

const showShiftInfo = (shift: EmployeeShift, projectId: string) => {
  isClickedEmployeeShift.value = {}
  isClickedEmployeeShift.value[`${shift.employeeShiftId}`] = true
  clickedEmployeeShift.value = shift
  clickedOutsourceShift.value = undefined
  clickedSchedule.value = undefined
  emit('showShiftInfo', shift, projectId)
}

const showOutsourceShiftInfo = (outsourceShift: OutsourceShift, projectId: string) => {
  isClickedEmployeeShift.value = {}
  isClickedEmployeeShift.value[`${outsourceShift.outSourceShiftId}`] = true
  clickedOutsourceShift.value = outsourceShift
  clickedEmployeeShift.value = undefined
  clickedSchedule.value = undefined
  emit('showOutsourceShiftInfo', outsourceShift, projectId)
}

const showScheduleInfo = (evt: MouseEvent, projectId: string, workingDate: string, type: 'EDIT' | 'ADD_NEW', schedule?: ScheduleItem) => {
  if (evt.ctrlKey || evt.metaKey) {
    clickedCells[`${projectId}_${workingDate}`] = !clickedCells[`${projectId}_${workingDate}`]
  }
  else {
    if (!clickedCells[`${projectId}_${workingDate}`]) {
      Object.keys(clickedCells).forEach((key) => {
        clickedCells[key] = false
      })
      clickedCells[`${projectId}_${workingDate}`] = true
    }
    else {
      clickedCells[`${projectId}_${workingDate}`] = false
    }
  }
  if (clickedEmployeeShift.value) {
    isClickedEmployeeShift.value[`${clickedEmployeeShift.value.employeeShiftId}`] = false
  }
  if (clickedOutsourceShift.value) {
    isClickedEmployeeShift.value[`${clickedOutsourceShift.value.outSourceShiftId}`] = false
  }
  emit('showScheduleInfo', projectId, workingDate, type, schedule)
  clickedSchedule.value = schedule
}

// const getStyleByWorkingrole = (employeeShift: Shift) => {
//   let style = ''
//   switch (employeeShift.workingRole) {
//     case Skills.SUPERVISOR:
//       style = 'bg-[#FFEAE8] text-[#A72326]'
//       break
//     case Skills.FOREMAN:
//       style = 'bg-[#D0F3E9] text-[#11661E]'
//       break
//     case Skills.WORKER:
//       style = 'bg-[#F2EEFF] text-[#53318E]'
//       break
//     case Skills.FORMWORK_WORKER:
//       style = 'bg-[#F6F6D3] text-[#E66B0D]'
//       break
//     case Skills.HEAVY_MACHINERY_OPERATOR:
//       style = 'bg-[#DEF0FF] text-[#24598E]'
//       break
//     case Skills.REBAR_WORKER:
//       style = 'bg-[#FFE5CC] text-[#E66B0D]'
//       break
//     case Skills.CONTRACT_TYPE:
//       style = 'bg#F1F6FF]'
//       break
//     case Skills.UNASSIGNED:
//       style = 'bg-[#F1F6FF]'
//       break

//     default:
//       style = 'bg-[#F1F6FF] text-[#1554A3]'
//       break
//   }
//   return style
// }

const isScheduleExists = (projectId: string, workingDate: string) => {
  if (!props.projectScheduleData)
    return false
  const idx = props.projectScheduleData.findIndex((project: ProjectScheduleItem) => project.projectId === projectId)
  if (idx === -1) {
    return false
  }
  const scheduleIdx = props.projectScheduleData[idx].schedules.findIndex((schedule: ScheduleItem) => schedule.workingDate === workingDate)
  if (scheduleIdx === -1) {
    return false
  }
  return true
}

const saveTargetInfo = (projectId: string, workingDate: string) => {
  if (!isScheduleExists(projectId, workingDate)) {
    targetCopyInfo.projectId = undefined
    targetCopyInfo.workingDate = undefined
    return
  }
  targetCopyInfo.projectId = projectId
  targetCopyInfo.workingDate = workingDate
}

function handleClickOutsideProjectSchedule(event: any) {
  if (event.target.closest('.schedule-infor-form')) {
    return
  }
  Object.keys(clickedCells).forEach((key) => {
    clickedCells[key] = false
  })
}

onClickOutside(projectScheduleRef, handleClickOutsideProjectSchedule)

watch(props.projectScheduleData, (_) => {
}, {
  deep: true,
})

watch(() => props.isEmployeeDragging, (newVal) => {
  isEmployeeDragging.value = newVal
})

watch(keys['Ctrl+c'], (pressed) => {
  if (pressed) {
    if (clickedEmployeeShift.value) {
      copiedEmployeeShiftId.value = clickedEmployeeShift.value.employeeShiftId
      message.info('Copied employee shift success!')
    }
    else if (clickedOutsourceShift.value) {
      copiedOutsourceShiftId.value = clickedOutsourceShift.value.outSourceShiftId
      message.info('Copied outsource shift success!')
    }
  }
})

watch(keys['Ctrl+v'], (pressed) => {
  if (pressed) {
    if (!targetCopyInfo.projectId || !targetCopyInfo.workingDate) {
      message.error('Schedule not found')
      return
    }
    if (copiedEmployeeShiftId.value) {
      emit('duplicateAnEmployeeShift', copiedEmployeeShiftId.value, targetCopyInfo)
    }
    else if (copiedOutsourceShiftId.value) {
      emit('duplicateAnOutsourceShift', copiedOutsourceShiftId.value, targetCopyInfo)
    }
  }
})

watch(keys.Delete, (pressed) => {
  if (pressed) {
    if (clickedEmployeeShift.value) {
      emit('deleteEmployeeShift', clickedEmployeeShift.value.employeeShiftId)
    }
    else if (clickedOutsourceShift.value) {
      emit('deleteOutsourceShift', clickedOutsourceShift.value.outSourceShiftId)
    }
    else if (clickedSchedule.value) {
      emit('deleteSchedule', clickedSchedule.value.scheduleId)
    }
  }
})

onMounted(() => {
})
</script>

<template>
  <div
    v-for="(projectSchedule) in projectScheduleData"
    ref="projectScheduleRef" :key="projectSchedule.projectId"
    class="mb-1 bg-[white]"
  >
    <!-- Phần hiển thị thông tin shift -->
    <div class="flex border-t-1 border-b-1 border-l-0 border-r-0 border-gray-300 border-solid gap-x-2">
      <div
        class="flex justify-between p-2 border-r-1 border-l-0 border-t-0 border-b-0 border-gray-300 border-solid w-1/8"
        @click="handleProjectClicked(projectSchedule.projectId)"
      >
        <div>
          <div class="font-bold">
            {{ projectSchedule.projectCode }}&nbsp;-&nbsp;{{ projectSchedule.projectName }}
          </div>
          <!-- <div class="text-xs text-gray-500">
            {{ projectSchedule.projectCode }}
          </div> -->
        </div>
        <div class="cursor-pointer">
          <div v-if="isShowAllEmployeeShift || isShowEmployeeShift?.[projectSchedule.projectId]">
            <CaretUpOutlined />
          </div>
          <div v-else>
            <CaretDownOutlined />
          </div>
        </div>
      </div>
      <div class="flex w-7/8 bg-[white] border-l-1 border-r-0 border-t-0 border-b-0 border-gray-300 border-solid">
        <ScheduleCell
          :title-date="titleDate.format('YYYY-MM-DD')"
          :project-id="projectSchedule.projectId"
          :schedules="projectSchedule.schedules"
          :clicked-cells="clickedCells"
          @show-schedule-info="showScheduleInfo"
        />
      </div>
    </div>

    <!-- Phần hiển thị thông tin userShift -->
    <NCollapseTransition :show="isShowAllEmployeeShift || isShowEmployeeShift?.[projectSchedule.projectId]" class="flex border-t-1 border-b-1 border-l-0 border-r-0 border-gray-300 border-solid gap-x-2">
      <template v-if="isShowAllEmployeeShift || isShowEmployeeShift?.[projectSchedule.projectId]">
        <div class="p-2 border-r-1 border-l-0 border-t-0 border-b-0 border-gray-300 border-solid w-1/8" @click="handleProjectClicked(projectSchedule.projectId)">
          <div class="font-bold" />
          <div class="text-xs text-gray-500" />
        </div>
        <div
          class="flex w-7/8 bg-[white] border-l-1 border-r-0 border-t-0 border-b-0 border-gray-300 border-solid min-h-[100px]"
        >
          <ShiftCell
            :project-id="projectSchedule.projectId"
            :title-date="titleDate.format('YYYY-MM-DD')"
            :shifts="projectSchedule.shifts"
            :outsource-shifts="projectSchedule.outsourceShifts"
            :is-employee-dragging="isEmployeeDragging"
            :is-dragging-enter="isDraggingEnter"
            :is-clicked-employee-shift="isClickedEmployeeShift"
            @save-target-info="saveTargetInfo"
            @on-employee-drop="onEmployeeDrop"
            @on-outsource-drop="onOutsourceDrop"
            @on-employee-drag-over="onEmployeeDragOver"
            @on-employee-drag-enter="onEmployeeDragEnter"
            @on-employee-drag-leave="onEmployeeDragLeave"
            @show-shift-info="showShiftInfo"
            @show-outsource-shift-info="showOutsourceShiftInfo"
          />
        </div>
      </template>
    </NCollapseTransition>
  </div>
</template>

<style scoped>
</style>
