<script lang="ts" setup>
import type { ConstructionItem } from '~@/api/construction'
import { getConstructionByIdApi } from '~@/api/construction'
import type { ConstructionCostItem } from '~@/api/construction-cost'

const props = defineProps({
  constructionCost: {
    type: Object as () => ConstructionCostItem,
    required: true,
  },
})

const { t } = useI18n()
const messageNotify = useMessage()

const construction = ref<ConstructionItem | null>()

const previousAmount = computed(() => {
  return props.constructionCost?.lastAccumulateCost?.requestAmount ?? 0
})
const previousProgressPercentage = computed(() => {
  if (!construction.value?.contractualCosts?.totalModifiedCost)
    return 0
  return (previousAmount.value / construction.value?.contractualCosts?.totalModifiedCost) * 100
})

const currentAmount = computed(() => {
  return props.constructionCost?.currentCost?.requestAmount ?? 0
})

const currentProgressPercentage = computed(() => {
  if (!construction.value?.contractualCosts?.totalModifiedCost)
    return 0
  return (currentAmount.value / construction.value?.contractualCosts?.totalModifiedCost) * 100
})

const accumulatedAmount = computed(() => {
  return props.constructionCost?.currentAccumulateCost?.requestAmount ?? 0
})

const accumulatedProgressPercentage = computed(() => {
  if (!construction.value?.contractualCosts?.totalModifiedCost)
    return 0
  return (accumulatedAmount.value / construction.value?.contractualCosts?.totalModifiedCost) * 100
})

const remainingBalance = computed(() => {
  return (construction.value?.contractualCosts?.totalModifiedCost ?? 0) - accumulatedAmount.value
})

async function fetchConstructionById() {
  const constructionId = props.constructionCost.constructionId
  const { data, status, message } = await getConstructionByIdApi(constructionId)
  if (status === 200) {
    construction.value = data
  }
  else {
    messageNotify.error(message)
  }
}

onMounted(() => {
  // Cần 1 API call đến -> construction/{id}
  fetchConstructionById()
})
</script>

<template>
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-xl font-bold text-gray-800">
      {{ construction?.constructionName }}
    </h2>
    <a-tag v-if="constructionCost?.isPrimary" color="blue">
      {{ t('mainConstruction') }}
    </a-tag>
    <a-tag v-else color="green">
      {{ t('subConstruction') }}
    </a-tag>
  </div>

  <div class="space-y-2">
    <div class="flex justify-between items-center">
      <div class="flex items-center gap-2">
        <span class="text-gray-600">{{ t('previousAmount') }}</span>
        <span class="font-bold text-green-700">{{ previousAmount }}</span>
      </div>
      <div class="font-bold px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
        {{ previousProgressPercentage }}%
      </div>
    </div>

    <div class="flex justify-between items-center">
      <div class="flex items-center gap-2">
        <span class="text-gray-600">{{ t('currentAmount') }}</span>
        <span class="font-bold text-blue-700">{{ currentAmount }}</span>
      </div>
      <div class="font-bold px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
        {{ currentProgressPercentage }}%
      </div>
    </div>

    <div class="border-t pt-4 space-y-2">
      <div class="flex justify-between items-center">
        <div class="flex items-center gap-2">
          <span class="text-gray-600">{{ t('accumulatedAmount') }}</span>
          <span class="font-bold text-red-700">{{ accumulatedAmount }}</span>
        </div>
        <div
          class="font-bold px-3 py-1 rounded-full text-sm"
          :class="accumulatedProgressPercentage > 0
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'"
        >
          {{ accumulatedProgressPercentage }}%
        </div>
      </div>
      <div class="flex justify-between items-center">
        <div class="flex items-center gap-2">
          <span class="text-gray-600">{{ t('remainingBalance') }}</span>
          <span class="font-bold text-red-700">{{ remainingBalance }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
