<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { format } from 'date-fns'
// import type { Dayjs } from 'dayjs'
import { useEmployees } from '~@/composables/employee/useEmployees'
import { useOutsourcess } from '~@/composables/outsource/useOutsource'
import type { ScheduleParams } from '~@/api/company/schedule'
import { SCHEDULE_ROLES } from '~@/api/company/schedule'
import { useWorkshifts } from '~@/composables/worshifts/useWorkshifts'
import type { WorkshiftItem } from '~@/api/company/work-shift'
import { convertToHHMM, formatTimeToHHMMSS } from '~@/utils/apiTimer'

const props = defineProps({
  projectOptions: {
    type: Array as () => { label: string; value: string }[],
  },
})

const emit = defineEmits(['createSchedule'])

const { t } = useI18n()
const loading = ref(false)

const { employeesOptions, fetchEmployees } = useEmployees()
const { outsourcesOptions, fetchOutsources } = useOutsourcess()
const { workshifts, fetchWorkshifts } = useWorkshifts()

// Form data
const scheduleFormState = reactive<ScheduleParams>({
  projectId: '',
  workingDate: '',
  plannedWorkload: 8,
  estimatedWorkload: 8,
  description: '',
  shifts: [],
  outsourceShifts: [],
})

// Form ref
const formRef = ref()

// Add new employee shift
function addEmployeeShift() {
  if (!scheduleFormState.shifts)
    return
  scheduleFormState.shifts.push({
    employeeId: '',
    startTime: '08:00',
    endTime: '17:00',
    totalScheduledWorkTime: 8,
    assignedRole: '',
  })
}

// Add new outsource shift
function addOutsourceShift() {
  if (!scheduleFormState.outsourceShifts)
    return
  scheduleFormState.outsourceShifts.push({
    outSourceId: '',
    startTime: '08:00',
    endTime: '17:00',
    assignedWorkload: 0,
    assignedRole: '',
  })
}

// Remove employee shift
function removeEmployeeShift(index: number) {
  if (!scheduleFormState.shifts)
    return
  scheduleFormState.shifts.splice(index, 1)
}

// Remove outsource shift
function removeOutsourceShift(index: number) {
  if (!scheduleFormState.outsourceShifts)
    return
  scheduleFormState.outsourceShifts.splice(index, 1)
}

// Calculate work time based on start and end times
function calculateWorkTime(startTime?: string, endTime?: string): number {
  if (!startTime || !endTime)
    return 0
  const start = new Date(`2000-01-01T${startTime}`)
  const end = new Date(`2000-01-01T${endTime}`)

  // Calculate difference in hours
  return (end.getTime() - start.getTime()) / (1000 * 60 * 60)
}

// Update total work time when time changes
function updateEmployeeWorkTime(index: number) {
  if (!scheduleFormState.shifts)
    return
  const shift = scheduleFormState.shifts[index]
  shift.totalScheduledWorkTime = calculateWorkTime(shift.startTime, shift.endTime)
}

// // Time format change handler
// function onTimeChange(time: string | Dayjs | null, timeString: string, shiftIndex: number, field: 'startTime' | 'endTime', type: 'EMPLOYEE' | 'OUTSOURCE') {
//   if (!scheduleFormState?.shifts || !scheduleFormState?.outsourceShifts)
//     return
//   if (time && scheduleFormState.shifts[shiftIndex]) {
//     if (type === 'EMPLOYEE') {
//       scheduleFormState.shifts[shiftIndex][field] = timeString
//       updateEmployeeWorkTime(shiftIndex)
//     }
//     else if (type === 'OUTSOURCE') {
//       scheduleFormState.outsourceShifts[shiftIndex][field] = timeString
//     }
//   }
// }

// function handleTimeSelect(time: Dayjs | null, index: number, field: 'startTime' | 'endTime', type: 'EMPLOYEE' | 'OUTSOURCE') {
//   if (!scheduleFormState?.shifts || !scheduleFormState?.outsourceShifts)
//     return
//   if (time && scheduleFormState.shifts![index]) {
//     if (type === 'EMPLOYEE') {
//       scheduleFormState.shifts![index][field] = time.format('HH:mm')
//       updateEmployeeWorkTime(index)
//     }
//     else if (type === 'OUTSOURCE') {
//       scheduleFormState.outsourceShifts[index][field] = time.format('HH:mm')
//     }
//   }
// }

// Get employee name by ID
function getEmployeeName(id: string): string {
  const employee = employeesOptions.value.find(emp => emp.value === id)
  return employee ? employee.label : t('notSelected')
}

// Get outsource name by ID
function getOutsourceName(id: string): string {
  const outsource = outsourcesOptions.value.find(src => src.value === id)
  return outsource ? outsource.label : t('notSelected')
}

// Get project name by ID
function getProjectName(id: string): string {
  if (!props.projectOptions)
    return t('notSelected')
  const project = props.projectOptions.find(proj => proj.value === id)
  return project ? project.label : t('notSelected')
}

// Form submission
const submitForm = useDebounceFn(() => {
  formRef.value
    .validate()
    .then(() => {
      if (!scheduleFormState.shifts || !scheduleFormState.outsourceShifts)
        return
      // Here you would typically send the data to an API
      scheduleFormState.shifts.forEach((shift) => {
        shift.startTime = formatTimeToHHMMSS(shift.startTime)
        shift.endTime = formatTimeToHHMMSS(shift.endTime)
      })
      scheduleFormState.outsourceShifts.forEach((shift) => {
        shift.startTime = formatTimeToHHMMSS(shift.startTime)
        shift.endTime = formatTimeToHHMMSS(shift.endTime)
      })

      const params: ScheduleParams = {
        projectId: scheduleFormState.projectId,
        workingDate: scheduleFormState.workingDate,
        plannedWorkload: scheduleFormState.plannedWorkload,
        estimatedWorkload: scheduleFormState.estimatedWorkload,
        description: scheduleFormState.description,
        shifts: scheduleFormState.shifts,
        outsourceShifts: scheduleFormState.outsourceShifts,
      }
      emit('createSchedule', params)
    })
    .catch((_error: any) => {
      loading.value = false
    })
}, 300)

// Reset form
function resetForm() {
  formRef.value.resetFields()
  scheduleFormState.projectId = ''
  scheduleFormState.workingDate = format(new Date(), 'yyyy-MM-dd')
  scheduleFormState.plannedWorkload = 8
  scheduleFormState.estimatedWorkload = 8
  scheduleFormState.description = ''
  scheduleFormState.shifts = []
  scheduleFormState.outsourceShifts = []
}

const handleEmployeeWorkshiftChange = (value: string | undefined, index: number) => {
  if (!workshifts.value || !value)
    return
  const workshift = workshifts.value.find((item: WorkshiftItem) => item.workShiftId === value)
  if (!workshift)
    return

  if (!scheduleFormState?.shifts)
    return
  scheduleFormState.shifts[index].startTime = convertToHHMM(workshift.checkInTime)
  scheduleFormState.shifts[index].endTime = convertToHHMM(workshift.checkOutTime)
  scheduleFormState.shifts[index].totalScheduledWorkTime = workshift.totalRequiredTime
}

const handleOutsourceWorkshiftChange = (value: string | undefined, index: number) => {
  if (!workshifts.value || !value)
    return
  const workshift = workshifts.value.find((item: WorkshiftItem) => item.workShiftId === value)
  if (!workshift)
    return

  if (!scheduleFormState?.outsourceShifts)
    return
  scheduleFormState.outsourceShifts[index].startTime = workshift.checkInTime
  scheduleFormState.outsourceShifts[index].endTime = workshift.checkOutTime
  scheduleFormState.outsourceShifts[index].assignedWorkload = Number((calculateWorkTime(workshift.checkInTime, workshift.checkOutTime) / workshift.totalRequiredTime).toFixed(2))
}

onMounted(() => {
  fetchEmployees()
  fetchOutsources()
  fetchWorkshifts()
})
</script>

<template>
  <a-card :bordered="false" class="work-schedule-card">
    <a-form
      ref="formRef"
      :model="scheduleFormState"
      layout="vertical"
      @finish="submitForm"
    >
      <!-- Thông tin cơ bản -->
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item
            :label="t('project')"
            name="projectId"
            :rules="[{ required: true, message: t('message.please-select', { msg: t('project') }) }]"
          >
            <a-select
              v-model:value="scheduleFormState.projectId"
              :placeholder="t('placeholder.select', { msg: t('project') })"
              show-search
              option-filter-prop="label"
            >
              <a-select-option v-for="project in projectOptions" :key="project.value" :value="project.value" :label="project.label">
                {{ project.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            :label="t('workingDate')"
            name="workingDate"
            :rules="[{ required: true, message: t('message.please-select', { msg: t('workingDate') }) }]"
          >
            <a-date-picker
              v-model:value="scheduleFormState.workingDate"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item
            :label="t('schedule.plannedWorkload')"
            name="plannedWorkload"
            :rules="[{ required: true, message: t('message.please-enter', { msg: t('schedule.plannedWorkload') }) }]"
          >
            <a-input-number
              v-model:value="scheduleFormState.plannedWorkload"
              :min="0"
              :step="0.5"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            :label="t('schedule.estimatedWorkload')"
            name="estimatedWorkload"
            :rules="[{ required: true, message: t('message.please-enter', { msg: t('schedule.estimatedWorkload') }) }]"
          >
            <a-input-number
              v-model:value="scheduleFormState.estimatedWorkload"
              :min="0"
              :step="0.5"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item
        :label="t('description')"
        name="description"
        :rules="[{ required: false, message: t('message.please-enter', { msg: t('description') }) }]"
      >
        <a-textarea
          v-model:value="scheduleFormState.description"
          :rows="3"
          :placeholder="t('placeholder.enter', { msg: t('description') })"
        />
      </a-form-item>

      <!-- Ca làm việc nhân viên -->
      <a-card class="custom-card" :bordered="true" :title="t('employeeShift')">
        <template #extra>
          <a-button type="primary" class="flex items-center" @click="addEmployeeShift">
            <template #icon>
              <PlusOutlined />
            </template>
            {{ t('button.add') }}
          </a-button>
        </template>

        <a-empty v-if="scheduleFormState?.shifts?.length === 0" :description="t('message.noEmployeeShift')" />

        <div v-for="(shift, index) in scheduleFormState.shifts" :key="index">
          <a-divider v-if="index > 0" />

          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item
                :label="`${t('employee')} ${index + 1}`"
                :name="['employeeShifts', index, 'employeeId']"
                :rules="[{ required: true, message: t('message.please-select', { msg: t('employee') }) }]"
              >
                <a-select
                  v-model:value="shift.employeeId"
                  :placeholder="t('placeholder.select', { msg: t('employee') })"
                  show-search
                  option-filter-prop="label"
                  style="width: 100%"
                >
                  <a-select-option v-for="employee in employeesOptions" :key="employee.value" :value="employee.value" :label="employee.label">
                    {{ employee.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                :label="`${t('role')} ${index + 1}`"
                :name="['employeeShifts', index, 'assignedRole']"
                :rules="[{ required: true, message: t('message.please-select', { msg: t('role') }) }]"
              >
                <a-select
                  v-model:value="shift.assignedRole"
                  :placeholder="t('placeholder.select', { msg: t('role') })"
                  show-search
                  style="width: 100%"
                >
                  <a-select-option v-for="role in SCHEDULE_ROLES" :key="role" :value="role">
                    {{ t(role) }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8" style="display: flex; justify-content: flex-end; align-items: flex-end; padding-bottom: 24px;">
              <a-button type="text" class="flex items-center justify-center" @click="removeEmployeeShift(index)">
                <template #icon>
                  <DeleteOutlined class="text-red-500" />
                </template>
              </a-button>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item
                :label="t('workshift')"
                :name="['employeeShifts', index, 'workshift']"
              >
                <a-select
                  v-model:value="shift.workshiftId"
                  :placeholder="t('placeholder.select', { msg: t('workshift') })"
                  style="width: 100%"
                  allow-clear
                  @change="(value) => handleEmployeeWorkshiftChange(value?.toString(), index)"
                >
                  <a-select-option v-for="workshift in workshifts" :key="workshift.workShiftId" :label="workshift.workShiftName" :value="workshift.workShiftId">
                    {{ workshift?.workShiftName }} ({{ workshift?.checkInTime }} - {{ workshift?.checkOutTime }})
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                :label="t('startTime')"
                :name="['employeeShifts', index, 'startTime']"
                :rules="[{ required: true, message: t('message.please-select', { msg: t('startTime') }) }]"
              >
                <!-- <a-time-picker
                  v-model:value="shift.startTime"
                  format="HH:mm"
                  value-format="HH:mm"
                  style="width: 100%"
                  :disabled="!!shift.workshiftId"
                  @change="(time: string | Dayjs | null, timeString: string) => onTimeChange(time, timeString, index, 'startTime', 'EMPLOYEE')"
                  @select="(time: Dayjs) => handleTimeSelect(time, index, 'startTime', 'EMPLOYEE')"
                /> -->
                <TimePicker 
                  v-model:value="shift.startTime"
                  value-type="string"
                  value-format="HH:mm"
                  :disabled="!!shift.workshiftId"
                  @change="() => updateEmployeeWorkTime(index)"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                :label="t('endTime')"
                :name="['employeeShifts', index, 'endTime']"
                :rules="[{ required: true, message: t('message.please-select', { msg: t('endTime') }) }]"
              >
                <!-- <a-time-picker
                  v-model:value="shift.endTime"
                  format="HH:mm"
                  value-format="HH:mm"
                  :disabled="!!shift.workshiftId"
                  style="width: 100%"
                  @change="(time: string | Dayjs | null, timeString: string) => onTimeChange(time, timeString, index, 'endTime', 'EMPLOYEE')"
                  @select="(time: Dayjs) => handleTimeSelect(time, index, 'endTime', 'EMPLOYEE')"
                /> -->
                <TimePicker 
                  v-model:value="shift.endTime"
                  value-type="string"
                  value-format="HH:mm"
                  :disabled="!!shift.workshiftId"
                  @change="() => updateEmployeeWorkTime(index)"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row>
            <a-col :span="24" style="text-align: right;">
              <a-space>
                <a-typography-text>{{ t('totalScheduledWorkTime') }}:</a-typography-text>
                <a-typography-text v-if="shift?.totalScheduledWorkTime" strong>
                  {{ shift?.totalScheduledWorkTime.toFixed(2) }}
                </a-typography-text>
              </a-space>
            </a-col>
          </a-row>
        </div>
      </a-card>

      <!-- Ca làm việc thuê ngoài -->
      <a-card class="custom-card" :bordered="true" :title="t('outsourcingShift')" style="margin-top: 24px;">
        <template #extra>
          <a-button type="primary" class="flex items-center" @click="addOutsourceShift">
            <template #icon>
              <PlusOutlined />
            </template>
            {{ t('button.add') }}
          </a-button>
        </template>

        <a-empty v-if="scheduleFormState?.outsourceShifts?.length === 0" :description="t('message.noOutsourceShift')" />

        <div v-for="(shift, index) in scheduleFormState.outsourceShifts" :key="index">
          <a-divider v-if="index > 0" />

          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item
                :label="`${t('outsourcing')} ${index + 1}`"
                :name="['outsourceShifts', index, 'outSourceId']"
                :rules="[{ required: true, message: t('message.please-select', { msg: t('outsourcing') }) }]"
              >
                <a-select
                  v-model:value="shift.outSourceId"
                  :placeholder="t('placeholder.select', { msg: t('outsourcing') })"
                  show-search
                  option-filter-prop="label"
                  style="width: 100%"
                >
                  <a-select-option v-for="outsource in outsourcesOptions" :key="outsource.value" :value="outsource.value" :label="outsource.label">
                    {{ outsource.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                :label="`${t('role')} ${index + 1}`"
                :name="['outsourceShifts', index, 'assignedRole']"
                :rules="[{ required: true, message: t('message.please-select', { msg: t('role') }) }]"
              >
                <a-select
                  v-model:value="shift.assignedRole"
                  :placeholder="t('placeholder.select', { msg: t('role') })"
                  show-search
                  style="width: 100%"
                >
                  <a-select-option v-for="role in SCHEDULE_ROLES" :key="role" :value="role">
                    {{ t(role) }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8" style="display: flex; justify-content: flex-end; align-items: flex-end; padding-bottom: 24px;">
              <a-button type="text" class="flex items-center justify-center" @click="removeOutsourceShift(index)">
                <template #icon>
                  <DeleteOutlined class="text-red-500" />
                </template>
              </a-button>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item
                :label="t('workshift')"
                :name="['outsourceShifts', index, 'workshift']"
              >
                <a-select
                  v-model:value="shift.workshiftId"
                  :placeholder="t('placeholder.select', { msg: t('workshift') })"
                  style="width: 100%"
                  allow-clear
                  @change="(value) => handleOutsourceWorkshiftChange(value?.toString(), index)"
                >
                  <a-select-option v-for="workshift in workshifts" :key="workshift.workShiftId" :label="workshift.workShiftName" :value="workshift.workShiftId">
                    {{ workshift?.workShiftName }} ({{ workshift?.checkInTime }} - {{ workshift?.checkOutTime }})
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                :label="t('startTime')"
                :name="['outsourceShifts', index, 'startTime']"
                :rules="[{ required: true, message: t('message.please-select', { msg: t('startTime') }) }]"
              >
                <!-- <a-time-picker
                  v-model:value="shift.startTime"
                  format="HH:mm"
                  value-format="HH:mm"
                  style="width: 100%"
                  :disabled="!!shift.workshiftId"
                  @change="(time: string | Dayjs | null, timeString: string) => onTimeChange(time, timeString, index, 'startTime', 'OUTSOURCE')"
                  @select="(time: Dayjs) => handleTimeSelect(time, index, 'startTime', 'OUTSOURCE')"
                /> -->
                <TimePicker 
                  v-model:value="shift.startTime"
                  value-type="string"
                  value-format="HH:mm"
                  :disabled="!!shift.workshiftId"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                :label="t('endTime')"
                :name="['outsourceShifts', index, 'endTime']"
                :rules="[{ required: true, message: t('message.please-select', { msg: t('endTime') }) }]"
              >
                <!-- <a-time-picker
                  v-model:value="shift.endTime"
                  format="HH:mm"
                  value-format="HH:mm"
                  style="width: 100%"
                  :disabled="!!shift.workshiftId"
                  @change="(time: string | Dayjs | null, timeString: string) => onTimeChange(time, timeString, index, 'endTime', 'OUTSOURCE')"
                  @select="(time: Dayjs) => handleTimeSelect(time, index, 'endTime', 'OUTSOURCE')"
                /> -->
                <TimePicker 
                  v-model:value="shift.endTime"
                  value-type="string"
                  value-format="HH:mm"
                  :disabled="!!shift.workshiftId"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row>
            <a-col :span="24" style="text-align: right;">
              <a-space>
                <a-typography-text>{{ t('workload') }}:</a-typography-text>
                <a-input-number
                  v-model:value="shift.assignedWorkload"
                  :min="0"
                  :step="0.5"
                  style="width: 100px"
                  :disabled="!!shift.workshiftId"
                />
              </a-space>
            </a-col>
          </a-row>
        </div>
      </a-card>

      <!-- Tổng quan -->
      <a-card class="custom-card overview-card" :bordered="true" :title="t('overview')" style="margin-top: 24px; background-color: #f0f5ff; border-color: #d6e4ff;">
        <a-descriptions :column="1">
          <a-descriptions-item v-if="scheduleFormState.projectId" :label="t('project')">
            {{ getProjectName(scheduleFormState.projectId) }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('workingDate')">
            {{ scheduleFormState.workingDate }}
          </a-descriptions-item>
          <a-descriptions-item :label="t('workload')">
            {{ scheduleFormState.plannedWorkload }} {{ `${t('oclock')} (${t('schedule.plannedWorkload')})` }} / {{ scheduleFormState.estimatedWorkload }} {{ `${t('oclock')} (${t('schedule.estimatedWorkload')})` }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider />

        <div v-if="scheduleFormState?.shifts?.length && scheduleFormState?.shifts?.length > 0">
          <a-typography-title :level="5">
            {{ t('employee') }}:
          </a-typography-title>
          <a-list size="small">
            <a-list-item v-for="(shift, index) in scheduleFormState.shifts" :key="`emp-${index}`">
              {{ getEmployeeName(shift.employeeId) || t('notSelected') }}&nbsp;:&nbsp;{{ shift?.assignedRole ? t(shift.assignedRole) : t('notAssigned') }} ({{ shift.startTime }} - {{ shift.endTime }})
            </a-list-item>
          </a-list>
        </div>

        <div v-if="scheduleFormState?.outsourceShifts?.length && scheduleFormState?.outsourceShifts?.length > 0" style="margin-top: 16px;">
          <a-typography-title :level="5">
            {{ t('outsourcingShift') }}:
          </a-typography-title>
          <a-list size="small">
            <a-list-item v-for="(shift, index) in scheduleFormState.outsourceShifts" :key="`out-${index}`">
              {{ getOutsourceName(shift.outSourceId) || t('notSelected') }}&nbsp;:&nbsp;{{ shift?.assignedRole ? t(shift?.assignedRole) : t('notAssigned') }} ({{ shift.assignedWorkload }} {{ t('workload') }})
            </a-list-item>
          </a-list>
        </div>
      </a-card>

      <!-- Buttons -->
      <div style="margin-top: 24px; text-align: right;">
        <a-space>
          <a-button @click="resetForm">
            {{ t('button.reset') }}
          </a-button>
          <a-button type="primary" html-type="submit" :loading="loading" @click="submitForm">
            {{ t('button.create') }}
          </a-button>
        </a-space>
      </div>
    </a-form>
  </a-card>
</template>

<style scoped>
.work-schedule-card {
  max-width: 700px;
  margin: 0 auto;
}

.custom-card {
  background-color: #fff;
  border-radius: 8px;
}

.overview-card .ant-descriptions-item-label {
  font-weight: bold;
}
</style>
