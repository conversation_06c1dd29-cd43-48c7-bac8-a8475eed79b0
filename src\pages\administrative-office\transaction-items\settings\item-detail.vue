<script lang="ts" setup>
import {
  CloseOutlined,
  PlusOutlined,
  ToTopOutlined,
} from '@ant-design/icons-vue';
import { ColumnGroupType, ColumnType } from 'ant-design-vue/es/table';
import {
  CostCategoryItem,
  createCostCategory,
  deleteCostCategory,
  getCostCategory,
  updateCostCategory,
} from '~@/api/company/cost-category';
import { ResponseStatusEnum } from '~@/enums/response-status-enum';
import logger from '~@/utils/logger';

const props = defineProps({
  parentId: {
    type: String,
    default: '',
  },
});

type ColumnItemType<T> = ColumnGroupType<T> | ColumnType<T>;

type CostCategoryType = CostCategoryItem & {
  isEdit?: boolean;
  isAdd?: boolean;
};

interface Params {
  parentId?: string;
}

const { t } = useI18n();
const loading = ref(false);
const categories = defineModel<CostCategoryType[]>('categories', {
  default: [],
});
const editableData = defineModel<Record<string, CostCategoryType>>(
  'editableData',
  { default: {} }
);

const queryData = async (params?: Params) => {
  loading.value = true;
  const res = await getCostCategory(params);
  categories.value.push(...(res.data?.items ?? []));
  loading.value = false;
};

const columns = computed<ColumnItemType<CostCategoryType>[]>(() => [
  { title: t('form.code'), dataIndex: 'code', width: 150 },
  { title: t('form.category'), dataIndex: 'name', width: 150 },
  { title: t('form.products'), dataIndex: 'products', width: 150 },
  { title: t('note'), dataIndex: 'note', width: 150 },
  {
    title: t('action'),
    dataIndex: 'action',
    width: 150,
    align: 'center',
    fixed: 'right',
  },
]);

const handleCreate = async (key: string) => {
  try {
    const create = await createCostCategory({
      categoryCode: editableData.value[key]?.categoryCode,
      categoryName: editableData.value[key]?.categoryName,
      description: editableData.value[key]?.description,
      parentId: props.parentId,
    });
    if (create.status !== ResponseStatusEnum.SUCCESS) return;

    Object.assign(
      categories.value.filter((item) => key === item.categoryId)[0],
      create.data
    );
    delete editableData.value[key];
  } catch (error) {
    logger.error(error);
  }
};

const handleUpdate = async (key: string) => {
  try {
    const update = await updateCostCategory(key, {
      categoryCode: editableData.value[key]?.categoryCode,
      categoryName: editableData.value[key]?.categoryName,
      description: editableData.value[key]?.description,
      parentId: props.parentId,
    });
    if (update.status !== ResponseStatusEnum.SUCCESS) return;

    Object.assign(
      categories.value.filter((item) => key === item.categoryId)[0],
      update.data
    );
    delete editableData.value[key];
  } catch (error) {
    logger.error(error);
  }
};

const handleEdit = (key: string) => {
  const findCategory = categories.value.find((item) => key === item.categoryId);
  if (!findCategory) return;
  editableData.value[key] = { ...findCategory, isEdit: true, isAdd: false };
};

const handleDelete = async (key: string) => {
  try {
    const deleted = await deleteCostCategory(key);
    if (deleted.status !== ResponseStatusEnum.SUCCESS) return;

    categories.value = categories.value.filter(
      (item) => key !== item.categoryId
    );
  } catch (error) {
    logger.error(error);
  }
};

const handleCancel = (key: string) => {
  categories.value = categories.value.filter((item) => {
    if (key !== item.categoryId) return true;
    if (editableData.value[key].isEdit) return true;
    return false;
  });
  delete editableData.value[key];
};

onMounted(async () => {
  await queryData({ parentId: props.parentId });
});
</script>

<template>
  <div>
    <a-table
      class="tableCategory"
      :scroll="{ x: 'max-content' }"
      :columns="columns"
      :data-source="categories"
      :loading="loading"
      :pagination="false"
      row-key="categoryId"
      :show-header="false"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'code'">
          <div>
            <a-input
              v-if="editableData[record.categoryId]"
              v-model:value="editableData[record.categoryId]['categoryCode']"
              :placeholder="t('form.code')"
            />
            <template v-else>
              {{ record.categoryCode }}
            </template>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'name'">
          <a-input
            v-if="editableData[record.categoryId]"
            v-model:value="editableData[record.categoryId]['categoryName']"
            :placeholder="t('form.category')"
          />
          <template v-else>
            {{ record.categoryName }}
          </template>
        </template>
        <template v-else-if="column.dataIndex === 'note'">
          <a-input
            v-if="editableData[record.categoryId]"
            v-model:value="editableData[record.categoryId]['description']"
            :placeholder="t('form.note')"
          />
          <template v-else>
            {{ record.description }}
          </template>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <div
            v-if="editableData[record.categoryId]"
            class="flex flex-justify-center gap-2"
          >
            <a-button
              v-if="editableData[record.categoryId].isAdd"
              class="flex items-center"
              size="small"
              type="primary"
              @click="handleCreate(record.categoryId)"
            >
              <PlusOutlined />
            </a-button>
            <a-button
              v-if="editableData[record.categoryId].isEdit"
              class="flex items-center"
              size="small"
              type="primary"
              @click="handleUpdate(record.categoryId)"
            >
              <ToTopOutlined />
            </a-button>
            <a-popconfirm
              :title="t('message.cancel-confirmation')"
              @confirm="handleCancel(record.categoryId)"
            >
              <a-button class="flex items-center" size="small" danger>
                <CloseOutlined />
              </a-button>
            </a-popconfirm>
          </div>
          <template v-else>
            <div class="flex flex-justify-center gap-2">
              <a-button
                class="flex items-center"
                size="small"
                type="text"
                @click="handleEdit(record.categoryId)"
              >
                <img src="/icon/edit.svg" class="w-[20px]" />
              </a-button>
              <a-popconfirm
                :title="t('message.delete-confirmation')"
                @confirm="handleDelete(record.categoryId)"
              >
                <a-button class="flex items-center" size="small" type="text">
                  <img src="/icon/delete.svg" class="w-[20px]" />
                </a-button>
              </a-popconfirm>
            </div>
          </template>
        </template>
      </template>
      <template #expandIcon>
        <span />
      </template>
      <template #expandedRowRender>
        <span />
      </template>
      <template #expandColumnTitle>
        <span />
      </template>
    </a-table>
  </div>
</template>

<style lang="less" scoped>
.tableCategory {
  :deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
    background: #f2f8fd;
  }
  :deep(.ant-table-tbody > tr) {
    border-radius: 50px;
    td:first-child {
      border: 0;
      background: #fff !important;
    }
    // td second child
    td:nth-child(2) {
      border-bottom-left-radius: 10px;
    }
  }
}
</style>
