export interface Item {
  createTime: string
  updateTime: string
  itemId: string
  itemCode: string
  itemName: string
  itemSubName: string
  size: string
  serialNumber: string
  categoryId: string
  categoryCode: string
  categoryName: string
  manufacturerId: string
  manufacturerCode: string
  manufacturerName: string
  description: string
}

export interface ItemResponse {
  items: Item[]
  pageNum: number
  pageSize: number
  totalRecords: number
}

export interface ItemParams {
  itemCode: string
  itemName: string
  itemSubName: string
  size: string
  serialNumber: string
  categoryId: string
  manufacturerId: string
  description: string
}

export interface ItemFilterRequest {
  keyword?: string
  categoryId?: string
  manufacturerId?: string
  pageNum?: number
  pageSize?: number
}

export async function getItemListApi(params: ItemFilterRequest) {
  return useGet<ItemResponse>('v1/cost/item', params)
}
