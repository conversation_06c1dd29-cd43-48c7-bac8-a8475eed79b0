import type { FunctionItem } from '~/api/company/permission'
import { getPermissionMenuFunc } from '~/api/company/permission'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'
import logger from '~@/utils/logger'

export function useMenuPermission() {
  const menuPermissions = ref<FunctionItem[]>([])
  const messageNotification = useMessage()
  const loadMenuPermissions = async () => {
    try {
      const { data, status, message } = await getPermissionMenuFunc()
      if (status === ResponseStatusEnum.SUCCESS)
        menuPermissions.value = data?.Items || []

      else
        messageNotification.error(message || 'Có lỗi xảy ra khi tải danh sách quyền')
    }
    catch (error) {
      logger.error(error)
    }
  }

  const checkPermission = (menuName: string) => {
    const permission = menuPermissions.value.find(item => item.Title === menuName)
    if (!permission)
      return false

    return {
      canRead: permission.CanRead,
      canCreate: permission.CanCreate,
      canUpdate: permission.CanUpdate,
      canDelete: permission.CanDelete,
    }
  }

  const hasReadPermission = (menuName: string) => {
    const permission = menuPermissions.value.find(item => item.Title === menuName)
    return permission?.CanRead || false
  }

  // Check permission for employee cost detail
  const viewPermissionByRoleByRuleOne = [
    '01958bb0-2a50-7c9a-8497-25c3abcfa9be',
    '0195a2a7-9f58-7317-88ec-2743a7ffa212',
    '01958bb0-2a4f-7a2d-967f-7cc4f1230cbb',
  ]
  const viewPermissionByRoleByRuleTwo = [
    '01958bb0-2a50-7c9a-8497-25c3abcfa9be',
    '0195a2a7-9f58-7317-88ec-2743a7ffa212',
    '01958bb0-2a4f-7a2d-967f-7cc4f1230cbb',
    '01958bb0-2a4f-7779-b99b-8fd4b31b22bf',
    '01958bb0-2a4f-7df1-8e18-90ca124fa94b',
  ]

  const checkEmployeeCostVisibleAll = (roleId: string, type: 'RULE_1' | 'RULE_2') => {
    if (type === 'RULE_1')
      return viewPermissionByRoleByRuleOne.includes(roleId)

    else if (type === 'RULE_2')
      return viewPermissionByRoleByRuleTwo.includes(roleId)

    return false
  }

  return {
    loadMenuPermissions,
    checkPermission,
    hasReadPermission,
    checkEmployeeCostVisibleAll,
  }
}
