// TimeTracking.vue
<script setup lang="ts">
import { Card } from 'ant-design-vue'

defineProps({
  totalWorktimeOfWeek: {
    type: Number,
    required: true,
  },
  totalOvertimeOfWeek: {
    type: Number,
    required: true,
  },
  totalWorktimeOfDay: {
    type: Number,
    required: true,
  },
  totalOvertimeOfDay: {
    type: Number,
    required: true,
  },
})

const { t } = useI18n()
</script>

<template>
  <div class="xl:col-start-4 flex flex-col gap-4 bg-white shadow-lg rounded-lg p-4">
    <!-- Weekly Stats -->
    <Card class="bg-orange-50 border-orange-100">
      <div class="space-y-2">
        <div class="text-gray-700 font-medium">
          {{ t('total-worktime-of-day') }}
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <div class="text-gray-500 text-sm">
              {{ t('worktime') }}
            </div>
            <div class="font-medium">
              {{ totalWorktimeOfDay.toFixed(2) }}
            </div>
          </div>
          <div>
            <div class="text-gray-500 text-sm">
              {{ t('overtime') }}
            </div>
            <div class="font-medium">
              {{ totalOvertimeOfDay.toFixed(2) }}
            </div>
          </div>
        </div>
      </div>
    </Card>

    <!-- Monthly Stats -->
    <Card class="bg-blue-50 border-blue-100">
      <div class="space-y-2">
        <div class="text-gray-700 font-medium">
          {{ t('total-worktime-of-week') }}
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <div class="text-gray-500 text-sm">
              {{ t('worktime') }}
            </div>
            <div class="font-medium">
              {{ totalWorktimeOfWeek.toFixed(2) }}
            </div>
          </div>
          <div>
            <div class="text-gray-500 text-sm">
              {{ t('overtime') }}
            </div>
            <div class="font-medium">
              {{ totalOvertimeOfWeek.toFixed(2) }}
            </div>
          </div>
        </div>
      </div>
    </Card>
  </div>
</template>
