import type { DateRangeParams, QueryParams } from '../common-params'
import type { WorkshiftItem } from './work-shift'

export interface ProjectItem {
  projectId: string
  projectCode: string
  projectName: string
  projectTypeId: string
  projectTypeName: string
  orgId: string
  orgName: string
  address: string
  workplaceName: string
  isOffice: boolean
  isHeadOffice: boolean
  managersInfo: ManagerInfo[]
  expectedStartDate: string
  expectedEndDate: string
  actualStartDate: string
  actualEndDate: string
  initialBudget: number | null
  actualBudget: number | null
  description: string
  statusId: string
  statusName: string
  statusCode: string
  createTime: string
  updateTime: string | null

  customerId: string | null
  customerName: string | null
  contractorId: string | null
  contractorName: string | null
  workShifts: WorkshiftItem[]
}

export interface ManagerInfo {
  managerId: string
  managerName: string
  isPrimaryManager: boolean
  isOutSource: boolean
}

export interface ProjectParams {
  projectCode: string
  projectName: string
  primaryManagerEmployeeIds: string[]
  workShiftIds: string[]
  workShifts: {
    workShiftId: string
    isDefault: boolean
  }[]
  statusCode: string
  projectTypeId?: string
  address?: string
  isHeadOffice?: boolean
  isOffice?: boolean
  subManagerEmployeeIds?: string[]
  expectedStartDate?: string
  expectedEndDate?: string
  actualStartDate?: string
  actualEndDate?: string
  description?: string
  initialBudget?: number
  actualBudget?: number

  customerId?: string
  contractorId?: string
}

export interface ConstructionProgress {
  constructionId: string
  isPrimary: boolean
  disbursementProgressRatio: number
}

export interface EmployeeAttendance {
  employeeId: string
  employeeName: string
  isCheckedIn: boolean
  isRequestedOff: boolean
}

export interface CategorizedCost {
  categoryId: string
  categoryCode: string
  categoryName: string
  totalAmount: number
  totalAvgAmount: number
}

export interface ProjectAttendance {
  presignedWorkload: number
  actualWorkload: number
  employeeAttendances: EmployeeAttendance[]
}

export interface ProjectCost {
  totalCost: number
  totalAvgCost: number
  rootCategorizedCosts: CategorizedCost[]
}

export interface ProjectProgress {
  constructionsProgress: ConstructionProgress[]
}

export interface ProjectSummaryItem {
  projectId: string
  projectCode: string
  projectName: string
  projectTypeName: string
  address: string
  customerName: string
  contractorName: string
  primaryManager: string
  projectStatus: string
  projectStartDate: string
  projectEndDate: string
  projectProgress: ProjectProgress
  projectAttendance: ProjectAttendance
  projectCosts: ProjectCost
  expectedStartDate: string
  expectedEndDate: string
  actualStartDate: string
  actualEndDate: string
}

export interface ProjectSummaryResponse {
  items: ProjectSummaryItem[]
  pageNum: number
  pageSize: number
  totalRecords: number
}
interface Construction {
  constructionId: string
  constructionName: string
  description: string
  isPrimary: boolean
}

export interface ProjectManager {
  employeeId: string;
  employeeCode: string;
  employeeName: string;
}

export interface BreakTime {
  startBreak: string;
  endBreak: string;
}

export interface WorkShift {
  startTime: string;
  endTime: string;
  breakTimes: BreakTime[];
}

export interface ProjectComboItem {
  constructions: Construction[]
  projectManagers: ProjectManager[]
  defaultWorkShift: WorkShift;
  id: string;
  code: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  isDefault: boolean;
  isOffice: boolean;
}
export interface ManagedProjectItem {
  id: string
  code: string
  address: string | null
  isOffice: boolean | null
  managers: any[]
  name: string
  isDefault: false
  constructions: []
}

export interface ManagedProjectResponse {
  items: ManagedProjectItem[]
  pageNum: number
  pageSize: number
}

export interface ProjectComboResponse {
  items: ProjectComboItem[]
  pageNum: number
  pageSize: number
  totalRecords: number
}

export interface ProjectDataResponse {
  items: ProjectItem[]
  pageNum: number
  pageSize: number
  totalRecords: number
}

export interface ProjectType {
  projectTypeId: string
  projectTypeName: string
  description?: string
}
export interface ProjectTypeResponse {
  items: ProjectType[]
}

export interface FilterParams {
  projectName?: string
  pageNum: number
  pageSize: number
  typeId?: string
  statusCode?: string
  exStartDate?: string
  exEndDate?: string
  actStartDate?: string
  actEndDate?: string
  budgetMin?: number
  budgetMax?: number
  costMin?: number
  costMax?: number
}

export interface AssignManagerParams {
  projectId: string
  primaryManagerEmployeeIds: number[]
  subManagerEmployeeIds: number[]
}

export interface EmployeeWorkload {
  key?: string
  employeeId: string
  employeeName: string
  rankingName: string
  workloadOnMainConstruction: number
  workloadOnSubConstruction: number
  totalWorkload?: number
  totalWorkTime?: number
}

export interface OutSourceWorkload {
  key?: string
  outSourceId: string
  outSourceName: string
  workloadOnMainConstruction: number
  workloadOnSubConstruction: number
  totalWorkload?: number
}

export interface EmployeeWorkloadParams {
  employeeId: string
  workloadOnMainConstruction: number
  workloadOnSubConstruction: number
}

export interface OutSourceWorkloadParams {
  outSourceId: string
  workloadOnMainConstruction: number
  workloadOnSubConstruction: number
}

export interface DailyReportItem {
  reportId: string
  projectId: string
  projectCode: string
  projectName: string
  address: string
  reportDate: string
  description: string
  employeeWorkloads: EmployeeWorkload[]
  outSourceWorkloads: OutSourceWorkload[]
}

export interface DailyReportParams {
  reportDate: string
  description: string
  employeeWorkload: EmployeeWorkloadParams[]
  outSourceWorkload: OutSourceWorkloadParams[]
}

export async function getPaginatedProjectListApi(params?: FilterParams) {
  return useGet<ProjectDataResponse>('v1/project', params)
}

export async function getProjectByIdApi(projectId: string) {
  return useGet<ProjectItem>(`v1/project/${projectId}`)
}

export async function getManagedProjectsApi() {
  return useGet<ManagedProjectResponse>('v1/project/managed-project')
}

export async function getProjectComboApi(params?: QueryParams) {
  return useGet<ProjectComboResponse>('v1/project/simple-project-info', params)
}

export async function getProjectTypeComboApi() {
  return useGet<ProjectTypeResponse>('v1/common/project-types')
}

export async function getProjectSummaryApi(params: Partial<QueryParams> & Partial<DateRangeParams>) {
  return await useGet<ProjectSummaryResponse>('v1/project/summary', params)
}

export async function createProjectApi(params: ProjectParams) {
  return usePost<ProjectItem>('v1/project', params)
}

export async function updateProjectApi(projectId: string, param: ProjectParams) {
  return usePut<ProjectItem>(`v1/project/${projectId}`, param)
}

export async function deleteProjectApi(params: any) {
  return usePut<any>('v1/project/update', params)
}

export async function getDailyReportApi(projectId: string, reportDate: string) {
  return useGet<DailyReportItem>(`v1/project/${projectId}/daily-report`, { reportDate })
}

export async function getOneProjectSummaryApi(projectId: string, params?: DateRangeParams) {
  return useGet<ProjectSummaryItem>(`v1/project/${projectId}/summary`, params)
}

export async function createDailyReportApi(projectId: string, params: DailyReportParams) {
  return usePost<DailyReportItem>(`v1/project/${projectId}/daily-report`, params)
}

export async function updateDailyReportApi(reportId: string, params: Partial<DailyReportParams>) {
  return usePut<boolean>(`v1/project/daily-report/${reportId}`, params)
}

export async function deleteDailyReportApi(reportId: string) {
  return useDelete<boolean>(`v1/project/daily-report/${reportId}`)
}
