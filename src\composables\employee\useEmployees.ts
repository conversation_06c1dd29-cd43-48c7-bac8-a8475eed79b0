import type { EmployeeItem, EmployeeQueryParams } from '~@/api/employee/employee'
import { getEmployeeDataApi } from '~@/api/employee/employee'

export function useEmployees() {
  const employees = ref<EmployeeItem[]>()
  const loading = ref(true)
  const error = ref()
  const messageNotify = useMessage()

  const fetchEmployees = async (queryParams?: EmployeeQueryParams) => {
    if (!queryParams) {
      queryParams = {
        pageNum: 1,
        pageSize: 1000,
      }
    }
    loading.value = true
    try {
      const { data, status, message } = await getEmployeeDataApi(queryParams)
      if (status === 200) {
        employees.value = data?.items ?? []
      }
      else {
        messageNotify.error(message)
        error.value = message
      }
    }
    catch (error) {
    }
    finally {
      loading.value = false
    }
  }
  const employeesOptions = computed(() => {
    return employees.value?.map((employee: EmployeeItem) => ({
      value: employee.employeeId,
      label: `${employee.employeeCode} - ${employee.employeeName}`,
    })) ?? []
  })

  return {
    employees,
    employeesOptions,
    fetchEmployees,
    loading,
    error,
  }
}
