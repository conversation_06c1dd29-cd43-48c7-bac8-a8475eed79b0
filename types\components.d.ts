/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('ant-design-vue/es')['Alert']
    AApp: typeof import('ant-design-vue/es')['App']
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACarousel: typeof import('ant-design-vue/es')['Carousel']
    ACascader: typeof import('ant-design-vue/es')['Cascader']
    Access: typeof import('./../src/components/access/index.vue')['default']
    ACol: typeof import('ant-design-vue/es')['Col']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADrawer: typeof import('ant-design-vue/es')['Drawer']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutFooter: typeof import('ant-design-vue/es')['LayoutFooter']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AList: typeof import('ant-design-vue/es')['List']
    AListItem: typeof import('ant-design-vue/es')['ListItem']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuDivider: typeof import('ant-design-vue/es')['MenuDivider']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    APagination: typeof import('ant-design-vue/es')['Pagination']
    APopover: typeof import('ant-design-vue/es')['Popover']
    ARate: typeof import('ant-design-vue/es')['Rate']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASkeletonButton: typeof import('ant-design-vue/es')['SkeletonButton']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATypographyTitle: typeof import('ant-design-vue/es')['TypographyTitle']
    BaseLoading: typeof import('./../src/components/base-loading/index.vue')['default']
    CarbonAddress: typeof import('./../src/components/icons/carbon-address.vue')['default']
    CarbonApprove: typeof import('./../src/components/icons/carbon-approve.vue')['default']
    CarbonArrow: typeof import('./../src/components/icons/carbon-arrow.vue')['default']
    CarbonArrowDown: typeof import('./../src/components/icons/carbon-arrow-down.vue')['default']
    CarbonArrowLeft: typeof import('./../src/components/icons/carbon-arrow-left.vue')['default']
    CarbonArrowRight: typeof import('./../src/components/icons/carbon-arrow-right.vue')['default']
    CarbonBreakOne: typeof import('./../src/components/icons/carbon-break-one.vue')['default']
    CarbonCalendar: typeof import('./../src/components/icons/carbon-calendar.vue')['default']
    CarbonCalendarFilter: typeof import('./../src/components/icons/carbon-calendar-filter.vue')['default']
    CarbonClose: typeof import('./../src/components/icons/carbon-close.vue')['default']
    CarbonCoinYen: typeof import('./../src/components/icons/carbon-coin-yen.vue')['default']
    CarbonCollapsed: typeof import('./../src/components/icons/carbon-collapsed.vue')['default']
    CarbonCopy: typeof import('./../src/components/icons/carbon-copy.vue')['default']
    CarbonDelete: typeof import('./../src/components/icons/carbon-delete.vue')['default']
    CarbonEdit: typeof import('./../src/components/icons/carbon-edit.vue')['default']
    CarbonEmail: typeof import('./../src/components/icons/carbon-email.vue')['default']
    CarbonEn: typeof import('./../src/components/icons/carbon-en.vue')['default']
    CarbonEnDashboard: typeof import('./../src/components/icons/carbon-en-dashboard.vue')['default']
    CarbonFilter: typeof import('./../src/components/icons/carbon-filter.vue')['default']
    CarbonFilterNew: typeof import('./../src/components/icons/carbon-filter-new.vue')['default']
    CarbonFrac: typeof import('./../src/components/icons/carbon-frac.vue')['default']
    CarbonGroupUser: typeof import('./../src/components/icons/carbon-group-user.vue')['default']
    CarbonJp: typeof import('./../src/components/icons/carbon-jp.vue')['default']
    CarbonJpDashboard: typeof import('./../src/components/icons/carbon-jp-dashboard.vue')['default']
    CarbonLanguage: typeof import('./../src/components/icons/carbon-language.vue')['default']
    CarbonLargePlus: typeof import('./../src/components/icons/carbon-large-plus.vue')['default']
    CarbonMenuDotsVertical: typeof import('./../src/components/icons/carbon-menu-dots-vertical.vue')['default']
    CarbonMoon: typeof import('./../src/components/icons/carbon-moon.vue')['default']
    CarbonObservation: typeof import('./../src/components/icons/carbon-observation.vue')['default']
    CarbonPagninationArrowDown: typeof import('./../src/components/icons/carbon-pagnination-arrow-down.vue')['default']
    CarbonPhone: typeof import('./../src/components/icons/carbon-phone.vue')['default']
    CarbonPlus: typeof import('./../src/components/icons/carbon-plus.vue')['default']
    CarbonProjectCalendar: typeof import('./../src/components/icons/carbon-project-calendar.vue')['default']
    CarbonProjectCalendar2: typeof import('./../src/components/icons/carbon-project-calendar-2.vue')['default']
    CarbonProjectFilter: typeof import('./../src/components/icons/carbon-project-filter.vue')['default']
    CarbonRectangle: typeof import('./../src/components/icons/carbon-rectangle.vue')['default']
    CarbonReject: typeof import('./../src/components/icons/carbon-reject.vue')['default']
    CarbonReload: typeof import('./../src/components/icons/carbon-reload.vue')['default']
    CarbonRepair: typeof import('./../src/components/icons/carbon-repair.vue')['default']
    CarbonRightCircle: typeof import('./../src/components/icons/carbon-right-circle.vue')['default']
    CarbonSearch: typeof import('./../src/components/icons/carbon-search.vue')['default']
    CarbonSun: typeof import('./../src/components/icons/carbon-sun.vue')['default']
    CarbonTruct: typeof import('./../src/components/icons/carbon-truct.vue')['default']
    CarbonUncollpse: typeof import('./../src/components/icons/carbon-uncollpse.vue')['default']
    CarbonUser: typeof import('./../src/components/icons/carbon-user.vue')['default']
    CarbonView: typeof import('./../src/components/icons/carbon-view.vue')['default']
    CarbonYen: typeof import('./../src/components/icons/carbon-yen.vue')['default']
    CardOverviewItem: typeof import('./../src/components/card-overview-item/card-overview-item.vue')['default']
    ChaseSpin: typeof import('./../src/components/base-loading/spin/chase-spin.vue')['default']
    CreateButton: typeof import('./../src/components/common/CreateButton.vue')['default']
    CubeSpin: typeof import('./../src/components/base-loading/spin/cube-spin.vue')['default']
    DocLink: typeof import('./../src/components/doc-link/index.vue')['default']
    DotSpin: typeof import('./../src/components/base-loading/spin/dot-spin.vue')['default']
    DownloadButton: typeof import('./../src/components/common/DownloadButton.vue')['default']
    Empty: typeof import('ant-design-vue/es')['Empty']
    FilterButton: typeof import('./../src/components/common/FilterButton.vue')['default']
    FooterLinks: typeof import('./../src/components/footer-links.vue')['default']
    FooterToolBar: typeof import('./../src/components/footer-tool-bar/index.vue')['default']
    GiteeLink: typeof import('./../src/components/gitee-link/index.vue')['default']
    GithubLink: typeof import('./../src/components/github-link/index.vue')['default']
    NotificateAlert: typeof import('./../src/components/notificate-alert/index.vue')['default']
    PageContainer: typeof import('./../src/components/page-container/index.vue')['default']
    PlaneSpin: typeof import('./../src/components/base-loading/spin/plane-spin.vue')['default']
    PreloaderSpin: typeof import('./../src/components/base-loading/spin/preloader-spin.vue')['default']
    ProjectSelect: typeof import('./../src/components/common/ProjectSelect.vue')['default']
    PulseSpin: typeof import('./../src/components/base-loading/spin/pulse-spin.vue')['default']
    RectSpin: typeof import('./../src/components/base-loading/spin/rect-spin.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectLang: typeof import('./../src/components/select-lang/index.vue')['default']
    TimePicker: typeof import('./../src/components/common/TimePicker.vue')['default']
    TokenProvider: typeof import('./../src/components/token-provider/index.vue')['default']
    TourButton: typeof import('./../src/components/common/TourButton.vue')['default']
    UserAvatar: typeof import('./../src/components/user-avatar/index.vue')['default']
    VirtualList: typeof import('./../src/components/virtual-list/index.vue')['default']
    WorkTimeModal: typeof import('./../src/components/common/WorkTimeModal.vue')['default']
  }
}
