export interface StructureMenuItem {
  StructureId: string
  StructureName: string
  RoleItems: RoleMenuItem[]
}

export interface FunctionMenuItem {
  Id: string
  Locale: string
  Title: string
  RoleId: string
  CanRead: boolean
  CanCreate: boolean
  CanUpdate: boolean
  CanDelete: boolean
}

export interface RoleMenuItem {
  RoleId: string
  RoleName: string
  FunctionPrivilegesCount: number
  FunctionItems: FunctionMenuItem[]
}

export interface PermissionMenuResponse {
  StructureItems: StructureMenuItem[]
  message: string
  status: number
}

export interface PermissionMenuUpdateParams {
  functionName: string
  roleId: string
  canRead?: boolean
  canCreate?: boolean
  canUpdate?: boolean
  canDelete?: boolean
}

export interface FunctionItem {
  Id: string
  Title: string
  CanRead: boolean
  CanCreate: boolean
  CanUpdate: boolean
  CanDelete: boolean
}

export interface PermissionResponse {
  Items: FunctionItem[]
}

export async function getAllPermissionMenu(params?: any) {
  return useGet<PermissionMenuResponse>('v1/permission/menu', params)
}

export async function getOnePermissionMenuRole(id: string, params?: any) {
  return useGet<PermissionMenuResponse>(`v1/permission/menu/role/${id}`, params)
}

export async function getOnePermissionMenuStructure(id: string, params?: any) {
  return useGet<StructureMenuItem>(`v1/permission/menu/structure/${id}`, params)
}

export async function updateOnePermissionMenu(
  data: PermissionMenuUpdateParams,
) {
  return usePut('v1/permission/menu', data)
}

export async function getPermissionMenuFunc() {
  return useGet<PermissionResponse>('v1/permission/menu/employee/current')
}
