<!-- src/views/CompanySetup.vue -->
<script setup lang="ts">
import { reactive, ref } from 'vue'
import { EditOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import type { ColumnType } from 'ant-design-vue/es/table'
import { usePagination } from 'vue-request'
import { getOrganizationDetailApi, getOrgsByCurrentAccountApi, updateOrganizationApi } from '~@/api/company/org'
import type { OrganizationItem, OrganizationParams, SimpleOrgItem } from '~@/api/company/org'

const { t } = useI18n()
// Data variables
const orgDetailData = ref<OrganizationItem>()
const orgModalVisible = ref(false)

// Company form
const orgForm = reactive<OrganizationParams & { orgId: string }>({
  orgId: '',
  orgCode: '',
  orgName: '',
  orgSubName: '',
  postalCode: '',
  address: '',
  phoneNumber: '',
  email: '',
  fax: '',
  website: '',
  registrationNumber: '',
  registrationDate: '',
  registrationLicenseType: false,
  legalOrgNumber: '',
  legalTaxNumber: '',
  legalRepresentative: '',
  description: '',
  timeZone: '',
})

const orgsColumns = reactive<ColumnType<SimpleOrgItem>[]>([
  {
    title: t('company.name'),
    dataIndex: 'orgName',
    key: 'orgName',
  },
  {
    title: t('company.code'),
    dataIndex: 'orgCode',
    key: 'orgCode',
  },
  {
    title: t('action'),
    width: 120,
    key: 'action',
  },
])

async function editOrg(orgId: string) {
  if (!orgId)
    return
  const { data, status, message: mess } = await getOrganizationDetailApi(orgId)
  if (status === 200) {
    orgDetailData.value = data ?? undefined
    orgModalVisible.value = true
    orgForm.orgId = orgId
    orgForm.orgCode = data?.orgCode ?? ''
    orgForm.orgName = data?.orgName ?? ''
    orgForm.orgSubName = data?.orgSubName ?? ''
    orgForm.postalCode = data?.postalCode ?? ''
    orgForm.address = data?.address ?? ''
    orgForm.phoneNumber = data?.phoneNumber ?? ''
    orgForm.email = data?.email ?? ''
    orgForm.fax = data?.fax ?? ''
    orgForm.website = data?.website ?? ''
    orgForm.registrationNumber = data?.registrationNumber ?? ''
    orgForm.registrationDate = data?.registrationDate ?? ''
    orgForm.registrationLicenseType = data?.registrationLicenseType ?? false
    orgForm.legalOrgNumber = data?.legalOrgNumber ?? ''
    orgForm.legalTaxNumber = data?.legalTaxNumber ?? ''
    orgForm.legalRepresentative = data?.legalRepresentative ?? ''
    orgForm.description = data?.description ?? ''
    orgForm.timeZone = data?.timeZone ?? ''
  }
  else {
    message.error(mess)
  }
}

function resetOrgForm() {
  orgForm.orgId = ''
  orgForm.orgName = ''
  orgForm.orgSubName = ''
  orgForm.postalCode = ''
  orgForm.address = ''
  orgForm.phoneNumber = ''
  orgForm.email = ''
  orgForm.fax = ''
  orgForm.website = ''
  orgForm.registrationNumber = ''
  orgForm.registrationDate = ''
  orgForm.registrationLicenseType = false
  orgForm.legalTaxNumber = ''
  orgForm.legalRepresentative = ''
  orgForm.description = ''
  orgForm.timeZone = ''
}

async function queryData() {
  const { data, status, message: mess } = await getOrgsByCurrentAccountApi()
  if (status === 200)
    return data
  else
    message.error(mess)
}

const {
  data: orgDataSource,
  current,
  totalPage,
  loading,
  pageSize,
  run,
} = usePagination(queryData, {
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

async function handleOrgSubmit() {
  const params: OrganizationParams = {
    ...orgForm,
  }
  const { status, message: mess } = await updateOrganizationApi(orgForm.orgId, params)
  if (status === 200) {
    message.success(mess)
    orgModalVisible.value = false
    resetOrgForm()
  }
  else {
    message.error(mess)
  }
}

function handleOrgCancel() {
  orgModalVisible.value = false
}

onMounted(async () => {
})
</script>

<template>
  <!-- Thông tin công ty -->
  <div class="mb-4">
    <!-- <a-button type="primary" @click="showStructureModal">
              <PlusOutlined /> Thêm
            </a-button> -->
  </div>

  <a-table
    :columns="orgsColumns"
    :data-source="orgDataSource?.items"
    :loading="loading"
    :total="totalPage"
    :page-size="pageSize"
    :current="current"
    @change="run"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'action'">
        <a-space>
          <a-button type="link" @click="editOrg(record.orgId)">
            <EditOutlined />
          </a-button>
          <!-- <a-popconfirm
                    title="Bạn có chắc muốn xóa?"
                    @confirm="deleteOrg(record.orgId)"
                  >
                    <a-button type="link" danger>
                      <DeleteOutlined />
                    </a-button>
                  </a-popconfirm> -->
        </a-space>
      </template>
    </template>
  </a-table>

  <!-- Organization Modal -->
  <a-modal
    v-model:visible="orgModalVisible"
    :title="orgDetailData ? t('company.edit') : t('company.add')"
    :ok-text="orgDetailData ? t('button.edit') : t('button.add')"
    @ok="handleOrgSubmit"
    @cancel="handleOrgCancel"
  >
    <a-form :model="orgForm" layout="vertical">
      <a-form-item :label="t('company.code')" required>
        <a-input v-model:value="orgForm.orgCode" />
      </a-form-item>
      <a-form-item :label="t('company.name')" required>
        <a-input v-model:value="orgForm.orgName" />
      </a-form-item>
      <a-form-item :label="t('company.address')">
        <a-input v-model:value="orgForm.address" />
      </a-form-item>
      <a-form-item :label="t('company.postalCode')">
        <a-input v-model:value="orgForm.postalCode" />
      </a-form-item>
      <a-form-item :label="t('company.legalTaxNumber')">
        <a-input v-model:value="orgForm.legalTaxNumber" />
      </a-form-item>
      <a-form-item :label="t('company.description')">
        <a-textarea v-model:value="orgForm.description" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
