<!-- eslint-disable antfu/top-level-function -->
<!-- eslint-disable curly -->
<script lang="ts" setup>
import { DownOutlined } from '@ant-design/icons-vue'
import { usePagination } from 'vue-request'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import type { UnwrapRef } from 'vue'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'
import type { BaseLeaveParams, LeaveInfo, LeaveQueryParams, UsedLeaveTime } from '~@/api/company/leave'
import { getEmployeeLeaveApi, getEmployeeLeaveUsageApi, updateBaseLeaveApi } from '~@/api/company/leave'
import type { DateRangeParams } from '~@/api/common-params'
import { useWindowScrollSize } from '~@/composables/use-window-scroll-size'
import { useAvatarStore } from '~@/stores/avatar'

const { t } = useI18n()
const messageNotify = useMessage()
const avatarStore = useAvatarStore()

const currentUserLeaveInfo = ref<LeaveInfo>()
const selectedMonth = ref<Dayjs>(dayjs())
const selectedYear = ref<Dayjs>(dayjs())
const visibleFilter = ref(false)
const usedLeaveTime = ref<UsedLeaveTime[]>([])
const visibleModal = ref(false)

const baseLeaveValidDateRange = ref<[Dayjs, Dayjs] | undefined>()
const lastLeaveRemainingValidDateRange = ref<[Dayjs, Dayjs] | undefined>()
const baseLeaveFormstateRef = ref()
const clickEmployeeLeaveId = ref<string>()
const clickedDateCell = ref<Dayjs>(dayjs())

const userInfoSearchFormstate = ref<string | undefined>()
const { height } = useWindowScrollSize()

const baseLeaveFormstate = reactive<Partial<BaseLeaveParams>>({
  baseLeave: undefined,
  baseLeaveExpire: undefined,
})

const formFilterState = reactive<LeaveQueryParams>({
  pageNum: 1,
  pageSize: 20,
  baseLeaveValidFrom: undefined,
  baseLeaveValidTo: undefined,
  lastLeaveRemainingValidFrom: undefined,
  lastLeaveRemainingValidTo: undefined,
  personalUsedMax: undefined,
  personalUsedMin: undefined,
  usedMax: undefined,
  usedMin: undefined,
  baseLeaveMin: undefined,
  baseLeaveMax: undefined,
  lastLeaveRemainingMin: undefined,
  lastLeaveRemainingMax: undefined,
})

const isFilter = ref<boolean>(false)
const isSearch = ref<boolean>(false)

async function queryData(params: LeaveQueryParams) {
  params.baseLeaveValidFrom = baseLeaveValidDateRange?.value?.[0]?.format('YYYY-MM-DD')
  params.baseLeaveValidTo = baseLeaveValidDateRange?.value?.[1]?.format('YYYY-MM-DD')
  params.lastLeaveRemainingValidFrom = lastLeaveRemainingValidDateRange?.value?.[0]?.format('YYYY-MM-DD')
  params.lastLeaveRemainingValidTo = lastLeaveRemainingValidDateRange?.value?.[1]?.format('YYYY-MM-DD')
  params.baseLeaveMin = !formFilterState?.baseLeaveMin ? undefined : Number(formFilterState?.baseLeaveMin) ?? undefined
  params.baseLeaveMax = !formFilterState?.baseLeaveMax ? undefined : Number(formFilterState?.baseLeaveMax) ?? undefined
  params.lastLeaveRemainingMin = !formFilterState?.lastLeaveRemainingMin ? undefined : Number(formFilterState.lastLeaveRemainingMin) ?? undefined
  params.lastLeaveRemainingMax = !formFilterState?.lastLeaveRemainingMax ? undefined : Number(formFilterState.lastLeaveRemainingMax) ?? undefined
  params.usedMin = !formFilterState?.usedMin ? undefined : Number(formFilterState.usedMin) ?? undefined
  params.usedMax = !formFilterState?.usedMax ? undefined : Number(formFilterState.usedMax) ?? undefined
  params.personalUsedMin = !formFilterState?.personalUsedMin ? undefined : Number(formFilterState.personalUsedMin) ?? undefined
  params.personalUsedMax = !formFilterState?.personalUsedMax ? undefined : Number(formFilterState.personalUsedMax) ?? undefined
  if (isSearch.value) {
    params.userInfo = userInfoSearchFormstate.value
    params.queryFrom = selectedYear.value.startOf('year').format('YYYY-MM-DD')
    params.queryTo = selectedYear.value.endOf('year').format('YYYY-MM-DD')
  }
  else {
    params.queryFrom = selectedYear.value.startOf('year').format('YYYY-MM-DD')
    params.queryTo = selectedYear.value.endOf('year').format('YYYY-MM-DD')
  }
  try {
    const { data, status } = await getEmployeeLeaveApi(params)
    if (status === ResponseStatusEnum.SUCCESS) {
      return data
    }
    else {
      return undefined
    }
  }
  catch (e) {
  }
}

const {
  data: dataSource,
  loading,
  total,
  current,
  pageSize,
  changeCurrent,
} = usePagination(queryData, {
  defaultParams: [{
    pageNum: 1,
    pageSize: 20,
  }],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const personalUseLeave: UnwrapRef<Record<string, boolean>> = reactive({})
const forceUseLeave: UnwrapRef<Record<string, boolean>> = reactive({})

const customDateCellRender = () => {
  usedLeaveTime.value.forEach((item: UsedLeaveTime) => {
    let startTime = item.requestFrom
    const endTime = item.requestTo
    while (dayjs(startTime).isBefore(dayjs(endTime)) || dayjs(startTime).isSame(dayjs(endTime))) {
      startTime = dayjs(startTime).add(1, 'day').format('YYYY-MM-DD')
      if (item.isPersonalUse) {
        personalUseLeave[startTime] = true
      }
      else {
        forceUseLeave[startTime] = true
      }
    }
  })
}

const viewEmployeeLeaveTime = async (record: LeaveInfo) => {
  Object.keys(personalUseLeave).forEach(key => delete personalUseLeave[key])
  Object.keys(forceUseLeave).forEach(key => delete forceUseLeave[key])
  currentUserLeaveInfo.value = record
  await getEmployeeLeaveUsage(record.employeeId)
  customDateCellRender()
}

const columns: any = computed(() => [
  {
    title: t('employeeName'),
    dataIndex: 'employeeName',
    key: 'employeeName',
    width: 300,
    align: 'center',
  },
  {
    title: t('baseLeave'),
    dataIndex: 'baseLeave',
    key: 'baseLeave',
  },
  // {
  //   title: t('baseLeaveExpire'),
  //   dataIndex: 'baseLeaveExpire',
  //   key: 'baseLeaveExpire',
  // },
  {
    title: t('lastRemainLeave'),
    dataIndex: 'lastRemainLeave',
    key: 'lastRemainLeave',
  },
  // {
  //   title: t('lastRemainLeaveExpire'),
  //   dataIndex: 'lastRemainLeaveExpire',
  //   key: 'lastRemainLeaveExpire',
  // },
  {
    title: t('totalUsedLeave'),
    dataIndex: 'totalUsedLeave',
    key: 'totalUsedLeave',
  },
  {
    title: t('totalSelfUsedLeave'),
    dataIndex: 'totalSelfUsedLeave',
    key: 'totalSelfUsedLeave',
  },
  {
    title: t('totalOrgUsedLeave'),
    dataIndex: 'totalOrgUsedLeave',
    key: 'totalOrgUsedLeave',
  },
  {
    title: t('updateTime'),
    dataIndex: 'updateTime',
    key: 'updateTime',
  },
  {
    title: t('action'),
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 100,
  },
])

// const onSearch = () => {
//   const filter: any = {
//     ...searchForm.value,
//   }

//   if (filter.gender != null)
//     filter.gender = filter.gender === 1

//   if (!filter.status)
//     delete filter.status

//   // if (searchForm.value.type != null)
//   //   filter.type = searchForm.value.type == 0 ? false : true;
//   console.log(filter)
//   handleTableChange(
//     {
//       pageSize: 10,
//       current: 1,
//     },
//     filter,
//   )
// }

const pageSizeOptions = [
  {
    label: '10',
    value: 10,
  },
  {
    label: '20',
    value: 20,
  },
  {
    label: '50',
    value: 50,
  },
  {
    label: '100',
    value: 100,
  },
]

const onChangeYear = (changeType: 'prev' | 'next') => {
  currentUserLeaveInfo.value = undefined
  if (changeType === 'prev') {
    selectedYear.value = selectedYear.value.subtract(1, 'year')
  }
  else {
    selectedYear.value = selectedYear.value.add(1, 'year')
  }
  changeCurrent(current.value)
}

async function getEmployeeLeaveUsage(employeeId: string) {
  const params: DateRangeParams = {
    dateFrom: selectedMonth.value.startOf('month').format('YYYY-MM-DD'),
    dateTo: selectedMonth.value.endOf('month').format('YYYY-MM-DD'),
  }
  try {
    const { data, status } = await getEmployeeLeaveUsageApi(employeeId, params)
    if (status === 200) {
      usedLeaveTime.value = data?.items ?? []
    }
  }
  catch (e) {
  }
}

async function updateBaseLeave(leaveEmployeeId: string, params: BaseLeaveParams) {
  try {
    const { status, message } = await updateBaseLeaveApi(leaveEmployeeId, params)
    if (status === 200) {
      messageNotify.success('変更成功')
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (e) {
  }
}

const customeDateStyle = (date: Dayjs) => {
  let style = ''
  const dateStr = date.format('YYYY-MM-DD')
  if (personalUseLeave[dateStr]) {
    style = 'bg-[#C51D26] text-white rounded-sm'
  }
  else if (forceUseLeave[dateStr]) {
    style = 'bg-[#FFC0C4] text-black rounded-sm '
  }
  if (date.format('YYYY-MM-DD') === clickedDateCell.value.format('YYYY-MM-DD')) {
    style = 'text-white rounded-sm'
  }
  return style
}

const handleFieldFilter = () => {
  isSearch.value = false
  userInfoSearchFormstate.value = undefined
  isFilter.value = true
  changeCurrent(current.value)
}

const resetFieldFilter = () => {
  formFilterState.baseLeaveMin = undefined
  formFilterState.baseLeaveMax = undefined
  formFilterState.lastLeaveRemainingMin = undefined
  formFilterState.lastLeaveRemainingMax = undefined
  formFilterState.usedMin = undefined
  formFilterState.usedMax = undefined
  formFilterState.personalUsedMin = undefined
  formFilterState.personalUsedMax = undefined
  formFilterState.baseLeaveValidTo = undefined
  formFilterState.baseLeaveValidFrom = undefined
  formFilterState.lastLeaveRemainingValidTo = undefined
  formFilterState.lastLeaveRemainingValidFrom = undefined
  lastLeaveRemainingValidDateRange.value = undefined
  baseLeaveValidDateRange.value = undefined
  isFilter.value = false
  isSearch.value = false
}

const resetFilter = () => {
  resetFieldFilter()
  changeCurrent(current.value)
}

const handleClosefilter = () => {
  visibleFilter.value = false
}

const openModal = (employeeLeaveId: string) => {
  clickEmployeeLeaveId.value = employeeLeaveId
  visibleModal.value = true
}

const handleCancel = () => {
  visibleModal.value = false
  baseLeaveFormstateRef.value.resetFields()
  baseLeaveFormstate.baseLeave = undefined
  baseLeaveFormstate.baseLeaveExpire = undefined
}

const handleOk = async () => {
  if (!clickEmployeeLeaveId.value)
    return
  baseLeaveFormstateRef.value.validate()
  const params: BaseLeaveParams = {
    baseLeave: Number(baseLeaveFormstate.baseLeave),
    baseLeaveExpire: dayjs(baseLeaveFormstate.baseLeaveExpire).format('YYYY-MM-DD'),
  }
  await updateBaseLeave(clickEmployeeLeaveId.value, params)
  await changeCurrent(current.value)
  handleCancel()
}

const typeLeave = ref<string>('')
const onSelectDate = (date: Dayjs) => {
  clickedDateCell.value = date
  if (personalUseLeave[date.format('YYYY-MM-DD')]) {
    typeLeave.value = 'personalUsed'
  }
  else if (forceUseLeave[date.format('YYYY-MM-DD')]) {
    typeLeave.value = 'forceUsed'
  }
  else typeLeave.value = ''
}

const handleSearch = () => {
  isFilter.value = false
  resetFieldFilter()
  isSearch.value = true
  changeCurrent(current.value)
}

const handleChangePageSize = () => {
  current.value = 1
  changeCurrent(current.value)
}

onMounted(async () => {
})
</script>

<!-- class="flex lg:flex-row gap-x-[20px]" -->
<!--    -->
<template>
  <page-container>
    <div class="grid grid-cols-1 xl:grid-cols-4 gap-x-[20px] xl:min-h-[calc(100vh-8rem)]">
      <!-- left -->
      <div class="col-span-3 flex flex-col justify-between min-w-[400px] bg-white shadow-lg rounded-lg p-4 gap-y-[20px]">
        <div class="flex flex-col gap-y-4">
          <div class="flex justify-between items-center">
            <a-space>
              <a-input-search
                v-model:value="userInfoSearchFormstate"
                :placeholder="`${t('employeeName')} / ${t('employeeCode')}`"
                style="width: 270px"
                @press-enter="handleSearch"
                @search="handleSearch"
              />
            </a-space>
            <div class="flex items-center gap-x-[12px]">
              <a-popover
                v-model:open="visibleFilter" trigger="click" placement="bottomRight"
                overlay-class-name="filter-popover"
              >
                <template #content>
                  <div class="p-1">
                    <div class="flex justify-between items-center h-[36px] px-[12px]">
                      <a-space :size="4">
                        <CarbonFilter />
                        <div class="font-bold">
                          {{ t('filter') }}
                        </div>
                      </a-space>
                      <div class="cursor-pointer" @click="handleClosefilter">
                        <CarbonClose />
                      </div>
                    </div>
                    <div class="mx-auto p-2">
                      <a-form
                        class="form-filter" layout="vertical" :model="formFilterState"
                      >
                        <div class="flex flex-col gap-y-[15px]">
                          <div class="mt-[12px]">
                            <a-divider orientation="left" :style="{ margin: 0 }">
                              {{ t('baseLeave') }}
                            </a-divider>
                            <div class="grid grid-cols-1 gap-x-[12px]">
                              <div class="grid grid-cols-2 gap-x-[12px]">
                                <a-form-item class="m-0 pt-[8px]" :label="t('from')" name="baseLeaveValidFrom">
                                  <a-input v-model:value="formFilterState.baseLeaveMin" type="number" />
                                </a-form-item>
                                <a-form-item class="m-0 pt-[8px]" :label="t('to')" name="baseLeaveValidTo">
                                  <a-input v-model:value="formFilterState.baseLeaveMax" type="number" />
                                </a-form-item>
                              </div>
                              <div>
                                <a-form-item class="m-0 pt-[8px] pb-[16px]" :label="t('baseLeaveValidDateRange')" name="date">
                                  <a-range-picker v-model:value="baseLeaveValidDateRange" class="w-full" />
                                </a-form-item>
                              </div>
                            </div>
                          </div>
                          <div>
                            <a-divider orientation="left" :style="{ margin: 0 }">
                              {{ t('lastLeaveRemaining') }}
                            </a-divider>
                            <div class="grid grid-cols-1">
                              <div class="grid grid-cols-2 gap-x-[12px]">
                                <a-form-item class="m-0 pt-[8px]" :label="t('from')" name="baseLeaveMin">
                                  <a-input v-model:value="formFilterState.lastLeaveRemainingMin" type="number" />
                                </a-form-item>
                                <a-form-item class="m-0 pt-[8px]" :label="t('to')" name="baseLeaveMax">
                                  <a-input v-model:value="formFilterState.lastLeaveRemainingMax" type="number" />
                                </a-form-item>
                              </div>
                              <div>
                                <a-form-item class="m-0 pt-[8px] pb-[16px]" :label="t('lastLeaveRemainingValidDateRange')" name="date">
                                  <a-range-picker v-model:value="lastLeaveRemainingValidDateRange" class="w-full" />
                                </a-form-item>
                              </div>
                            </div>
                          </div>
                          <div>
                            <a-divider :style="{ margin: 0 }" orientation="left">
                              {{ t('totalUsedLeave') }}
                            </a-divider>
                            <div class="grid grid-cols-2 gap-x-[12px]">
                              <a-form-item class="m-0 pt-[8px] pb-[16px]" :label="t('from')" name="baseLeaveMin">
                                <a-input v-model:value="formFilterState.usedMin" type="number" />
                              </a-form-item>
                              <a-form-item class="m-0 pt-[8px] pb-[16px]" :label="t('to')" name="baseLeaveMax">
                                <a-input v-model:value="formFilterState.usedMax" type="number" />
                              </a-form-item>
                            </div>
                          </div>
                          <div>
                            <a-divider :style="{ margin: 0 }" orientation="left">
                              {{ t('totalPersonalUsedLeave') }}
                            </a-divider>
                            <div class="grid grid-cols-2 gap-x-[12px]">
                              <a-form-item class="m-0 pt-[8px] pb-[16px]" :label="t('from')" name="baseLeaveMin">
                                <a-input v-model:value="formFilterState.personalUsedMin" type="number" />
                              </a-form-item>
                              <a-form-item class="m-0 pt-[8px] pb-[16px]" :label="t('to')" name="baseLeaveMax">
                                <a-input v-model:value="formFilterState.personalUsedMax" type="number" />
                              </a-form-item>
                            </div>
                          </div>
                        </div>
                        <a-divider orientation="left" class="mb-[12px]" />
                        <div class="flex justify-between">
                          <a-button @click="resetFieldFilter">
                            {{ t('button.reset') }}
                          </a-button>
                          <a-button type="primary" html-type="submit" @click="handleFieldFilter">
                            {{ t('button.apply') }}
                          </a-button>
                        </div>
                      </a-form>
                    </div>
                  </div>
                </template>
                <a-button class="flex items-center gap-x-2">
                  <CarbonFilter />
                  {{ t('filter') }}
                  <DownOutlined />
                </a-button>
              </a-popover>
              <div>
                <a-button class="flex items-center gap-x-2" @click="resetFilter">
                  {{ t('button.resetFilter') }}
                </a-button>
              </div>
              <div class="flex ">
                <a-space class="gap-x-2">
                  <div class="flex justify-between items-center gap-x-4">
                    <CarbonArrowLeft class="hover:bg-gray-200 cursor-pointer" @click="onChangeYear('prev')" />
                    <div class="w-[50px] flex justify-center">
                      <span class="text-lg font-bold text-blue-500">
                        {{ dayjs(selectedYear).format("YYYY") }}
                      </span>
                    </div>
                    <CarbonArrowRight class="hover:bg-gray-200 cursor-pointer" @click="onChangeYear('next')" />
                  </div>
                </a-space>
              </div>
            </div>
          </div>
          <a-table
            :columns="columns"
            :data-source="dataSource?.items"
            :loading="loading"
            class="office-request-table"
            :bordered="true"
            :scroll="{ x: 1500, y: height - 300 }"
            row-key="uid"
            :pagination="false"
            :custom-row="(record: any) => {
              return record.employeeId === currentUserLeaveInfo?.employeeId
                ? {
                  tag: 'tr',
                  class: 'bg-[#e6f4ff]',
                }
                : {};
            }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'employeeName'">
                <div class="flex">
                  <div class="mr-2">
                    <a-avatar :size="48" :src="avatarStore.getImageSrcByEmployeeId(record.employeeId) ?? ''" class="shadow-lg" />
                  </div>
                  <div class="text-left flex flex-col">
                    <span class="text-md font-bold">
                      {{ record.employeeName }}
                    </span>
                    <span class="text-gray-500">
                      {{ `${t('code')}: ${record.employeeCode}` }}
                    </span>
                  </div>
                </div>
              </template>
              <template v-if="column.dataIndex === 'baseLeave'">
                <div class="flex flex-items-center">
                  <div class="ml-2">
                    <strong>{{ record.baseLeave }}</strong>
                    <br>
                    <a-typography-text type="secondary">
                      {{ `${t('expire')}: ${record.baseLeaveExpire}` }}
                    </a-typography-text>
                  </div>
                </div>
              </template>
              <template v-if="column.dataIndex === 'lastRemainLeave'">
                <div class="flex flex-items-center">
                  <div class="ml-2">
                    <strong>{{ record?.lastRemainLeave ?? 0 }}</strong>
                    <br>
                    <a-typography-text type="secondary">
                      {{ `${t('expire')}: ${record.lastRemainLeaveExpire ?? ''}` }}
                    </a-typography-text>
                  </div>
                </div>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <div class="flex gap-x-2">
                  <CarbonView class="cursor-pointer hover:text-blue" @click="viewEmployeeLeaveTime(record as LeaveInfo)" />
                  <CarbonRepair class="cursor-pointer hover:text-blue" @click="openModal(record.employeeLeaveId)" />
                </div>
              </template>
            </template>
          </a-table>
        </div>
        <div class="flex justify-between">
          <a-pagination
            v-model:current="current"
            v-model:page-size="pageSize"
            :total="total"
            class="text-[0.875rem] project-pagination"
            :show-size-changer="false"
          />
          <div
            class="flex items-center gap-x-[8px] text-[#74797A] project-select-page-size"
          >
            <span>{{ t("button.show") }}</span>
            <a-select
              v-model:value="pageSize"
              :options="pageSizeOptions"
              @change="handleChangePageSize"
            >
              <template #suffixIcon>
                <CarbonPagninationArrowDown />
              </template>
            </a-select>
            <span>{{ t("button.entries") }}</span>
          </div>
        </div>
      </div>
      <!-- right -->
      <div class="col-span-1 flex min-w-[400px]">
        <template v-if="!currentUserLeaveInfo">
          <div class="flex items-center justify-center bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <Empty />
          </div>
        </template>
        <template v-else>
          <div class="flex flex-col bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <div class="flex items-center mb-4 border-b-1 border-t-0 border-l-0 border-r-0 border-gray-300 border-solid pb-4">
              <a-avatar :size="48" :src="avatarStore.getImageSrcByEmployeeId(currentUserLeaveInfo?.employeeId) ?? ''" class="shadow-lg mr-2" />
              <div>
                <div class="text-lg font-medium">
                  {{ currentUserLeaveInfo?.employeeName }}
                </div>
                <div class="text-gray-500">
                  {{ t('code') }}: {{ currentUserLeaveInfo?.employeeCode }}
                </div>
              </div>
            </div>
            <div class="border-b-1 border-t-0 border-l-0 border-r-0 border-gray-300 border-solid mb-4">
              <a-calendar
                :fullscreen="false"
                @select="onSelectDate"
              >
                <template #dateFullCellRender="{ current }">
                  <div class="ant-picker-cell-inner ant-picker-calendar-date">
                    <div class="ant-picker-calendar-date-value" :class="customeDateStyle(current)">
                      {{ current.date() }}
                    </div>
                  </div>
                </template>
              </a-calendar>
            </div>
            <div class="flex gap-y-4">
              <a-badge v-if="typeLeave !== ''" status="error" />
              <p>{{ t(typeLeave) }}</p>
            </div>
          </div>
        </template>
      </div>
    </div>
    <a-modal
      v-model:visible="visibleModal"
      :title="t('title.baseLeaveChange')"
      class="user-modal" width="500px"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form
        ref="baseLeaveFormstateRef"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        :model="baseLeaveFormstate"
      >
        <a-form-item
          name="baseLeave"
          :label="t('baseLeave')"
          :rules="[{ required: true, message: t('placeholder.enter-data', { msg: t('baseLeave') }) }]"
        >
          <a-input
            v-model:value="baseLeaveFormstate.baseLeave"
            type="number"
          />
        </a-form-item>
        <a-form-item
          name="baseLeaveExpire"
          :label="t('baseLeaveExpire')"
          :rules="[{ required: true, message: t('placeholder.enter-data', { msg: t('baseLeaveExpire') }) }]"
        >
          <a-date-picker v-model:value="baseLeaveFormstate.baseLeaveExpire" class="w-full" />
        </a-form-item>
      </a-form>
    </a-modal>
  </page-container>
</template>

<style lang="less">
.custom-layout {
  > div {
    gap: 12px;
    .ant-col {
      flex: 0 0 auto;
      padding-bottom: 0;
    }
  }
}
.user-modal {
  .ant-modal-header {
    text-align: center;
    .ant-modal-title {
      font-size: 20px;
      color: #1c4771;
    }
  }
}
// .filter-popover {
//   .ant-popover-inner {
//     padding: 0;
//   }
// }

.office-request-table .ant-table-pagination .ant-pagination {
  display: none;
}

.custom-height {
  height: calc(100vh - 140px);
}

:deep(.project-pagination .ant-pagination) {
  display: none;
}
</style>
