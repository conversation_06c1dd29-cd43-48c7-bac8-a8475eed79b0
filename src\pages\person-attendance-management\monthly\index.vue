<script lang="ts" setup>
import { LeftOutlined, MinusOutlined, PlusOutlined, RightOutlined, SendOutlined } from '@ant-design/icons-vue';

import dayjs from 'dayjs';
import Calendar from './tabs/calendar.vue';
import Schedule from './tabs/schedule.vue';

const pickedMonth = ref(dayjs())
const activeKey = ref('CALENDAR')
const fontSize = ref(12)
const rowHeight = ref(28)

function handleChangeMonth(type: number) {
  pickedMonth.value = pickedMonth.value.add(type, 'month')
}

</script>

<template>
  <a-tabs v-model:activeKey="activeKey" destroy-inactive-tab-pane type="card">
    <a-tab-pane key="CALENDAR" :tab="$t('tab.calendar')">
      <Calendar />
    </a-tab-pane>
    <a-tab-pane key="SCHEDULE" :tab="$t('tab.schedule')">
      <Schedule :picked-month="pickedMonth" :font-size="fontSize" :row-height="rowHeight" />
    </a-tab-pane>
    <template v-if="activeKey === 'SCHEDULE'" #tabBarExtraContent>
      <div class="flex items-center justify-between w-full">
        <div
          class="flex items-center justify-center gap-5 font-bold text-base mr-28"
        >
          <LeftOutlined class="cursor-pointer" @click="handleChangeMonth(-1)" />
          <span>
            {{ pickedMonth.locale($t('locale')).format('YYYY年MM月') }}
          </span>
          <RightOutlined class="cursor-pointer" @click="handleChangeMonth(1)" />
        </div>
        <div class="flex-center gap-x-2">
          <!-- Control Panel for Font Size and Row Height -->
          <div class="flex items-center gap-6">
            <div class="flex items-center gap-2">
              <div class="w-30  ">
                <span class="text-sm font-medium">{{ $t('fontSize') }}:</span>
              </div>
              <div>
                <a-button class="flex-center" size="middle" @click="fontSize++">
                  <template #icon>
                    <PlusOutlined />
                  </template>
                </a-button>
              </div>
              <a-input v-model:value="fontSize" type="number" class="w-24" />
              <div>
                <a-button class="flex-center" size="middle" @click="fontSize--">
                  <template #icon>
                    <MinusOutlined />
                  </template>
                </a-button>
              </div>
            </div>

            <div class="flex items-center gap-2">
              <div class="w-20">
                <span class="text-sm font-medium">{{ $t('rowHeight') }}:</span>
              </div>
              <div class="flex items-center gap-2">
                <a-button class="flex-center" size="middle" @click="rowHeight++">
                  <template #icon>
                    <PlusOutlined />
                  </template>
                </a-button>
              </div>
              <div>
                <a-input v-model:value="rowHeight" type="number" class="w-24" />
              </div>
              <div>
                <a-button class="flex-center" size="middle" @click="rowHeight--">
                  <template #icon>
                    <MinusOutlined />
                  </template>
                </a-button>
              </div>
            </div>
          </div>
          <a-button type="primary" class="flex flex-items-center">
            {{ $t('button.monthlyAttendanceRequest') }}
            <template #icon>
              <SendOutlined />
            </template>
          </a-button>
        </div>

      </div>
    </template>
  </a-tabs>
</template>
