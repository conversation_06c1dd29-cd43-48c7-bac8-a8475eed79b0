// AttendanceDetail.vue
<script setup lang="ts">
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue'
import TodayAttendanceCard from './today-attendance-card.vue'
import type { AttendanceItem, AttendanceUpdateParams, FastCheckInParams } from '~@/api/attendance'

const props = defineProps<{
  todayAttendanceData: AttendanceItem[]
  currentAttendanceItemId: string
  currentIndex: number
}>()

const emit = defineEmits<{
  (event: 'updateAttendanceItem', employeeShiftId: string, params: AttendanceUpdateParams): void
  (event: 'requestApproval', employeeShiftId: string): void
  (event: 'fastCheckIn', employeeShiftId: string, params: FastCheckInParams): void
}>()

const todayAttendanceLocalData = ref([
  ...props.todayAttendanceData,
])
const currentIndex = ref(props.currentIndex)

function requestApproval(employeeShiftId: string) {
  // Handle approval request
  emit('requestApproval', employeeShiftId)
}

function fastCheckIn(employeeShiftId: string) {
  // Handle approval request
  const params: FastCheckInParams = {
    latitude: '',
    longitude: '',
  }
  emit('fastCheckIn', employeeShiftId, params)
}

async function updateAttendanceItem(employeeShiftId: string, params: AttendanceUpdateParams) {
  emit('updateAttendanceItem', employeeShiftId, params)
}

watch (() => props.currentIndex, () => {
  currentIndex.value = props.currentIndex
})

watch (() => props.todayAttendanceData, () => {
  todayAttendanceLocalData.value = [
    ...props.todayAttendanceData,
  ]
}, { deep: true })

onMounted(() => {
})
</script>

<template>
  <div v-if="!todayAttendanceLocalData.length" class="h-full w-full">
    <Empty class="mt-10" />
  </div>
  <div v-else>
    <a-carousel arrows>
      <template #prevArrow>
        <div class="custom-slick-arrow" style="left: 10px; z-index: 1">
          <LeftOutlined />
        </div>
      </template>
      <template #nextArrow>
        <div class="custom-slick-arrow" style="right: 10px; z-index: 1">
          <RightOutlined />
        </div>
      </template>
      <div
        v-for="(item, index) in todayAttendanceLocalData" :key="item.employeeShiftId"
        class="p-4"
      >
        <TodayAttendanceCard
          :employee-shift="item"
          :is-editable="currentAttendanceItemId !== item.employeeShiftId"
          :index="index"
          :total="todayAttendanceLocalData.length"
          @update-attendance-item="updateAttendanceItem"
          @request-approval="requestApproval"
          @fast-check-in="fastCheckIn"
        />
      </div>
    </a-carousel>
  </div>
</template>

<style scoped>
/* Hide arrows by default */
:deep(.slick-arrow.custom-slick-arrow) {
  width: 30px;
  height: 30px;
  font-size: 20px;
  color: #1890ff;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  transition: ease all 0.3s;
  opacity: 0; /* Hide by default */
  z-index: 1;
  display: flex !important;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  visibility: hidden; /* Also hide to prevent clicking when invisible */
}

/* Show arrows when hovering over the carousel */
:deep(.ant-carousel:hover .slick-arrow.custom-slick-arrow) {
  opacity: 0.8;
  visibility: visible;
}

:deep(.slick-arrow.custom-slick-arrow:before) {
  display: none;
}

:deep(.slick-arrow.custom-slick-arrow:hover) {
  color: #40a9ff;
  opacity: 1 !important;
  transform: scale(1.1);
}

:deep(.slick-slide h3) {
  color: #fff;
}
</style>
