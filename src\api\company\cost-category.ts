import _ from 'lodash';
import { formatData } from '~@/utils/tools';

export interface CostCategoryResponse {
  items: CostCategoryItem[];
  pageNum?: number;
  pageSize?: number;
  totalRecords?: number;
}

export interface CostCategoryItem {
  categoryId: string;
  categoryCode: string;
  categoryName: string;
  description?: string;
  numberOfItems?: number;
  parentId?: string;
  parentName?: string;
  parentCode?: string;
  hasChildren?: boolean;
  isSystemCategory?: boolean;
  categories?: CostCategoryItem[];
  editableData?: Record<string, CostCategoryItem>;
  isAdd?: boolean;
}

interface CostCategoryLogsResponse {
  entityChanges: CostCategoryLogItem[];
  pageNum: number;
  pageSize: number;
  totalRecords: number;
}

export interface CostCategoryChangedListItem {
  fieldName: string;
  valueAfter: string | number | boolean | number[] | string[];
  valueBefore: string | number | boolean | number[] | string[];
}

export interface CostCategoryLogItem {
  action: string;
  auditLogId: string;
  changedList: CostCategoryChangedListItem[];
  entityId: string;
  description: string;
  modifiedTime: string;
  modifiedUserId: string;
  modifiedUserName: string;
}

export interface GetCostCategoryParams {
  keyword?: string;
  parentId?: string;
  pageNum?: number;
  pageSize?: number;
}

interface GetCostCategoryLogsParams {
  dateFrom?: string;
  dateTo?: string;
  action?: string;
  pageNum?: number;
  pageSize?: number;
}

export async function getCostCategory(params?: GetCostCategoryParams) {
  return useGet<CostCategoryResponse>('v1/cost/category', formatData(params));
}

export async function getOneCostCategory(id: string) {
  return useGet<CostCategoryItem>(`v1/cost/category/${id}`);
}

export async function createCostCategory(data: Partial<CostCategoryItem>) {
  return usePost('v1/cost/category', formatData(data));
}

export async function updateCostCategory(
  id: string,
  data: Partial<CostCategoryItem>
) {
  return usePut(`v1/cost/category/${id}`, formatData(data));
}

export async function deleteCostCategory(id: string) {
  return useDelete(`v1/cost/category/${id}`);
}

export async function getCostCategoryLogs(
  id: string,
  params?: GetCostCategoryLogsParams
) {
  return useGet<CostCategoryLogsResponse>(`v1/cost/category/${id}/logs`, params);
}
