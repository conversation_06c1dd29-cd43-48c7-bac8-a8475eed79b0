import { getListApi, onCreate } from '~@/api/log/activity-logs'
import logger from '~@/utils/logger'

const message = useMessage()
const { currentRoute } = useCurrentRoute()

export async function onCreateLog(data: any) {
  const router = currentRoute.value

  const routerId = router.meta.id
  data.functionKey = routerId
  const result: any = await onCreate(data)
  if (!result.Success) {
    //message.error(result.Message)
    logger.error(result)
  }
}

export async function logLists(data: any) {
  const router = currentRoute.value

  const routerId = router.meta.id
  data.functionKey = routerId
  const result: any = await getListApi(data)
  if (!result.Success) {
    message.error(result.Message)
    logger.error(result)
  }

  return result.Data
}
