import type { UploadFile } from 'ant-design-vue'
import { useOrg } from '~@/composables/org'
import { formatData } from '~@/utils/tools'

export interface VendorResponse {
  items: VendorItem[]
  pageIndex: number
  pageSize: number
  totalRecords: number
}

export interface VendorInvoiceItem {
  inputCostId: string
  originalNumber: string
  invoiceTitle: string
  issueDate: string
  paymentDate: string
  totalAmount: number
  description: string
}

export interface VendorItem {
  logo?: UploadFile
  logoUrl?: string
  vendorId: string
  vendorCode: string
  vendorName: string
  vendorSubName?: string
  corporateNumber?: string
  address?: string
  phoneNumber?: string
  email?: string
  contactPerson: {
    name?: string
    phoneNumber?: string
    email?: string
  }
  description?: string
  vendorInvoices?: VendorInvoiceItem[]
}

interface VendorLogsResponse {
  entityChanges: VendorLogItem[]
  pageNum: number
  pageSize: number
  totalRecords: number
}

export interface VendorChangedListItem {
  fieldName: string
  valueAfter: string | number | boolean | number[] | string[]
  valueBefore: string | number | boolean | number[] | string[]
}

export interface VendorLogItem {
  action: string
  auditLogId: string
  changedList: VendorChangedListItem[]
  entityId: string
  description: string
  modifiedTime: string
  modifiedUserId: string
  modifiedUserName: string
}

export interface GetVendorParams {
  keyword?: string
  pageNum?: number
  pageSize?: number
}

export interface GetVendorLogsParams {
  dateFrom?: string
  dateTo?: string
  action?: string
  pageNum?: number
  pageSize?: number
}

export async function getVendor(params?: GetVendorParams) {
  return useGet<VendorResponse>('v1/cost/vendor', formatData(params))
}

export async function getOneVendor(id: string, params?: GetVendorParams) {
  return useGet<VendorItem>(`v1/cost/vendor/${id}`, formatData(params))
}

export function getVendorLogo(id: string): string {
  const host = import.meta.env.VITE_APP_BASE_API ?? ''
  return `${host}/v1/cost/vendor/${id}/logo?orgId=${useOrg().value}`
}

export async function createVendor(data: Partial<VendorItem>) {
  return usePost('/v1/cost/vendor', formatData(data), {
    headers: { 'Content-Type': 'multipart/form-data' },
  })
}

export async function updateVendor(id: string, data: Partial<VendorItem>) {
  return usePut(`v1/cost/vendor/${id}`, formatData(data), {
    headers: { 'Content-Type': 'multipart/form-data' },
  })
}

export async function deleteVendor(id: string) {
  return useDelete(`v1/cost/vendor/${id}`)
}

export async function getVendorLogs(id: string, params?: GetVendorLogsParams) {
  return useGet<VendorLogsResponse>(`v1/cost/vendor/${id}/logs`, params)
}
