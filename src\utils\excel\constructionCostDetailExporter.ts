import * as XLSX from 'xlsx'
import type { ConstructionCostItem } from '~@/api/construction-cost'

interface ExportOptions {
  constructionCosts: ConstructionCostItem[]
}

function formatNumber(value: number): string {
  return new Intl.NumberFormat('ja-JP').format(value)
}

function calculatePercentage(totalAmount: number, totalCost: number): string {
  if (totalCost === 0)
    return '0.00%'
  const result = (totalAmount / totalCost) * 100
  return `${result.toFixed(2)}%`
}

function createCostDataRows(constructionCost?: ConstructionCostItem): string[][] {
  if (!constructionCost)
    return []

  const rows: string[][] = []
  const lastAccumulateCost = constructionCost?.lastAccumulateCost
  const lastRequestAmount = constructionCost?.lastAccumulateCost?.requestAmount ?? 0
  const currentCost = constructionCost.currentCost
  const currentRequestAmount = constructionCost?.currentCost?.requestAmount ?? 0
  const currentAccumulateCost = constructionCost.currentAccumulateCost
  const currentAccumulateRequestAmount = constructionCost?.currentAccumulateCost?.requestAmount ?? 0
  const maxLength = Math.max(lastAccumulateCost?.humanCategorizedCosts?.length ?? 0, currentCost?.humanCategorizedCosts?.length ?? 0, currentAccumulateCost?.humanCategorizedCosts?.length ?? 0)
  for (let i = 0; i < maxLength; i++) {
    const lastCategoryCode = lastAccumulateCost?.humanCategorizedCosts?.[i]?.categoryCode ?? ''
    const lastCategoryName = lastAccumulateCost?.humanCategorizedCosts?.[i]?.categoryName ?? ''
    const lastWorkload = lastAccumulateCost?.humanCategorizedCosts?.[i]?.subCategories?.reduce((sum, sub) => sum + sub.workload, 0) ?? 0
    const currentCategoryCode = currentCost?.humanCategorizedCosts?.[i]?.categoryCode ?? ''
    const currentCategoryName = currentCost?.humanCategorizedCosts?.[i]?.categoryName ?? ''
    const currentWorkload = currentCost?.humanCategorizedCosts?.[i]?.subCategories?.reduce((sum, sub) => sum + sub.workload, 0) ?? 0
    const currentAccumulateCategoryCode = currentAccumulateCost?.humanCategorizedCosts?.[i]?.categoryCode ?? ''
    const currentAccumulateCategoryName = currentAccumulateCost?.humanCategorizedCosts?.[i]?.categoryName ?? ''
    const currentAccumulateWorkload = currentAccumulateCost?.humanCategorizedCosts?.[i]?.subCategories?.reduce((sum, sub) => sum + sub.workload, 0) ?? 0
    rows.push([
      lastCategoryCode === 'EMPLOYEE' || lastCategoryCode === 'OUTSOURCE_DAILY' ? `${lastCategoryName} ${lastWorkload}人工` : lastCategoryName,
      lastAccumulateCost?.humanCategorizedCosts?.[i] ? `¥${formatNumber(lastAccumulateCost.humanCategorizedCosts[i].totalAmount)}` : '',
      lastAccumulateCost?.humanCategorizedCosts?.[i] ? calculatePercentage(lastAccumulateCost.humanCategorizedCosts[i].totalAmount, lastRequestAmount) : '',
      '',
      currentCategoryCode === 'EMPLOYEE' || currentCategoryCode === 'OUTSOURCE_DAILY' ? `${currentCategoryName} ${currentWorkload}人工` : currentCategoryName,
      currentCost?.humanCategorizedCosts?.[i] ? `¥${formatNumber(currentCost.humanCategorizedCosts[i].totalAmount)}` : '',
      currentCost?.humanCategorizedCosts?.[i] ? calculatePercentage(currentCost.humanCategorizedCosts[i].totalAmount, currentRequestAmount) : '',
      '',
      currentAccumulateCategoryCode === 'EMPLOYEE' || currentAccumulateCategoryCode === 'OUTSOURCE_DAILY' ? `${currentAccumulateCategoryName} ${currentAccumulateWorkload}人工` : currentAccumulateCategoryName,
      currentAccumulateCost?.humanCategorizedCosts?.[i] ? `¥${formatNumber(currentAccumulateCost.humanCategorizedCosts[i].totalAmount)}` : '',
      currentAccumulateCost?.humanCategorizedCosts?.[i] ? calculatePercentage(currentAccumulateCost.humanCategorizedCosts[i].totalAmount, currentAccumulateRequestAmount) : '',
    ])
  }

  // Add risk amount row
  rows.push([
    'リスク金額',
    `¥${formatNumber(lastAccumulateCost?.riskAmount ?? 0)}`,
    calculatePercentage(lastAccumulateCost?.riskAmount ?? 0, lastRequestAmount),
    '',
    'リスク金額',
    `¥${formatNumber(currentCost?.riskAmount ?? 0)}`,
    calculatePercentage(currentCost?.riskAmount ?? 0, currentRequestAmount),
    '',
    'リスク金額',
    `¥${formatNumber(currentAccumulateCost?.riskAmount ?? 0)}`,
    calculatePercentage(currentAccumulateCost?.riskAmount ?? 0, currentAccumulateRequestAmount),
  ])

  // Add total row
  rows.push([
    '工事原価合計',
    `¥${formatNumber(lastAccumulateCost?.totalCost ?? 0)}`,
    calculatePercentage(lastAccumulateCost?.totalCost ?? 0, lastRequestAmount),
    '',
    '工事原価合計',
    `¥${formatNumber(currentCost?.totalCost ?? 0)}`,
    calculatePercentage(currentCost?.totalCost ?? 0, currentRequestAmount),
    '',
    '工事原価合計',
    `¥${formatNumber(currentAccumulateCost?.totalCost ?? 0)}`,
    calculatePercentage(currentAccumulateCost?.totalCost ?? 0, currentAccumulateRequestAmount),
  ])

  rows.push([
    '出来高金額',
    `¥${formatNumber(lastRequestAmount)}`,
    '',
    '',
    '出来高金額',
    `¥${formatNumber(currentRequestAmount)}`,
    '',
    '',
    '出来高金額',
    `¥${formatNumber(currentAccumulateRequestAmount)}`,
    '',
  ])

  rows.push([
    '保留金',
    lastAccumulateCost?.retentionAmount ? `¥${formatNumber(lastAccumulateCost.retentionAmount)}` : '0',
    '',
    '',
    '保留金',
    currentCost?.retentionAmount ? `¥${formatNumber(currentCost.retentionAmount)}` : '0',
    '',
    '',
    '保留金',
    currentAccumulateCost?.retentionAmount ? `¥${formatNumber(currentAccumulateCost.retentionAmount)}` : '0',
    '',
  ])

  rows.push([
    '保留金解除額',
    lastAccumulateCost?.releasedAmount ? `¥${formatNumber(lastAccumulateCost.releasedAmount)}` : '0',
    '',
    '',
    '保留金解除額',
    currentCost?.releasedAmount ? `¥${formatNumber(currentCost.releasedAmount)}` : '0',
    '',
    '',
    '保留金解除額',
    currentAccumulateCost?.releasedAmount ? `¥${formatNumber(currentAccumulateCost.releasedAmount)}` : '0',
    '',
  ])

  rows.push([
    '保留金除外の請求金額',
    lastAccumulateCost?.totalClaimedAmount ? `¥${formatNumber(lastAccumulateCost.totalClaimedAmount)}` : '0',
    '',
    '',
    '保留金除外の請求金額',
    currentCost?.totalClaimedAmount ? `¥${formatNumber(currentCost.totalClaimedAmount)}` : '0',
    '',
    '',
    '保留金除外の請求金額',
    currentAccumulateCost?.totalClaimedAmount ? `¥${formatNumber(currentAccumulateCost.totalClaimedAmount)}` : '0',
    '',
  ])

  rows.push([
    '出来高に対する利益費',
    lastAccumulateCost?.profitByRequestedAmount ? `¥${formatNumber(lastAccumulateCost.profitByRequestedAmount)}` : '0',
    '',
    '',
    '出来高に対する利益費',
    currentCost?.profitByRequestedAmount ? `¥${formatNumber(currentCost.profitByRequestedAmount)}` : '0',
    '',
    '',
    '出来高に対する利益費',
    currentAccumulateCost?.profitByRequestedAmount ? `¥${formatNumber(currentAccumulateCost.profitByRequestedAmount)}` : '0',
    '',
  ])

  rows.push([
    '請求に対する利益金額',
    lastAccumulateCost?.profitByClaimedAmount ? `¥${formatNumber(lastAccumulateCost.profitByClaimedAmount)}` : '0',
    '',
    '',
    '請求に対する利益金額',
    currentCost?.profitByClaimedAmount ? `¥${formatNumber(currentCost.profitByClaimedAmount)}` : '0',
    '',
    '',
    '請求に対する利益金額',
    currentAccumulateCost?.profitByClaimedAmount ? `¥${formatNumber(currentAccumulateCost.profitByClaimedAmount)}` : '0',
    '',
  ])

  return rows
}

export function createConstructionSheet(constructionCost: ConstructionCostItem): XLSX.WorkSheet {
  const wsData: string[][] = [
    ['前回までの諸費用', '', '', '', '今回の諸費用', '', '', '', '累計諸費用', '', '', ''],
    ['カテゴリ', '金額', '構成比', '', 'カテゴリ', '金額', '構成比', '', 'カテゴリ', '金額', '構成比'],
    ...createCostDataRows(constructionCost),
  ]

  const ws = XLSX.utils.aoa_to_sheet(wsData)

  const wsHeight = wsData.length
  const wsWidth = wsData[0].length
  ws['!cols'] = []
  for (let i = 0; i < wsWidth; i++)
    ws['!cols'].push({ wch: 10 })

  // Set row heights for all rows
  ws['!rows'] = []
  for (let i = 0; i < wsHeight; i++)
    ws['!rows'].push({ hpt: 20 })

  // Add styles for headers
  // const headerStyle = {
  //   font: { bold: true },
  //   fill: { fgColor: { rgb: 'E6E6E6' } },
  // }

  // Add merge cells for title
  ws['!merges'] = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 2 } }, // 前回までの諸費用
    { s: { r: 0, c: 4 }, e: { r: 0, c: 6 } }, // 今回の諸費用
    { s: { r: 0, c: 8 }, e: { r: 0, c: 10 } }, // 累計諸費用
  ]

  return ws
}

export function exportConstructionCostDetail({ constructionCosts }: ExportOptions): void {
  const wb = XLSX.utils.book_new()

  // Create a worksheet for each construction
  constructionCosts.forEach((constructionCost, index) => {
    const ws = createConstructionSheet(constructionCost)
    XLSX.utils.book_append_sheet(wb, ws, `工事${index + 1}`)
  })

  // Export file
  const fileName = `工事原価明細_${new Date().toISOString().split('T')[0]}.xlsx`
  XLSX.writeFile(wb, fileName)
}
