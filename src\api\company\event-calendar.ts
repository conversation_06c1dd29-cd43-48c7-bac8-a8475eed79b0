export interface EventCalendarRuleResponse {
  eventCalendars: EventCalendarRuleItem[];
  pageNumber: number;
  pageSize: number;
  totalRecords: number;
}

export interface EventCalendarResponse {
  eventCalendarRules: EventCalendarItem[];
  pageNumber: number;
  pageSize: number;
  totalRecords: number;
}

export interface EventCalendarRuleItem {
  applyDate: string;
  description: string;
  eventId: string;
  eventEndTime: string;
  eventName: string;
  eventStartTime: string;
  isDayOff: boolean;
}

export interface EventCalendarLogsResponse {
  entityChanges: EventCalendarLogItem[];
  pageNum: number;
  pageSize: number;
  totalRecords: number;
}

export interface EventCalendarLogItem {
  action: string;
  auditLogId: string;
  changedList: EventCalendarChangedListItem[];
  entityId: string;
  description: string;
  modifiedTime: string;
  modifiedUserId: string;
  modifiedUserName: string;
}

export interface EventCalendarChangedListItem {
  fieldName: string;
  valueAfter: string | number | boolean | number[] | string[];
  valueBefore: string | number | boolean | number[] | string[];
}

export interface EventCalendarItem {
  eventId?: string;
  eventName: string;
  eventStartDate?: string;
  eventEndDate?: string;
  eventStartTime?: string;
  eventEndTime?: string;
  description?: string;
  isDayOff: boolean;
  isRecurring: boolean;
  recurringFrom?: string;
  recurringTo?: string;
  recurringType?: string;
  recurringDay?: number[];
  recurringWeek?: number[];
  recurringMonth?: number[];
}

interface EventCalendarDateParams {
  from: string;
  to: string;
}

export async function getEventCalendar(params?: any) {
  return useGet<EventCalendarResponse>('v1/eventcalendar', params);
}

export async function getEventCalendarDate(params: EventCalendarDateParams) {
  return useGet<EventCalendarRuleResponse>('v1/eventcalendar/date', params);
}

export async function getEventCalendarLogs(id: string, params?: any) {
  return useGet<EventCalendarLogsResponse>(`v1/eventcalendar/${id}/logs`, params);
}

export async function getOneEventCalendar(id: string, params?: any) {
  return useGet<EventCalendarItem>(`v1/eventcalendar/${id}`, params);
}

export function createEventCalendar(data: EventCalendarItem) {
  return usePost('v1/eventcalendar', data);
}

export function updateEventCalendar(id: string, data: EventCalendarItem) {
  return usePut(`v1/eventcalendar/${id}`, data);
}

export function deleteEventCalendar(id: string) {
  return useDelete(`v1/eventcalendar/${id}`);
}
