<script setup lang="ts">
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select'
import dayjs from 'dayjs'
import { isEmpty } from 'lodash'
import type { AttendanceItem } from '~@/api/attendance';
import { getAttendanceByDateApi } from '~@/api/attendance'
import type { ProjectComboItem } from '~@/api/company/project';
import { useApiRequest } from '~@/composables/useApiRequest'
import { useProjectStore } from '~@/stores/project'

const props = defineProps({
  value: { type: String },
  placeholder: { type: String },
  class: { type: String },
  disabled: { type: Boolean },
  date: { type: String, default: dayjs().format('YYYY-MM-DD') },
  options: { type: Array as () => DefaultOptionType[] },
  allowClear: { type: Boolean },
  showSearch: { type: Boolean },
  filterOption: { type: [Boolean, Function] as PropType<boolean | ((inputValue: string, option?: DefaultOptionType) => boolean)> },
})

const emit = defineEmits(['update:value', 'change'])

const attendanceObject = ref<Record<string, number>>({})
const options = ref<DefaultOptionType[]>(props.options ?? [])
const { projects } = useProjectStore()
const { data: attendanceData, execute: executeAttendance } = useApiRequest(getAttendanceByDateApi, { immediate: false, showNotify: false })

function handleChange(value: SelectValue, option: DefaultOptionType | DefaultOptionType[]) {
  emit('update:value', value)
  emit('change', value, option)
}

function getParams(date: string) {
  const selectedDate = dayjs(date)
  const today = dayjs().startOf('day')

  const daysAfter = 7
  let daysBefore = 7

  let toDate = selectedDate.add(daysAfter, 'day')

  if (toDate.isAfter(today)) {
    const excessDays = toDate.diff(today, 'day')
    daysBefore += excessDays
    toDate = today
  }

  const fromDate = selectedDate.subtract(daysBefore, 'day')

  return {
    fromDate: fromDate.format('YYYY-MM-DD'),
    toDate: toDate.format('YYYY-MM-DD'),
  }
}

const projectOptions = computed(() => {
  let projectOps = projects.map((item: ProjectComboItem) => ({
    label: `${item.code} - ${item.name}`,
    value: item.id,
    ...item,
  })) as DefaultOptionType[]
  if (!isEmpty(options.value))
    projectOps = options.value

  return projectOps.sort((a, b) => {
    a.count = attendanceObject.value[a.value ?? ''] ?? 0
    b.count = attendanceObject.value[b.value ?? ''] ?? 0
    return b.count - a.count
  })
})

onMounted(async () => {
  await executeAttendance(getParams(props.date))
  attendanceData?.value?.items?.forEach((item: AttendanceItem) => {
    if (item.projectId) {
      const count = attendanceObject.value[item.projectId] ?? 0
      attendanceObject.value[item.projectId] = count + 1
    }
  })
})

watch(
  () => props.date,
  async (newDate) => {
    await executeAttendance(getParams(newDate))
    attendanceObject.value = {}
    attendanceData?.value?.items?.forEach((item: AttendanceItem) => {
      if (item.projectId) {
        const count = attendanceObject.value[item.projectId] ?? 0
        attendanceObject.value[item.projectId] = count + 1
      }
    })
  },
)

watch(
  () => props.options,
  (newOptions) => {
    options.value = newOptions ?? []
  },
)
</script>

<template>
  <a-select
    :value="props.value"
    :placeholder="
      props.placeholder ?? $t('placeholder.select', { msg: $t('projectName') })
    "
    :class="props?.class ?? 'w-full'"
    :allow-clear="props.allowClear"
    :show-search="props.showSearch"
    :filter-option="props.filterOption"
    @change="handleChange"
  >
    <a-select-option
      v-for="item in projectOptions.filter((i) => i.value === '')"
      :key="item.value"
      :value="item.value"
    >
      {{ item.label }}
    </a-select-option>
    <a-select-option
      v-for="item in projectOptions.filter((i) => i.value !== '')"
      :key="item.value"
      :value="item.value"
      :="item"
    >
      <div class="flex items-center justify-between w-full">
        <span class="inline-block max-w-[calc(100%-20px)] truncate">
          {{ item.label }}
        </span>
        <a-rate
          v-if="item.count > 0"
          class="flex-shrink-0 text-sm ml-1"
          :value="1"
          :count="1"
          disabled
        />
      </div>
    </a-select-option>
  </a-select>
</template>
