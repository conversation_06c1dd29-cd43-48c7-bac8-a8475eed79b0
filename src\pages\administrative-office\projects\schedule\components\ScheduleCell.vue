<script lang="ts" setup>
import dayjs from 'dayjs'
import type { ScheduleItem } from '~@/api/company/schedule'

const props = defineProps({
  projectId: {
    type: String,
    required: true,
  },
  titleDate: {
    type: String,
    required: true,
  },
  schedules: Object as PropType<ScheduleItem[]>,
  clickedCells: Object as PropType<Record<string, boolean>>,
})

const emit = defineEmits<{
  (event: 'showScheduleInfo', evt: MouseEvent, projectId: string, workingDate: string, type: 'EDIT' | 'ADD_NEW', schedule?: ScheduleItem): void
}>()

const { t } = useI18n()

const iconColor = ref<string>('#CDCECD')

const schedulesComputed = computed(() => {
  const startOfWeek = dayjs(props.titleDate).startOf('week')
  const newSchedules: Partial<ScheduleItem>[] = []
  for (let i = 0; i < 7; i++) {
    const date = startOfWeek.add(i, 'day').format('YYYY-MM-DD')
    const schedule = props.schedules?.find(schedule => schedule.workingDate === date)
    if (!schedule) {
      newSchedules.push({
        workingDate: date,
      })
    }
    else {
      newSchedules.push(schedule)
    }
  }
  return newSchedules
})

function showScheduleInfo(evt: MouseEvent, workingDate: string, type: 'EDIT' | 'ADD_NEW', schedule?: ScheduleItem) {
  evt.stopPropagation()
  emit('showScheduleInfo', evt, props.projectId, workingDate, type, schedule)
}

function getIconColor(day: string) {
  const key = `${props.projectId}_${day}`
  if (props.clickedCells?.[key])
    return '#1554A3'

  return iconColor.value.startsWith(key) ? iconColor.value.split('_')[2] : '#CDCECD'
}

function getDiffPercentage(plan: number, actual: number) {
  const diff = (plan > actual ? plan - actual : actual - plan)
  const diffPercentage = diff / plan * 100
  return `${diffPercentage.toFixed(2)}%`
}

function getColor(plan: number, actual: number) {
  const diff = (plan > actual ? plan - actual : actual - plan)
  const diffPercentage = diff / plan * 100
  if (diffPercentage > 75)
    return 'text-red-600'
  if (diffPercentage >= 50)
    return 'text-yellow-600'

  return 'text-green-600'
}

watch(props?.clickedCells ?? {}, () => {
})

onMounted(() => {
})
</script>

<template>
  <template
    v-for="schedule in schedulesComputed" :key="schedule.workingDate"
  >
    <div class="flex flex-col overflow-y-hidden w-1/7 justify-top border-r-1 border-l-0 border-t-0 border-b-0 border-gray-300 border-solid p-1">
      <!-- Nút thêm mới schedule -->
      <template v-if="!schedule.scheduleId ">
        <div
          class="bg-[#F1F6FF] min-h-20 rounded-lg flex items-center justify-center cursor-pointer hover:bg-[#A8D5FF] hover:shadow-lg"
          :style="{ backgroundColor: props.clickedCells?.[`${props.projectId}_${schedule.workingDate}`] ? '#A8D5FF' : '' }"
          @mouseenter="iconColor = `${props.projectId}_${schedule.workingDate}_#1554A3`"
          @mouseleave="iconColor = `${props.projectId}_${schedule.workingDate}_#CDCECD`"
          @click.stop="(evt: MouseEvent) => showScheduleInfo(evt, schedule.workingDate!, 'ADD_NEW')"
        >
          <CarbonLargePlus :color="getIconColor(schedule.workingDate!)" />
        </div>
      </template>

      <!-- Hiển thị thông tin schedule -->
      <div
        v-else
        class="bg-[#DDDFFD] rounded-lg p-2"
        :class="{
          'border-2 border-solid border-[#1554A3]': clickedCells?.[`${props.projectId}_${schedule.workingDate}`],
        }"
        @click.stop="(evt: MouseEvent) => showScheduleInfo(evt, schedule.workingDate!, 'EDIT', schedule as ScheduleItem)"
      >
        <div class="text-[#2C3E50]">
          <div class="flex gap-x-2 justify-between">
            <span>{{ t('schedule.estimate') }}</span>
            <span class="font-bold text-green-600">{{ schedule.totalEstimatedWorkload }}</span>
          </div>
          <div class="flex gap-x-2 justify-between">
            <span>{{ t('schedule.plan') }}</span>
            <span class="font-bold text-green-600">{{ schedule.totalPlannedWorkload }}</span>
          </div>
          <div class="flex gap-x-2 justify-between">
            <span>{{ t('schedule.actual') }}</span>
            <a-tooltip :title="getDiffPercentage(schedule?.totalPlannedWorkload ?? 0, schedule?.totalActualWorkload ?? 0)" color="blue">
              <span class="font-bold cursor-pointer" :class="getColor(schedule?.totalPlannedWorkload ?? 0, schedule?.totalActualWorkload ?? 0)">{{ schedule.totalActualWorkload }}</span>
            </a-tooltip>
          </div>
        </div>
      </div>
    </div>
  </template>
</template>
