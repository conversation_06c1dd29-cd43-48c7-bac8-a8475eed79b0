<!-- eslint-disable curly -->
<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
import {
  DeleteOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue'
import { Modal } from 'ant-design-vue'
import { createVNode } from 'vue'
import type {
  StructureItem,
  StructureParams,
  StructureQueryParams,
} from '~@/api/company/struct'
import {
  createStructureApi,
  deleteStructureApi,
  getStructureListApi,
  updateStructureApi,
} from '~@/api/company/struct'
import { ModalType } from '~@/enums/system-status-enum'
import logger from '~@/utils/logger'

const loading = ref<boolean>(false)
const message = useMessage()
const treeLine = ref(true)
const showLeafIcon = ref(false)
const isAdd = ref<boolean>(false)
const openModalValue = ref(false)

const formRef = ref()
const { t } = useI18n()

const columns: any = computed(() => [
  {
    title: t('OrgStructureCode'),
    dataIndex: 'structureCode',
    key: 'structureCode',
  },
  {
    title: t('OrgStructureName'),
    dataIndex: 'structureName',
    key: 'structureName',
  },
  {
    title: t('Description'),
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: t('OrgStructureType'),
    dataIndex: 'isOutSource',
    key: 'isOutSource',
  },
  {
    title: t('action'),
    dataIndex: 'actions',
    key: 'actions',
    align: 'center',
  },
])

interface FormState {
  structureId: string | undefined
  structureCode: string
  structureName: string
  description: string
  isOutSource: boolean
  structureParentId: string | undefined
}

const formState = reactive<FormState>({
  structureId: undefined,
  structureCode: '',
  structureName: '',
  description: '',
  isOutSource: false,
  structureParentId: undefined,
})

const dataSource = ref<StructureItem[]>([])

const pagination = ref({
  pageSize: 5,
  pageSizeOptions: ['10', '20', '30', '40', '50'],
  showQuickJumper: true,
  total: 0,
})

async function getStructureList() {
  const params: StructureQueryParams = {
    pageNum: 1,
    pageSize: 100,
  }
  loading.value = true
  try {
    const { data, status, code } = await getStructureListApi(params)
    if (status === 200) {
      dataSource.value = data?.items ?? []
      pagination.value.total = data?.totalRecords ?? 0
      logger.log('data', dataSource)
    }
    else {
      logger.error('status', status)
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
  finally {
    loading.value = false
  }
}

const cancelModal = () => {
  formRef.value.resetFields()
  formState.structureId = undefined
  formState.structureCode = ''
  formState.structureName = ''
  formState.description = ''
  formState.isOutSource = false
  formState.structureParentId = undefined
}
const onDelete = async () => {
  if (formState.structureId === undefined) {
    return
  }
  const structureId = formState.structureId
  await deleteStructure(structureId)
  cancelModal()
}

const showConfirm = () => {
  Modal.confirm({
    title: `${t('title.delete')}`,
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', {}, `${t('alert.confirmDelete')}`),
    cancelText: `${t('button.cancel')}`,
    okText: `${t('button.ok')}`,
    onOk: async () => {
      await onDelete()
      await getStructureList()
      // cancelModal()
      // logger.log('onOK')
    },
  })
}

function openModal(item: any, type: string) {
  if (type === ModalType.ADD) {
    isAdd.value = true
    openModalValue.value = true
  }
  else {
    isAdd.value = false
    formState.structureId = item.structureId
    formState.structureCode = item.structureCode
    formState.structureName = item.structureName
    formState.description = item.description
    formState.isOutSource = item.isOutSource
    formState.structureParentId = item.structureParentId
    if (type === ModalType.EDIT) {
      openModalValue.value = true
      return
    }
    if (type === ModalType.DELETE) {
      showConfirm()
    }
  }
}

async function createStructure() {
  logger.log('debug structure')
  const newStructure: StructureParams = {
    structureCode: formState.structureCode,
    structureName: formState.structureName,
    description: formState.description,
    isOutSource: formState.isOutSource,
    structureParentId: formState.structureParentId,
  }
  try {
    const { status, code } = await createStructureApi(newStructure)
    if (status === 200) {
      logger.log('createStructure', newStructure)
      message.success('成作成功')
    }
    else {
      logger.error('status', status)
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

async function updateStructure(structureId: string, params: StructureParams) {
  try {
    const { status, code } = await updateStructureApi(structureId, params)
    if (status === 200) {
      message.success('成作成功')
    }
    else {
      logger.error('status', status)
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

async function deleteStructure(structureId: string) {
  try {
    const { status, code } = await deleteStructureApi(structureId)
    if (status === 200) {
      message.success('成作成功')
    }
    else {
      logger.error('status', status)
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

const handleOk = async () => {
  logger.log('debug handleOK')
  formRef.value.validate().then(async () => {
    if (isAdd.value) {
      await createStructure()
    }
    else {
      const newStructure: StructureParams = {
        structureCode: formState.structureCode,
        structureName: formState.structureName,
        description: formState.description,
        isOutSource: formState.isOutSource,
        structureParentId: formState.structureParentId,
      }
      if (!formState.structureId) {
        return
      }
      const structureId = formState.structureId
      if (!structureId)
        return
      await updateStructure(structureId, newStructure)
    }

    openModalValue.value = false
    cancelModal()
    getStructureList()
  })
}

onMounted(() => {
  getStructureList()
})
</script>

<template>
  <page-container>
    <a-row :gutter="24">
      <a-col :xxl="24" :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
        <a-card :style="{ marginBottom: '24px' }" :bordered="false">
          <template #title>
            <a-card :bordered="false" :body-style="{ padding: 0 }">
              <a-row style="font-weight: normal">
                <a-col :span="14">
                  <!-- <a-input-search
                    v-model:value="defautParams.SearchParams"
                    :placeholder="t('input.placeholder')"
                    style="width: 270px"
                    @search="onSearch"
                  /> -->
                </a-col>
                <a-col :span="10" class="flex flex-justify-end">
                  <a-button type="primary" @click="openModal(null, ModalType.ADD)">
                    <PlusOutlined /> {{ t("button.add") }}
                  </a-button>
                </a-col>
              </a-row>
            </a-card>
          </template>
          <a-table
            :columns="columns"
            :data-source="dataSource"
            children-column-name="children"
            :loading="loading"
            row-key="structureId"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'isOutSource'">
                <a-tag v-if="record.isOutSource" color="error">
                  {{ t('OutSource') }}
                </a-tag>
                <a-tag v-else color="success">
                  {{ t('InHourse') }}
                </a-tag>
              </template>
              <template v-if="column.dataIndex === 'status'">
                <a-tag v-if="record.status" color="success">Active</a-tag>
                <a-tag v-else color="error">Deactive</a-tag>
              </template>
              <template v-if="column.dataIndex === 'actions'">
                <a-button type="text" @click="openModal(record, ModalType.EDIT)">
                  <EditOutlined />
                </a-button>
                <a-button type="text" @click="openModal(record, ModalType.DELETE)">
                  <DeleteOutlined />
                </a-button>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>

    <!-- Modal -->
    <a-modal
      v-model:open="openModalValue"
      :title="isAdd ? t('button.add') : t('button.update')"
      @ok="handleOk"
      @cancel="cancelModal"
    >
      <a-form
        ref="formRef"
        :model="formState"
        name="basic"
        :label-col="{ span: 24 }"
        :wrapper-col="{ span: 24 }"
        autocomplete="off"
      >
        <a-form-item
          v-if="isAdd"
          :label="t('OrgStructureCode')"
          name="structureCode"
          :rules="[{ required: true, message: 'OrgStructureCode' }]"
        >
          <a-input
            v-model:value="formState.structureCode"
            :placeholder="t('placeholder.enter-data', { msg: t('OrgStructureCode') })"
          />
        </a-form-item>
        <a-form-item
          :label="t('OrgStructureName')"
          name="structureName"
          :rules="[{ required: true, message: t('OrgStructureName') }]"
        >
          <a-input
            v-model:value="formState.structureName"
            :placeholder="t('placeholder.enter-data', { msg: t('OrgStructureName') })"
          />
        </a-form-item>
        <a-form-item
          :label="t('OrgStructureParent')"
          name="structureParentId"
          :rules="[{ required: false, message: t('OrgStructureParent') }]"
        >
          <a-tree-select
            v-model:value="formState.structureParentId"
            :allow-clear="true"
            :placeholder="t('placeholder.select-data', { msg: t('OrgStructureParent') })"
            :tree-line="treeLine && { showLeafIcon }"
            :tree-data="dataSource"
            :field-names="{
              children: 'children',
              label: 'structureName',
              value: 'structureId',
            }"
          />
        </a-form-item>
        <a-form-item :label="t('Description')" name="description">
          <a-textarea
            v-model:value="formState.description"
            :rows="3"
          />
        </a-form-item>
        <a-form-item :label="t('OrgStructureType')">
          <a-radio-group v-model:value="formState.isOutSource">
            <a-radio :value="false">
              {{ t('InHourse') }}
            </a-radio>
            <a-radio :value="true">
              {{ t('OutSource') }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <!-- <a-form-item v-if="!isAdd" :label="t('status')">
          <a-switch v-model:checked="formState.status" />
        </a-form-item> -->
      </a-form>
    </a-modal>
  </page-container>
</template>
