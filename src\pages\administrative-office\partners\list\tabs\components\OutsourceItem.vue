<script lang="ts" setup>
import { type OutSourceItem, getOutsourceLogo } from '~@/api/outsource'

const props = defineProps({
  outsourceData: {
    type: Object as PropType<OutSourceItem>,
    default: () => ({}),
    required: true,
  },
})

const emit = defineEmits<{
  (event: 'openModal', type: 'ADD-NEW' | 'EDIT', outsourceData?: OutSourceItem): void
  (event: 'deleteOutsource', outSourceId: string): void
}>()

const { t } = useI18n()
const orgId = useOrg()

const outSourceLogo = computed(() => {
  return getOutsourceLogo(props.outsourceData?.outSourceId, orgId.value ?? '')
})

function openModal(type: 'ADD-NEW' | 'EDIT', outsourceData?: OutSourceItem) {
  emit('openModal', type, outsourceData)
}

function deleteOutsource(outSourceId: string) {
  emit('deleteOutsource', outSourceId)
}

onMounted(() => {
})
</script>

<template>
  <div class="flex items-center gap-x-2 w-full bg-white rounded-lg p-4 shadow-md justify-between">
    <div class="flex items-center gap-x-2">
      <img
        class="h-20 w-20 rounded-md"
        :src="outSourceLogo"
      >
      <div class="flex flex-col">
        <a-typography-title :level="5">
          {{ outsourceData?.outSourceName }}
        </a-typography-title>
        <a-typography-text>{{ outsourceData?.outSourceCode }}</a-typography-text>
      </div>
    </div>
    <div class="flex flex-col gap-2">
      <div class="flex gap-x-2">
        <CarbonPhone />
        <a-typography-text>{{ outsourceData?.phoneNumber }}</a-typography-text>
      </div>
      <div class="flex gap-x-2">
        <CarbonEmail />
        <a-typography-text>{{ outsourceData?.email }}</a-typography-text>
      </div>
      <div class="flex gap-x-2">
        <CarbonAddress />
        <a-typography-text>{{ outsourceData?.address }}</a-typography-text>
      </div>
    </div>
    <div class="flex flex-col gap-2">
      <div class="flex gap-x-2">
        <CarbonUser />
        <a-typography-text>{{ outsourceData?.contactPerson?.name }}</a-typography-text>
      </div>
      <div class="flex gap-x-2">
        <CarbonEmail />
        <a-typography-text>{{ outsourceData?.contactPerson?.email }}</a-typography-text>
      </div>
      <div class="flex gap-x-2">
        <CarbonPhone />
        <a-typography-text>{{ outsourceData?.contactPerson?.phoneNumber }}</a-typography-text>
      </div>
    </div>
    <div>
      <a-tag v-for="(expertise, index) in outsourceData?.expertise" :key="index">
        {{ expertise }}
      </a-tag>
    </div>
    <div class="flex gap-x-2">
      <a-button @click="openModal('EDIT', outsourceData as OutSourceItem)">
        {{ t('button.edit') }}
      </a-button>
      <a-button danger @click="deleteOutsource(outsourceData?.outSourceId)">
        {{ t('button.delete') }}
      </a-button>
    </div>
  </div>
</template>
