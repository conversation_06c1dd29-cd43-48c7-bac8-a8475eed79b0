import type { InputCostItemQuery } from '~@/api/input-cost-item'
import { getInputCostItemApi, getInputCostItemListApi } from '~@/api/input-cost-item'

export function useInputCostItem() {
  const messageNotify = useMessage()
  const isLoading = ref(false)

  const fetchInputCostItemList = async (params: InputCostItemQuery) => {
    isLoading.value = true
    try {
      const { data, status, message } = await getInputCostItemListApi(params)
      if (status === 200) {
        return data?.items ?? []
      }
      else {
        messageNotify.error(message ?? 'Fetch input cost items failed!')
        return []
      }
    }
    catch (error) {
      messageNotify.error('Failed to fetch input cost items')
      return []
    }
    finally {
      isLoading.value = false
    }
  }

  const fetchInputCostItemByCategory = async (categoryId: string, params?: InputCostItemQuery) => {
    isLoading.value = true
    try {
      const { data, status, message } = await getInputCostItemApi(categoryId, params)
      if (status === 200) {
        return data?.items ?? []
      }
      else {
        messageNotify.error(message ?? 'Fetch input cost items by category failed!')
        return []
      }
    }
    catch (error) {
      messageNotify.error('Failed to fetch input cost items by category')
      return []
    }
    finally {
      isLoading.value = false
    }
  }

  return {
    isLoading,
    fetchInputCostItemList,
    fetchInputCostItemByCategory,
  }
}
