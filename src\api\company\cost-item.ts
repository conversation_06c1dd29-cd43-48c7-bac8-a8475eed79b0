import type { UploadFile } from 'ant-design-vue'
import qs from 'qs'
import { formatData } from '~@/utils/tools'
import { useOrg } from '~@/composables/org'

export interface CostItemPriceResponse {
  items: CostItemPrice[]
  pageNum?: number
  pageSize?: number
  totalRecords?: number
}
export interface CostItemPrice {
  createTime?: string
  updateTime?: string
  itemPriceId: string
  itemId?: string
  itemCode?: string
  itemName?: string
  vendorId?: string
  vendorCode?: string
  vendorName?: string
  unit?: string
  price?: number
  validFrom?: string
  validTo?: string
}

export interface CostItemResponse {
  items: CostItem[]
  pageIndex: number
  pageSize: number
}

export interface CostItem {
  image?: UploadFile
  imageUrl?: string
  itemId: string
  itemCode: string
  itemName: string
  itemSubName?: string
  description?: string
  categoryId?: string
  categoryCode?: string
  categoryName?: string
  size?: string
  serialNumber?: string
  manufacturerId?: string
  manufacturerCode?: string
  manufacturerName?: string
}

export interface GetCostItemParams {
  keyword?: string
  pageNum?: number
  pageSize?: number
  fromDate?: string
  toDate?: string
  priceMin?: number
  priceMax?: number
  categoryId?: string
  manufacturerId?: string
  size?: string
}

interface GetCostItemLogsParams {
  dateFrom?: string
  dateTo?: string
  action?: string
  pageNum?: number
  pageSize?: number
}

interface CostItemLogsResponse {
  entityChanges: CostItemLogItem[]
  pageNum: number
  pageSize: number
  totalRecords: number
}

export interface CostItemChangedListItem {
  fieldName: string
  valueAfter: string | number | boolean | number[] | string[]
  valueBefore: string | number | boolean | number[] | string[]
}

export interface CostItemLogItem {
  action: string
  auditLogId: string
  changedList: CostItemChangedListItem[]
  entityId: string
  description: string
  modifiedTime: string
  modifiedUserId: string
  modifiedUserName: string
}

export async function getCostItem(params?: GetCostItemParams) {
  return useGet<CostItemResponse>('v1/cost/item', params, {
    paramsSerializer: params => qs.stringify(params, { indices: false }),
  })
}

export async function getOneCostItem(id: string, params?: GetCostItemParams) {
  return useGet<CostItem>(`v1/cost/item/${id}`, params)
}

export function getCostItemLogo(id: string): string {
  const host = import.meta.env.VITE_APP_BASE_API ?? ''
  return `${host}/v1/cost/item/${id}/image?orgId=${useOrg().value}`
}

export async function createCostItem(data: Partial<CostItem>) {
  return usePost('/v1/cost/item', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  })
}

export async function updateCostItem(id: string, data: Partial<CostItem>) {
  return usePut(`/v1/cost/item/${id}`, data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  })
}

export async function deleteCostItem(id: string) {
  return useDelete(`v1/cost/item/${id}`)
}

export async function getManufacturerLogs(
  id: string,
  params?: GetCostItemLogsParams,
) {
  return useGet<CostItemLogsResponse>(`v1/cost/item/${id}/logs`, params)
}

export async function getCostItemPrice(id: string, params?: GetCostItemParams) {
  return useGet<CostItemPriceResponse>(
    `v1/cost/item/${id}/price`,
    formatData(params),
  )
}

export async function createCostItemPrice(
  id: string,
  data: Partial<CostItemPrice>,
) {
  return usePost(`v1/cost/item/${id}/price`, data)
}
