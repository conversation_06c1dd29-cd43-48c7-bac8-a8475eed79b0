import { computed, ref } from 'vue'
import dayjs from 'dayjs'

export function useAttendance() {
  const selectedDate = ref(dayjs())
  const dateFormat = 'YYYY-MM-DD'

  const startOfWeek = computed(() => {
    return selectedDate.value.startOf('week')
  })

  const endOfWeek = computed(() => {
    return selectedDate.value.endOf('week')
  })

  const disabledDate = (current: dayjs.Dayjs) => {
    return current && current > dayjs().endOf('day')
  }

  const onChangeWeek = (type: 'prev' | 'next') => {
    if (type === 'prev')
      selectedDate.value = selectedDate.value.subtract(1, 'week')
    else
      selectedDate.value = selectedDate.value.add(1, 'week')
  }

  return {
    selectedDate,
    dateFormat,
    startOfWeek,
    endOfWeek,
    disabledDate,
    onChangeWeek,
  }
}
