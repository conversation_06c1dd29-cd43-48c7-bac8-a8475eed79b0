import { AxiosError } from 'axios'
import type { NavigationGuardNext, RouteLocation } from 'vue-router'
import router from '~/router'
import { useMetaTitle } from '~/composables/meta-title'
import { setRouteEmitter } from '~@/utils/route-listener'
import logger from '~@/utils/logger'
import { useAuth } from '~@/composables/account/useAuth'

// Set -> O(1)
const allowList = new Set([
  '/login',
  '/error',
  '/401',
  '/404',
  '/403',
  '/forgot-account',
  '/change-password',
  '/account/sign-up',
])
const loginPath = '/login'
const invitationAcceptPath = '/invitation/accept'

const isAllowedRoute = (path: string) => allowList.has(path)
const isRedirectRoute = (path: string) => path.startsWith('/redirect')
const shouldRedirect = (path: string) => !isAllowedRoute(path) && !isRedirectRoute(path)

async function handleUnauthenticated(to: RouteLocation, next: NavigationGuardNext) {
  if (shouldRedirect(to.path)) {
    next({
      path: loginPath,
      query: { redirect: encodeURIComponent(to.fullPath) },
    })
  }
  else {
    next()
  }
}

async function handleDynamicRoutes() {
  const userStore = useUserStore()
  const currentRoute = await userStore.generateDynamicRoutes()
  if (currentRoute?.name && !router.hasRoute(currentRoute.name))
    router.addRoute(currentRoute)
}

function isPathValid(path: string) {
  return router.getRoutes().some(route => route.path === path)
}

// Thêm hàm check nếu là detail route với parameter
function isDetailRoute(path: string) {
  // Check if path matches any route with parameters
  return router.getRoutes().some((route) => {
    if (!route.path.includes(':'))
      return false

    // Convert route pattern to regex
    const routePattern = route.path.replace(/:([^/]+)/g, '([^/]+)')
    const regex = new RegExp(`^${routePattern}$`)
    return regex.test(path)
  })
}

async function handleAuthenticated(to: RouteLocation, next: NavigationGuardNext) {
  const userStore = useUserStore()
  const projectStore = useProjectStore()
  const avatarStore = useAvatarStore()
  // Redirect authenticated users away from login page
  if (to.path === loginPath) {
    const { clearToken } = useAuth()
    clearToken()
    return next({
      path: '/',
      query: {
        redirect: encodeURIComponent(to.path),
      },
    })
  }

  if (to.path === invitationAcceptPath)
    return next()

  // Nếu có token nhưng chưa có thông tin user -> Lấy dữ liệu 1 lần
  if (!userStore.userInfo) {
    const result = await userStore.getEmployeeInfo()
    await userStore.getUserAvatar()
    await projectStore.fetchProjects()
    await avatarStore.fetchAvatar()
    if (!result) {
      return next({
        path: loginPath,
        query: { redirect: encodeURIComponent(to.fullPath) },
      })
    }
    await handleDynamicRoutes()
    await userStore.fetchCurrentRoles()

    if (to.path === '/') {
      const defaultRoute = router.getRoutes().find(route => route.name === 'ROOT_EMPTY_PATH')
      if (defaultRoute)
        return next({ path: defaultRoute.path, replace: true })
    }
    else if (isPathValid(to.path) || isDetailRoute(to.path)) {
      return next({ ...to, replace: true })
    }
    else {
      const defaultRoute = router.getRoutes().find(route => route.name === 'ROOT_EMPTY_PATH')
      if (defaultRoute)
        return next({ ...defaultRoute, replace: true })
    }
  }

  return next()
}

function handleNavigationError(error: unknown, next: NavigationGuardNext, clearToken: () => void) {
  logger.error(error)

  if (error instanceof AxiosError) {
    switch (error.response?.status) {
      case 401:
        clearToken()
        next({ path: loginPath })
        break
      case 403:
        next('/403')
        break
      case 404:
        next('/404')
        break
      default:
        next('/error')
    }
  }
  else {
    next('/error')
  }
}

// Hàm để duyệt tất cả các đường dẫn trước khi được gửi đi
router.beforeEach(async (to, _, next) => {
  setRouteEmitter(to)
  const { hasToken, clearToken } = useAuth()
  try {
    if (!hasToken.value)
      return await handleUnauthenticated(to, next)

    return await handleAuthenticated(to, next)
  }
  catch (error) {
    handleNavigationError(error, next, clearToken)
  }
})

router.afterEach((to) => {
  useMetaTitle(to)
  useLoadingCheck()
  useScrollToTop()
})
