<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
import {
  PlusOutlined,
} from '@ant-design/icons-vue'
import { usePagination } from 'vue-request'
import type {
  WorkplaceItem,
  WorkplaceItemParams,
} from '~@/api/company/office'
import {
  createWorkplaceApi,
  getWorkplaceListApi,
  updateWorkplaceApi,
} from '~@/api/company/office'
import logger from '~@/utils/logger'

const formRef = ref()
const { t } = useI18n()
const message = useMessage()
const isAdd = ref<boolean>(false)
const openModalValue = ref(false)
const searchInput = ref()

const defautParams = reactive<WorkplaceItemParams>({
})

// function onSearch() {
//   run(defautParams)
// }

const columns: any = computed(() => {
  return [
    {
      title: t('form.officeCode'),
      dataIndex: 'workplaceCode',
      key: 'workplaceCode',
      customFilterDropdown: true,
      onFilter: (value: string, record: WorkplaceItem) => {
        return record.workplaceCode?.toString().toLowerCase().includes(value.toLowerCase())
      },
      onFilterDropdownOpenChange: (visible: boolean) => {
        if (visible) {
          setTimeout(() => {
            searchInput.value.focus()
          }, 100)
        }
      },
    },
    {
      title: t('form.officeName'),
      dataIndex: 'workplaceName',
      key: 'workplaceName',
      customFilterDropdown: true,
      onFilter: (value: string, record: WorkplaceItem) => {
        return record.workplaceName?.toString().toLowerCase().includes(value.toLowerCase())
      },
      onFilterDropdownOpenChange: (visible: boolean) => {
        if (visible) {
          setTimeout(() => {
            searchInput.value.focus()
          }, 100)
        }
      },
    },
    {
      title: t('form.address'),
      dataIndex: 'address',
      key: 'address',
      customFilterDropdown: true,
      onFilter: (value: string, record: WorkplaceItem) => {
        return record.address?.toString().toLowerCase().includes(value.toLowerCase())
      },
      onFilterDropdownOpenChange: (visible: boolean) => {
        if (visible) {
          setTimeout(() => {
            searchInput.value.focus()
          }, 100)
        }
      },
    },
    {
      title: t('form.description'),
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: t('form.status'),
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: t('action'),
      dataIndex: 'actions',
      key: 'actions',
      width: '150px',
      align: 'center',
    },
  ]
})

const formState = reactive<WorkplaceItemParams>({
  address: '',
  isWorkSite: false,
  workplaceCode: '',
  workplaceName: '',
  description: '',
  status: true,
})

const queryData = async (params: any): Promise<WorkplaceItem[] | undefined> => {
  try {
    const { data, status, code } = await getWorkplaceListApi(params)
    if (status === 200) {
      return data?.items ?? []
    }
    else {
      message.error(code)
      return undefined
    }
  }
  catch (e) {
    logger.error(e)
  }
}

const {
  data: dataSource,
  run,
  loading,
  refresh,
  current,
  pageSize,
  total,
// eslint-disable-next-line ts/no-use-before-define
} = usePagination(queryData, {
  defaultParams: [defautParams],
  pagination: {
    currentKey: 'pageIndex',
    pageSizeKey: 'pageSize',
    totalKey: 'TotalRow',
  },
})

const pagination = computed(() => ({
  total: total.value,
  current: current.value,
  pageSize: pageSize.value,
}))

const handleTableChange: any = (
  pag: { pageSize: number; current: number },
  filters: any,
) => {
  run({
    PageSize: pag.pageSize,
    PageIndex: pag.current,
    ...filters,
  })
}

function openModal(item?: any, charge?: boolean) {
  if (charge) {
    isAdd.value = true
    openModalValue.value = true
  }
  else {
    isAdd.value = false
    openModalValue.value = true
    formState.workplaceId = item?.workplaceId
    formState.workplaceCode = item?.workplaceCode
    formState.workplaceName = item?.workplaceName
    formState.address = item?.address
    formState.description = item?.description
    formState.status = item?.status
  }
}

const cancelModal = () => {
  formRef.value.resetFields()
  formState.workplaceCode = ''
  formState.workplaceName = ''
  formState.description = ''
  formState.address = ''
  formState.status = true
}

const handleOk = async () => {
  formRef.value
    .validate()
    .then(async () => {
      if (isAdd.value) {
        const data = {
          address: formState.address,
          description: formState.description,
          orgId: formState.orgId,
          workplaceCode: formState.workplaceCode,
          workplaceName: formState.workplaceName,
        }
        const { status } = await createWorkplaceApi(data)
        if (status === 200) {
          message.success('新作成功')
        }
        else {
          message.error('新作失敗')
          logger.error('status', status)
        }
      }
      else {
        const updateData = {
          workplaceId: formState.workplaceId,
          address: formState.address,
          description: formState.description,
          workplaceCode: formState.workplaceCode,
          workplaceName: formState.workplaceName,
          status: formState.status,
          isWorkSite: false,
        }
        const { status, code } = await updateWorkplaceApi(updateData)
        if (status === 200) {
          message.success('変更成功')
        }
        else {
          message.error(t(code))
          logger.error('status', status)
          logger.error(code, t(code))
        }
      }
      openModalValue.value = false
      cancelModal()
      refresh()
    })
    .catch((err: any) => {
      logger.log('error', err)
    })
}

const state = reactive({
  searchText: '',
  searchedColumn: '',
})

const handleSearch = (selectedKeys: string[], confirm: () => void, dataIndex: string) => {
  confirm()
  state.searchText = selectedKeys[0]
  state.searchedColumn = dataIndex
}

const handleReset = (clearFilters: (options: { confirm: boolean }) => void) => {
  clearFilters({ confirm: true })
  state.searchText = ''
}

// function showConfirm(item: any) {
//   Modal.confirm({
//     title: t('title.delete'),
//     icon: createVNode(ExclamationCircleOutlined),
//     content: createVNode('div', {}, `${t('alert.confirmDelete')}`),
//     cancelText: `${t('button.cancel')}`,
//     okText: `${t('button.ok')}`,
//     async onOk() {
//       const data = {
//         workplaceId: item.workplaceId,
//         workplaceCode: item.workplaceCode,
//         workplaceName: item.workplaceName,
//         address: item.address,
//         description: item.description,
//         status: false,
//         isWorkSite: false,
//       }
//       await updateWorkplaceApi(data)
//       refresh()
//     },
//     onCancel() {
//       Modal.destroyAll()
//     },
//     class: 'test',
//   })
// }
</script>

<template>
  <page-container>
    <a-row :gutter="24">
      <a-col :xxl="24" :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
        <a-card :style="{ marginBottom: '24px' }" :bordered="false">
          <template #title>
            <a-card :bordered="false" :body-style="{ padding: 0 }">
              <a-row style="font-weight: normal">
                <a-col :span="14">
                  <!-- <a-input-search
                    v-model:value="defautParams.SearchParams"
                    :placeholder="t('input.placeholder')"
                    style="width: 270px"
                    @search="onSearch"
                  /> -->
                </a-col>
                <a-col :span="10" class="flex flex-justify-end">
                  <a-button type="primary" @click="openModal(undefined, true)">
                    <PlusOutlined /> {{ t("button.add") }}
                  </a-button>
                </a-col>
              </a-row>
            </a-card>
          </template>
          <a-table
            :columns="columns"
            :data-source="dataSource"
            :pagination="pagination"
            :loading="loading"
            :scroll="{ x: 1600 }"
            @change="handleTableChange"
          >
            <template #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
              <div style="padding: 8px">
                <a-input
                  ref="searchInput"
                  :placeholder="`Search ${column.dataIndex}`"
                  :value="selectedKeys[0]"
                  style="width: 188px; margin-bottom: 8px; display: block"
                  @change="(e: any) => setSelectedKeys(e.target.value ? [e.target.value] : [])"
                  @press-enter="handleSearch(selectedKeys, confirm, column.dataIndex)"
                />
                <a-button
                  type="primary"
                  size="small"
                  style="width: 90px; margin-right: 8px"
                  @click="handleSearch(selectedKeys, confirm, column.dataIndex)"
                >
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  Search
                </a-button>
                <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
                  Reset
                </a-button>
              </div>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'status'">
                <a-tag v-if="record.status" color="success">
                  Active
                </a-tag>
                <a-tag v-else color="error">
                  Deactive
                </a-tag>
              </template>
              <template v-else-if="column.dataIndex === 'actions'">
                <a-button type="primary" class="me-0 me-sm-2" @click="openModal(record, false)">
                  <i class="fa-solid fa-square-pen" />
                </a-button>
                <!-- <a-button type="primary" @click="showConfirm(record)">
                  <i class="fa-regular fa-trash-can" />
                </a-button> -->
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>

    <!-- Modal -->
    <a-modal
      v-model:open="openModalValue"
      :title="isAdd ? t('button.add') : t('button.edit')"
      @ok="handleOk"
      @cancel="cancelModal"
    >
      <a-form
        ref="formRef"
        :model="formState"
        name="basic"
        :label-col="{ span: 24 }"
        :wrapper-col="{ span: 24 }"
        autocomplete="off"
      >
        <a-form-item
          :label="t('form.officeCode')"
          name="workplaceCode"
          :rules="[{ required: true, message: '現場コードを入力してください' },
                   { max: 10, message: '10文字以下にしてください' },
          ]"
        >
          <a-input v-model:value="formState.workplaceCode" />
        </a-form-item>
        <a-form-item
          :label="t('form.officeName')"
          name="workplaceName"
          :rules="[{ required: true, message: '現場名を入力してください' }]"
        >
          <a-input v-model:value="formState.workplaceName" />
        </a-form-item>
        <a-form-item
          :label="t('form.address')"
          name="address"
          :rules="[{ required: true }]"
        >
          <a-input
            v-model:value="formState.address"
            :placeholder="t('form.address')"
          />
        </a-form-item>
        <a-form-item :label="t('form.description')" name="description">
          <a-textarea
            v-model:value="formState.description"
            :placeholder="t('form.description')"
            :rows="3"
          />
        </a-form-item>
        <a-form-item v-if="!isAdd" :label="t('form.status')" name="status">
          <a-switch v-model:checked="formState.status" />
        </a-form-item>
      </a-form>
    </a-modal>
  </page-container>
</template>
