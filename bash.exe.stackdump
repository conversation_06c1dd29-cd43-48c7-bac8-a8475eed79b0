Stack trace:
Frame         Function      Args
0007FFFFB740  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x2118E
0007FFFFB740  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x69BA
0007FFFFB740  0002100469F2 (00021028DF99, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB740  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB740  00021006A545 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFBA20  00021006B9A5 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFDDF50000 ntdll.dll
7FFFDC830000 KERNEL32.DLL
7FFFDB8C0000 KERNELBASE.dll
7FFFD7510000 apphelp.dll
7FFFDC5E0000 USER32.dll
7FFFDB4A0000 win32u.dll
7FFFDC800000 GDI32.dll
7FFFDB380000 gdi32full.dll
7FFFDB580000 msvcp_win.dll
7FFFDB260000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFDBCD0000 advapi32.dll
7FFFDC9A0000 msvcrt.dll
7FFFDC3A0000 sechost.dll
7FFFDB4D0000 bcrypt.dll
7FFFDC4C0000 RPCRT4.dll
7FFFDAA00000 CRYPTBASE.DLL
7FFFDB500000 bcryptPrimitives.dll
7FFFDCC80000 IMM32.DLL
