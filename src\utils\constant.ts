export enum AccessEnum {
  ADMIN = 'ADMIN',
  USER = 'USER',
}

export enum DynamicLoadEnum {
  FRONTEND = 'FRONTEND',
  BACKEND = 'BACKEND',
}

export enum STATUS {
  OFF = '0',
  RUNNING = '1',
  ONLINE = '2',
  ERROR = '3',
}

export const DYNAMIC_LOAD_WAY
  = import.meta.env.VITE_APP_LOAD_ROUTE_WAY ?? DynamicLoadEnum.BACKEND

// Enum for working role in Schedule
export enum Skills {
  SUPERVISOR = 'supervisor',
  FOREMAN = 'foreman',
  WORKER = 'worker',
  HEAVY_MACHINERY_OPERATOR = 'heavyMachineryOperator',
  FORMWORK_WORKER = 'formworkWorker',
  REBAR_WORKER = 'rebarWorker',
  ENGINEER = 'engineer',
  CONTRACT_TYPE = 'contractType',
  UNASSIGNED = 'unassigned',
  OFFICE_OPERATION = 'officeOperation',
}

export enum ConstructionType {
  MAIN = 'MAIN',
  SUB = 'SUB',
  OVERALL = 'OVERALL',
}
