<script setup lang="ts">
const multiTabStore = useMultiTab()
const layoutMenuStore = useLayoutMenu()
const userStore = useUserStore()
const router = useRouter()
// async function back() {
//   if (!userStore.()) {
//     message.warning(t('message.noPermission'), 7)
//     router.replace({
//       path: '/login',
//     })
//     try {
//       await userStore.logout()
//     }
//     finally {
//       // hide()
//       // message.success('退出登录成功', 3)
//       router
//         .push({
//           path: '/login',
//         })
//         .then(() => {
//           multiTabStore.clear()
//           layoutMenuStore.clear()
//         })
//     }
//   }
//   else {
//     message.warning('ページが存在しません', 3)
//     // router.go(-1)
//   }
//   // router.go(-1)
// }

async function backToLogin() {
  router.replace({
    path: '/login',
  })
  try {
    await userStore.logout()
  }
  finally {
    // hide()
    // message.success('退出登录成功', 3)
    router
      .push({
        path: '/login',
      })
      .then(() => {
        multiTabStore.clear()
        layoutMenuStore.clear()
      })
  }
}
</script>

<template>
  <a-result status="404" title="404" sub-title="Page not found (404)">
    <template #extra>
      <a-button type="primary" @click="backToLogin">
        Logout
      </a-button>
    </template>
  </a-result>
</template>
