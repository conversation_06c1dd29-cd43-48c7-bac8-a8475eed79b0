import { getSchedules } from '../services/scheduleService'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'
import { type DuplicateScheduleParams, type ProjectScheduleItem, type ProjectScheduleParams, duplicateScheduleApi } from '~@/api/company/schedule'

export function useSchedules() {
  const projectScheduleData = ref<ProjectScheduleItem[]>()
  const loading = ref(true)
  const error = ref()

  const fetchSchedules = async (queryParams: ProjectScheduleParams) => {
    loading.value = true
    try {
      projectScheduleData.value = await getSchedules(queryParams)
    }
    catch (error) {
    }
    finally {
      loading.value = false
    }
  }

  const duplicateSchedule = async (params: DuplicateScheduleParams) => {
    loading.value = true
    try {
      const { status, message } = await duplicateScheduleApi(params)
      if (status === ResponseStatusEnum.SUCCESS) {
        return true
      }
      else {
        error.value = message
        return false
      }
    }
    catch (e) {
    }
    finally {
      loading.value = false
    }
  }

  const projectOptions = computed(() => {
    return projectScheduleData.value?.map((item: ProjectScheduleItem) => ({
      label: `${item.projectCode} - ${item.projectName}`,
      value: item.projectId,
    }))
  })
  return {
    projectScheduleData,
    projectOptions,
    fetchSchedules,
    duplicateSchedule,
    loading,
    error,
  }
}
