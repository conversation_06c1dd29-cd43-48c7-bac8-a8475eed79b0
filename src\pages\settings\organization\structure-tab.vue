<!-- src/views/CompanySetup.vue -->
<script setup lang="ts">
import { reactive } from 'vue'
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import type { ColumnType, TablePaginationConfig, TableProps } from 'ant-design-vue/es/table'
import { usePagination } from 'vue-request'
import { type StructureItem, type StructureParams, type StructureQueryParams, createStructureApi, deleteStructureApi, getStructureListApi, updateStructureApi } from '~@/api/company/struct'

const { t } = useI18n()
// Data variables

// Structure form
const structureForm = reactive<StructureParams & { structureId: string }>({
  structureId: '',
  structureCode: '',
  structureName: '',
  description: '',
  isOutSource: false,
  structureParentId: undefined,
})

const structureColumns = reactive<ColumnType<StructureItem>[]>([
  {
    title: t('structure.code'),
    dataIndex: 'structureCode',
    key: 'structureCode',
    sorter: (a, b) => a.structureCode.localeCompare(b.structureCode),
    defaultSortOrder: 'ascend',
  },
  {
    title: t('structure.name'),
    dataIndex: 'structureName',
    key: 'structureName',
  },
  {
    title: t('structure.roles'),
    dataIndex: 'roles',
    key: 'roles',
  },
  {
    title: t('structure.isOutSource'),
    dataIndex: 'isOutSource',
    key: 'isOutSource',
  },
  {
    title: t('structure.description'),
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: t('action'),
    width: 150,
    key: 'action',
    align: 'center',
  },
])

const structureModal = reactive({
  loading: false,
  visible: false,
  isEdit: false,
})

// Structure methods
function showStructureModal() {
  structureModal.isEdit = false
  structureModal.visible = true
}

function editStructure(structure: StructureItem) {
  structureModal.isEdit = true
  structureForm.structureId = structure.structureId
  structureForm.structureCode = structure.structureCode
  structureForm.structureName = structure.structureName
  structureForm.description = structure.description
  structureForm.structureParentId = structure.structureParentId ?? undefined
  structureForm.isOutSource = structure.isOutSource
  structureModal.visible = true
}

async function queryData(params: StructureQueryParams) {
  const { data, status, message: mess } = await getStructureListApi(params)
  if (status === 200)
    return data
  else
    message.error(mess)
}

const {
  data,
  loading,
  run,
  refresh,
  current,
  total,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [{
    pageNum: 1,
    pageSize: 8,
  }],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const handleTableChange: TableProps['onChange'] = (
  pagination: TablePaginationConfig,
) => {
  run({
    pageNum: pagination.current || 1,
    pageSize: pagination.pageSize || 8,
  })
}
async function handleStructureSubmit() {
  if (structureModal.isEdit) {
    const { status, message: mess } = await updateStructureApi(structureForm.structureId, structureForm)
    if (status === 200) {
      refresh()
      structureModal.visible = false
      message.success(mess)
    }
    else {
      message.error(mess)
    }
  }
  else {
    const params = {
      ...structureForm,
      structureParentId: structureForm.structureParentId ?? undefined,
    }

    const { status, message: mess } = await createStructureApi(params)
    if (status === 200) {
      refresh()
      structureModal.visible = false
      message.success(mess)
    }
    else {
      message.error(mess)
    }
  }
}

async function deleteStructure(structureId: string) {
  const { status, message: mess } = await deleteStructureApi(structureId)
  if (status === 200) {
    refresh()
    message.success(mess)
  }
  else {
    message.error(mess)
  }
}

const pagination = computed(() => ({
  current: current.value,
  pageSize: pageSize.value,
  total: total.value,
}))
</script>

<template>
  <div class="mb-4">
    <div class="mb-4">
      <a-button type="primary" @click="showStructureModal">
        <PlusOutlined /> {{ t('button.add') }}
      </a-button>
    </div>

    <a-table
      :columns="structureColumns"
      :data-source="data?.items ?? []"
      :row-key="(record: StructureItem) => record.structureId"
      :pagination="pagination"
      :loading="loading"
      children-column="children"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" @click="editStructure(record as StructureItem)">
              <EditOutlined />
            </a-button>
            <a-popconfirm
              :title="t('confirm.delete')"
              @confirm="deleteStructure(record.structureId)"
            >
              <a-button type="link" danger>
                <DeleteOutlined />
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>
    <!-- Structure Modal -->
    <a-modal
      v-model:open="structureModal.visible"
      :title="structureModal.isEdit ? t('structure.edit') : t('structure.add')"
      @ok="handleStructureSubmit"
    >
      <a-form :model="structureForm" layout="vertical">
        <a-form-item :label="t('structure.code')" required>
          <a-input v-model:value="structureForm.structureCode" />
        </a-form-item>
        <a-form-item :label="t('structure.name')" required>
          <a-input v-model:value="structureForm.structureName" />
        </a-form-item>
        <a-form-item :label="t('structure.parent')" required>
          <a-select v-model:value="structureForm.structureParentId" allow-clear>
            <a-select-option v-for="dept in data?.items ?? []" :key="dept.structureId" :value="dept.structureId">
              {{ dept.structureName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item :label="t('structure.source')" required>
          <a-switch v-model:checked="structureForm.isOutSource" />
        </a-form-item>
        <a-form-item :label="t('structure.description')">
          <a-textarea v-model:value="structureForm.description" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
