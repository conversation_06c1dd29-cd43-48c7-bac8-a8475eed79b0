<!-- eslint-disable antfu/top-level-function
<script lang="ts" setup>
import { useRoute } from 'vue-router'
import TodayAttendanceCard from './components/today-attendance-card.vue'
import type { AttendanceItem, AttendanceSearchParams, AttendanceUpdateParams } from '~@/api/attendance'
import { getAttendanceByDateApi, sendRequestByAttendanceItem, updateAttendanceApi } from '~@/api/attendance'
import logger from '~@/utils/logger'

const { t } = useI18n()

const attendanceDataDetail = ref<AttendanceItem[]>([])
const route = useRoute()

const messageNotify = useMessage()

watch(() => route.query.workingDate, getAttendanceDateByDate, { immediate: true })

// const workingDate = route.params.workingDate as string

async function getAttendanceDateByDate(workingDate: any) {
  try {
    const params: AttendanceSearchParams = {
      fromDate: workingDate,
      toDate: workingDate,
    }
    const { data, status, code } = await getAttendanceByDateApi(params)
    if (status === 200) {
      attendanceDataDetail.value = data?.items ?? []
      logger.log('attendance data: ', attendanceDataDetail)
    }
    else {
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

const refreshAttendanceDetail = async (data: AttendanceItem) => {
  const idx = attendanceDataDetail.value.findIndex((item: AttendanceItem) => item.employeeShiftId === data.employeeShiftId)
  if (idx !== -1)
    attendanceDataDetail.value[idx] = data
}

async function requestApproval(employeeShiftId: string) {
  // Handle approval request
  const { data, status, message } = await sendRequestByAttendanceItem(employeeShiftId)
  if (status === 200) {
    messageNotify.success('Request sent successfully')
    refreshAttendanceDetail(data as AttendanceItem)
  }
  else {
    messageNotify.error(message)
  }
}

async function updateAttendanceItem(employeeShiftId: string, params: AttendanceUpdateParams) {
  const { data, status, message } = await updateAttendanceApi(employeeShiftId, params)
  if (status === 200) {
    refreshAttendanceDetail(data as AttendanceItem)
    messageNotify.success('Update successfully')
  }
  else {
    messageNotify.error(message)
  }
}

onMounted(() => {

})
</script>

<template>
  <page-container>
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-4">
      <div
        v-for="(item) in attendanceDataDetail" :key="item.employeeShiftId"
        class="bg-white shadow-lg rounded-lg p-4 min-h-60"
      >
        <TodayAttendanceCard
          :employee-shift="item"
          :is-editable="true"
          :total="attendanceDataDetail.length"
          :index="index"
          @update-employee-shift="updateAttendanceItem"
          @request-approval="requestApproval"
        />
      </div>
    </div>
  </page-container>
</template>

<style scoped>
</style> -->
<template>
  <div />
</template>
