import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { useI18n } from 'vue-i18n'

export function useAttendanceActions() {
  const { t } = useI18n()
  const isLoading = ref(false)

  const handleAction = async (
    action: () => Promise<any>,
    successMessage?: string,
  ) => {
    if (isLoading.value)
      return

    try {
      isLoading.value = true
      const result = await action()

      if (result.status === 200) {
        message.success(successMessage || t('common.success'))
        return result.data
      }

      throw new Error(result.message || t('common.error'))
    }
    catch (error: any) {
      message.error(error.message || t('common.error'))
      return null
    }
    finally {
      isLoading.value = false
    }
  }

  return {
    isLoading,
    handleAction,
  }
}
