<!-- CostsSection.vue -->
<script setup lang="ts">
import type { CostItem } from './types.ts'
import CostItemCard from './cost-item-card.vue'

defineProps<{
  title: string
  items: CostItem[]
}>()
</script>

<template>
  <div class="bg-gray-50 rounded-lg p-5 mb-6">
    <h2 class="text-xl font-bold text-gray-900 mb-4">
      {{ title }}
    </h2>

    <div v-if="items.length === 0" class="text-center py-6 text-gray-500">
      No cost items available
    </div>

    <div v-else>
      <CostItemCard
        v-for="item in items"
        :key="item.categoryId"
        :item="item"
      />

      <div class="flex justify-end mt-4 pt-3 border-t border-gray-200">
        <div class="text-right">
          <p class="text-sm text-gray-600">
            Total
          </p>
          <p class="text-xl font-bold text-gray-800">
            {{ new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(
              items.reduce((sum, item) => sum + item.totalAmount, 0),
            ) }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
