import { onMounted, ref } from 'vue'

export function useWindowScrollSize() {
  const width = ref(0)
  const height = ref(0)

  const updateSize = () => {
    width.value = document.documentElement.scrollHeight;
    height.value = document.documentElement.clientHeight;
  }

  onMounted(() => {
    updateSize()
    window.addEventListener('resize', updateSize)
  })

  onUnmounted(() => window.removeEventListener('resize', updateSize))

  return { width, height }
}
