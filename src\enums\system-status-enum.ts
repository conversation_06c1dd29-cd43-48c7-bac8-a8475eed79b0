export enum RequestStatusEnum {
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  PENDING = 'PENDING',
  CANCELLED = 'CANCELLED',
}

export enum NotificationStatusEnum {
  FAILED = 'FAILED',
  PUBLISHED = 'PUBLISHED',
  PARTIALLY_PUBLISHED = 'PARTIALLY_PUBLISHED',
  PARTIALLY_FAILED = 'PARTIALLY_FAILED',
  PENDING = 'PENDING',
}

export enum ModalType {
  ADD = 'add',
  COPY = 'copy',
  EDIT = 'edit',
  DELETE = 'delete',
  LOG = 'log',
  INVOICE = 'invoice',
  PROJECT = 'project',
}

export enum RecurringType {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  ANNUALLY = 'ANNUALLY',
}

export enum TimeLineColor {
  CREATE = 'green',
  UPDATE = 'blue',
  DELETE = 'red',
}

export type SystemStatusEnumKey = keyof typeof RequestStatusEnum;

export const RequestStatus = {
  [RequestStatusEnum.APPROVED]: {
    color: 'green',
    value: RequestStatusEnum.APPROVED,
  },
  [RequestStatusEnum.REJECTED]: {
    color: 'red',
    value: RequestStatusEnum.REJECTED,
  },
  [RequestStatusEnum.PENDING]: {
    color: 'orange',
    value: RequestStatusEnum.PENDING,
  },
  [RequestStatusEnum.CANCELLED]: {
    color: 'red',
    value: RequestStatusEnum.CANCELLED,
  },
};

export type NotificationStatusEnumKey = keyof typeof NotificationStatusEnum;

export const NotificationStatus = {
  [NotificationStatusEnum.FAILED]: {
    color: 'red',
    value: NotificationStatusEnum.FAILED,
  },
  [NotificationStatusEnum.PUBLISHED]: {
    color: 'green',
    value: NotificationStatusEnum.PUBLISHED,
  },
  [NotificationStatusEnum.PENDING]: {
    color: 'orange',
    value: NotificationStatusEnum.PENDING,
  },
  [NotificationStatusEnum.PARTIALLY_PUBLISHED]: {
    color: 'yellow',
    value: NotificationStatusEnum.PARTIALLY_PUBLISHED,
  },
  [NotificationStatusEnum.PARTIALLY_FAILED]: {
    color: 'orange',
    value: NotificationStatusEnum.PARTIALLY_FAILED,
  },
};

export enum RequestTypeEnum {
  LEAVE = 'LEAVE',
  WORK = 'WORK',
  OVERTIME = 'OVERTIME',
  IN_OUT = 'IN_OUT',
}

export enum SelectTimeEnum {
  DAY = 'DAY',
  HALF_DAY = 'HALF_DAY',
  CUSTOM_DAY = 'CUSTOM_DAY',
}

export enum TimeFrameEnum {
  MORNING = 'MORNING',
  AFTERNOON = 'AFTERNOON',
}

export const RequestType = {
  [RequestTypeEnum.LEAVE]: {
    color: 'blue',
    value: RequestTypeEnum.LEAVE,
    description: 'leave-request-description',
  },
  [RequestTypeEnum.WORK]: {
    color: 'green',
    value: RequestTypeEnum.WORK,
    description: 'work-request-description',
  },
  [RequestTypeEnum.OVERTIME]: {
    color: 'orange',
    value: RequestTypeEnum.OVERTIME,
    description: 'overtime-request-description',
  },
  [RequestTypeEnum.IN_OUT]: {
    color: 'yellow',
    value: RequestTypeEnum.IN_OUT,
    description: 'in-out-request-description',
  },
};
