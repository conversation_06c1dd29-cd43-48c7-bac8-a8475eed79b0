export interface UnitItem {
  unitId: string
  unitCode: string
  unitName: string
  description: string | null
  status: boolean
  byMinute: number
}

export interface PaginatedUnitData {
  items: UnitItem[]
  pageIndex: number
  pageSize: number
  totalRow: number
  pageCount: number
}

export interface RequestTypeItem {
  requestTypeId: string
  requestTypeCode: string
  requestTypeName: string
  description: string | null
  status: boolean
  requiredLevel1Approval: boolean
  requiredLevel2Approval: boolean
}

export interface PaginatedRequestTypeData {
  items: RequestTypeItem[]
  pageIndex: number
  pageSize: number
  totalRow: number
  pageCount: number
}

export interface LeaveTypeItem {
  leaveTypeId: string
  leaveTypeCode: string
  leaveTypeName: string
  description: string | null
  requestTypeId: string | null
  status: boolean
}

export interface PaginatedLeaveTypeData {
  items: LeaveTypeItem[]
  pageIndex: number
  pageSize: number
  totalRow: number
  pageCount: number
}

export interface StatusItem {
  statusId: string
  statusCode: string
  statusName: string
  description: string | null
}

export interface PaginatedStatusData {
  items: StatusItem[]
  pageIndex: number
  pageSize: number
  totalRow: number
  pageCount: number
}

export interface CustomerTypeItem {
  customerTypeCode: string
  customerTypeName: string
  customerTypeDescription?: string
}

export interface PaginatedCustomerTypeData {
  items: CustomerTypeItem[]
  pageIndex: number
  pageSize: number
  totalRow: number
  pageCount: number
}

export interface GetCustomerTypeParams {
  customerTypeCode?: string
  pageNum?: number
  pageSize?: number
}

export async function getUnitList() {
  return useGet<PaginatedUnitData>('v1/common/units')
}

export async function getRequestTypeList() {
  return useGet<PaginatedRequestTypeData>('v1/common/request-types')
}

export async function getLeaveTypeList() {
  return useGet<PaginatedLeaveTypeData>('v1/common/leave-types')
}

export async function getStatusList() {
  return useGet<PaginatedStatusData>('v1/common/status/request')
}

export async function getProjectStatusListApi() {
  return useGet<PaginatedStatusData>('v1/common/status/project')
}

export async function getWorkingStatusApi() {
  return useGet<PaginatedStatusData>('v1/common/status/employee')
}

export async function getCustomerTypes(params?: GetCustomerTypeParams) {
  return useGet<PaginatedCustomerTypeData>('v1/common/customer-types', params)
}
