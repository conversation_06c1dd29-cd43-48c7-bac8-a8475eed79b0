<script setup lang="ts">
import type { SortingState } from '@tanstack/vue-table'
import { FlexRender, createColumnHelper, getCoreRowModel, getSortedRowModel, useVueTable } from '@tanstack/vue-table'
import type { BreakTimeItem } from '~@/api/attendance'
import type { InlineMonthlyAttendanceItem, MonthlyAttendanceItem } from '~@/api/monthly-attendance'

const props = defineProps({
  dataSource: {
    type: Array as PropType<MonthlyAttendanceItem[]>,
    required: true,
  },
})

const { t } = useI18n()
const columnHelper = createColumnHelper<InlineMonthlyAttendanceItem>()

const processedDataSource = ref<InlineMonthlyAttendanceItem[]>([])

const sorting = ref<SortingState>([
  {
    id: 'workingDate',
    desc: false,
  },
])

function calculateRowSpans<T>(rows: T[], key: keyof T): number[] {
  const spans: number[] = Array(rows.length).fill(0)

  let prevValue: any = null
  let startIndex = 0

  for (let i = 0; i < rows.length; i++) {
    const currentValue = rows[i][key]

    if (currentValue !== prevValue) {
      if (i > startIndex)
        spans[startIndex] = i - startIndex

      startIndex = i
      prevValue = currentValue
    }

    if (i === rows.length - 1)
      spans[startIndex] = i - startIndex + 1
  }

  return spans
}

const columns = computed(() => [
  columnHelper.accessor('workingDate', {
    header: t('workingDate'),
    enableSorting: true,
  }),
  {
    header: t('projectName'),
    accessorKey: 'projectNames',
    cell: (props: any) => {
      const projectNames = props.row.original.projectNames ?? []
      return h('div', {
        class: 'flex flex-col gap-x-1',
      }, [
        ...projectNames.map((projectName: string) => {
          return h('span', {
            class: 'text-sm',
          }, projectName)
        }),
      ])
    },
  },
  columnHelper.accessor('checkInTimes', {
    header: t('checkInTime'),
    cell: (props) => {
      const checkInTimes = props.row.original.checkInTimes ?? []
      return h('div', {
        class: 'flex flex-col gap-x-1',
      }, [
        ...checkInTimes.map((checkInTime) => {
          return h('span', {
            class: 'text-sm',
          }, checkInTime)
        }),
      ])
    },
  }),
  columnHelper.accessor('checkOutTimes', {
    header: t('checkOutTime'),
    cell: (props: any) => {
      const checkOutTimes = props.row.original.checkOutTimes ?? []
      return h('div', {
        class: 'flex flex-col gap-x-1',
      }, [
        ...checkOutTimes.map((checkOutTime: string) => {
          return h('span', {
            class: 'text-sm',
          }, checkOutTime)
        }),
      ])
    },
  }),
  {
    header: t('breakTime'),
    minSize: 100,
    accessorKey: 'breakTimes',
    cell: (props: any) => {
      const breakTimes = props.row.original.breakTimes ?? []
      return h('div', {
        class: 'flex flex-col gap-x-1',
      }, [
        ...breakTimes.map((breakTime: string) => {
          return h('span', {
            class: 'text-sm',
          }, breakTime)
        }),
      ])
    },
  },
  {
    header: t('totalWorkTime'),
    accessorKey: 'totalWorkTime',
  },
  {
    header: t('totalOverTime'),
    accessorKey: 'totalOverTime',
  },
  {
    header: t('totalBreakTime'),
    accessorKey: 'totalBreakTime',
  },
  {
    header: t('approvalStatus'),
    accessorKey: 'isApproved',
    cell: (props: any) => {
      const isApproved = props.row.original.isApproved
      return h('span', {
        class: isApproved ? 'text-green-500' : 'text-red-500',
      }, isApproved ? t('status.approved') : t('status.notApproved'))
    },
  },
])

const table = useVueTable({
  get data() {
    return processedDataSource.value
  },
  get columns() {
    return columns.value
  },
  getCoreRowModel: getCoreRowModel(),
  getSortedRowModel: getSortedRowModel(),
  state: {
    get sorting() {
      return sorting.value
    },
  },
  onSortingChange: (updaterOrValue: any) => {
    sorting.value
      = typeof updaterOrValue === 'function'
        ? updaterOrValue(sorting.value)
        : updaterOrValue
  },
  debugTable: true,
})

const rowSpans = computed(() => calculateRowSpans(table.getRowModel().rows.map(row => row.original), 'workingDate'))

function preprocessData() {
  if (!props.dataSource.length)
    return []

  // Nhóm dữ liệu theo workingDate
  const groupedData = props.dataSource.reduce((acc: Record<string, MonthlyAttendanceItem[]>, item) => {
    const date = item.workingDate
    if (!acc[date])
      acc[date] = []

    acc[date].push(item)
    return acc
  }, {})

  // Tạo mảng mới với dữ liệu đã gộp
  const processedData: InlineMonthlyAttendanceItem[] = []

  Object.keys(groupedData).forEach((date) => {
    const employeeShifts = groupedData[date]

    if (employeeShifts.length === 1) {
      // Nếu chỉ có 1 item, giữ nguyên
      const firstEmployeeShiftItem: InlineMonthlyAttendanceItem = {
        workingDate: employeeShifts[0].workingDate,
        originalEmployeeShiftIds: [employeeShifts[0].originalEmployeeShiftId],
        employeeAttendanceResultIds: [employeeShifts[0].employeeAttendanceResultId],
        projectCodes: [employeeShifts[0].projectCode],
        projectNames: [employeeShifts[0].projectName],
        checkInTimes: [employeeShifts[0].checkInTime?.slice(0, 5)],
        checkOutTimes: [employeeShifts[0].checkOutTime?.slice(0, 5)],
        breakTimes: [employeeShifts[0].breakTimes?.map(bt => `${bt.breakInTime?.slice(0, 5)}~${bt.breakOutTime?.slice(0, 5)}`).join('\n')],
        totalWorkTime: employeeShifts[0].totalWorkTime,
        totalBreakTime: employeeShifts[0].totalBreakTime,
        totalOverTime: employeeShifts[0].totalOverTime,
        totalMovingTime: employeeShifts[0].totalMovingTime,
        descriptions: [employeeShifts[0].description],
        isApproved: employeeShifts[0].isApproved,
      }
      processedData.push(firstEmployeeShiftItem)
    }
    else {
      // Nếu có nhiều item, gộp lại
      const firstEmployeeShiftItem = employeeShifts[0]

      // Gộp tất cả breakTimes từ các items
      const allBreakTimes: BreakTimeItem[] = []
      employeeShifts.forEach((item) => {
        if (item.breakTimes && item.breakTimes.length > 0) {
          // Thêm prefix để phân biệt breakTime từ project nào
          item.breakTimes.forEach((breakTime) => {
            if (breakTime.breakInTime !== '' && breakTime.breakOutTime !== '') {
              allBreakTimes.push({
                ...breakTime,
                // Có thể thêm metadata để biết break này từ project nào
              } as BreakTimeItem & { projectSource?: string })
            }
          })
        }
      })

      const mergedEmployeeShift: InlineMonthlyAttendanceItem = {
        originalEmployeeShiftIds: employeeShifts.map(item => item.originalEmployeeShiftId),
        employeeAttendanceResultIds: employeeShifts.map(item => item.employeeAttendanceResultId),
        projectCodes: employeeShifts.map(item => item.projectCode),
        projectNames: employeeShifts.map(item => item.projectName),
        checkInTimes: employeeShifts.map(item => item.checkInTime?.slice(0, 5)),
        checkOutTimes: employeeShifts.map(item => item.checkOutTime?.slice(0, 5)),
        descriptions: employeeShifts.map(item => item.description),
        approverNames: employeeShifts.map(item => item.approverName),
        approvedDateTimes: employeeShifts.map(item => item.approvedDateTime),
        // Tính tổng các giá trị số
        totalWorkTime: employeeShifts.reduce((sum, item) => sum + (item.totalWorkTime || 0), 0),
        totalBreakTime: employeeShifts.reduce((sum, item) => sum + (item.totalBreakTime || 0), 0),
        totalOverTime: employeeShifts.reduce((sum, item) => sum + (item.totalOverTime || 0), 0),
        totalMovingTime: employeeShifts.reduce((sum, item) => sum + (item.totalMovingTime || 0), 0),
        // Convert break times array thành string
        breakTimes: allBreakTimes.map(bt => `${bt.breakInTime?.slice(0, 5)}~${bt.breakOutTime?.slice(0, 5)}`),
        isApproved: employeeShifts.every(item => item.isApproved),
        workingDate: firstEmployeeShiftItem.workingDate,
      }

      processedData.push(mergedEmployeeShift)
    }
  })

  processedDataSource.value = processedData
  return processedData
}

onMounted(() => {
  preprocessData()
})

// Watch for changes in dataSource to reprocess data
watch(() => props.dataSource, () => {
  preprocessData()
}, { deep: true })
</script>

<template>
  <table class="min-w-full divide-y divide-gray-200 text-left">
    <thead class="bg-gradient-to-r from-gray-50 via-gray-100 to-gray-50">
      <tr>
        <th
          v-for="column in table.getHeaderGroups()[0].headers"
          :key="column.id"
          class="group px-6 py-4 text-xs font-bold text-gray-700 uppercase tracking-wider border-b border-gray-200 hover:bg-gray-100 transition-all duration-200 cursor-pointer text-center"
        >
          <div class="flex items-center gap-2">
            <FlexRender
              :render="column.column.columnDef.header"
              :props="column.getContext()"
            />
            <!-- Sort indicator placeholder -->
            <div
              v-if="column.id === 'workingDate'"
              @click="table.getColumn('workingDate')?.getToggleSortingHandler()?.($event)"
            >
              <svg class="w-4 h-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
              </svg>
            </div>
          </div>
        </th>
      </tr>
    </thead>

    <tbody class="bg-white divide-y divide-gray-100">
      <tr
        v-for="(row, rowIndex) in table.getRowModel().rows"
        :key="row.id"
        class="hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent transition-all duration-200 group"
      >
        <template v-for="cell in row.getVisibleCells()" :key="cell.id">
          <td
            :rowspan="rowSpans[rowIndex]"
            class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-50 group-hover:border-blue-100 transition-colors"
          >
            <FlexRender
              :render="cell.column.columnDef.cell"
              :props="cell.getContext()"
            />
          </td>
        </template>
      </tr>
    </tbody>
  </table>
</template>
