/* eslint-disable style/member-delimiter-style */
export interface WorkplaceItem {
  workplaceId: string;
  workplaceCode?: string;
  workplaceName?: string;
  orgId: string;
  description?: string;
  address?: string;
  status?: boolean;
  isWorkSite?: boolean;
  createTime?: string;
  updateTime?: string;
}

export interface PaginatedWorkplaceResponse {
  items?: WorkplaceItem[];
  pageIndex?: number;
  pageSize?: number;
  totalRow?: number;
  pageCount?: number;
}

export type WorkplaceItemParams = Partial<WorkplaceItem>

export async function getWorkplaceListApi(params?: WorkplaceItemParams) {
  return useGet<PaginatedWorkplaceResponse>('v1/workplace/offices', params)
}

export async function createWorkplaceApi(data?: WorkplaceItemParams) {
  return usePost<WorkplaceItem>('v1/workplace/create', data)
}

export async function updateWorkplaceApi(data?: WorkplaceItemParams) {
  return usePut<WorkplaceItem>('v1/workplace/update', data)
}
