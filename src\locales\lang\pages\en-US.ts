export default {
  //  result page
  'result.success.title': 'Submission Success',
  'result.success.description':
    'The submission results page is used to feed back the results of a series of operational tasks. If it is a simple operation, use the Message global prompt feedback. This text area can show a simple supplementary explanation. If there is a similar requirement for displaying "documents", the following gray area can present more complicated content.',
  'result.success.operate-title': 'Project Name',
  'result.success.operate-id': 'Project ID',
  'result.success.principal': 'Principal',
  'result.success.operate-time': 'Effective time',
  'result.success.step1-title': 'Create project',
  'result.success.step1-operator': '<PERSON>',
  'result.success.step2-title': 'Departmental preliminary review',
  'result.success.step2-operator': '<PERSON><PERSON>',
  'result.success.step2-extra': 'Urge',
  'result.success.step3-title': 'Financial review',
  'result.success.step4-title': 'Finish',
  'result.success.btn-return': 'Back List',
  'result.success.btn-project': 'View Project',
  'result.success.btn-print': 'Print',
  'result.fail.error.title': 'Submission Failed',
  'result.fail.error.description':
    'Please check and modify the following information before resubmitting.',
  'result.fail.error.hint-title': 'The content you submitted has the following error:',
  'result.fail.error.hint-text1': 'Your account has been frozen',
  'result.fail.error.hint-btn1': 'Thaw immediately',
  'result.fail.error.hint-text2': 'Your account is not yet eligible to apply',
  'result.fail.error.hint-btn2': 'Upgrade immediately',
  'result.fail.error.btn-text': 'Return to modify',

  'profile.basic.orderDetailTitle': 'Order Details',
  'profile.basic.orderNumber': 'Order Number',
  'profile.basic.orderStatus': 'Order Status',
  'profile.basic.orderStatusValue': 'Finished',
  'profile.basic.transactionNumber': 'Transaction Number',
  'profile.basic.subOrderNumber': 'Suborder Number',
  'profile.basic.customerInfoTitle': 'Customer Information',
  'profile.basic.customerName': 'Customer Name',
  'profile.basic.contactNumber': 'Contact Number',
  'profile.basic.deliveryMethod': 'Delivery Method',
  'profile.basic.deliveryAddress': 'Delivery Address',
  'profile.basic.remarks': 'Remarks',
  'profile.basic.productInfoTitle': 'Product Information',
  'profile.basic.productName': 'Product Name',
  'profile.basic.unitPrice': 'Unit Price',
  'profile.basic.quantity': 'Quantity',
  'profile.basic.subtotal': 'Subtotal',
  'profile.basic.paymentInfoTitle': 'Payment Information',
  'profile.basic.paymentMethod': 'Payment Method',
  'profile.basic.paymentTime': 'Payment Time',
  'profile.basic.paymentAmount': 'Payment Amount',
  'profile.basic.paymentStatus': 'Payment Status',

  'profile.advanced.creater': 'Creator',
  'profile.advanced.create-time': 'Creation time',
  'profile.advanced.create-effective-date': 'effective date',
  'profile.advanced.create-product': 'product',
  'profile.advanced.create-id': 'Product documents',
  'profile.advanced.create-info': 'memo',
  'profile.advanced.create-status': 'Status',
  'profile.advanced.create-status-finshed': 'Finished',
  'profile.advanced.create-price': 'Price',
  'profile.advanced.create-do1': 'Opt1',
  'profile.advanced.create-do2': 'Opt1',
  'profile.advanced.create-do3': 'Opt1',
  'profile.advanced.tab1': 'detail',
  'profile.advanced.tab2': 'rules',
  'profile.advanced.card': 'Customer Card',
  'profile.advanced.id': 'ID card',
  'profile.advanced.group': 'Data Group',
  'profile.advanced.group-data': 'Data ID',
  'profile.advanced.group-data-update': 'Update Time',
  'profile.advanced.call-log': 'Contact records',
  'profile.advanced.call-spent': 'Contact duration',
  'profile.advanced.call-date': 'Contact Date',
  'profile.advanced.log': 'log1',
  'profile.advanced.log1': 'log1',
  'profile.advanced.log2': 'log3',
  'profile.advanced.log-type': 'type',
  'profile.advanced.log-owner': 'operator',
  'profile.advanced.log-result': 'result',
  'profile.advanced.log-time': 'start time',
  'profile.advanced.log-info': 'memo',
  'profile.advanced.remove': 'remove',
  'profile.advanced.step-title': 'progress',
  'profile.advanced.step-notice': 'Remind',

  'form.basic-form.basic.title': 'Basic form',
  'form.basic-form.basic.description':
    'Form pages are used to collect or verify information to users, and basic forms are common in scenarios where there are fewer data items.',
  'form.basic-form.title.label': 'Title',
  'form.basic-form.title.placeholder': 'Give the target a name',
  'form.basic-form.title.required': 'Please enter a title',
  'form.basic-form.date.label': 'Start and end date',
  'form.basic-form.placeholder.start': 'Start date',
  'form.basic-form.placeholder.end': 'End date',
  'form.basic-form.date.required': 'Please select the start and end date',
  'form.basic-form.goal.label': 'Goal description',
  'form.basic-form.goal.placeholder': 'Please enter your work goals',
  'form.basic-form.goal.required': 'Please enter a description of the goal',
  'form.basic-form.standard.label': 'Metrics',
  'form.basic-form.standard.placeholder': 'Please enter a metric',
  'form.basic-form.standard.required': 'Please enter a metric',
  'form.basic-form.client.label': 'Client',
  'form.basic-form.label.tooltip': 'Target service object',
  'form.basic-form.client.placeholder':
    'Please describe your customer service, internal customers directly {\'@\'}Name / job number',
  'form.basic-form.client.required': 'Please describe the customers you serve',
  'form.basic-form.invites.label': 'Inviting critics',
  'form.basic-form.invites.placeholder':
    'Please direct {\'@\'}Name / job number, you can invite up to 5 people',
  'form.basic-form.weight.label': 'Weight',
  'form.basic-form.weight.placeholder': 'Please enter weight',
  'form.basic-form.public.label': 'Target disclosure',
  'form.basic-form.label.help': 'Customers and invitees are shared by default',
  'form.basic-form.radio.public': 'Public',
  'form.basic-form.radio.partially-public': 'Partially public',
  'form.basic-form.radio.private': 'Private',
  'form.basic-form.publicUsers.placeholder': 'Open to',
  'form.basic-form.option.A': 'Colleague A',
  'form.basic-form.option.B': 'Colleague B',
  'form.basic-form.option.C': 'Colleague C',
  'form.basic-form.email.required': 'Please enter your email!',
  'form.basic-form.email.wrong-format': 'The email address is in the wrong format!',
  'form.basic-form.userName.required': 'Please enter your userName!',
  'form.basic-form.password.required': 'Please enter your password!',
  'form.basic-form.password.twice': 'The passwords entered twice do not match!',
  'form.basic-form.strength.msg':
    'Please enter at least 6 characters and don\'t use passwords that are easy to guess.',
  'form.basic-form.strength.strong': 'Strength: strong',
  'form.basic-form.strength.medium': 'Strength: medium',
  'form.basic-form.strength.short': 'Strength: too short',
  'form.basic-form.confirm-password.required': 'Please confirm your password!',
  'form.basic-form.phone-number.required': 'Please enter your phone number!',
  'form.basic-form.phone-number.wrong-format': 'Malformed phone number!',
  'form.basic-form.verification-code.required': 'Please enter the verification code!',
  'form.basic-form.form.get-captcha': 'Get Captcha',
  'form.basic-form.captcha.second': 'sec',
  'form.basic-form.form.optional': ' (optional) ',
  'form.basic-form.form.submit': 'Submit',
  'form.basic-form.form.save': 'Save',
  'form.basic-form.email.placeholder': 'Email',
  'form.basic-form.password.placeholder': 'Password',
  'form.basic-form.confirm-password.placeholder': 'Confirm password',
  'form.basic-form.phone-number.placeholder': 'Phone number',
  'form.basic-form.verification-code.placeholder': 'Verification code',
  // account page
  'account.center.tags': 'tags',
  'account.center.team': 'team',
  'account.center.article': 'article',
  'account.center.application': 'application',
  'account.center.project': 'project',
  'account.center.posted': 'posted on',
  'account.center.activity-user': 'activity user',
  'account.center.new-user': 'new user',
  'account.center.updated': 'Updated a few minutes ago',

  'account.settings.basic-setting': 'Basic Setting',
  'account.settings.security-setting': 'Security Setting',
  'account.settings.account-setting': 'Account Binding',
  'account.settings.message-setting': 'New Message',
  'account.settings.form-email': 'email',
  'account.settings.form-name': 'nickname',
  'account.settings.form-region': 'nation/region',
  'account.settings.form-address': 'Street address',
  'account.settings.form-phoneNumber': 'phone number',
  'account.settings.form-desc': 'Personal profile',
  'account.settings.form-region-China': 'China',
  'account.settings.form-input-plac': 'please enter',
  'account.settings.form-select-plac': 'please select',
  'account.settings.form-rule-name': 'please enter nickname',
  'account.settings.form-rule-phoneNumber': 'Please enter phone number',
  'account.settings.form-rule-address': 'Please enter address',
  'account.settings.form-rule-region': 'please select',
  'account.settings.form-rule-email': 'please enter email',
  'account.settings.form-rule-desc': 'please enter profile',
  'account.settings.basic-avatar': 'avatar',
  'account.settings.basic-avatar.upload': 'Upload picture',
  'account.settings.form-submit': 'submit',

  'account.settings.security.account-password': 'Account password',
  'account.settings.security.account-password-desc':
    'Current password strength: Strong',
  'account.settings.security.phone': 'Security phone',
  'account.settings.security.phone-desc': 'Attached phone: 131****8888',
  'account.settings.security.email': 'Spare email',
  'account.settings.security.email-desc': 'Bound email: ant**.com',
  'account.settings.security.MFA': 'MFA',
  'account.settings.security.MFA-desc': 'No MFA device is bound',
  'account.settings.modify': 'edit',
  'account.settings.security-problem': 'security problem',
  'account.settings.security-problem-desc': 'Have set',
  'account.settings.account.taobao': 'Bind Taobao',
  'account.settings.account.alipay': 'Bind Alipay',
  'account.settings.account.dingding': 'Bind Ding',
  'account.settings.account.not.bind': 'Currently not bound',
  'account.settings.account.bind': 'bind',
  'account.settings.message.title1': 'Other message',
  'account.settings.message.title2': 'System message',
  'account.settings.message.title3': 'To-do task',
  'account.settings.message.desc1':
    'Messages from other users will be notified in the form of an internal message',
  'account.settings.message.desc2':
    'Messages from other System will be notified in the form of an internal message',
  'account.settings.message.desc3':
    'Messages from other task will be notified in the form of an internal message',

  // Dashboard page
  'dashboard.workplace.tableTitle': 'Time Period',
  'dashboard.workplace.timeKeepingData.date': 'Date',
  'dashboard.workplace.timeKeepingData.approval': 'Approval',
  'dashboard.workplace.timeKeepingData.cancel': 'Canceled',
  'dashboard.workplace.timeKeepingData.pending': 'Pending',
  'dashboard.workplace.timeKeepingData.User': 'User Name',
  'dashboard.workplace.timeKeepingData.workdayType': 'WorkdayType',
  'dashboard.workplace.timeKeepingData.schedule': 'Schedule',
  'dashboard.workplace.timeKeepingData.checkIn': 'Check-In',
  'dashboard.workplace.timeKeepingData.checkOut': 'Check-Out',
  'dashboard.workplace.timeKeepingData.fixHours': 'Fix Hours',
  'dashboard.workplace.timeKeepingData.overtime': 'Over time',
  'dashboard.workplace.timeKeepingData.breaktime': 'Break Time',
  'dashboard.workplace.timeKeepingData.comment': 'Comment',
  'dashboard.workplace.timeKeepingData.detail': 'Detail',
  'locale': 'en',
  'dashboard.workplace.button.checkIn': 'Check-In',
  'dashboard.workplace.button.checkOut': 'Check-Out',
  'dashboard.workplace.button.startBreak': 'Start break',
  'dashboard.workplace.button.endBreak': 'End break',

  'dashboard.workplace.timeKeepingData.title': 'Time keeping data',
  'dashboard.workplace.timeKeepingData.checkinGPS': 'Check-In-GPS',
  'dashboard.workplace.timeKeepingData.checkoutGPS': 'Check-Out-GPS',
  'dashboard.workplace.timeKeepingData.breakIn': 'Break In',
  'dashboard.workplace.timeKeepingData.breakOut': 'Break Out',
  'dashboard.workplace.timeKeepingData.weekDay': 'Week day',
  'dashboard.workplace.timeKeepingData.weekend': 'Weekend',
  'dashboard.workplace.locationOutside': 'Location outside',
  'dashboard.workplace.locationInside': 'Location inside',
  'dashboard.workplace.project': 'Project',
  'dashboard.workplace.project-menu': 'Project menu',
  'dashboard.workplace.attendanceDetail': 'Attendance Detail',
  'dashboard.workplace.location': 'Location',
  'dashboard.workplace.address': 'Address',
  'dashboard.workplace.attendance.textArea': 'Write note here',
  'dashboard.workplace.notification': 'Notification',
  'dashboard.workplace.notification.recordedWorkingHours': 'recorded working hours',
  'dashboard.workplace.notification.just': 'just',
  'dashboard.workplace.notification.user': 'User',
  'totalWorkTime': 'Total work time',
  'totalOvertime': 'Total overtime',
  'dashboard.totalTime': 'Working Time',
  'dashboard.overtime': 'Overtime',
  'dashboard.attendance.title': 'Attendance Detail For Today',

  'select.current-project': 'Current project',
  'select.other-project': 'Other project',
  'select.business': 'Business',
  'select.remote': 'Remote',
  'workplace': 'workplace',
  'enterWorkplace': 'Enter workplace',

  'total-worktime-of-day': 'Total worktime of day',
  'total-overtime-of-day': 'Total overtime of day',
  'total-worktime-of-week': 'Total worktime of week',
  'total-overtime-of-week': 'Total overtime of week',
  'worktime': 'Working time',

  'project-management': 'Project management',
  'attendance-detail': 'Attendance detail',
  'prime-contractor': 'Prime contractor',
  'person-in-charge': 'Person in charge',
  'expected-date': 'Expected date',
  'actual-date': 'Actual date',
  'main-construction': 'Main construction',
  'sub-construction': 'Sub construction',
  'overall': 'Overall',
  'project.sum': 'SUM',
  'worksite-attendance': 'Worksite attendance',
  'cost': 'Cost',
  'presigned': 'Presigned',
  'check-time': 'Check time',
  'break-time': 'Break time',
  'button.add-break': 'Add break',
  'note': 'Note',
  'total-hours': 'Total hours',
  'button.request-approval': 'Request approval',
  'button.check-in': 'Check-in',
  'notification.checkInTimeRequired': 'Please check in first',
  'attendance': 'Attendance',
  'invalidTime': 'Invalid time',
  'dashboard.clock': 'Clock',
  'dashboard.todayAttendance': 'Today Attendance',
  'dashboard.attendanceTable': 'Attendance Table',
  'dashboard.tour': 'How to use this page',
  'tour.dashboard.clock.description': 'This section displays the current time and your attendance status. Use it to check in, take breaks, or check out during your workday.',
  'tour.dashboard.todayAttendance.description': 'This section shows your attendance records for today, including check-in, break, and check-out times. You can also update your attendance or request approval here.',
  'tour.dashboard.totalTime.description': 'This section summarizes your total work time and overtime for today, helping you keep track of your daily working hours',
  'tour.dashboard.attendanceTable.description': 'This table displays your attendance records for the selected week, allowing you to review and manage your daily attendance details',
  'tour': 'How to use this page',

  'requireCheckInTimeBeforeCurrentTime': 'Check-in time must be before the current time',
  'requireCheckOutTimeBeforeCurrentTime': 'Check-out time must be before the current time',
  'requireCheckOutTimeAfterCheckInTime': 'Check-out time must be after check-in time',

  'productionCost': 'Production cost',
  'averageCost': 'Average cost',

  // User management
  'ranking': 'Ranking',
  'rankingName': 'Ranking name',
  'form.rankingDate': 'Rank Valid',
  'status.working': 'Working',
  'table.struct-rank': 'Structure & Ranking',
  'set-up-ranking': 'Set up ranking',
  'add-user': 'Add user',
  'form.loginId': 'Login ID',
  'edit-user': 'Edit user',
  'no-data': 'No data',
  'employeeType': 'Employee type',
  'office': 'Office',
  'construction': 'Construction',
  'form.address.placeholder': 'Please enter address',
  'form.orgStructureName.placeholder': 'Please enter struct name',
  'form.loginId.placeholder': 'Please enter login ID',
  'form.name.placeholder': 'Please enter name',
  'form.min-value-and-max-value-cannot-be-empty': 'The maximum and minimum values cannot both be blank.',
  'form.email.placeholder': 'Please enter email',
  'form.phone.placeholder': 'Please enter phone',
  'form.gender.placeholder': 'Please enter gender',
  'form.birthday.placeholder': 'Please enter birthday',
  'gender.male': 'Male',
  'gender.female': 'Female',
  'title.edit-employee': 'Edit employee',
  'steps.userInfoSetting': 'User info setting',
  'steps.employeeInfoSetting': 'Employee info setting',
  'steps.complete': 'Complete',
  'button.next': 'Next',
  'button.back': 'Back',
  'form.employeeMails': 'Employee mails',
  'form.employeePhones': 'Employee phones',
  'form.employeeAddress': 'Employee address',
  'form.employeeType': 'Employee type',
  'form.employeeRole': 'Employee role',
  'form.rankingStartDate': 'Ranking start date',
  'form.rankingEndDate': 'Ranking end date',
  'form.rankingName': 'Ranking name',
  'form.workingStatus': 'Working status',
  'form.employeeCode': 'Employee code',
  'form.orgStructureName': 'Org structure name',
  'form.orgPositionName': 'Org position name',
  'form.isEmployee': 'Employee',

  'form.salaryInHour': 'Salary in hour',
  'form.salaryInDay': 'Salary in day',
  'form.standardWorkingHours': 'Standard working hours',
  'form.salaryInMonth': 'Salary in month',
  'form.workingFromDate': 'Working from date',
  'form.workingToDate': 'Working to date',
  'userInfo': 'User info',
  'employeeInfo': 'Employee info',
  'birthday': 'Birthday',
  'gender': 'Gender',
  'steps.employeeInfo': 'Employee info',
  'steps.confirm': 'Confirm',
  'title.invite-employee': 'Invite employee',
  'success.invite-employee': 'Invite employee successfully',
  'success.invite-employee-sub-title': 'Please check your email for the invitation link',
  'button.close': 'Close',
  'button.continue': 'Continue',
  'type.employee': 'Employee',
  'type.outsource': 'Outsource',
  'form.positionName': 'Position name',
  'alert.confirmResetPass': 'Are you sure you want to reset the password?',
  'alert.confirmDelete': 'Are you sure you want to delete the {msg}?',
  'employee': 'Employee',
  'positionName': 'Position name',
  'workingStatus': 'Working status',

  // Manager Page
  'manager.team.worksite.attendanceRequest': 'Attendance Request',
  'manager.team.worksite.username': 'Username',
  'manager.team.worksite.workStatus': 'Working Status',

  // Company Page
  'company.project.projectCode': 'Project code',
  'company.project.projectName': 'Project name',
  'company.project.orgStructureKey': 'OrgStructure Key',
  'company.project.address': 'Address',
  'company.project.description': 'Description',

  // Settings Page
  'settings.title': 'Organization',
  'company.title': 'Company',
  'structure.title': 'Structure',
  'position.title': 'Position',

  // Position
  'position.code': 'Position code',
  'position.name': 'Position name',
  'position.description': 'Description',
  'position.edit': 'Edit position',
  'position.add': 'Add position',
  'position.form.message': 'Please enter the position code and name',
  'position.status': 'Status',

  // Organization
  'company.name': 'Company name',
  'company.code': 'Company code',
  'company.address': 'Address',
  'company.postalCode': 'Postal code',
  'company.legalTaxNumber': 'Legal tax number',
  'company.description': 'Description',

  // Structure management
  'structure.code': 'Code',
  'structure.name': 'Name',
  'structure.parent': 'Parent',
  'structure.source': 'Source',
  'structure.description': 'Description',
  'structure.roles': 'Roles',
  'structure.children': 'Children',
  'structure.edit': 'Edit structure',
  'structure.add': 'Add structure',
  'structure.delete': 'Delete structure',

  // Outsource
  'outsource.name': 'Name',
  'outsource.code': 'Code',
  'outsource.description': 'Description',
  'outsource.edit': 'Edit outsource',
  'outsource.add': 'Add outsource',
  'outsource.form.message': 'Please enter the outsource name and code',
  'outsource.status': 'Status',
  'outsource.corporate-number': 'Corporate number',
  'outsource.contact-person': 'Contact person',
  'outsource.contact-person-email': 'Contact person email',
  'outsource.contact-person-phone-number': 'Contact person phone number',
  'outsource.expertise': 'Expertise',
  'outsource.address': 'Address',
  'outsource.email': 'Email',
  'outsource.phoneNumber': 'Phone number',

  // Common
  'table.loginId': 'Account',
  'table.checkIn': 'CheckIn',
  'table.checkInOut': 'CheckIn/Out',
  'table.workdayType': 'Workday type',
  'table.checkinGPS': 'CheckIn(GPS)',
  'table.checkOut': 'CheckOut',
  'table.breakIn': 'BreakIn',
  'table.breakOut': 'BreakOut',
  'table.checkoutGPS': 'Checkout(GPS)',
  'table.fixHours': 'Fix Hours',
  'table.overtime': 'Overtime',
  'table.breakInOut': 'Break time',
  'table.breakTime': 'Break time',
  'table.comment': 'Comment',
  'table.name': 'Name',
  'table.userID': 'ID',
  'table.email': 'Email',
  'table.birthday': 'Birthday',
  'table.phone': 'Phone',
  'table.status': 'Status',
  'table.gender': 'Gender',
  'table.userRule': 'User Role',
  'table.action': 'Action',
  'table.totalHours': 'Total Hours',
  'table.note': 'Notes',
  'table.title.role-management': 'Role management',
  'table.title.role-name': 'Role name',
  'table.title.description': 'Description',
  'table.title.list-of-user': 'List of users',
  'table.title.action': 'Action',
  'table.title.screen': 'Screen',
  'table.userType': 'Type',
  'table.daytimeApproval': 'Daytime Approval',
  'table.person': 'Person',
  'table.contact': 'Contact',
  'table.struc-rank': 'Structure & Ranking',

  'form.code': 'Code',
  'form.name': 'Name',
  'form.userID': 'UserID',
  'form.email': 'Email',
  'form.birthday': 'Birthday',
  'form.phone': 'Phone',
  'form.status': 'Status',
  'form.gender': 'Gender',
  'form.userRule': 'UserRole',
  'form.parent-category': 'Parent Category',
  'form.vendor': 'Vendor',
  'form.unit': 'Unit',
  'form.category': 'Category',
  'form.sub-name': 'Sub Name',
  'form.approval-status': 'Approval Status',
  'common.am': 'AM',
  'common.pm': 'PM',

  // Project page
  'projectInfo': 'Project Info',
  'title.edit-project': 'Edit Project',
  'title.add-project': 'Add Project',
  'initialBudget': 'Initial Budget',
  'actualBudget': 'Actual Budget',
  'managersInfo': 'Managers Info',
  'action': 'Action',
  'projectCode': 'Project Code',
  'projectType': 'Project Type',
  'expectedPlan': 'Expected Plan',
  'expectedDate': 'Expected Date',
  'projectStructure': 'Structure',
  'workplaceName': 'Workplace',
  'actualPlan': 'Actual Plan',
  'actualDate': 'Actual Date',
  'mainManager': 'Main Manager',
  'subManager': 'Sub Manager',
  'description': 'Description',
  'status.planed': 'Not started',
  'status.started': 'In progress',
  'status.ended': 'Completed',
  'costRange': 'Cost Range',
  'budgetRange': 'Budget Range',
  'isOffice': 'Office',
  'isHeadOffice': 'Head Office',
  'isWorksite': 'Worksite',
  'form.managerUserName': 'Manager User Name',
  'form.subManagerUserName': 'Sub Manager User Name',
  'form.projectName': 'Project Name',
  'form.orgStructureKey': 'Organization Structure Key',
  'form.StructureName': 'Structure Name',
  'form.address': 'Address',
  'form.description': 'Description',
  'form.jurisdiction': 'Jurisdiction',
  'form.mainContractor': 'Main Contractor',
  'form.companyRole': 'Company Role',
  'form.mainManager': 'Main Manager',
  'form.endUserID': 'UserID',
  'form.subManagerList': 'Sub Manager List',
  'form.startDatePlan': 'Start Date Plan',
  'form.endDatePlan': 'End Date Plan',
  'form.startDateReal': 'Start Date Real',
  'form.endDateReal': 'End Date Real',
  'form.note': 'Note',
  'form.effective': 'Effective',
  'form.addressType': 'Address Type',

  'form.shiftWorkCode': 'Shift Work Code',
  'form.shiftWorkName': 'Shift Work Name',
  'form.checkInTime': 'Check-in Time',
  'form.checkOutTime': 'Check-out Time',
  'button.addBreak': 'Add Break',
  'form.breakStart': 'Break Start Time',
  'form.breakEnd': 'Break End Time',
  'form.isNextDay': 'Is Next Day',
  'form.orderNumber': 'Order Number',
  'form.orgPositionCode': 'Position Code',
  'form.orgPositionParent': 'Position Parent',

  'form.officeCode': 'Office Code',
  'form.officeName': 'Office Name',
  'form.totalRow': 'Total Row',

  'form.empContractCode': 'Contract Code',
  'form.empContractName': 'Contract Name',
  'form.fromDate': 'From Date',
  'form.toDate': 'To Date',
  'form.orgPositionKey': 'OrgPosition Key',
  'form.officeKey': 'Office Key',
  'form.isWorksiteManager': 'Worksite manager',
  'form.baseLeave': 'Base Leave',
  'form.baseLeaveExpire': 'Base Leave Expire',
  'alert.confirm': 'Are you sure?',
  'validate.mainManager': 'An employee cannot exist as both a main manager and a sub manager in the system.',
  'validate.subManager': 'An employee cannot exist as both a main manager and a sub manager in the system.',

  // Button
  'button.add': 'Add new',
  'button.confirm': 'Confirm',
  'button.resetPass': 'Reset pass',
  'button.sendLink': 'Send link',
  'button.edit': 'Edit',
  'button.delete': 'Delete',
  'button.update': 'Update',
  'button.cancel': 'Cancel',
  'button.create': 'Create',
  'button.ok': 'Oke',
  'button.save': 'Save',
  'button.cancelDaily': 'Cancel Request',
  'button.sendNote': 'Send Note',
  'button.showMore': 'Show More',
  'button.showLess': 'Show Less',
  'button.requestApproval': 'Request Approval',
  'button.editAttendaceData': 'Edit attendace data',
  'button.requestTimeOfWorking': 'Request time',
  'button.createDailyReport': 'Create daily report',
  'button.approve': 'Approve',
  'button.viewRequest': 'View request',
  'button.reject': 'Reject',
  'button.addSchedule': 'Add schedule',
  'button.download': 'Download',
  'button.send': 'Send',
  'button.reset-change': 'Reset change',
  'button.decentralize': 'Decentralize',
  'button.sendReport': 'Send report',
  'button.copy': 'Copy',
  'button.paste': 'Paste',
  'button.view': 'View',
  'button.search': 'Search',
  'button.reset': 'Reset',
  'button.view-detail': 'View detail',

  'button.filter': 'Filter',
  'button.resetFilter': 'Reset Filter',
  'button.new': 'New',
  'button.show': 'Show',
  'button.entries': 'Entries',
  'button.manager': 'Manager',
  'button.description': 'Description',
  'button.apply': 'Apply',
  'button.manuallyImport': 'Manually Import',
  'button.importFromInvoiceSlip': 'Import From Invoice/Slip',
  'button.editHistory': 'Edit History',
  'button.createDefaultBaseLeave': 'Create default base leave',
  'title.baseLeaveChange': 'Base Leave Change',
  'button.access': 'Access',
  'button.login': 'Login',
  'button.invite': 'Invite',
  'button.invoice-list': 'Invoice list',
  'button.filters': 'Filters',
  'button.createNewAttendance': 'Create new attendance',

  // input
  'input.placeholder': 'Please enter a keyword ...',
  'work-shift': 'Work-shift',
  'detail': 'Detail',

  // Invoice
  'invoice-detail': 'Invoice detail',

  // Request center
  'TotalTitle': 'Total of day',
  'TotalWorkingTime': 'Total working time',
  'TotalWorkingDays': 'Total working days',
  'TotalBreakTime': 'Total break time',
  'TotalWorkDays': 'Total work days',
  'TotalPaidLeaveRemainDays': 'Total paid leave remain days',
  'TotalUnpaidLeaveRemainDays': 'Total unpaid leave days',
  'TotalOvertime': 'Total overtime',
  'TotalDaysOff': 'Total dayoff',
  'TotalRemainingPaidLeaveDays': 'Total remainng paid leave days',
  'unpaidLeaveRemain': 'Unpaid leave remain',
  'Date': 'Date',
  'RequestType': 'Request type',
  'Quantity': 'Quantity',
  'LeaveType': 'Leave type',
  'Unit': 'Unit',
  'UserApprove1': 'User approve 1',
  'UserApprove2': 'User approve 2',
  'Note': 'Note',
  'UnitName': 'Unit name',
  'StatusName': 'Status name',
  'Hours': 'Hours',
  'Days': 'Days',
  'RequestList': 'Request List',
  'TypeOfRequest': 'Type of request',
  'tab.calendar': 'Calendar',
  'tab.timeTable': 'Time table',
  'tab.schedule': 'Schedule',
  'form.working-location': 'Working location',
  'form.break-time': 'Break time',
  'form.break-in-time': 'Break in time',
  'form.break-out-time': 'Break out time',
  'form.approval': 'Approval',

  // Calendar Page
  'address': 'Address',
  'project': 'Project',
  'check_in_time': 'Check in time',
  'check_out_time': 'Check out time',
  'check_in_gps': 'Check in GPS',
  'check_out_gps': 'Check out GPS',
  'working_time': 'Working time',
  'break_time': 'Break time',
  'no_break_time': 'No break time',
  'break_time_1': 'Break time 1',
  'break_time_2': 'Break time 2',
  'break_time_3': 'Break time 3',
  'approver': 'Approver',
  'reason': 'Reason',
  'total_work_time': 'Total work time',
  'total_overtime': 'Total overtime',
  'time': 'Time',
  'request-time': 'Request time',
  'request-type': 'Request type',
  'reson': 'Reson',
  'calendar.request': 'Request',
  'calendar.absent': 'Absent',
  'calendar.absent-days': 'Absent days',
  'calendar.attendance': 'Attendance',
  'calendar.working-day': 'Working day',
  'calendar.working-days': 'Working days',
  'calendar.working-time': 'Working time',
  'calendar.overtime': 'Overtime',
  'calendar.days-off': 'Days off',
  'calendar.paid-leave': 'Paid leave',
  'calendar.scheduled': 'Schedule',
  'error.projectRequired': 'Project is required',

  // Request time working
  'StartTime': 'Start time',
  'EndTime': 'End time',
  'Reason': 'Reason',
  'ReasonForRequest': 'Reason for request',
  'WorkingEarly': 'Working early',
  'WorkingLate': 'Working late',
  'Overtime': 'Overtime',
  'SendRequestToOffice': 'Send request to office',

  // Schedule management
  'title.edit-schedule': 'Edit schedule',
  'title.add-schedule': 'Add schedule',
  'title.ScheduleManagement': 'Schedule management',
  'WorksiteName': 'Worksite name',
  'Manager': 'Manager',
  'TotalUser': 'Total user',
  'expectedEmpNum': 'Expected number of employees',
  'expectedEmpIds': 'Expected employees',
  'projectName': 'Project name',
  'title.add-new-shift': 'Add shift',
  'title.edit-shift': 'Edit shift',
  'workshift': 'Workshift',
  'default-workshift': 'Default workshift',
  'startTime': 'Start time',
  'endTime': 'End time',
  'presignedWorkload': 'Presigned workload',
  'assignUsers': 'Assign users',
  'type.day': 'Day',
  'type.week': 'Week',
  'title.copyWorkSchedule': 'Copy work schedule',
  'title.sourceDate': 'Source date',
  'title.targetDate': 'Target date',
  'title.download-schedule': 'Download schedule',
  'timeRange': 'Time range',
  'title.scheduleInfo': 'Schedule information',
  'estimated': 'Estimated',
  'planned': 'Planned',
  'title.userShiftInfo': 'Employee shift information',
  'employee.internal': 'Internal',
  'employee.outsource': 'Outsource',
  'showAllEmployeeShifts': 'All',
  'workload': 'Workload',
  'schedule.plan': 'Plan',
  'schedule.estimate': 'Estimate',
  'schedule.actual': 'Actual',
  'title.outsourceShiftInfo': 'Outsource shift information',
  'officeOperation': 'Office operation',

  'title.createSchedule': 'Create schedule',
  'schedule.plannedWorkload': 'Planned workload',
  'workingDate': 'Working date',
  'schedule.estimatedWorkload': 'Estimated workload',
  'employeeShift': 'Employee shift',
  'outsourceShift': 'Outsource shift',
  'message.noEmployeeShift': 'No employee shift',
  'overview': 'Overview',
  'outsourcingShift': 'Outsourcing shift',
  'notSelected': 'Not selected',
  'notAssigned': 'Not assigned',
  'message.noOutsourceShift': 'No outsourcing shift',
  'role': 'Role',
  'totalScheduledWorkTime': 'Total scheduled work time',
  'oclock': 'o\'clock',
  'outsourcing': 'Outsourcing',
  'placeholder.select': 'Please select {msg}',
  'message.please-select': 'Please select {msg}',

  'label.exportFormat': 'Export format',

  // Workday
  'SUN': 'SUN',
  'MON': 'MON',
  'TUE': 'TUE',
  'WED': 'WED',
  'THU': 'THU',
  'FRI': 'FRI',
  'SAT': 'SAT',

  // Skills
  'rebarWorker': 'Rebar worker',
  'supervisor': 'Supervisor',
  'heavyMachineryOperator': 'Heavy machinery operator',
  'unassigned': 'Unassigned',
  'contractType': 'Contract type',
  'engineer': 'Engineer',
  'foreman': 'Foreman',
  'worker': 'Worker',
  'formworkWorker': 'Formwork worker',

  // Attendance management
  'unRanked': 'unRanked',
  'Approve1StatusName': 'Approve1\'s status',
  'Approve2StatusName': 'Approve2\'s status',
  'EndUserName': 'Requester',

  // Calendar
  'link.dayoffRequest': 'Dayoff request',
  'link.attendanceDetail': 'Timekeeping detail',
  'title.workingInformation': 'Working information',
  'startWorkingTime': 'Start time',
  'endWorkingTime': 'End time',
  'timekeeping-request-status.new': 'new',
  'timekeeping-request-status.requested': 'requested',
  'timekeeping-request-status.approved': 'approved',
  'timekeeping-request-status.rejected': 'rejected',
  'work-status.absent': 'ABSENT',
  'work-status.scheduled': 'SCHEDULED',
  'work-status.dayoff': 'DAYOFF',
  'work-status.request': 'REQUEST',
  'button.monthlyAttendanceRequest': 'Monthly attendance request',
  'monthlyAttendanceRequest': 'Monthly attendance request',

  // holiday
  'title.holiday-info': 'Holiday\'s information',
  'title.today': 'Today',
  'holiday-name': 'Holiday name',
  'notes': 'Notes',
  'is-annual': 'Is annual',
  'button.add-new-event': 'Add new event',
  'calendar-empty': 'There is no event',
  'form.event-name': 'Event name',
  'form.event-name.required': 'Please enter event name',
  'form.event-date': 'Event date',
  'form.event-start-date': 'Event start date',
  'form.event-end-date': 'Event end date',
  'form.event-time': 'Event time',
  'form.event-start-time': 'Event start time',
  'form.event-end-time': 'Event end time',
  'form.is-recurring': 'Is recurring',
  'form.recurring-type': 'Recurring type',
  'form.recurring-type.daily': 'Daily',
  'form.recurring-type.weekly': 'Weekly',
  'form.recurring-type.monthly': 'Monthly',
  'form.recurring-type.annually': 'Annually',
  'form.is-day-off': 'Is day off',
  'form.recurring-date': 'Recurring date',
  'form.recurring-from': 'Recurring from',
  'form.recurring-to': 'Recurring to',
  'form.recurring-day': 'Recurring day',
  'form.recurring-day.monday': 'Monday',
  'form.recurring-day.tuesday': 'Tuesday',
  'form.recurring-day.wednesday': 'Wednesday',
  'form.recurring-day.thursday': 'Thursday',
  'form.recurring-day.friday': 'Friday',
  'form.recurring-day.saturday': 'Saturday',
  'form.recurring-day.sunday': 'Sunday',
  'form.recurring-week': 'Recurring week',
  'form.recurring-week.first': 'Week 1',
  'form.recurring-week.second': 'Week 2',
  'form.recurring-week.third': 'Week 3',
  'form.recurring-week.fourth': 'Week 4',
  'form.recurring-week.last': 'Last week',
  'form.recurring-month': 'Recurring month',
  'form.recurring-month.january': 'January',
  'form.recurring-month.february': 'February',
  'form.recurring-month.march': 'March',
  'form.recurring-month.april': 'April',
  'form.recurring-month.may': 'May',
  'form.recurring-month.june': 'June',
  'form.recurring-month.july': 'July',
  'form.recurring-month.august': 'August',
  'form.recurring-month.september': 'September',
  'form.recurring-month.october': 'October',
  'form.recurring-month.november': 'November',
  'form.recurring-month.december': 'December',
  'add-event-calendar': 'Add event calendar',
  'edit-event-calendar': 'Edit event calendar',
  'log-event-calendar': 'Log event calendar',
  'log.UPDATE': 'has updated the data',
  'log.CREATE': 'has created the data',
  'log.DELETE': 'has deleted the data',
  'log.lastmodifiedtime': 'Last modified time',
  'log.lastmodifiedby': 'Last modified by',
  'log.changed': 'Changed <span color="blue">{field}</span> from <span color="warning">{oldValue}</span> to <span color="warning">{newValue}</span>',
  'button.yes': 'Yes',
  'button.no': 'No',
  'event-calendar': 'Event calendar',
  'event-calendar-rule-setting': 'Event calendar rule setting',
  'holiday': 'Holiday',

  // Role management
  'roleName': 'Role name',
  'company.work-shift.detail': 'Work shift detail',
  'RolesConfig': 'Roles config',
  'Screen': 'Screen',
  'PageUrl': 'Page url',
  'UserRulesName': 'User rules name',
  'OrderNumber': 'Order number',
  'ResetYourAccount': 'Reset your account',
  'EmailOrUsername': 'Email/Username',
  'title.delete': 'Delete',
  'isWorksiteManager': 'Is worksite manager',
  'worksiteAuth': 'Worksite auth',
  'officeAuth': 'Office auth',
  'role-management': 'Role management',
  'role-decentralize': 'Decentralize',
  'title.add-role': 'Add role',
  'title.edit-role': 'Edit role',
  'type': 'Type',
  'roleParent': 'Role parent',
  'users': 'Users',
  'functionName': 'Function name',
  'canWrite': 'Write',
  'canCreate': 'Create',
  'canRead': 'Read',
  'canUpdate': 'Update',
  'canDelete': 'Delete',
  'assignPermissFor': 'Assign permission for',
  'search': 'Search...',
  'structure': 'Structure',
  'page.profile.basic': 'Profile basic',
  'menu.company.work-shift.detail': 'Work shift detail',

  // Project management
  'form.structureName': 'Structure name',
  'form.expectedStartDate': 'Expected start date',
  'form.expectedEndDate': 'Expected end date',
  'form.actualStartDate': 'Actual start date',
  'form.actualEndDate': 'Actual end date',
  'project.status': 'Status',

  // Cost Config
  'payment-type': 'Payment Type',
  'entry-type': 'Entry Type',
  'add-payment-type': 'Add payment type',
  'edit-payment-type': 'Edit payment type',
  'form.payment-type-name': 'Payment type name',
  'add-entry-type': 'Add entry type',
  'edit-entry-type': 'Edit entry type',
  'form.entry-type-name': 'Entry type name',
  'log-entry-type': 'Log entry type',
  'log-payment-type': 'Log payment type',

  // Input Cost
  'form.original-document-number': 'Original document number',
  'form.original-document-number.required': 'Please enter original document number',
  'form.original-document-date': 'Original document date',
  'form.original-document-date.required': 'Please enter original document date',
  'form.process-type': 'Process type',
  'form.process-type.required': 'Please enter process type',
  'form.entry-type': 'Entry type',
  'form.entry-type.required': 'Please enter entry type',
  'form.payment-date': 'Payment date',
  'form.payment-date.required': 'Please enter payment date',
  'form.payment-type': 'Payment type',
  'form.payment-type.required': 'Please enter payment type',
  'form.provider': 'Provider',
  'form.provider.required': 'Please enter provider',
  'form.project': 'Project',
  'form.project.required': 'Please enter project',
  'form.item-name': 'Item name',
  'form.item-name.required': 'Please enter item name',
  'form.transaction-date': 'Transaction date',
  'form.transaction-date.required': 'Please enter transaction date',
  'form.quantity': 'Quantity',
  'form.quantity.required': 'Please enter quantity',
  'form.manufacturer': 'Manufacturer',
  'form.manufacturer.required': 'Please enter manufacturer',
  'form.price': 'Price',
  'form.price.required': 'Please enter price',
  'form.tax-rate': 'Tax rate',
  'form.tax-rate.required': 'Please enter tax rate',
  'form.price-excluding-tax': 'Price (excluding tax)',
  'form.price-excluding-tax.required': 'Please enter price (excluding tax)',
  'form.price-included-tax': 'Price (included tax)',
  'form.price-included-tax.required': 'Please enter price (included tax)',
  'form.total': 'Total',
  'form.total.required': 'Please enter total',
  'form.description.placeholder': 'Please enter description',
  'form.description.required': 'Please enter description',
  'employee-costs': 'Employee costs',
  'outsourcing-costs': 'Outsourcing costs',
  'material-costs': 'Material costs',
  'lease-costs': 'Lease',
  'machine-costs': 'Machine',
  'other-costs': 'Others',
  'cancellation': 'Cancellation',
  'form.from-date': 'From Date',
  'form.to-date': 'To Date',
  'form.site-name': 'Site name',
  'message.delete-confirmation': 'Are you sure you want to delete this item?',
  'message.edit-confirmation': 'Are you sure you want to edit this item?',
  'message.add-confirmation': 'Are you sure you want to add this item?',
  'message.cancel-confirmation': 'Are you sure you want to cancel this item?',
  'message.required': 'Please enter {field}',
  'rankName': 'Rank name',
  'manHours': 'Man hours',
  'approximatePrice': 'Approximate price',
  'fixedPrice': 'Fixed price',
  'averagePrice': 'Average price',
  'amount': 'Amount',
  'outsourceName': 'Outsource name',
  'message.process-data-confirmation': 'We will process your data in a few minutes.',
  'title.input-cost-item-delete': 'Delete input cost item',
  'confirm-delete-item': 'Are you sure you want to delete this item?',
  'manufacturer': 'Manufacturer',

  // Monthly attendance closing detail
  'attendance.summary': 'Attendance summary',
  'attendance.summary.description': 'Monthly statistics overview',
  'workingDays': 'Working days',
  'absentDays': 'Absent days',
  'workTime': 'Work time',
  'overtime': 'Overtime',
  'paidLeave': 'Paid leave',
  'unpaidLeave': 'Unpaid leave',
  'lastUpdated': 'Last updated',
  'chooseAction': 'Choose action',
  'CSVDownload': 'CSV Download',
  'success.updateAttendance': 'Changes confirmed successfully!',
  'error.updateAttendance': 'Failed to confirm changes',
  'error.invalidTimeFormat': 'Invalid time format',
  'error.loadData': 'Failed to load data',
  'success.exportCSV': 'CSV file exported successfully!',
  'error.exportCSV': 'Failed to export CSV file',
  'status': 'Status',
  'dateFrom': 'Date from',
  'dateTo': 'Date to',
  'createTime': 'Create time',
  'ago': 'ago',
  'error.isApprovedRecord': 'This attendance record has been approved and cannot be edited',
  'status.approved': 'Approved',
  'status.notApproved': 'Not approved',
  'error.invalidTimeFormatHHMM': 'Invalid time format (HH:MM)',
  'approvalStatus': 'Approval status',
  'button.async': 'Async',
  'success.syncAttendance': 'Attendance synchronization completed',
  'error.syncAttendance': 'Attendance synchronization failed',
  'tab.officeData': 'Office data',
  'tab.employeeData': 'Employee data',
  'showShiftInOneLine': 'Show shift in one line',

  // Employee shift table
  'fontSize': 'Font size',
  'rowHeight': 'Row height',

  // // Input Cost
  // 'form.original-document-number': 'Original document number',
  // 'form.original-document-number.required': 'Please enter original document number',
  // 'form.original-document-date': 'Original document date',
  // 'form.original-document-date.required': 'Please enter original document date',
  // 'form.process-type': 'Process type',
  // 'form.process-type.required': 'Please enter process type',
  // 'form.entry-type': 'Entry type',
  // 'form.entry-type.required': 'Please enter entry type',
  // 'form.payment-date': 'Payment date',
  // 'form.payment-date.required': 'Please enter payment date',
  // 'form.payment-type': 'Payment type',
  // 'form.payment-type.required': 'Please enter payment type',
  // 'form.provider': 'Provider',
  // 'form.provider.required': 'Please enter provider',
  // 'form.project': 'Project',
  // 'form.project.required': 'Please enter project',
  // 'form.item-name': 'Item name',
  // 'form.item-name.required': 'Please enter item name',
  // 'form.transaction-date': 'Transaction date',
  // 'form.transaction-date.required': 'Please enter transaction date',
  // 'form.quantity': 'Quantity',
  // 'form.quantity.required': 'Please enter quantity',
  // 'form.manufacturer': 'Manufacturer',
  // 'form.manufacturer.required': 'Please enter manufacturer',
  // 'form.price': 'Price',
  // 'form.price.required': 'Please enter price',
  // 'form.tax-rate': 'Tax rate',
  // 'form.tax-rate.required': 'Please enter tax rate',
  // 'form.price-excluding-tax': 'Price (excluding tax)',
  // 'form.price-excluding-tax.required': 'Please enter price (excluding tax)',
  // 'form.price-included-tax': 'Price (included tax)',
  // 'form.price-included-tax.required': 'Please enter price (included tax)',
  // 'form.total': 'Total',
  // 'form.total.required': 'Please enter total',
  // 'form.description.required': 'Please enter description',
  // 'employee-costs': 'Employee costs',
  // 'outsourcing-costs': 'Outsourcing costs',
  // 'material-costs': 'Material costs',
  // 'lease-costs': 'Lease',
  // 'machine-costs': 'Machine',
  // 'other-costs': 'Others',
  // 'cancellation': 'Cancellation',
  // 'form.from-date': 'From Date',
  // 'form.to-date': 'To Date',
  // 'form.site-name': 'Site name',
  // 'message.delete-confirmation': 'Are you sure you want to delete this item?',

  // Cost Category
  'add-cost-category': 'Add cost category',
  'edit-cost-category': 'Edit cost category',
  'log-cost-category': 'Log cost category',
  'categories-management': 'Categories management',
  'form.products': 'Products',
  'button.add-more': 'Add more',

  // Vendor
  'add-vendor': 'Add vendor',
  'edit-vendor': 'Edit vendor',
  'log-vendor': 'Log vendor',
  'form.corporate': 'Corporate',
  'company-information': 'Company information',
  'contact-person': 'Contact person',
  'invoice': 'Invoice',
  'form.original-number': 'Original number',
  'form.invoice-title': 'Invoice title',
  'form.issue-date': 'Issue date',
  'form.contractor': 'Contractor',
  'form.customer': 'Customer',
  'form.outsource': 'Outsource',
  'form.vendor-common-name': 'Vendor common name',
  'form.other-name': 'Other name',
  'form.corporate-number': 'Corporate number',
  'form.tax-code': 'Tax code',
  'representative-information': 'Representative information',
  'form.representative-name': 'Representative name',
  'form.phone-number': 'Phone number',
  'show': 'Show',
  'entries': 'Entries',
  'invoice-list-of': 'Invoice list of',
  'button.detail': 'Detail',
  'add-representative': 'Add representative',

  // Contractor
  'add-contractor': 'Add contractor',
  'edit-contractor': 'Edit contractor',
  'log-contractor': 'Log contractor',
  'form.contractor-common-name': 'Contractor common name',
  'button.project-list': 'Project list',
  'project-list-of': 'Project list of',

  // Customer
  'add-customer': 'Add customer',
  'edit-customer': 'Edit customer',
  'log-customer': 'Log customer',
  'form.type-of-customer': 'Type of customer',
  'form.customer-common-name': 'Customer common name',

  // OutSource
  'add-outsource': 'Add outsource',
  'edit-outsource': 'Edit outsource',
  'log-outsource': 'Log outsource',

  // Cost summary
  'subtotal': 'Subtotal',
  'total': 'Total',
  'expenses': 'Expenses',
  'benefit': 'Benefit',
  'main-construction-cost': 'Main construction cost',
  'separate-construction-cost': 'Separate construction cost',
  'overall-construction-cost': 'Overall construction cost',
  'contract-construction-period': 'Contract construction period',
  'cost-management-entry': 'Cost management entry',
  'check-details': 'Check details',
  'edit-cost-summary': 'Edit cost summary',
  'construction-cost-total': 'Construction cost total',
  'estimate-construction-cost': 'Estimate construction cost',
  'separate-construction-costs': 'Separate construction costs',
  'total-construction-cost': 'Total construction cost',
  'rich-amount': 'Rich amount',
  'contractor': 'Contractor',
  'customer': 'Customer',
  'main-construction-cost-and-separate-construction-cost': 'Main construction cost and separate construction cost',
  'view-cost-summary-detail': 'View cost summary detail',

  // Cost Item
  'add-cost-item': 'Add cost item',
  'edit-cost-item': 'Edit cost item',
  'form.item': 'Item',
  'form.item-list': 'Item list',
  'form.seri': 'Seri',
  'form.main-category': 'Main category',
  'form.time_period': 'Time period',
  'form.time-period-from': 'Time period from',
  'form.to': 'To',
  'form.price-from': 'Price from',
  'suppliers-comparison': 'Suppliers comparison',
  'monthly-price-comparison': 'Monthly price comparison',
  'form.subcontractor': 'Item name',

  // Cost Manufacturer
  'add-manufacturer': 'Add manufacturer',
  'edit-manufacturer': 'Edit manufacturer',
  'log-manufacturer': 'Log manufacturer',
  'form.size': 'Size',
  'form.serial-number': 'Serial number',
  'form.model': 'Model',
  'form.fuel-usage': 'Fuel usage',
  'form.equipment-status': 'Equipment status',
  'form.last-maintain-date': 'Last maintain date',
  'equipment': 'Equipment',
  'form.manufacturer-common-name': 'Manufacturer common name',

  // Worksite management
  'form.primaryManager': 'Primary manager',
  'form.subManagers': 'Sub manager',
  'selectDate': 'Select date',

  // Work-shift
  'confirm.updateStatus': 'Change status',
  'form.workShiftName': 'Workshift name',
  'form.inTime': 'In time',
  'form.outTime': 'Out time',
  'warning.updateStatus': 'Are you sure you want to change the status of the worksite from {msg1} to {msg2}?',
  'status.active': 'Active',
  'status.deactive': 'Deactive',
  'workShiftBreaks': 'Breaks',
  'form.workTime': 'Work time',
  'form.breakTime': 'Break time',
  'workshift.error.the-break-time-is-not-within-the-working-hours.': 'The break time is not within the working hours',
  'workshift.error.break-times-cannot-overlap.': 'Break times cannot overlap',
  'workshift.error.the-actual-working-time-calculation-is-unreasonable.': 'The actual working time calculation is unreasonable',
  'workshift.error.break-out-time-is-later-than-the-break-in-time-of-the-break.': 'Break out time is later than the break in time of the break.',
  'workshift.error.check-out-time-is-later-than-the-check-in-time.': 'Check out time is later than the check in time.',
  'form.workShiftCode': 'Workshift code',
  'workshift.error.check-in-time-is-required.': 'Check in time is required',
  'workshift.error.check-out-time-is-required.': 'Check out time is required',

  'error.checkOutTimeMustBeBeforeCurrentTime': 'Check out time must be before current time',
  'error.checkInTimeMustBeBeforeCurrentTime': 'Check in time must be before current time',
  'error.checkInTimeMustBeBeforeCheckOutTime': 'Check in time must be before check out time',
  'error.checkOutTimeMustBeBefore2359OfNextOneDayOfCheckInDate': 'Check out time must be before 23:59 of next one day of check in date',
  'error.checkInTimeRequired': 'Check in time is required',
  'error.checkOutTimeRequired': 'Check out time is required',

  // Mornitoring page
  'title.reject': 'Reject',
  'attendanceStatus': 'Attendance status',
  'workplaceAddress': 'Address',
  'totalBreakTime': 'Total break time',
  'isApproved': 'Is approved',
  'isRequested': 'Is requested',
  'request.isRequested': 'Requested',
  'request.isNotRequested': 'Not requested',
  'request.isApproved': 'Approved',
  'request.isRejected': 'Rejected',
  'workplaceId': 'Workplace ID',
  'workplaceCode': 'Code',
  'weekday': 'Weekday',
  'attendance-list': 'Attendance list',
  'employee-name': 'Employee name',
  'work-time': 'Work time',
  'total-over-time': 'Total over time',
  'coefficient': 'Coefficient',
  'attendance-status': 'Attendance status',
  'work-notes': 'Work notes',
  'button.export': 'Export',
  'ranking-name': 'Ranking name',
  'outsource-name': 'Outsource name',
  'workloadOnMainConstruction': 'Workload on main construction',
  'workloadOnSubConstruction': 'Workload on sub construction',
  'title.editAttendance': 'Edit attendance',
  'title.addAttendance': 'Add attendance',
  'workingTime': 'Working time',
  'totalWorkingTime': 'Total working time',
  'checkInTime': 'Check in time',
  'checkOutTime': 'Check out time',
  'breakTime': 'Break time',
  'noBreakTime': 'No break time',
  'standardHours': 'Standard hours',
  'realHours': 'Real hours',
  'lateHours': 'Late hours',
  'earlyHours': 'Early hours',
  'duration': 'Duration',
  'totalWorkload': 'Total workload',
  'button.dailyReportCreated': 'Daily report created',
  'title.timecard': 'Timecard management',
  'date': 'Date',
  'placeholder.rejectReason': 'Please enter reason',
  'requestStatus': 'Request status',

  // 'workplaceName': 'Name',
  'workplaceDes': 'Description',
  'workplaceType': 'Type',
  'status.scheduled': 'Scheduled',
  'status.checkIn': 'Check in',
  'status.break': 'Break',
  'status.checkOut': 'Check out',

  // Change password page
  'oldPassword': 'Old password',
  'newPassword': 'New password',
  'confirmNewPassword': 'Confirm new password',

  // Request center
  'requestFromTo': 'Date',
  'title.monthlyAttendanceRequest': 'Monthly attendance request',
  'view-detail': 'View detail',
  'form.type': 'Type',
  'form.days': 'Days',
  'form.rejected-by': 'Rejected by',
  'form.approved-by': 'Approved by',
  'form.reason': 'Reason',
  'form.request': 'Request',
  'form.author': 'Author',
  'form.your-request': 'Your request',
  'form.start-date': 'Start date',
  'form.end-date': 'End date',
  'form.request-type': 'Request type',
  'form.leave-type': 'Leave type',
  'form.minutes': 'Minutes',
  'form.hours': 'Hours',
  'NOT_APPROVED': 'Not approved yet',
  'APPROVED': 'Approved',
  'REJECTED': 'Rejected',
  'PENDING': 'Pending',
  'CANCELLED': 'Cancelled',
  'NOT_REQUESTED': 'Not requested',
  'form.request-duration': 'Request duration',
  'request-management': 'Request management',
  'message.approve-confirmation': 'Are you sure you want to approve this request?',
  'message.reject-confirmation': 'Are you sure you want to reject this request?',
  'placeholder.select-time': 'Select time',
  'placeholder.enter': 'Enter {msg}',
  'on-site-approver': 'On-site approver',
  'office-approver': 'Office approver',
  'representative-approver': 'Representative approver',
  'day': 'Day',
  'half-day': 'Half day',
  'custom-day': 'Custom day',
  'morning': 'Morning',
  'afternoon': 'Afternoon',
  'leave-request-description': 'Leave Request is a request for time off from work',
  'work-request-description': 'Work Request is a request related to work tasks or projects',
  'overtime-request-description': 'Overtime Request is a request for additional work hours beyond regular working hours',
  'in-out-request-description': 'In/Out Request is a request to log entry or exit times',

  // Human cost
  'form.ranking': 'Company rank',
  'form.rank-name': 'Rank name',
  'form.employee-cost': 'Employee cost',
  'form.employee-name': 'Employee name',
  'form.cost-amount': 'Cost amount',
  'form.avarage-cost-amount': 'Average cost amount',
  'form.ranking-matching-method': 'Ranking matching method',
  'form.outsource-cost': 'Outsource cost',
  'form.direct': 'Direct',
  'form.indirect': 'Calculate from salary',
  'form.min-value': 'Min value',
  'form.max-value': 'Max value',
  'form.average-value': 'Average value',
  'form.rank': 'Rank',
  'form.cost-by-day': 'Daily cost',
  'form.valid-date': 'Valid date',
  'form.empty': 'No data',

  // Common
  'placeholder.enter-data': 'Please enter the {msg}',
  'placeholder.select-data': 'Please select the {msg}',
  'placeholder.note': 'Note',
  'placeholder.write-here': 'Write here',
  'updatedBy': 'Updated by',
  'updatedAt': 'Updated at',

  // Profile basic
  'profile.basic.contactInfo': 'Contact information',
  'profile.basic.roleInfo': 'Role information',
  'profile.username': 'Username',
  'profile.email': 'Email',
  'profile.phone': 'Phone',
  'profile.address': 'Address',
  'profile.birthday': 'Birthday',
  'profile.company': 'Company',
  'profile.position': 'Position',
  'profile.structure': 'Structure',
  'profile.level': 'Level',

  'profile': 'Profile',
  'settings': 'Settings',
  'logout': 'Logout',
  'setNewAvatar': 'Set new profile picture',
  'cropYourNewAvatar': 'Crop your new avatar',
  'changePhoto': 'Change photo',

  // Office management
  'userName': 'User name',
  'code': 'Code',
  'workDays': 'Work days',
  'offdays': 'Off days',
  'usedLeaves': 'Used leaves',
  'remainLeaves': 'Remain leaves',
  'comment': 'Comment',

  // Leave setting
  'employeeId': 'Employee ID',
  'employeeName': 'Employee Name',
  'employeeCode': 'Employee Code',
  'baseLeave': 'Base Leave',
  'baseLeaveExpire': 'Base Leave Expire',
  'lastRemainLeave': 'Last Remain Leave',
  'lastRemainLeaveExpire': 'Last Remain Leave Expire',
  'totalUsedLeave': 'Total Used Leave',
  'totalSelfUsedLeave': 'Total Self-Used Leave',
  'totalOrgUsedLeave': 'Total Organization-Used Leave',
  'updateTime': 'Update Time',
  'from': 'From',
  'to': 'To',
  'baseLeaveValidDateRange': 'Base Leave Valid Date Range',
  'lastLeaveRemainingValidDateRange': 'Last Leave Remaining Valid Date Range',
  'totalPersonalUsedLeave': 'Total Personal Used Leave',
  'lastLeaveRemaining': 'Last Leave Remaining',
  'personalUsed': 'Personal Used',
  'forceUsed': 'Force Used',
  'expire': 'Expire',

  // Notification
  'notification': 'Notification',
  'post': 'Post',
  'form.title': 'Title',
  'form.body': 'Body',
  'form.target': 'Target',
  'form.target-type': 'Target type',
  'form.target-id': 'Target ID',
  'target-type.individual': 'Individual',
  'target-type.all': 'All',
  'all': 'All',
  'unread': 'Unread',
  'mark-all-as-read': 'Mark all as read',
  'target-type.role': 'Role',
  'noTarget': 'No target',
  'form.notification-type': 'Notification type',
  'notification-type.push': 'Push',
  'notification-type.email': 'Email',
  'test': 'Test',
  'FAILED': 'Failed',
  'PUBLISHED': 'Published',
  'PARTIALLY_PUBLISHED': 'Partially published',
  'PARTIALLY_FAILED': 'Partially failed',

  // Account
  'title.invite-to-organization': 'Invite to organization',
  'description.invite-to-organization': 'Manage your organization invitations',
  'message.no-invitations': 'No invitations',
  'message.expire-time': 'Expire time',
  'button.accept': 'Accept',
  'message.accept-invitation': 'Accepted invitation',
  'message.reject-invitation': 'Rejected invitation',
  'message.error-accept-invitation': 'Error accepting invitation',
  'button.signUp': 'Sign up',
  'account.settings.password.change': 'Change password',
  'placeholder.oldPassword': 'Old password',
  'placeholder.newPassword': 'New password',
  'placeholder.confirmNewPassword': 'Confirm new password',
  'validation.oldPassword.required': 'Please enter your old password',
  'validation.password.required': 'Please enter your new password',
  'validation.confirmPassword.required': 'Please confirm your new password',
  'button.submit': 'Submit',
  'validation.confirmPassword.notMatch': 'Password does not match',
  'validation.password.pattern': 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  'account.settings.password.changeSuccess': 'Password change successful',

  // Alert message
  'confirm.delete': 'Confirm delete',

  // Construction cost
  'requestAmount': 'Progress Payment Amount',
  'retentionAmount': 'Retention Amount',
  'releasedAmount': 'Retention Release Amount',
  'totalClaimedAmount': 'Claimed Amount Excluding Retention',
  'profitByRequestedAmount': 'Profit by Progress Payment',
  'profitByClaimedAmount': 'Profit by Claimed Amount',
  'construction-cost.totalCost': 'Total Cost',
  'humanCost': 'Human Cost',
  'inHumanCost': 'Non-Human Cost',
  'riskAmount': 'Risk Amount',
  'mainConstruction': 'Main',
  'subConstruction': 'Sub',
  'estimateBudgetAmount': 'Estimate Budget Amount',
  'againstBudget': 'Against Budget',
  'actualProfitMargin': 'Actual Profit Margin',
  'mcEstimatedProfitMargin': 'Estimated Profit Margin',
  'previousAmount': 'Previous Amount',
  'currentAmount': 'Current Amount',
  'accumulatedAmount': 'Accumulated Amount',
  'remainingBalance': 'Remaining Balance',
  'processRate': 'Process Rate',
  'construction-cost.main.estimateCost': 'Budget Comparison (Main Construction Cost) Summary',
  'construction-cost.sub.estimateCost': 'Budget Comparison (Sub Construction Cost) Summary',
  'construction-cost.overall.estimateCost': 'Budget Comparison (Overall Construction Cost) Summary',
  'mainConstructionCostAmount': 'Main Construction Cost Amount',
  'subConstructionCostAmount': 'Sub Construction Cost Amount',
  'overallConstructionCostAmount': 'Overall Construction Cost Amount',
  'mainConstructionCostSummary': 'Main Construction Cost Summary',
  'subConstructionCostSummary': 'Sub Construction Cost Summary',
  'mainConstructionCost': 'Main Construction Cost',
  'subConstructionCost': 'Sub Construction Cost',

  'previousMiscellaneousExpenses': 'Previous Miscellaneous Expenses',
  'currentMiscellaneousExpenses': 'Current Miscellaneous Expenses',
  'currentMonthEstimatedAmount': 'Current Month Estimated Amount',
  'currentMonthConfirmedAmount': 'Current Month Confirmed Amount',
  'amountExcludingRetention': 'Amount Excluding Retention',
  'accumulatedMiscellaneousCosts': 'Accumulated Miscellaneous Costs',
  'accumulatedRetentionAmount': 'Accumulated Retention Amount',
  'siteProgressSummary': 'Site Progress Summary',

  'budgetBasedOnConstructionProgress ': 'Budget Based on Construction Progress',
  'constructionInfoDetail': 'Construction Info Detail',

  'contractualCost': 'Contractual Cost',
  'estimatedCost': 'Estimated Cost',
  'initialCost': 'Initial Cost',
  'modifiedCost': 'Modified Cost',
  'profitRatio': 'Profit Ratio',

  // Sign up page
  'title.registration': 'Registration',
  'title.otp_verification': 'OTP Verification',
  'title.complete': 'Complete',
  'form.password': 'Password',
  'form.confirmPassword': 'Confirm Password',
  'button.verify': 'Verify',
  'button.resend': 'Resend',
  'message.registration_success': 'Registration successful',
  'message.registration_success_sub': 'You can now login to your account',
  'message.otp_sent_to_email': 'OTP has been sent to {email}',
  'message.otp_verification_success': 'OTP verification successful',
  'message.otp_verification_error': 'OTP verification failed',
  'message.resend_otp_error': 'Failed to resend OTP',
  'message.password_min': 'Password must be at least 6 characters',
  'message.password_not_match': 'Password does not match',
  'message.email_invalid': 'Invalid email',
  'message.loginId': 'Please enter login ID',
  'message.password': 'Please enter password',
  'message.confirmPassword': 'Please confirm password',
  'form.male': 'Male',
  'form.female': 'Female',
  'placeholder.name': 'Please enter name',
  'placeholder.address': 'Please enter address',
  'placeholder.phone': 'Please enter phone',
  'placeholder.loginId': 'Please enter login ID',
  'placeholder.password': 'Please enter password',
  'placeholder.confirmPassword': 'Please confirm password',
  'placeholder.birthday': 'Please enter birthday',
  'placeholder.email': 'Please enter email',

  // Placeholder
  'Please Select': 'Please Select',

  // Invite page
  'button.back-to-login-page': 'Back to login page',

  // Invoice
  'supplier': 'Supplier',
  'invoice-type': 'Invoice type',
  'release-date': 'Release date',
  'payment-term': 'Payment term',
  'payment-method': 'Payment method',
  'payment-date': 'Payment date',
  'invoice-number': 'Invoice number',
  'button.note': 'Note',
  'item-name': 'Item name',
  'unit': 'Unit',
  'quantity': 'Quantity',
  'price': 'Price',
  'unitPrice': 'Unit price',
  'tax-rate': 'Tax rate',
  'button.save-change': 'Save change',
  'phone-number': 'Phone number',
  'email': 'Email',
  'click-or-drag-file-to-this-area-to-upload': 'Click or drag file to this area to upload',
  'upload-image': 'Upload image',
  'support-for-a-single-upload': 'Support for a single upload',
  'vendor': 'Vendor',
  'transaction-date': 'Transaction date',
  'total-nontaxed': 'Total non-tax',
  'total-taxed': 'Total taxed',
  'button.upload': 'Upload',
  'input-cost-item-list': 'Input cost item list',
  'add-new-item': 'Add new item',
  'noMore': 'No more',
  'invoiceInput': 'Invoice input',
  'no-images': 'No images',
  'placeholder-search': 'Search',
  'currency.unit': '$',

  // Basic-setting
  'account.settings.form-male': 'Male',
  'account.settings.form-female': 'Female',
  'account.settings.form-birthday.placeholder': 'Please select birthday',
  'account.settings.form-baseLeave': 'Base leave',
  'name': 'Name',
  'avatar.updateSuccess': 'Avatar update success',
  'profile.role': 'Role',

  // Summary detail
  'price-after-tax': 'Price (after tax)',
  'price-before-tax': 'Price (before tax)',
  'original-input-cost-number': 'Original input cost number',
  'actions': 'Actions',
  'button.manual-import': 'Manually import',
  'button.image-import': 'Image import',
  'invoice-image': 'Invoice image',
  'constructure': 'Constructure',
  'invoice-title': 'Invoice title',
  'unit-price': 'Unit price',
  'tax-rate-percent': 'Tax rate (%)',
  'category': 'Category',
  'button.save-all': 'Save all',
  'update-time': 'Update time',
  'summary-detail.amount': 'Amount',
  'manDay': 'manday',
  'grandTotal': 'Grand total',
  'show-original-cost': 'Show original cost',

  // Simulation report
  'simulationReport': 'Simulation report',
  'export': 'Export',

  // Simulation report

  // Form
  'form.required': 'Please enter {msg}',

  // Error page
  'message.noPermission': 'You do not have access to the system screens. Please contact the administrator',
  'error.apiError': 'API error',
  // Danh sách mã lỗi
  'KAN500': 'Unknown Error',
  'KAN00': 'Operation Successful',
  'KAN01': 'Not Found',
  'KAN02': 'No Changes',
  'KAN03': 'Bad Request',
  'KAN04': 'Unauthorized',
  'KAN05': 'Forbidden',
  'AUTH01': 'Password is incorrect',
  'AUTH02': 'Password change failed',
  'AUTH03': 'Password confirmation mismatch',
  'AUTH04': 'Password does not meet the requirement',
  'AUTH05': 'Password reset failed',
  'AUTH06': 'Outsource blocked',
  'TKN01': 'Token has expired',
  'TKN02': 'Token is invalid',
  'TKN03': 'Token validation failed',
  'USR01': 'User not found',
  'USR02': 'User not found in the organization',
  'USR03': 'User creation failed',
  'USR04': 'User update failed',
  'USR05': 'Assign role failed',
  'USR06': 'User already exists',
  'USR07': 'User already exists in the organization',
  'USR08': 'Creating admin is forbidden',
  'USR09': 'Assign organization failed',
  'CRT01': 'Contract creation failed',
  'CRT02': 'Contract update failed',
  'ORG01': 'Organization not found',
  'ORG02': 'Organization creation failed',
  'ORG03': 'Organization update failed',
  'STR01': 'Structure not found',
  'STR02': 'Structure creation failed',
  'STR03': 'Structure update failed',
  'POS01': 'Position not found',
  'POS02': 'Position creation failed',
  'POS03': 'Position update failed',
  'RNK01': 'Ranking not found',
  'RNK02': 'Ranking creation failed',
  'RNK03': 'Ranking update failed',
  'WPL01': 'Workplace not found',
  'WPL02': 'Workplace creation failed',
  'WPL03': 'Workplace update failed',
  'WPL04': 'Assign manager failed',
  'GLC01': 'Global configuration not found',
  'MAIL01': 'Email template not found',
  'MAIL02': 'Failed to send email',
  'ROLE01': 'Role not found',
  'ROLE02': 'Role creation failed',
  'ROLE03': 'Role update failed',
  'UNIT01': 'Unit not found',
  'RQT01': 'Request type not found',
  'LVT01': 'Leave type not found',
  'REQ01': 'Request not found',
  'REQ02': 'Request creation failed',
  'REQ03': 'Request update failed',
  'REQ04': 'Approve attendance failed',
  'REQ05': 'Approve request failed',
  'REQ06': 'Reject request failed',
  'REQ07': 'Request details are invalid',
  'TRL01': 'Translator not found',
  'SFT01': 'Shift not found',
  'SFT02': 'Shift creation failed',
  'SFT03': 'Shift update failed',
  'SFT04': 'Shift removal failed',
  'SFT05': 'Check-in failed',
  'SFT06': 'Check-out failed',
  'SFT07': 'Break-in failed',
  'SFT08': 'Break-out failed',
  'SFT09': 'Already checked in',
  'SFT10': 'Already breaked in',
  'SFT11': 'Not checked in yet',
  'SFT12': 'Shift already ended',
  'SFT13': 'Not break-in yet',
  'SFT14': 'Not break-out yet',
  'SFT15': 'Already breaked out',
  'SCH01': 'Schedule not found',
  'SCH02': 'Schedule creation failed',
  'SCH03': 'Schedule update failed',
  'SCH04': 'Worker information not found',
  'PRJ01': 'Project not found',
  'PRJ02': 'Project creation failed',
  'PRJ03': 'Project update failed',
  'PRJ04': 'No ongoing projects',
  'GPS01': 'Location not found',
  'GPS02': 'Location information is invalid',
  'HOL01': 'Holiday not found',
  'HOL02': 'Holiday creation failed',
  'HOL03': 'Holiday update failed',
  'URF01': 'Functional role not found',
  'URF02': 'Functional role creation failed',
  'URF03': 'Functional role update failed',
  'FNC01': 'Function not found',
  'FNC02': 'Function creation failed',
  'FNC03': 'Function update failed',
  'HST01': 'Tracking history retrieval failed',
  'HST02': 'Tracking history creation failed',
}
