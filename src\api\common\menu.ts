import type { Dayjs } from 'dayjs'
import type { MenuData } from '~@/layouts/basic-layout/typing'

export interface MenuFunc {
  functionId?: number
  functionUrl?: string
  functionName?: string
  status?: boolean
  orderNumber?: number
  id: number
  name: string
  title: string
  locale: string
  path: string
  parentName?: string
  icon?: string
  redirect?: string
  component?: string
  hideInMenu?: boolean
  createTime?: Dayjs
  updateTime?: Dayjs
}

export function getRouteMenusApi() {
  return useGet<MenuData>('v1/menu')
}

// Code mới
export interface UserRole {
  userRoleId: string
  userRoleName: string
  orgId: string
  orderNumber?: number
  status?: boolean
  isWorksiteManager?: boolean
  createTime?: string
  updateTime?: string
}

export interface FunctionCombo {
  id: number
  title: string
  locale: string
}

export interface FunctionRoleItem {
  functionId: string
  allowedRoleId: string
  allowedStructureId: string
}

export interface FunctionAccessPrivilegeItem {
  Id: number
  Title: string
  Locale: string
  canRead: boolean
  canWrite: boolean
  canUpdate: boolean
  canDelete: boolean
}

export interface UserRoleResponse {
  items?: UserRole[]
  pageIndex?: number
  pageSize?: number
  totalRow?: number
  pageCount?: number
}

export interface FunctionAccessPrivilegeResponse {
  items?: FunctionAccessPrivilegeItem[]
}

export interface UpdateAllowedRoleParams {
  functionId: string
  allowedRoleId: string
  allowedStructureId: string
  isActive: boolean
}

export interface FunctionParams {
  roleId: string
}

export interface FunctionAccessParams {
  functionId: string
  roleId: string
  canRead?: boolean
  canCreate?: boolean
  canUpdate?: boolean
  canDelete?: boolean
}

export async function getUserRoleByIdApi(id: number) {
  return useGet<UserRole>(`v1/role/${id}`)
}

export async function getMenuFunc() {
  return useGet<MenuData>('v1/function')
}

export async function getFunctionComboApi() {
  return useGet<FunctionCombo[]>('v1/function')
}

export async function getFunctionRolesApi() {
  return useGet<FunctionRoleItem[]>('v1/function/functional-roles')
}

export async function getFunctionAccessPrivilegeApi(params: FunctionParams) {
  return useGet<FunctionAccessPrivilegeResponse>('v1/function/access-privilege', params)
}

export async function updateAllowedRole(params: UpdateAllowedRoleParams[]) {
  return usePut<any[]>('v1/function/update-allowed-role', params)
}

export async function updateAccessPrivilegeApi(params: FunctionAccessParams) {
  return usePut<any[]>('v1/function/access-privilege', params)
}
