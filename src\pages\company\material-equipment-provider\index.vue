<script lang="ts" setup>
import { ColumnGroupType, ColumnType } from "ant-design-vue/es/table";

const itemsColumns: (ColumnGroupType<any> | ColumnType<any>)[] = [
  {
    title: "No.",
    dataIndex: "no",
    key: "no",
    width: 150,
    align: "center",
  },
  {
    title: "Equipment Name",
    dataIndex: "equipmentName",
    key: "equipmentName",
    width: 150,
    align: "center",
  },
  {
    title: "Contents",
    dataIndex: "contents",
    key: "contents",
    width: 150,
    align: "center",
  },
  {
    title: "Model",
    dataIndex: "model",
    key: "model",
    width: 150,
    align: "center",
  },
  {
    title: "Usage Cost",
    dataIndex: "usageCost",
    key: "usageCost",
    width: 150,
    align: "center",
  },
  {
    title: "Estimated Fuel Consumption",
    dataIndex: "estimatedFuelConsumption",
    key: "estimatedFuelConsumption",
    width: 150,
    align: "center",
  },
  {
    title: "Description",
    dataIndex: "description",
    key: "description",
    width: 150,
    align: "center",
  },
  {
    title: "Equipment Status",
    dataIndex: "equipmentStatus",
    key: "equipmentStatus",
    width: 150,
    align: "center",
  },
];
const invoicesColumns: (ColumnGroupType<any> | ColumnType<any>)[] = [
  {
    title: "Invoice Number",
    dataIndex: "invoiceNumber",
    key: "invoiceNumber",
    width: 150,
    align: "center",
  },
  {
    title: "Invoice Title",
    dataIndex: "invoiceTitle",
    key: "invoiceTitle",
    width: 150,
    align: "center",
  },
  {
    title: "Issue Date",
    dataIndex: "issueDate",
    key: "issueDate",
    width: 150,
    align: "center",
  },
  {
    title: "Payment Date",
    dataIndex: "paymentDate",
    key: "paymentDate",
    width: 150,
    align: "center",
  },
  {
    title: "Billing Details",
    dataIndex: "billingDetails",
    key: "billingDetails",
    width: 150,
    align: "center",
  },
  {
    title: "Usage Cost",
    dataIndex: "usageCost",
    key: "usageCost",
    width: 150,
    align: "center",
  },
  {
    title: "Description",
    dataIndex: "description",
    key: "description",
    width: 150,
    align: "center",
  },
];
</script>

<template>
  <page-container>
    <a-card :bordered="false">
      <div class="flex gap-2 items-center">
        <span class="font-bold text-base">Provider:</span>
        <span>ABC Co., Ltd.</span>
      </div>
      <br />
      <a-divider orientation="left" orientation-margin="0px">
        <span class="font-bold text-base">Company</span>
      </a-divider>
      <div class="px-4">
        <a-row :gap="[12, 12]">
          <a-col :xs="24" :sm="24" :md="12">
            <div class="flex gap-2">
              <span class="font-bold">Official name:</span>
              <span></span>
            </div>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12">
            <div class="flex gap-2">
              <span class="font-bold">Corporate Number</span>
              <span></span>
            </div>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12">
            <div class="flex gap-2">
              <span class="font-bold">Name:</span>
              <span></span>
            </div>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12">
            <div class="flex gap-2">
              <span class="font-bold">Invoice number:</span>
              <span></span>
            </div>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12">
            <div class="flex gap-2">
              <span class="font-bold">Address:</span>
              <span></span>
            </div>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12">
            <div class="flex gap-2">
              <span class="font-bold">Contact Information:</span>
              <span></span>
            </div>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12">
            <div class="flex gap-2">
              <span class="font-bold">Phone number:</span>
              <span></span>
            </div>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12">
            <div class="flex gap-2">
              <span class="font-bold">Business Type:</span>
              <span></span>
            </div>
          </a-col>
        </a-row>
      </div>
      <br />
      <a-divider orientation="left" orientation-margin="0px">
        <span class="font-bold text-base">Items List</span>
      </a-divider>
      <a-table :columns="itemsColumns" :data-source="[]" />
      <br />
      <a-divider orientation="left" orientation-margin="0px">
        <span class="font-bold text-base">Invoice</span>
      </a-divider>
      <a-table :columns="invoicesColumns" :data-source="[]" />
    </a-card>
  </page-container>
</template>
