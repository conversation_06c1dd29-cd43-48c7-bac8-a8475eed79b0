<!-- src/views/Registration.vue -->
<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import type { FormInstance } from 'ant-design-vue'
import {
  LockOutlined,
  MailOutlined,
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import type { RuleObject } from 'ant-design-vue/es/form'
import type { UserAccountParams } from '~@/types/account'
import { createAccount, getOTP } from '~@/api/account/account'

const { t } = useI18n()
const messageNotify = useMessage()

const router = useRouter()
const formRef = ref<FormInstance>()
const currentStep = ref(1)
const loading = ref(false)
const otpDigits = ref(Array(6).fill(''))
const countdown = ref(0)
const otpInputs = ref<HTMLInputElement[]>([])

interface FormState {
  confirmPassword: string
  name: string
  address: string
  phone: string
  gender: boolean
  birthday: string
  email: string
  password: string
  loginId: string
}

const formData = reactive<FormState>({
  name: '',
  address: '',
  phone: '',
  gender: true,
  birthday: '',
  email: '',
  password: '',
  confirmPassword: '',
  loginId: '',
})

const rules = {
  email: [
    { required: true, message: t('message.email') },
    { type: 'email', message: t('message.email_invalid') },
  ],
  loginId: [
    { required: true, message: t('message.loginId') },
  ],
  password: [
    { required: true, message: t('message.password') },
    { min: 6, message: t('message.password_min') },
  ],
  confirmPassword: [
    { required: true, message: t('message.confirmPassword') },
    {
      validator: async (_: any, value: string) => {
        if (value !== formData.password)
          return Promise.reject(new Error(t('message.password_not_match')))
      },
    },
  ],
} as Record<string, RuleObject[]>

const isOTPComplete = computed(() => {
  return otpDigits.value.every(digit => digit !== '')
})

async function handleRegistration() {
  try {
    loading.value = true
    // Call API to register user
    const params = {
      email: formData.email,
    }
    const { status, message } = await getOTP(params)
    if (status === 200) {
      startOTPCountdown()
      currentStep.value += 1
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (error) {
    messageNotify.error(t('message.registration_error'))
  }
  finally {
    loading.value = false
  }
}

async function handleOTPVerification() {
  if (!isOTPComplete.value)
    return

  const otp = otpDigits.value.join('')

  loading.value = true
  const params: UserAccountParams = {
    userInfo: {
      name: formData.name,
      address: formData.address,
      phone: formData.phone,
      gender: formData.gender,
      birthday: formData.birthday ? dayjs(formData.birthday).format('YYYY-MM-DD') : undefined,
    },
    email: formData.email,
    password: formData.password,
    otp,
    loginId: formData.loginId,
  }

  const { status, message } = await createAccount(params)
  if (status === 200) {
    messageNotify.success(message)
    currentStep.value += 1
    loading.value = false
  }
  else {
    messageNotify.error(message)
    loading.value = false
  }
}

function handleOtpInput(event: Event, index: number) {
  const input = event.target as HTMLInputElement
  const value = input.value

  // Ensure only numbers
  otpDigits.value[index] = value.replace(/[^0-9]/g, '')

  // Move to next input if value is entered
  if (value && index < 5)
    otpInputs.value[index + 1].focus()
}

function handleOtpDelete(event: KeyboardEvent, index: number) {
  // Move to previous input on delete if current input is empty
  if (event.key === 'Backspace' && !otpDigits.value[index] && index > 0)
    otpInputs.value[index - 1].focus()
}

function startOTPCountdown() {
  countdown.value = 60 * 15
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value === 0)
      clearInterval(timer)
  }, 1000)
}

async function resendOTP() {
  if (countdown.value > 0)
    return

  try {
    loading.value = true
    // Call API to resend OTP
    const params = {
      email: formData.email,
    }
    const { status, message } = await getOTP(params)
    if (status === 200) {
      messageNotify.success(message)
      startOTPCountdown()
    }
    else {
      messageNotify.error(t('message.registration_error'))
    }
  }
  catch (error) {
    messageNotify.error(t('message.resend_otp_error'))
  }
  finally {
    loading.value = false
  }
}

function redirectToLogin() {
  router.push('/login')
}
</script>

<template>
  <div>
    <a-row align="middle" justify="center" class="min-h-screen bg-gray-50">
      <a-col :xs="23" :sm="20" :md="16" :lg="12" :xl="8">
        <a-card class="shadow-md">
          <a-steps
            :current="currentStep - 1"
            class="mb-8"
          >
            <a-step :title="t('title.registration')" />
            <a-step :title="t('title.otp_verification')" />
            <a-step :title="t('title.complete')" />
          </a-steps>

          <!-- Registration Form -->
          <div v-if="currentStep === 1">
            <a-form
              ref="formRef"
              :colon="true"
              :model="formData"
              :rules="rules"
              layout="horizontal"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 14 }"
              @finish="handleRegistration"
            >
              <a-form-item
                :label="t('form.name')"
                name="userInfo.name"
              >
                <a-input v-model:value="formData.name" :placeholder="t('placeholder.name')" />
              </a-form-item>
              <a-form-item
                :label="t('form.address')"
                name="userInfo.address"
              >
                <a-input v-model:value="formData.address" :placeholder="t('placeholder.address')" />
              </a-form-item>
              <a-form-item
                :label="t('form.phone')"
                name="userInfo.phone"
              >
                <a-input v-model:value="formData.phone" :placeholder="t('placeholder.phone')" />
              </a-form-item>
              <a-form-item
                :label="t('form.birthday')"
                name="userInfo.birthday"
              >
                <a-date-picker
                  v-model:value="formData.birthday"
                  :placeholder="t('placeholder.birthday')"
                  value-format="YYYY-MM-DD"
                />
              </a-form-item>
              <a-form-item
                :label="t('form.gender')"
                name="userInfo.gender"
              >
                <a-radio-group v-model:value="formData.gender">
                  <a-radio :value="true">
                    {{ t('form.male') }}
                  </a-radio>
                  <a-radio :value="false">
                    {{ t('form.female') }}
                  </a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item
                :label="t('form.email')"
                name="email"
              >
                <a-input
                  v-model:value="formData.email"
                  type="email"
                  :placeholder="t('placeholder.email')"
                >
                  <template #prefix>
                    <MailOutlined />
                  </template>
                </a-input>
              </a-form-item>
              <a-form-item
                :label="t('form.loginId')"
                name="loginId"
              >
                <a-input v-model:value="formData.loginId" :placeholder="t('placeholder.loginId')" />
              </a-form-item>

              <a-form-item
                :label="t('form.password')"
                name="password"
              >
                <a-input-password
                  v-model:value="formData.password"
                  :placeholder="t('placeholder.password')"
                >
                  <template #prefix>
                    <LockOutlined />
                  </template>
                </a-input-password>
              </a-form-item>

              <a-form-item
                :label="t('form.confirmPassword')"
                name="confirmPassword"
              >
                <a-input-password
                  v-model:value="formData.confirmPassword"
                  :placeholder="t('placeholder.confirmPassword')"
                >
                  <template #prefix>
                    <LockOutlined />
                  </template>
                </a-input-password>
              </a-form-item>
              <a-form-item
                :wrapper-col="{ span: 14, offset: 6 }"
              >
                <a-button
                  type="primary"
                  html-type="submit"
                  block
                  :loading="loading"
                  class="w-full"
                >
                  {{ t('button.next') }}
                </a-button>
              </a-form-item>
            </a-form>
          </div>

          <!-- OTP Verification -->
          <div v-if="currentStep === 2" class="text-center">
            <p class="mb-4">
              {{ t('message.otp_sent_to_email', { email: formData.email }) }}
            </p>

            <a-space direction="vertical" class="w-full">
              <a-input-group compact class="flex justify-center gap-x-2 mb-4">
                <a-input
                  v-for="(_, index) in 6"
                  :key="index"
                  ref="otpInputs"
                  v-model:value="otpDigits[index]"
                  class="!w-12 text-center"
                  :maxlength="1"
                  @input="handleOtpInput($event, index)"
                  @keydown.delete="handleOtpDelete($event, index)"
                />
              </a-input-group>

              <a-button
                type="primary"
                block
                :loading="loading"
                :disabled="!isOTPComplete"
                @click="handleOTPVerification"
              >
                {{ t('button.verify') }}
              </a-button>

              <a-button
                type="link"
                :disabled="countdown > 0"
                @click="resendOTP"
              >
                {{ t('button.resend') }} {{ countdown > 0 ? `(${countdown}s)` : '' }}
              </a-button>
            </a-space>
          </div>

          <!-- Success Message -->
          <div v-if="currentStep === 3" class="text-center">
            <a-result
              status="success"
              :title="t('message.registration_success')"
              :sub-title="t('message.registration_success_sub')"
            >
              <template #extra>
                <a-button type="primary" @click="redirectToLogin">
                  {{ t('button.login') }}
                </a-button>
              </template>
            </a-result>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<style scoped>
.ant-input {
  @apply text-center;
}
</style>
