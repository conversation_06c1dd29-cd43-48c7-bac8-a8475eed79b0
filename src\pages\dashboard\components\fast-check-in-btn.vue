<script setup lang="ts">
defineProps({
  content: {
    type: String,
    required: true,
    default: '',
  },
})
</script>

<template>
  <div class="flex flex-col items-center justify-center space-y-4">
    <button
      class="px-2 py-1.5 rounded-lg font-bold text-white
              bg-green-600 shadow-lg transform hover:scale-105 min-w-[100px] min-h-[30px]"
    >
      <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping" />
      <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full" />
      {{ content }}
    </button>
  </div>
</template>
