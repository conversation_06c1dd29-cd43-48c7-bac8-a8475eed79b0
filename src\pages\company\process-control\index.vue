<script lang="ts" setup>
import { ColumnGroupType, ColumnType } from "ant-design-vue/es/table";

const processColumns: (ColumnGroupType<any> | ColumnType<any>)[] = [
  {
    title: "Code",
    dataIndex: "code",
    key: "code",
    width: 150,
    align: "center",
  },
  {
    title: "Category",
    dataIndex: "category",
    key: "category",
    width: 150,
    align: "center",
  },
  {
    title: "Process Name",
    dataIndex: "processName",
    key: "processName",
    width: 150,
    align: "center",
  },
  {
    title: "Valid From",
    dataIndex: "validFrom",
    key: "validFrom",
    width: 150,
    align: "center",
  },
  {
    title: "Valid To",
    dataIndex: "validTo",
    key: "validTo",
    width: 150,
    align: "center",
  },
  {
    title: "Description",
    dataIndex: "description",
    key: "description",
    width: 150,
    align: "center",
  },
];
</script>

<template>
  <page-container>
    <a-card :bordered="false">
      <a-divider orientation="left" orientation-margin="0px">
        <span class="font-bold text-base">Process List</span>
      </a-divider>
      <a-form>
        <a-row :gutter="[12, 12]" justify="end">
          <a-col flex="auto">
            <a-row :gutter="[12, 12]">
              <a-col :xs="24" :sm="12" :md="12" :xl="8" :lg="8">
                <a-form-item label="Code">
                  <a-input />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="12" :xl="8" :lg="8">
                <a-form-item label="Process Name">
                  <a-input />
                </a-form-item>
              </a-col>
            </a-row>
          </a-col>
          <a-col flex="100px" class="flex justify-center">
            <a-button type="primary">Search</a-button>
          </a-col>
        </a-row>
      </a-form>
      <a-table :columns="processColumns" :data-source="[]" />
    </a-card>
  </page-container>
</template>
