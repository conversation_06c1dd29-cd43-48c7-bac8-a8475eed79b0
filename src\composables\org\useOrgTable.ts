import { usePagination } from 'vue-request'
import { getOrgsByCurrentAccountApi } from '~@/api/company/org'

export function useOrgTable() {
  const {
    data: orgDataSource,
    current,
    totalPage,
    loading,
    pageSize,
    run,
  } = usePagination(getOrgsByCurrentAccountApi, {
    pagination: {
      currentKey: 'pageNum',
      pageSizeKey: 'pageSize',
      totalKey: 'totalRecords',
    },
  })

  const pagination = computed(() => ({
    total: totalPage.value,
    current: current.value,
    pageSize: pageSize.value,
  }))

  return {
    orgDataSource,
    loading,
    pagination,
    run,
  }
}
