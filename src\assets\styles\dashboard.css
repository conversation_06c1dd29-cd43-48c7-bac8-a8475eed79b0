/********************** local *******************/

/* chia khung man hinh  */
/* .ant-layout .ant-layout-content{
  padding-top: 54px;
  display: flex;
  justify-content: space-between;
  margin: 0 auto;
  max-width: 1616px;
  padding: 36px 20px 0 20px;
} */

/* check in area  */

/* .ant-select-selector {
  height: 48px; 
  display: flex;
}
.ant-card-bordered {
  border: none;
} */

/* .ant-card .ant-card-head{
  padding-left: 10px!important;
}

.ant-card .ant-card-head-title {
  font-size: 24px!important;
  color: #101F23 !important;
} */

/* .ant-select-selector {
  color: #74797A !important;
  border: 1px solid #74797A !important;
  background-color: #fff;
}

.ant-select-single .ant-select-selector .ant-select-selection-placeholder{
  color: #74797A !important;
} */


/* .ant-btn-default{
  color: #74797A  !important;
  border: 1px solid #74797A !important;
}

.ant-pro-basicLayout .ant-btn.ant-btn-block{
  color: #74797A;
  border: 1px solid #B7B9B8 !important;
  padding: 0px;
} */


/* .ant-pro-basicLayout .ant-card-head{
  margin-bottom: 12px;
}
.ant-carousel :deep(.slick-slide) {
  text-align: center;
  line-height: 20px;
  overflow: hidden;
}

.ant-carousel .slick-dots {
  bottom: -50px;
}

.ant-carousel .slick-dots li{
 width: 19px !important;
}

.ant-carousel .slick-dots li {
  width: 19px !important;
  margin: 0 8px !important;
}

.ant-carousel .slick-dots li button{
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  background-color: #E4E4E2;
}

.ant-carousel .slick-dots li.slick-active button {
  background-color: #F99649;
}

.ant-carousel .slick-dots-bottom{
  bottom: -50px;
} */


/* total block area */



/* Dashboad table */ 
/* .ant-pro-basicLayout .ant-table-wrapper .ant-table-thead > tr > th {
  font-size: 16px !important;
  padding: 10px 10px !important;
  color: #0565B7 !important;
  background-color: #DEF0FF !important;
  font-weight: 500;
  border: none!important;
}

.ant-pro-basicLayout .ant-table-wrapper {
  border-radius: 0 0 10px 10px; 
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); 
}

.ant-pro-basicLayout .ant-table-wrapper .ant-table.ant-table-bordered .ant-table-tbody > tr > td:first-child{
  border-inline-start: none!important;
}

.ant-pro-basicLayout .ant-table-wrapper .ant-table.ant-table-bordered .ant-table-tbody > tr > td{
  font-size: 16px;
  color: #08223B;
  font-weight: 500;
  text-align: center;
  border-inline-end: none!important;
  padding: 10px 10px !important;
}

.ant-table-wrapper .ant-table.ant-table-bordered >.ant-table-container >.ant-table-header >table{
  border-top: none;
}

.ant-tag {
  font-size: 16px!important;
} */


/* .ant-card .ant-card-head-title{
  font-size: 18px;
  color: #848484;
} */

/* .ant-pro-basicLayout .ant-card .ant-card-body{
  padding: 0px !important;
}

.ant-table-wrapper td.ant-table-column-sort {
background-color: #fff;
} */

/* .ant-card .ant-card-meta-detail >div:not(:last-child){
  margin-bottom: 0px;
} */
