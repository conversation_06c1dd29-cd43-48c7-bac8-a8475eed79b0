<script lang="ts" setup>
import { CheckOutlined, CloseOutlined, EditOutlined } from '@ant-design/icons-vue'
import { updateConstructionCostApi } from '~@/api/construction-cost'
import type { ConstructionCostItem, ConstructionCostParams, CostData, HumanCategorizedCost } from '~@/api/construction-cost'
import { getInputCostItemApi } from '~@/api/input-cost-item'
import type { InputCostItem, InputCostItemQueryParams } from '~@/api/input-cost-item'

const props = defineProps({
  constructionCost: {
    type: Object as () => ConstructionCostItem,
    default: () => ({}),
  },
  lastAccumulateCost: {
    type: Object as () => CostData,
    default: () => ({}),
  },
  currentAccumulateCost: {
    type: Object as () => CostData,
    default: () => ({}),
  },
  currentCost: {
    type: Object as () => CostData,
    default: () => ({}),
  },
})

const { t } = useI18n()
const messageNotify = useMessage()

// State
const riskAmountDraftIsEditing = ref(false)
const riskAmountDraft = ref(0)
const lastEmployeeCategoryData = ref<InputCostItem[]>([])
const currentEmployeeCategoryData = ref<InputCostItem[]>([])
const accumulatedEmployeeCategoryData = ref<InputCostItem[]>([])

const lastAccumulateCost = computed(() => props.lastAccumulateCost)
const currentCost = computed(() => props.currentCost)
const currentAccumulateCost = computed(() => props.currentAccumulateCost)
// Mock categorized costs data (based on the image)
const humanPreviousCostCategories = computed(() => props.lastAccumulateCost.humanCategorizedCosts)
const humanCurrentCostCategories = computed(() => props.currentCost.humanCategorizedCosts)
const humanAccumulatedCostCategories = computed(() => props.currentAccumulateCost.humanCategorizedCosts)

// InHuman categorized costs
const inHumanPreviousCostCategories = computed(() => props.lastAccumulateCost.inHumanCategorizedCosts)
const inHumanCurrentCostCategories = computed(() => props.currentCost.inHumanCategorizedCosts)
const inHumanAccumulatedCostCategories = computed(() => props.currentAccumulateCost.inHumanCategorizedCosts)

// Combined categorized costs for display with type indicator
const combinedPreviousCostCategories = computed(() => [
  ...(humanPreviousCostCategories.value || []).map(item => ({ ...item, costType: 'Human' })),
  ...(inHumanPreviousCostCategories.value || []).map(item => ({ ...item, costType: 'InHuman' })),
])
const combinedCurrentCostCategories = computed(() => [
  ...(humanCurrentCostCategories.value || []).map(item => ({ ...item, costType: 'Human' })),
  ...(inHumanCurrentCostCategories.value || []).map(item => ({ ...item, costType: 'InHuman' })),
])
const combinedAccumulatedCostCategories = computed(() => [
  ...(humanAccumulatedCostCategories.value || []).map(item => ({ ...item, costType: 'Human' })),
  ...(inHumanAccumulatedCostCategories.value || []).map(item => ({ ...item, costType: 'InHuman' })),
])

// Expanded data structure with subcategories
const expandedPreviousCostCategories = computed(() => {
  const result: any[] = []
  combinedPreviousCostCategories.value.forEach((category) => {
    result.push({
      ...category,
      key: category.categoryId,
      isSubCategory: false,
    })
    if (category.subCategories && category.subCategories.length > 0) {
      category.subCategories.forEach((subCategory) => {
        result.push({
          ...subCategory,
          key: `${category.categoryId}-${subCategory.subCategoryId}`,
          parentCategoryId: category.categoryId,
          categoryName: subCategory.subCategoryName,
          totalAmount: subCategory.amount,
          totalAvgAmount: subCategory.avgAmount,
          isSubCategory: true,
        })
      })
    }
  })
  return result
})

const expandedCurrentCostCategories = computed(() => {
  const result: any[] = []
  combinedCurrentCostCategories.value.forEach((category) => {
    result.push({
      ...category,
      key: category.categoryId,
      isSubCategory: false,
    })
    if (category.subCategories && category.subCategories.length > 0) {
      category.subCategories.forEach((subCategory) => {
        result.push({
          ...subCategory,
          key: `${category.categoryId}-${subCategory.subCategoryId}`,
          parentCategoryId: category.categoryId,
          categoryName: subCategory.subCategoryName,
          totalAmount: subCategory.amount,
          totalAvgAmount: subCategory.avgAmount,
          isSubCategory: true,
        })
      })
    }
  })
  return result
})

const expandedAccumulatedCostCategories = computed(() => {
  const result: any[] = []
  combinedAccumulatedCostCategories.value.forEach((category) => {
    result.push({
      ...category,
      key: category.categoryId,
      isSubCategory: false,
    })
    if (category.subCategories && category.subCategories.length > 0) {
      category.subCategories.forEach((subCategory) => {
        result.push({
          ...subCategory,
          key: `${category.categoryId}-${subCategory.subCategoryId}`,
          parentCategoryId: category.categoryId,
          categoryName: subCategory.subCategoryName,
          totalAmount: subCategory.amount,
          totalAvgAmount: subCategory.avgAmount,
          isSubCategory: true,
        })
      })
    }
  })
  return result
})

// Columns definition
const previousColumns = [
  {
    title: '',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: '40%',
  },
  // {
  //   title: '',
  //   dataIndex: 'quantity',
  //   key: 'quantity',
  //   width: '20%',
  // },
  {
    title: '',
    dataIndex: 'totalAmount',
    key: 'totalAmount',
    width: '25%',
    align: 'right' as const,
    // customRender: ({ text }: { text: number }) => `¥${formatNumber(text)}`,
  },
  {
    title: '構成比',
    dataIndex: 'percentage',
    key: 'percentage',
    width: '15%',
    align: 'right' as const,
  },
]

const currentColumns = [...previousColumns]
const accumulatedColumns = [...previousColumns]

// Format number with commas
function formatNumber(num: number) {
  if (!num)
    return 0
  return num.toLocaleString()
}

// Get CSS class based on percentage value
// function getPercentageClass(percentage: number) {
//   if (percentage > 100)
//     return 'text-red-600 font-bold'
//   if (percentage > 50)
//     return 'text-blue-600'
//   return ''
// }

const requestAmountDraftIsEditing = ref(false)
const retentionAmountDraftIsEditing = ref(false)
const releasedAmountDraftIsEditing = ref(false)
const releasedAmountDraft = ref(0)
const requestAmountDraft = ref(0)
const retentionAmountDraft = ref(0)

function editRetentionAmountDraft() {
  // TODO
  retentionAmountDraftIsEditing.value = true
}
function saveRetentionAmountDraft() {
  // TODO
  retentionAmountDraftIsEditing.value = false
}

function cancelRetentionAmountDraft() {
  // TODO
  retentionAmountDraftIsEditing.value = false
}

function editReleasedAmountDraft() {
  // TODO
  releasedAmountDraftIsEditing.value = true
}
function saveReleasedAmountDraft() {
  // TODO
  releasedAmountDraftIsEditing.value = false
}

function cancelReleasedAmountDraft() {
  // TODO
  releasedAmountDraftIsEditing.value = false
}

function editRequestAmountDraft() {
  // TODO
  requestAmountDraftIsEditing.value = true
}

async function saveRequestAmountDraft() {
  const params: ConstructionCostParams = {
    riskAmount: riskAmountDraft.value,
    requestAmount: requestAmountDraft.value,
    retentionAmount: retentionAmountDraft.value,
    releaseAmount: releasedAmountDraft.value,
  }
  const { status, message } = await updateConstructionCostApi(props.constructionCost.constructionId, params)
  if (status === 200)
    requestAmountDraftIsEditing.value = false

  else
    messageNotify.error(message)
}

function cancelRequestAmountDraft() {
  // TODO
  requestAmountDraftIsEditing.value = false
}

function getPercentageValue(totalAmount: number, total: number) {
  if (!totalAmount || !total)
    return '0%'
  const result = ((totalAmount / total) * 100).toFixed(2)
  return `${result}%`
}

function getPercentageValueDraft(totalAmount: number, total: number) {
  if (!totalAmount || !total)
    return '0%'
  const result = ((totalAmount / total) * 100).toFixed(2)
  return `${result}%`
}

async function saveRiskAmountDraft() {
  const params: ConstructionCostParams = {
    riskAmount: riskAmountDraft.value,
    requestAmount: requestAmountDraft.value,
    retentionAmount: retentionAmountDraft.value,
    releaseAmount: releasedAmountDraft.value,
  }
  const { status, message } = await updateConstructionCostApi(props.constructionCost.constructionId, params)
  if (status === 200) {
    requestAmountDraftIsEditing.value = false
    riskAmountDraftIsEditing.value = false
  }
  else {
    messageNotify.error(message)
  }
}

function cancelRiskAmountDraft() {
  riskAmountDraftIsEditing.value = false
}

function editRiskAmountDraft() {
  riskAmountDraftIsEditing.value = true
}

async function fetchEmployeeCategory(costData?: CostData) {
  const employeeCategoryId = costData?.humanCategorizedCosts?.find((item: HumanCategorizedCost) => item.categoryId === '018f2b3e-1b7a-7c3e-8e1a-3e4f5e6a7b8c')?.categoryId
  const constructionId = props.constructionCost?.constructionId
  // const dateFrom = parseDate(costData?.reportFrom)
  // const dateTo = parseDate(costData?.reportTo)
  const params: InputCostItemQueryParams = {
    constructionId,
    pageNum: 1,
    pageSize: 100,
  }
  if (!employeeCategoryId)
    return
  try {
    const { data, status, message } = await getInputCostItemApi(employeeCategoryId, params)
    if (status === 200)
      return data?.items ?? []

    else
      messageNotify.error(message)
  }
  catch (error) {
  }
}

async function initEmployeeCategoryData() {
  lastEmployeeCategoryData.value = await fetchEmployeeCategory(props.lastAccumulateCost) ?? []
  currentEmployeeCategoryData.value = await fetchEmployeeCategory(props.currentCost) ?? []
  accumulatedEmployeeCategoryData.value = await fetchEmployeeCategory(props.currentAccumulateCost) ?? []
}

watch(() => props.constructionCost, () => {
  if (!props.constructionCost?.constructionId)
    return
  initEmployeeCategoryData()
}, { immediate: true })

// Lifecycle
onMounted(() => {
  // fetchData();
  // initEmployeeCategoryData()
})
</script>

<template>
  <div class="p-4">
    <!-- Header -->
    <div class="flex justify-between items-center mb-4">
      <h1 v-if="constructionCost.isPrimary" class="text-xl font-bold">
        {{ t('mainConstructionCostSummary') }}
      </h1>
      <h1 v-else class="text-xl font-bold">
        {{ t('subConstructionCostSummary') }}
      </h1>
    </div>

    <!-- Main Content -->
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-4">
      <!-- Previous Period -->
      <a-card :bordered="true" class="border-blue-500">
        <template #title>
          <div class="text-center font-bold">
            {{ t('previousMiscellaneousExpenses') }}
          </div>
        </template>
        <a-table
          :data-source="expandedPreviousCostCategories"
          :columns="previousColumns"
          :pagination="false"
          size="small"
          :bordered="true"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'categoryName'">
              <div v-if="record.isSubCategory" class="flex justify-between pl-6 text-gray-600 text-sm">
                <span>├── {{ record.categoryName }}</span>
                <div v-if="record.workload !== null" class="flex gap-2">
                  <span>{{ record.workload }}</span>
                  <span>{{ t('manDay') }}</span>
                </div>
              </div>
              <div v-else-if="record.categoryCode === 'EMPLOYEE' || record.categoryCode === 'OUTSOURCE_DAILY'" class="flex justify-between font-medium">
                <span>{{ record.categoryName }}</span>
                <div class="flex gap-2">
                  <span>{{ record.quantity }}</span>
                  <span>{{ t('manDay') }}</span>
                </div>
              </div>
              <div v-else class="font-medium">
                {{ record.categoryName }}
              </div>
            </template>
            <template v-if="column.dataIndex === 'totalAmount'">
              <div :class="{ 'text-gray-600 text-sm': record.isSubCategory, 'font-medium': !record.isSubCategory }">
                ¥{{ formatNumber(record.totalAvgAmount) }}
              </div>
            </template>
            <template v-if="column.dataIndex === 'percentage'">
              <div :class="{ 'text-gray-600 text-sm': record.isSubCategory, 'font-medium': !record.isSubCategory }">
                {{ getPercentageValue(record?.totalAvgAmount ?? 0, lastAccumulateCost?.requestAmount ?? 0) }}
              </div>
            </template>
          </template>
        </a-table>
        <div class="mt-2 ml-2 border-t pt-2">
          <div class="grid grid-cols-5 gap-2">
            <div class="font-bold text-left col-span-2">
              {{ t('riskAmount') }}
            </div>
            <div class="flex items-center gap-2 justify-end text-right col-span-2 col-start-3">
              <span>¥{{ formatNumber(lastAccumulateCost?.riskAmount ?? 0) }}</span>
            </div>
            <div class="text-right">
              <span>
                {{ getPercentageValue(lastAccumulateCost?.riskAmount ?? 0, lastAccumulateCost?.requestAmount ?? 0) }}
              </span>
            </div>
          </div>
        </div>
        <div class="mt-2 border-t pt-2 ml-2">
          <div class="grid grid-cols-5 gap-2">
            <div class="font-bold text-left col-start-1 col-span-2">
              {{ t('construction-cost.totalCost') }}
            </div>
            <div class="text-right col-span-2 col-start-3">
              ¥{{ formatNumber(lastAccumulateCost.totalCost) }}
            </div>
            <div class="text-right">
              <span>
                {{ getPercentageValue(lastAccumulateCost.riskAmount, lastAccumulateCost?.requestAmount ?? 0) }}
              </span>
            </div>
          </div>
        </div>
        <div class="mt-2 border-t pt-2">
          <div class="grid grid-cols-2 gap-2">
            <div class="font-bold">
              {{ t('requestAmount') }}
            </div>
            <div class="text-right text-red-600">
              ¥{{ formatNumber(lastAccumulateCost?.requestAmount) }}
            </div>

            <!-- <div class="font-bold">
              {{ t('requestAmount') }}
            </div> -->
            <!-- <div class="text-right" /> -->

            <div class="font-bold">
              {{ t('retentionAmount') }}
            </div>
            <div class="text-right">
              ¥{{ formatNumber(lastAccumulateCost?.retentionAmount) }}
            </div>

            <div class="border-t pt-2 font-bold">
              {{ t('releasedAmount') }}
            </div>
            <div class="border-t pt-2 text-right">
              ¥{{ formatNumber(lastAccumulateCost?.releasedAmount) }}
            </div>

            <div class="font-bold">
              {{ t('profitByRequestedAmount') }}
            </div>
            <div class="text-right text-red-600">
              ¥{{ formatNumber(lastAccumulateCost?.profitByRequestedAmount ?? 0) }}
            </div>

            <div class="font-bold">
              {{ t('profitByClaimedAmount') }}
            </div>
            <div class="text-right text-red-600">
              ¥{{ formatNumber(lastAccumulateCost?.profitByClaimedAmount ?? 0) }}
            </div>
          </div>
        </div>
      </a-card>

      <!-- Current Period -->
      <a-card :bordered="true" class="border-yellow-500">
        <template #title>
          <div class="text-center font-bold">
            {{ t('currentMiscellaneousExpenses') }}
          </div>
        </template>
        <a-table
          :data-source="expandedCurrentCostCategories"
          :columns="currentColumns"
          :pagination="false"
          size="small"
          :bordered="true"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'categoryName'">
              <div v-if="record.isSubCategory" class="flex justify-between pl-6 text-gray-600 text-sm">
                <span>├── {{ record.categoryName }}</span>
                <div v-if="record.workload !== null" class="flex gap-2">
                  <span>{{ record.workload }}</span>
                  <span>{{ t('manDay') }}</span>
                </div>
              </div>
              <div v-else-if="record.categoryCode === 'EMPLOYEE' || record.categoryCode === 'OUTSOURCE_DAILY'" class="flex justify-between font-medium">
                <span>{{ record.categoryName }}</span>
                <div class="flex gap-2">
                  <span>{{ record.quantity }}</span>
                  <span>{{ t('manDay') }}</span>
                </div>
              </div>
              <div v-else class="font-medium">
                {{ record.categoryName }}
              </div>
            </template>
            <template v-if="column.dataIndex === 'totalAmount'">
              <div :class="{ 'text-gray-600 text-sm': record.isSubCategory, 'font-medium': !record.isSubCategory }">
                ¥{{ formatNumber(record.totalAvgAmount) }}
              </div>
            </template>
            <template v-if="column.dataIndex === 'percentage'">
              <div v-if="!requestAmountDraftIsEditing" :class="{ 'text-gray-600 text-sm': record.isSubCategory, 'font-medium': !record.isSubCategory }">
                {{ getPercentageValue(record.totalAmount, currentCost?.requestAmount ?? 0) }}
              </div>
              <div v-else :class="{ 'text-gray-600 text-sm': record.isSubCategory, 'font-medium text-blue': !record.isSubCategory }">
                {{ getPercentageValueDraft(record.totalAmount, requestAmountDraft) }}
              </div>
            </template>
          </template>
        </a-table>

        <div class="mt-2 ml-2 border-t pt-2">
          <div class="grid grid-cols-5 gap-2">
            <div class="font-bold text-left col-span-2">
              {{ t('riskAmount') }}
            </div>
            <div v-if="riskAmountDraftIsEditing" class="flex items-center gap-2 col-span-2 col-start-3">
              <a-input v-model:value="riskAmountDraft" type="number" />
              <CheckOutlined class="text-blue cursor-pointer" @click="saveRiskAmountDraft" />
              <CloseOutlined class="text-red cursor-pointer" @click="cancelRiskAmountDraft" />
            </div>
            <div v-else class="flex items-center gap-2 justify-end text-right col-span-2 col-start-3">
              <span>¥{{ formatNumber(currentCost?.riskAmount ?? 0) }}</span>
              <EditOutlined class="text-blue cursor-pointer" @click="editRiskAmountDraft" />
            </div>
            <div class="text-right">
              <span v-if="!riskAmountDraftIsEditing">
                {{ getPercentageValue(currentCost.riskAmount, currentCost?.requestAmount ?? 0) }}
              </span>
              <span v-else class="text-blue">
                {{ getPercentageValue(riskAmountDraft, currentCost?.requestAmount ?? 0) }}
              </span>
            </div>
          </div>
        </div>
        <div class="mt-2 border-t pt-2 ml-2">
          <div class="grid grid-cols-5 gap-2">
            <div class="font-bold text-left col-start-1 col-span-2">
              {{ t('construction-cost.totalCost') }}
            </div>
            <div class="text-right col-span-2 col-start-3">
              ¥{{ formatNumber(currentCost.totalCost) }}
            </div>
            <div class="text-right">
              <span>
                {{ getPercentageValue(currentCost?.riskAmount ?? 0, currentCost?.requestAmount ?? 0) }}
              </span>
            </div>
          </div>
        </div>

        <div class="mt-2 border-t pt-2">
          <div class="grid grid-cols-2 gap-2">
            <div class="font-bold">
              {{ t('currentMonthEstimatedAmount') }}
            </div>
            <div v-if="requestAmountDraftIsEditing" class="flex items-center gap-2">
              <a-input v-model:value="requestAmountDraft" type="number" />
              <CheckOutlined class="text-blue cursor-pointer" @click="saveRequestAmountDraft" />
              <CloseOutlined class="text-red cursor-pointer" @click="cancelRequestAmountDraft" />
            </div>
            <div v-else class="flex items-center gap-2 justify-end">
              <span>¥{{ formatNumber(Number(requestAmountDraft)) }}</span>
              <EditOutlined class="text-blue cursor-pointer" @click="editRequestAmountDraft" />
            </div>

            <div class="font-bold">
              {{ t('currentMonthConfirmedAmount') }}
            </div>
            <div class="text-right text-yellow-600">
              ¥{{ formatNumber(currentCost?.requestAmount) }}
            </div>

            <div class="font-bold">
              {{ t('retentionAmount') }}
            </div>
            <div class="text-right">
              <div v-if="retentionAmountDraftIsEditing" class="flex items-center gap-2">
                <a-input v-model:value="retentionAmountDraft" type="number" />
                <CheckOutlined class="text-blue cursor-pointer" @click="saveRetentionAmountDraft" />
                <CloseOutlined class="text-red cursor-pointer" @click="cancelRetentionAmountDraft" />
              </div>
              <div v-else class="flex items-center gap-2 justify-end">
                <span>¥{{ formatNumber(Number(retentionAmountDraft)) }}</span>
                <EditOutlined class="text-blue cursor-pointer" @click="editRetentionAmountDraft" />
              </div>
            </div>

            <div class="font-bold">
              {{ t('releasedAmount') }}
            </div>
            <div v-if="releasedAmountDraftIsEditing" class="text-right text-blue flex items-center gap-2">
              <a-input v-model:value="releasedAmountDraft" type="number" />
              <CheckOutlined class="text-blue cursor-pointer" @click="saveReleasedAmountDraft" />
              <CloseOutlined class="text-red cursor-pointer" @click="cancelReleasedAmountDraft" />
            </div>
            <div v-else class="flex justify-end items-center gap-2">
              <span>¥{{ formatNumber(currentCost?.releasedAmount) }}</span>
              <EditOutlined class="text-blue cursor-pointer" @click="editReleasedAmountDraft" />
            </div>

            <div class="font-bold">
              {{ t('amountExcludingRetention') }}
            </div>
            <div class="text-right">
              ¥{{ formatNumber(currentCost?.retentionAmount) }}
            </div>

            <div class="font-bold">
              {{ t('profitByRequestedAmount') }}
            </div>
            <div class="text-right">
              ¥{{ formatNumber(currentCost?.profitByRequestedAmount ?? 0) }}
            </div>

            <div class="font-bold">
              {{ t('profitByClaimedAmount') }}
            </div>
            <div class="text-right">
              ¥{{ formatNumber(currentCost?.profitByClaimedAmount ?? 0) }}
            </div>
          </div>
        </div>
      </a-card>

      <!-- Accumulated Total -->
      <a-card :bordered="true" class="border-green-500">
        <template #title>
          <div class="text-center font-bold">
            {{ t('accumulatedMiscellaneousCosts') }}
          </div>
        </template>
        <a-table
          :data-source="expandedAccumulatedCostCategories"
          :columns="accumulatedColumns"
          :pagination="false"
          size="small"
          :bordered="true"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'categoryName'">
              <div v-if="record.isSubCategory" class="flex justify-between pl-6 text-gray-600 text-sm">
                <span>├── {{ record.categoryName }}</span>
                <div v-if="record.workload !== null" class="flex gap-2">
                  <span>{{ record.workload }}</span>
                  <span>{{ t('manDay') }}</span>
                </div>
              </div>
              <div v-else-if="record.categoryCode === 'EMPLOYEE' || record.categoryCode === 'OUTSOURCE_DAILY'" class="flex justify-between font-medium">
                <span>{{ record.categoryName }}</span>
                <div class="flex gap-2">
                  <span>{{ record.quantity }}</span>
                  <span>{{ t('manDay') }}</span>
                </div>
              </div>
              <div v-else class="font-medium">
                {{ record.categoryName }}
              </div>
            </template>
            <template v-if="column.dataIndex === 'totalAmount'">
              <div :class="{ 'text-gray-600 text-sm': record.isSubCategory, 'font-medium': !record.isSubCategory }">
                ¥{{ formatNumber(record.totalAvgAmount) }}
              </div>
            </template>
            <template v-if="column.dataIndex === 'percentage'">
              <div :class="{ 'text-gray-600 text-sm': record.isSubCategory, 'font-medium': !record.isSubCategory }">
                {{ getPercentageValue(record?.totalAvgAmount ?? 0, currentAccumulateCost?.requestAmount ?? 0) }}
              </div>
            </template>
          </template>
        </a-table>
        <div class="mt-2 ml-2 border-t pt-2">
          <div class="grid grid-cols-5 gap-2">
            <div class="font-bold text-left col-span-2">
              {{ t('riskAmount') }}
            </div>
            <div class="flex items-center gap-2 justify-end text-right col-span-2 col-start-3">
              <span>¥{{ formatNumber(lastAccumulateCost?.riskAmount ?? 0) }}</span>
            </div>
            <div class="text-right">
              <span>
                {{ getPercentageValue(lastAccumulateCost?.riskAmount ?? 0, lastAccumulateCost?.requestAmount ?? 0) }}
              </span>
            </div>
          </div>
        </div>
        <div class="mt-2 border-t pt-2 ml-2">
          <div class="grid grid-cols-5 gap-2">
            <div class="font-bold text-left col-start-1 col-span-2">
              {{ t('construction-cost.totalCost') }}
            </div>
            <div class="text-right col-span-2 col-start-3">
              ¥{{ formatNumber(currentAccumulateCost.totalCost) }}
            </div>
            <div class="text-right">
              <span>
                {{ getPercentageValue(currentAccumulateCost.riskAmount, currentAccumulateCost?.requestAmount ?? 0) }}
              </span>
            </div>
          </div>
        </div>
        <div class="mt-2 border-t pt-2">
          <div class="grid grid-cols-2 gap-2">
            <div class="font-bold">
              {{ t('requestAmount') }}
            </div>
            <div class="text-right text-red-600">
              ¥{{ formatNumber(currentAccumulateCost?.requestAmount) }}
            </div>

            <div class="font-bold">
              {{ t('retentionAmount') }}
            </div>
            <div class="text-right" />

            <div class="font-bold">
              {{ t('releasedAmount') }}
            </div>
            <div class="text-right text-red-600">
              ¥{{ formatNumber(currentAccumulateCost?.totalClaimedAmount) }}
            </div>

            <div class="font-bold">
              {{ t('accumulatedRetentionAmount') }}
            </div>
            <div class="text-right text-red-600">
              ¥{{ formatNumber(currentAccumulateCost?.releasedAmount) }}
            </div>

            <div class="font-bold">
              {{ t('profitByRequestedAmount') }}
            </div>
            <div class="text-right text-red-600">
              ¥{{ formatNumber(currentAccumulateCost?.profitByRequestedAmount ?? 0) }}
            </div>

            <div class="font-bold">
              {{ t('profitByClaimedAmount') }}
            </div>
            <div class="text-right text-red-600">
              ¥{{ formatNumber(currentAccumulateCost?.profitByClaimedAmount ?? 0) }}
            </div>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

  <style scoped>
  /* You can add any additional custom styling here */
  .ant-table-cell {
    padding: 4px 8px !important;
  }
  </style>
