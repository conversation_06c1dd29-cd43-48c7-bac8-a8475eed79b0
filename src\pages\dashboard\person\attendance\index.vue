<!-- eslint-disable style/space-before-blocks -->
<!-- eslint-disable curly -->
<!-- eslint-disable antfu/top-level-function -->
<script setup lang="ts">
import dayjs from 'dayjs'
import { usePagination } from 'vue-request'
import { QuestionCircleOutlined } from '@ant-design/icons-vue'
import type { TourProps } from 'ant-design-vue'
import TotalTimeSection from '../../components/total-time-section.vue'
import AttendanceTable from '~/pages/dashboard/components/attendance-table.vue'
import TodayAttendanceSection from '~/pages/dashboard/components/today-attendance-section.vue'
import DashboardClock from '~/pages/dashboard/components/dashboard-clock.vue'
import type { AttendanceItem, AttendanceSearchParams, AttendanceUpdateParams, CheckInParams, CheckOutParams, FastCheckInParams } from '~@/api/attendance'
import { checkInApi, checkInByShiftIdApi, getAttendanceByDateApi, sendRequestByAttendanceItem, updateA<PERSON><PERSON><PERSON><PERSON>, updateBreakInApi, updateBreakOutA<PERSON>, updateC<PERSON><PERSON>outA<PERSON> } from '~@/api/attendance'
import TourButton from '~@/components/common/TourButton.vue'
import logger from '~@/utils/logger'

defineOptions({
  name: 'Workplace',
})
const { t } = useI18n()
const message = useMessage()

// State Variables
const selectedDate = ref<string>(dayjs().format('YYYY-MM-DD'))
const startOfWeek = computed(() =>
  dayjs(selectedDate.value).subtract(5, 'days').format('YYYY-MM-DD'),
)
const endOfWeek = computed(() => dayjs(selectedDate.value).add(1, 'days').format('YYYY-MM-DD'))

// const currentAttendance = ref<AttendanceItem>()
const attendanceTableData = ref<AttendanceItem[]>([])
const currentIndex = ref(0)

const currentTime = ref(dayjs())
const today = ref(dayjs())
const messageNotify = useMessage()
const todayAttendanceData = ref<AttendanceItem[]>([])
const currentTour = ref(null)
const isTourOpen = ref(false)
const dashboardClockRef = ref<InstanceType<typeof DashboardClock> | null>(null)
const todayAttendanceSectionRef = ref<InstanceType<typeof TodayAttendanceSection> | null>(null)
const attendanceTableRef = ref<InstanceType<typeof AttendanceTable> | null>(null)
const totalTimeSectionRef = ref<InstanceType<typeof TotalTimeSection> | null>(null)
const tourSteps: TourProps['steps'] = [
  {
    title: t('dashboard.clock'),
    description: t('tour.dashboard.clock.description'),
    placement: 'right',
    target: () => dashboardClockRef.value?.$el,
  },
  {
    title: t('dashboard.todayAttendance'),
    description: t('tour.dashboard.todayAttendance.description'),
    placement: 'right',
    target: () => todayAttendanceSectionRef.value?.$el,
  },
  {
    title: t('dashboard.totalTime'),
    description: t('tour.dashboard.totalTime.description'),
    placement: 'right',
    target: () => totalTimeSectionRef.value?.$el,
  },
  {
    title: t('dashboard.attendanceTable'),
    description: t('tour.dashboard.attendanceTable.description'),
    placement: 'right',
    target: () => attendanceTableRef.value?.$el,
  },
] as TourProps['steps']

const totalWorktimeOfDay = computed(() => {
  return todayAttendanceData.value.reduce((total, item) => {
    return total + (item.totalWorkTime ?? 0)
  }, 0)
})
const totalOvertimeOfDay = computed(() => {
  return todayAttendanceData.value.reduce((total, item) => {
    return total + (item.totalOverTime ?? 0)
  }, 0)
})

setInterval(() => {
  today.value = dayjs()
  currentTime.value = dayjs()
}, 1000)

const params: AttendanceSearchParams = {
  fromDate: startOfWeek.value,
  toDate: endOfWeek.value,
  pageNum: 1,
  pageSize: 50,
}
const initTodayAttendanceData = (data: AttendanceItem[]) => {
  if (selectedDate.value !== today.value.format('YYYY-MM-DD')) {
    return
  }
  const employeeShift = data.filter((item: AttendanceItem) => item.workingDate === today.value.format('YYYY-MM-DD'))
  if (employeeShift) {
    todayAttendanceData.value = employeeShift
    todayAttendanceData.value.sort((a: AttendanceItem, b: AttendanceItem) => {
      return (a.createTime ?? '').localeCompare(b.createTime ?? '') * -1
    })
  }
}

const attendanceTableDataMap = ref<Map<string, AttendanceItem[]>>(new Map())

const createAttendanceTableDataMap = (data: AttendanceItem[]) => {
  const existingData = new Map<string, AttendanceItem[]>()
  data.forEach((item: AttendanceItem) => {
    if (item.workingDate) {
      if (existingData.has(item.workingDate)) {
        existingData.get(item.workingDate)?.push(item)
      }
      else {
        existingData.set(item.workingDate, [item])
      }
    }
  })
  return existingData
}

// Insert fake attendance table data
const insertFakeAttendanceTableData = (data: AttendanceItem[]) => {
  attendanceTableDataMap.value = createAttendanceTableDataMap(data)

  let endDay = dayjs(endOfWeek.value)
  const startDay = dayjs(startOfWeek.value)
  attendanceTableData.value = []
  while (endDay.isAfter(startDay) || endDay.isSame(startDay, 'day')) {
    const date = endDay.format('YYYY-MM-DD')
    if (!attendanceTableDataMap.value.has(date)) {
      attendanceTableData.value.push({
        workingDate: date,
        createTime: dayjs(date).format('YYYY-MM-DD HH:mm:ss'),
      })
    }
    else {
      attendanceTableDataMap.value.get(date)?.forEach((item: AttendanceItem) => {
        attendanceTableData.value.push(item)
      })
    }
    endDay = endDay.subtract(1, 'day')
  }
  attendanceTableData.value.sort((a: AttendanceItem, b: AttendanceItem) => {
    return (a.createTime ?? '').localeCompare(b.createTime ?? '') * -1
  })
}

async function queryData(params: AttendanceSearchParams) {
  try {
    const { data, status } = await getAttendanceByDateApi(params)
    if (status === 200) {
      insertFakeAttendanceTableData(data?.items as AttendanceItem[])
      initTodayAttendanceData(data?.items ?? [])
      return data
    }
    else {
      insertFakeAttendanceTableData([])
      initTodayAttendanceData([])
    }
  }
  catch (e) {
    logger.error(e)
  }
}

const {
  run,
  refresh,
} = usePagination(queryData, {
  defaultParams: [params],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const onChangeWeek = async (type: string) => {
  if (type === 'next'){
    selectedDate.value = dayjs(selectedDate.value).add(7, 'days').format('YYYY-MM-DD')
  }
  else selectedDate.value = dayjs(selectedDate.value).subtract(7, 'days').format('YYYY-MM-DD')
  const params: AttendanceSearchParams = {
    fromDate: startOfWeek.value,
    toDate: endOfWeek.value,
    pageNum: 1,
    pageSize: 50,
  }
  run(params)
}

const isLoading = shallowRef(false)

const normalCheckIn = async (params: CheckInParams) => {
  if (isLoading.value)
    return

  isLoading.value = true
  const { status, code } = await checkInApi(params)
  if (status === 200) {
    selectedDate.value = dayjs().format('YYYY-MM-DD')
    refresh()
    message.success(t(code))
  }
  else {
    message.error(t(code))
  }
  isLoading.value = false
}

const fastCheckIn = async (employeeShiftId: string, params: FastCheckInParams) => {
  if (isLoading.value)
    return
  try {
    isLoading.value = true
    const { status, code } = await checkInByShiftIdApi(employeeShiftId, params)
    if (status === 200) {
      selectedDate.value = dayjs().format('YYYY-MM-DD')
      refresh()
      message.success(t(code))
    }
    else {
      message.error(t(code))
    }
  }
  catch (e) {
    console.error(e)
  }
  finally {
    isLoading.value = false
  }
}

const breakIn = async (employeeShiftId: string) => {
  if (isLoading.value)
    return

  try {
    isLoading.value = true
    const params: any = {}
    const { status, code } = await updateBreakInApi(employeeShiftId, params)
    if (status === 200) {
      refresh()
      selectedDate.value = dayjs().format('YYYY-MM-DD')
      message.success(t(code))
    }
    else {
      message.error(t(code))
    }
  }
  catch (e) {
    console.error(e)
  }
  finally {
    isLoading.value = false
  }
}

const breakOut = async (employeeShiftId: string) => {
  if (isLoading.value)
    return

  try {
    isLoading.value = true
    const params: any = {}
    const { status, code } = await updateBreakOutApi(employeeShiftId, params)
    if (status === 200) {
      refresh()
      selectedDate.value = dayjs().format('YYYY-MM-DD')
      message.success(t(code))
    }
    else {
      message.error(t(code))
    }
  }
  catch (e) {
    console.error(e)
  }
  finally {
    isLoading.value = false
  }
}

const checkOut = async (employeeShiftId: string, params: CheckOutParams) => {
  if (isLoading.value)
    return

  try {
    isLoading.value = true
    const { status, code } = await updateCheckoutApi(employeeShiftId, params)
    if (status === 200) {
      refresh()
      selectedDate.value = dayjs().format('YYYY-MM-DD')
      message.success(t(code))
    }
    else {
      message.error(t(code))
    }
  }
  catch (e) {
    console.error(e)
  }
  finally {
    isLoading.value = false
  }
}

// const setTimekeepingDetailByDate = (data: any) => {
//   timekeepingDetailByDate.value = data
//   isOpened.value = true
// }

const todayAttendanceDate = computed(() => {
  return todayAttendanceData.value.filter((item: AttendanceItem) => item.workingDate === today.value.format('YYYY-MM-DD'))
})

const currentAttendance = computed(() => {
  const idx = todayAttendanceDate.value.findIndex((item: AttendanceItem) => !!item.checkInTime && item.checkOutTime === null)
  if (idx !== -1) {
    currentIndex.value = idx
    return todayAttendanceDate.value[idx]
  }
  return undefined
})

const refreshAttendance = (employeeShift: AttendanceItem) => {
  const index = attendanceTableData.value.findIndex((item: AttendanceItem) => item.employeeShiftId === employeeShift.employeeShiftId)
  if (index !== -1) {
    attendanceTableData.value[index] = employeeShift
  }
  const idx = todayAttendanceData.value.findIndex((item: AttendanceItem) => item.employeeShiftId === employeeShift.employeeShiftId)
  if (idx !== -1) {
    todayAttendanceData.value[idx] = employeeShift
  }
}

const refreshAttendance2 = () => {
  refresh()
}

async function updateAttendanceItem(employeeShiftId: string, params: AttendanceUpdateParams) {
  if (isLoading.value)
    return

  isLoading.value = true
  const { status } = await updateAttendanceApi(employeeShiftId, params)
  if (status === 200) {
    refresh()
    messageNotify.success('変更成功！')
  }
  isLoading.value = false
}

async function requestApproval(employeeShiftId: string) {
  if (isLoading.value)
    return

  isLoading.value = true
  const { data, status, message } = await sendRequestByAttendanceItem(employeeShiftId)
  if (status === 200) {
    refreshAttendance(data as AttendanceItem)
    messageNotify.success('承認成功！')
  }
  else {
    console.error(message)
  }
  isLoading.value = false
}

const handleBeginTour = () => {
  console.log('handleBeginTour')
  isTourOpen.value = true
}

const handleCloseTour = () => {
  console.log('handleCloseTour')
  isTourOpen.value = false
}

// Lifecycle Hooks
onMounted(() => {
})

onActivated(() => {
  console.log('activated')
})
</script>

<template>
  <page-container>
    <div class="flex flex-col gap-[24px] p-2">
      <!-- Grid container với responsive columns -->
      <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
        <!-- DashboardClock component -->
        <a-space ref="dashboardClockRef" direction="vertical" class="col-span-1 md:col-span-2 xl:col-span-1 bg-white shadow-sm hover:shadow-lg rounded-lg">
          <DashboardClock
            :current-attendance="currentAttendance"
            @normal-check-in="normalCheckIn"
            @break-in="breakIn"
            @break-out="breakOut"
            @check-out="checkOut"
          />
        </a-space>

        <!-- TodayAttendanceSection -->
        <a-space ref="todayAttendanceSectionRef" direction="vertical" class="col-span-1 md:col-span-2 xl:col-span-2 bg-white shadow-sm hover:shadow-lg rounded-lg">
          <TodayAttendanceSection
            :today-attendance-data="todayAttendanceData"
            :current-attendance-item-id="currentAttendance?.employeeShiftId ?? ''"
            :current-index="currentIndex"
            @update-employee-shift="updateAttendanceItem"
            @request-approval="requestApproval"
            @fast-check-in="fastCheckIn"
          />
        </a-space>

        <!-- Total time section -->
        <a-space ref="totalTimeSectionRef" direction="vertical" class="bg-white shadow-sm hover:shadow-lg rounded-lg p-2">
          <div class="col-span-1 lg:col-span-2 xl:col-span-1">
            <TotalTimeSection
              :total-worktime-of-day="totalWorktimeOfDay"
              :total-overtime-of-day="totalOvertimeOfDay"
            />
          </div>
        </a-space>
      </div>

      <!-- Attendance table section -->
      <div class="w-full">
        <a-row>
          <a-col :span="24">
            <div class="flex flex-wrap justify-between items-center min-h-[50px] text-[1rem] font-medium">
              <div class="flex items-center gap-2 mb-2 sm:mb-0">
                <span>{{ t('dashboard.workplace.tableTitle') }}:</span>
                <div class="flex items-center gap-2">
                  <a-avatar
                    src="/icon/left_outlined.svg"
                    shape="square"
                    class="p-1 cursor-pointer"
                    @click="onChangeWeek('prev')"
                  />
                  <span class="whitespace-nowrap">
                    {{ dayjs(startOfWeek).format('MM/DD') }} ~
                    {{ dayjs(endOfWeek).format('MM/DD') }}
                  </span>
                  <a-avatar
                    src="/icon/right_outlined.svg"
                    shape="square"
                    class="p-1 cursor-pointer"
                    @click="onChangeWeek('next')"
                  />
                </div>
              </div>
            </div>
          </a-col>
        </a-row>

        <!-- AttendanceTable Component -->
        <a-space ref="attendanceTableRef" direction="vertical" class="overflow-x-auto">
          <AttendanceTable
            :data-source="attendanceTableData"
            :loading="isLoading"
            @refresh-attendance="refreshAttendance2"
            @attendance-daily-request="requestApproval"
            @fast-check-in="fastCheckIn"
          />
        </a-space>
      </div>
    </div>
    <!-- <a-float-button shape="circle" type="primary" @click="handleBeginTour">
      <template #icon>
        <QuestionCircleOutlined />
      </template>
      <template #tooltip>
        {{ t('tour') }}
      </template>
    </a-float-button> -->
    <div class="fixed bottom-0 right-0">
      <TourButton
        :icon="QuestionCircleOutlined"
        :tooltip="t('tour')"
        type="primary"
        shape="circle"
        @click="handleBeginTour"
      />
    </div>

    <a-tour v-model="currentTour" :open="isTourOpen" :steps="tourSteps" @close="handleCloseTour" />
  </page-container>
</template>

<style scoped lang="less">
// section clock and detail
.ant-btn-primary:disabled {
  background-color: #CDCECD;
  color: #74797A !important;
  border-radius: 4px;
}

.detail-title{
  font-size: 1rem;
  font-weight: 500;
  font: Noto Sans JP;
  margin-right: 2px;
}

.attend-detail::-webkit-scrollbar {
  width: 7px;
  margin-bottom: '12px';
  height: '280px';
}

.attend-detail::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, .5);
  box-shadow: 0 0 1px rgba(255, 255, 255, .5);
}

.icon-circle-over {
  background-color: #FCBF91;
  padding: 1rem;
  width: 4.5rem;
  height: 4.5rem;
}

.icon-circle-time{
  background-color: #CEE1F1;
  padding: 1.25rem;
  width: 4.5rem;
  height: 4.5rem;
}

.total-timer{
  font-size: 2rem;
  font-weight: 500;
}

.total-text{
  color: #74797A;
  font-size: 1rem;
  font-weight: 500;
}

.date{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* Custom scrollbar cho table overflow */
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 6px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Responsive styles */
@media (max-width: 640px) {
  :deep(.ant-table-thead > tr > th),
  :deep(.ant-table-tbody > tr > td) {
    padding: 8px 4px;
    white-space: nowrap;
  }

  .total-timer {
    font-size: 1.5rem;
  }

  .icon-circle-time,
  .icon-circle-over {
    width: 3.5rem;
    height: 3.5rem;
    padding: 0.75rem;
  }
}

@media (max-width: 768px) {
  .total-text {
    font-size: 0.875rem;
  }
}
</style>
