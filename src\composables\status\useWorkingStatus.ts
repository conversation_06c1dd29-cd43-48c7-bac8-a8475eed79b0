import { getWorkingStatusApi } from '~@/api/common/common'

export function useWorkingStatus() {
  const messageNotification = useMessage()
  const fetchWorkingStatusList = async () => {
    try {
      const { data, status, message } = await getWorkingStatusApi()
      if (status === 200)
        return data?.items
      else
        messageNotification.error(message ?? 'Fetch working status list failed!')
    }
    catch (error) {
      throw new Error(error as string)
    }
  }

  return {
    fetchWorkingStatusList,
  }
}
