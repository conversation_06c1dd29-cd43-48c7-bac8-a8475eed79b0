import { getUserInfoAvatarApi } from '~@/api/company/user-info'

export function useAvatar() {
  const userImgSrc = ref<string>('')
  const loading = ref(false)

  async function fetchUserAvatar() {
    if (loading.value)
      return

    try {
      loading.value = true
      const { data, status } = await getUserInfoAvatarApi()
      if (status === 200 && data)
        userImgSrc.value = `data:image/jpeg;base64,${data.avatarBase64}`
    }
    catch (error) {
    }
    finally {
      loading.value = false
    }
  }

  return {
    userImgSrc,
    fetchUserAvatar,
    loading,
  }
}
