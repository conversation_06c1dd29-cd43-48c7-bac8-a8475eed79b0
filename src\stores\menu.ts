import { defineStore } from 'pinia'
import type { RouteRecordRaw } from 'vue-router'
import { generateTreeRoutes } from '~/router/generate-route'
import type { MenuData } from '~@/layouts/basic-layout/typing'
import { STATIC_MENUS, menuLists } from '~@/utils/menuData'

export const useMenuStore = defineStore('menu', () => {
  const menuData = ref<MenuData>([])
  const routeData = ref<RouteRecordRaw[]>([])

  const generateDynamicRoutes = async () => {
    const data = [...menuLists, ...STATIC_MENUS]

    // Generate routes and menu structure
    const { menuData: treeMenuData, routeData: treeRouteData }
          = await generateTreeRoutes(data)

    menuData.value = treeMenuData
    routeData.value = treeRouteData

    return {
      menuData: treeMenuData,
      routeData: treeRouteData,
    }
  }

  return {
    menuData,
    routeData,
    generateDynamicRoutes,
  }
})
