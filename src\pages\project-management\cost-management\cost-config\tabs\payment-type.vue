<script lang="ts" setup>
import {
  EditOutlined,
  PlusOutlined,
  CopyOutlined,
  FileSearchOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue';
import type { ColumnGroupType, ColumnType } from 'ant-design-vue/es/table';
import { message } from 'ant-design-vue';
import { usePagination } from 'vue-request';
import { ResponseStatusEnum } from '~@/enums/response-status-enum';
import {
  createPaymentType,
  deletePaymentType,
  getOnePaymentType,
  getPaymentType,
  getPaymentTypeLogs,
  PaymentTypeItem,
  updatePaymentType,
} from '~@/api/company/payment-type';
import { ModalType, TimeLineColor } from '~@/enums/system-status-enum';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';

interface Params {
  pageNum?: number;
  pageSize?: number;
  [key: string]: any;
}

interface LogState {
  logId: string;
  pageSize: number;
  pageNum: number;
  hasMore: boolean;
}

interface FormState {
  paymentTypeId: string;
  paymentTypeName: string;
  description: string;
}

const initFormState: FormState = {
  paymentTypeId: '',
  paymentTypeName: '',
  description: '',
};

const modalType = ref<ModalType>(ModalType.ADD);
const modalLoading = ref<boolean>(false);
const logLoading = ref<boolean>(false);
const openLog = ref<boolean>(false);
const logRef = ref();
const { arrivedState } = useScroll(logRef);
const isOpenModal = ref<boolean>(false);
const { t } = useI18n();
const formState = reactive<FormState>({ ...initFormState });
const searchForm = ref<Params>({
  pageSize: 10,
  pageNum: 1,
});
const logState = reactive<LogState>({
  logId: '',
  pageSize: 10,
  pageNum: 1,
  hasMore: true,
});
const logData = ref<any[]>([]);

async function queryData(params?: Params) {
  const { data } = await getPaymentType(params);
  return data;
}

const {
  data: dataSource,
  loading,
  refresh,
  total,
  current,
  run,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [searchForm.value],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
});

const pagination = computed(() => ({
  total: total.value ?? 10,
  current: current.value,
  pageSize: pageSize.value,
}));

const columns = computed<
  (ColumnGroupType<PaymentTypeItem> | ColumnType<PaymentTypeItem>)[]
>(() => [
  {
    title: t('form.payment-type-name'),
    dataIndex: 'paymentTypeName',
    key: 'paymentTypeName',
    width: 150,
    align: 'center',
  },
  {
    title: t('form.description'),
    dataIndex: 'description',
    key: 'description',
    width: 150,
    align: 'center',
  },
  {
    title: t('action'),
    dataIndex: 'actions',
    key: 'actions',
    width: 50,
    align: 'center',
    fixed: 'right',
  },
]);

async function onFinish(values: PaymentTypeItem) {
  switch (modalType.value) {
    case ModalType.COPY:
    case ModalType.ADD: {
      const create = await createPaymentType(values);
      if (create.status === ResponseStatusEnum.SUCCESS) {
        message.success(create.message);
      } else {
        message.error(create.message);
        return;
      }

      break;
    }
    case ModalType.EDIT: {
      const update = await updatePaymentType(formState.paymentTypeId, values);
      if (update.status === ResponseStatusEnum.SUCCESS) {
        message.success(update.message);
      } else {
        message.error(update.message);
        return;
      }
      break;
    }
    default:
      break;
  }
  isOpenModal.value = false;
  onReset();
  refresh();
}

const handleDeletePaymentType = async (id: string) => {
  try {
    const del = await deletePaymentType(id);
    if (del.status === ResponseStatusEnum.SUCCESS) {
      message.success(del.message);
    } else {
      message.error(del.message);
      return;
    }
  } catch (error) {
  } finally {
    refresh();
  }
};

async function openModal(id: string, type: ModalType) {
  switch (type) {
    case ModalType.ADD:
      modalType.value = type;
      isOpenModal.value = true;
      break;
    case ModalType.COPY:
    case ModalType.EDIT: {
      modalType.value = type;
      isOpenModal.value = true;
      modalLoading.value = true;

      const update = await getOnePaymentType(id);
      formState.paymentTypeId = update.data?.paymentTypeId ?? '';
      formState.paymentTypeName = update.data?.paymentTypeName ?? '';
      formState.description = update.data?.description ?? '';

      modalLoading.value = false;
      break;
    }
    case ModalType.LOG: {
      modalType.value = type;
      logLoading.value = true;
      openLog.value = true;
      logState.pageNum = 1;

      const logs = await getPaymentTypeLogs(id, {
        pageSize: logState.pageSize,
        pageNum: logState.pageNum,
      });
      logData.value = logs.data?.entityChanges ?? [];

      logState.logId = id;
      logState.hasMore = logData.value.length === logState.pageSize;
      logLoading.value = false;
      break;
    }
    default:
      break;
  }
}

const handleTableChange: any = (
  pag: { pageSize: number; current: number },
  filters?: any
) => {
  run({
    pageSize: pag.pageSize,
    pageNum: pag.current,
    ...filters,
  });
};

const onSearch = () => {
  handleTableChange(
    { pageSize: searchForm.value.pageSize, current: 1 },
    { ...searchForm.value }
  );
};

function onReset() {
  Object.assign(formState, initFormState);
}

const renderTitle = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
    case ModalType.COPY:
      return t('add-payment-type');
    case ModalType.EDIT:
      return t('edit-payment-type');
    case ModalType.LOG:
      return t('log-payment-type');
    default:
      return '';
  }
});

const renderOkText = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
    case ModalType.COPY:
      return t('button.add');
    case ModalType.EDIT:
      return t('button.update');
    default:
      return '';
  }
});

const getTimeLineColor = computed(() => {
  return (action: keyof typeof TimeLineColor) => {
    return TimeLineColor[action];
  };
});

const getTimeLineTitle = computed(() => {
  return (field: string) => {
    switch (field.toLowerCase()) {
      case 'paymenttypename':
        return t('form.payment-type-name');
      case 'description':
        return t('form.description');
      case 'lastmodifiedtime':
        return t('log.lastmodifiedtime');
      case 'lastmodifiedby':
        return t('log.lastmodifiedby');
      default:
        return '';
    }
  };
});

const getTimeLineValue = computed(() => {
  return (field: string, value: string | boolean | number[]) => {
    switch (field.toLowerCase()) {
      case 'paymenttypename':
      case 'description': {
        if (!value) return `''`;
        return value;
      }
      case 'lastmodifiedtime': {
        if (!value) return `''`;
        return dayjs(value as string).format('YYYY-MM-DD HH:mm:ss');
      }
      default:
        return value;
    }
  };
});

const showChangedItem = computed(() => {
  return (changedItem: any) => {
    if (changedItem.fieldName === 'PaymentTypeUid') return false;
    if (changedItem.fieldName === 'LastModifiedBy') return false;
    if (changedItem.fieldName === 'OrgUid') return false;
    if (changedItem.fieldName === 'CreatedBy') return false;
    if (changedItem.fieldName === 'CreatedTime') return false;

    const valueBefore = isEmpty(changedItem.valueBefore)
      ? ''
      : changedItem.valueBefore;
    const valueAfter = isEmpty(changedItem.valueAfter)
      ? ''
      : changedItem.valueAfter;
    return valueBefore !== valueAfter;
  };
});

const handleScroll = async () => {
  if (!logState.hasMore) return;
  logState.pageNum += 1;

  const logs = await getPaymentTypeLogs(logState.logId, {
    pageSize: logState.pageSize,
    pageNum: logState.pageNum,
  });

  const entityChanges = logs.data?.entityChanges ?? [];
  logData.value = [...logData.value, ...entityChanges];
  logState.hasMore = entityChanges.length === logState.pageSize;
};

watch(
  () => arrivedState.bottom,
  async (value: boolean) => {
    if (value) await handleScroll();
  }
);
</script>

<template>
  <page-container>
    <a-card :bordered="false">
      <template #title>
        <a-row :gutter="[12, 12]" style="font-weight: normal">
          <a-col :xs="24" :sm="12">
            <a-input-search
              v-model:value="searchForm.keyword"
              :placeholder="t('input.placeholder')"
              style="width: 270px"
              @search="onSearch"
              allow-clear
            />
          </a-col>
          <a-col :xs="24" :sm="12" class="flex flex-justify-end">
            <a-button
              class="flex flex-items-center"
              type="primary"
              @click="openModal('', ModalType.ADD)"
            >
              <PlusOutlined /> {{ t('button.add') }}
            </a-button>
          </a-col>
        </a-row>
      </template>
      <a-table
        :scroll="{ x: 'max-content' }"
        :columns="columns"
        :data-source="dataSource?.items"
        :loading="loading"
        :pagination="pagination"
        row-key="paymentTypeId"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'actions'">
            <div class="flex flex-justify-center">
              <a-button
                class="flex items-center justify-center"
                type="text"
                @click="openModal(record.paymentTypeId, ModalType.EDIT)"
                size="small"
                color="primary"
              >
                <EditOutlined />
              </a-button>
              <a-button
                class="flex items-center justify-center"
                type="text"
                @click="openModal(record.paymentTypeId, ModalType.COPY)"
                color="orange"
                size="small"
              >
                <CopyOutlined />
              </a-button>
              <a-button
                class="flex items-center justify-center"
                type="text"
                @click="openModal(record.paymentTypeId, ModalType.LOG)"
                color="warning"
                size="small"
              >
                <FileSearchOutlined />
              </a-button>
              <a-popconfirm
                :title="t('message.delete-confirmation')"
                @confirm="() => handleDeletePaymentType(record.paymentTypeId)"
              >
                <a-button
                  class="flex items-center justify-center"
                  type="text"
                  danger
                  size="small"
                >
                  <DeleteOutlined />
                </a-button>
              </a-popconfirm>
            </div>
          </template>
        </template>
        <template #expandColumnTitle>
          <span />
        </template>
      </a-table>
    </a-card>

    <a-modal
      v-model:open="isOpenModal"
      :title="renderTitle"
      width="600px"
      :footer="false"
      @cancel="onReset"
    >
      <a-card class="ant-pro-basicLayout" :loading="modalLoading">
        <a-form
          :model="formState"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
          autocomplete="off"
          @finish="onFinish"
        >
          <a-row :gutter="[12, 12]">
            <a-col span="24">
              <a-form-item
                :label="t('form.payment-type-name')"
                name="paymentTypeName"
                :rules="[{ required: true }]"
              >
                <a-input
                  v-model:value="formState.paymentTypeName"
                  :placeholder="t('form.payment-type-name')"
                />
              </a-form-item>
            </a-col>
            <a-col span="24">
              <a-form-item :label="t('form.description')" name="description">
                <a-textarea
                  v-model:value="formState.description"
                  :placeholder="t('form.description')"
                  allow-clear
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col span="24" class="flex gap-2 flex-justify-end">
              <a-button @click="onReset" v-if="modalType === ModalType.ADD">
                {{ t('button.reset') }}
              </a-button>
              <a-button type="primary" html-type="submit">
                {{ renderOkText }}
              </a-button>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </a-modal>

    <a-drawer
      :title="renderTitle"
      size="large"
      :open="openLog"
      @close="openLog = false"
      :body-style="{ padding: 0, overflow: 'hidden' }"
    >
      <a-card
        :loading="logLoading"
        class="overflow-auto h-full log-payment-type"
        border-style="none"
        ref="logRef"
      >
        <a-timeline>
          <a-timeline-item
            v-for="item in logData"
            :key="item.auditLogId"
            :color="getTimeLineColor(item.action)"
          >
            <a-collapse ghost expandIconPosition="end">
              <a-collapse-panel key="1">
                <template #header>
                  <span color="blue">{{ item.modifiedUserName }}</span>
                  {{ t(`log.${item.action}`) }}
                  <div class="text-gray-400">
                    {{ dayjs(item.modifiedTime).format('YYYY-MM-DD HH:mm:ss') }}
                  </div>
                </template>
                <div
                  class="text-gray-400"
                  v-for="changedItem in item.changedList"
                  :key="changedItem.fieldName"
                >
                  <div
                    v-if="showChangedItem(changedItem)"
                    v-html="
                      t('log.changed', {
                        field: getTimeLineTitle(changedItem.fieldName),
                        oldValue: getTimeLineValue(
                          changedItem.fieldName,
                          changedItem.valueBefore
                        ),
                        newValue: getTimeLineValue(
                          changedItem.fieldName,
                          changedItem.valueAfter
                        ),
                      })
                    "
                  />
                </div>
              </a-collapse-panel>
            </a-collapse>
          </a-timeline-item>
        </a-timeline>
        <div class="flex justify-center items-center" v-if="logState.hasMore">
          <a-spin />
        </div>
      </a-card>
    </a-drawer>
  </page-container>
</template>

<style lang="less" scoped>
.log-payment-type {
  :deep(.ant-collapse-header) {
    padding: 0;
  }
}
</style>
