<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
import type { PropType } from 'vue'
import type { CategorizedCost } from '~@/api/construction'

defineProps({
  totalCost: {
    type: Number,
    required: true,
  },
  categorizedCosts: {
    type: Array as PropType<CategorizedCost[]>,
    required: true,
  },
  benefit: {
    type: Number,
    required: true,
  },
  isBetweenFilterDate: {
    type: Boolean,
    required: true,
  },
  isShowProductCost: {
    type: Boolean,
    required: true,
  },
})

const { t } = useI18n()

function formatNumber(value: number) {
  if (!value)
    return 0
  return value.toLocaleString()
}

// const onLeftClick = (totalAvgAmount: number) => {
//   employeeTotalAmount.value = totalAvgAmount
//   employeeTotalType.value = 'totalAvgAmount'
//   isLeftDisabled.value = true
//   isRightDisabled.value = false
// }

// const onRightClick = (newAmount: number) => {
//   employeeTotalAmount.value = newAmount
//   employeeTotalType.value = 'totalAmount'
//   isRightDisabled.value = true
//   isLeftDisabled.value = false
// }
</script>

<template>
  <div class="space-y-2">
    <div class="grid grid-cols-2 border-b-1 border-t-0 border-l-0 border-r-0 border-gray-200 border-solid pb-2">
      <div>{{ t("total-construction-cost") }}</div>
      <div class="text-right">
        {{ formatNumber(totalCost) }}
      </div>
    </div>

    <!-- Employee Costs -->
    <div v-for="(cost, index) in categorizedCosts" :key="`ec-${index}`" class="grid grid-cols-3">
      <div>{{ cost?.categoryName }}</div>
      <div>{{ cost?.subCategories?.length }}</div>
      <!-- <template v-if="cost?.categoryCode === CategoryCode.EMPLOYEE">
        <div v-if="!isEmployeeCostVisibleAll" class="text-right">
          {{ formatNumber(cost?.totalAvgAmount) }}
        </div>
        <div v-else class="flex justify-end">
          <LeftOutlined class="cursor-pointer" :class="{ 'opacity-50': isLeftDisabled }" @click="onLeftClick(cost?.totalAvgAmount)" />
          <a-tooltip :title="employeeTotalType">
            <div class="p-1">
              {{ formatNumber(employeeTotalAmount ?? cost?.totalAvgAmount) }}
            </div>
          </a-tooltip>
          <RightOutlined class="cursor-pointer" :class="{ 'opacity-50': isRightDisabled }" @click="onRightClick(cost?.totalAmount)" />
        </div>
      </template>
      <template v-else> -->
      <div v-if="isShowProductCost" class="text-right">
        {{ formatNumber(cost?.totalAmount) }}
      </div>
      <div v-else class="text-right">
        {{ formatNumber(cost?.totalAvgAmount) }}
      </div>
    </div>

    <!-- Totals -->
    <div class="grid grid-cols-2">
      <div>{{ t("rich-amount") }}</div>
      <div class="text-right">
        {{ formatNumber(benefit) }}
      </div>
    </div>
  </div>
</template>
