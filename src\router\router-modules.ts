// Tự động import các file Vue trong thư mực pages loại trừ các thư mục không phải là route
// Các pattern bắt đầu bằng '~/' sẽ được tự động import
// Các pattern bắt đầu bằng '!~/pages/**/*copy.vue' sẽ được loại bỏ
const routerModules = import.meta.glob([
  '~/pages/**/*.vue',
  '!~/pages/**/*copy.vue',
  '!~/pages/**/component',
  '!~/pages/**/components',
  '!~/pages/**/composables',
  '!~/pages/**/hooks',
  '!~/pages/**/locales',
  '!~/pages/**/modules',
  '!~/pages/**/plugins',
  '!~/pages/**/tests',
  '!~/pages/**/test',
  '!~/pages/common',
])

// Định nghĩa các component đặc biệt không nằm trong cấu trúc thư mục pages thông thường
export const basicRouteMap = {
  // Dashboard: {
  //   path: '/dashboard',
  //   component: () => import('~/layouts/index.vue'),
  //   children: [
  //     {
  //       path: 'ps/attendance',
  //       name: 'PersonalAttendance',
  //       component: () => import('~/pages/dashboard/person/attendance/index.vue'),
  //       meta: {
  //         title: 'Personal Attendance',
  //         keepAlive: true,
  //       },
  //     },
  //   ],
  // },
  Iframe: () => import('~/pages/common/iframe.vue'),
  RouteView: () => import('~/pages/common/route-view.vue'),
  ComponentError: () => import('~/pages/exception/component-error.vue'),
}

// Xử lý các trường hợp import (eager và dynamic)
function checkEager(module: any) {
  if (typeof module === 'object' && 'default' in module)
    return module.default

  return module
}

// Ánh xạ đường dẫn path đến các component tương ứng
export function getRouterModule(path?: string): any {
  // if (!path) return basicRouteMap.ComponentError;
  if (!path)
    return basicRouteMap.RouteView
  if (path in basicRouteMap)
    return (basicRouteMap as any)[path]

  if (path.startsWith('/'))
    path = path.slice(1)
  const fullPath = `/src/pages/${path}.vue`
  const fullPathIndex = `/src/pages/${path}/index.vue`
  if (fullPathIndex in routerModules)
    return checkEager(routerModules[fullPathIndex])
  return checkEager(routerModules[fullPath])
}

export default routerModules
