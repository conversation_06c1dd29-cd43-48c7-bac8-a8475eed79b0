<script setup lang="ts">
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { ArrowLeftOutlined } from '@ant-design/icons-vue'
import EmployeeTab from './components/employee-tab.vue'
import type { AttendanceEmployeeSummaryParams, AttendanceItem, AttendanceUpdateParams, BreakTimeItem } from '@/api/attendance'
import { getAttendanceEmployeeApi, getAttendanceEmployeeSummaryApi, updateAttendanceApi } from '@/api/attendance'
import { HHMMRegex, HHMMSSRegex, YYYYMMDDRegex, formatTimeToHHMM } from '~@/utils/apiTimer'
import { createMonthlyAttendanceApi, getMonthlyAttendanceResultApi, getMonthlyAttendanceSummaryApi, syncMonthlyAttendanceApi } from '~@/api/monthly-attendance'
import type { AttendanceQueryParams, MonthlyAttendanceItem, MonthlyAttendanceItemData, UpdateMonthlyAttendanceParams } from '~@/api/monthly-attendance'
import logger from '~@/utils/logger'
import type { QueryParams } from '~@/api/common-params'
import WorkTimeModal from '~@/components/common/WorkTimeModal.vue'
import { ModalType } from '~@/enums/system-status-enum'
import { useApiRequest } from '~@/composables/useApiRequest'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const loading = ref(false)
const workingDateSet = ref<Set<string>>(new Set())
const messageNotify = useMessage()
const selectedMonth = ref(dayjs(route.query.month as string))
const startOfMonth = computed(() => selectedMonth.value.startOf('month').format('YYYY-MM-DD'))
const endOfMonth = computed(() => selectedMonth.value.endOf('month').format('YYYY-MM-DD'))
const activeTab = ref('EMPLOYEE')
const employeeCode = route.query.employeeCode as string
const employeeName = route.query.employeeName as string
const syncLoading = ref(false)
const officeDataSource = ref<MonthlyAttendanceItem[]>([])
const employeeDataSource = ref<AttendanceItem[]>([])
const totalWorkingDays = ref(0)
const totalAbsentDays = ref(0)
const totalWorkTime = ref(0)
const totalOverTime = ref(0)
const totalPaidLeaveUsed = ref(0)
const totalUnpaidLeaveUsed = ref(0)
const showShiftInOneLine = ref(false)
const workTimeModalIsVisible = ref(false)
const workTimeModalType = ref(ModalType.ADD)
const createAttendanceRequest = useApiRequest(createMonthlyAttendanceApi, { immediate: false, showNotify: true })

function initEmployeeDataSource(data: AttendanceItem[]) {
  data.forEach((item: AttendanceItem) => {
    if (!item.breakList || item.breakList.length === 0) {
      item.breakList = [{
        breakInTime: '',
        breakOutTime: '',
      }]
    }
    if (YYYYMMDDRegex.test(item.workingDate ?? ''))
      workingDateSet.value.add(item.workingDate ?? '')
  })
  const daysInMonth = selectedMonth.value.daysInMonth()
  for (let i = 0; i < daysInMonth; i++) {
    const dateString = selectedMonth.value.date(i + 1).format('YYYY-MM-DD')
    if (!workingDateSet.value.has(dateString)) {
      const emptyAttendanceItem: MonthlyAttendanceItem = {
        projectCode: '',
        projectName: '',
        originalEmployeeShiftId: '',
        employeeAttendanceResultId: '',
        workingDate: dateString,
        checkInTime: '',
        checkOutTime: '',
        totalWorkTime: 0,
        totalOverTime: 0,
        totalBreakTime: 0,
        totalMovingTime: 0,
        description: '',
        approverName: '',
        approvedDateTime: '',
        isApproved: false,
        breakTimes: [
          {
            breakInTime: '',
            breakOutTime: '',
          },
        ],
      }
      employeeDataSource.value.push(emptyAttendanceItem)
      workingDateSet.value.add(dateString)
    }
  }
  workingDateSet.value.clear()
}

function initMonthlyDataSource(data: MonthlyAttendanceItem[]) {
  data.forEach((item: MonthlyAttendanceItem) => {
    if (!item.breakTimes || item.breakTimes.length === 0) {
      item.breakTimes = [{
        breakInTime: '',
        breakOutTime: '',
      }]
    }
    if (YYYYMMDDRegex.test(item.workingDate ?? ''))
      workingDateSet.value.add(item.workingDate ?? '')
  })
  const daysInMonth = selectedMonth.value.daysInMonth()
  for (let i = 0; i < daysInMonth; i++) {
    const dateString = selectedMonth.value.date(i + 1).format('YYYY-MM-DD')
    if (!workingDateSet.value.has(dateString)) {
      const emptyAttendanceItem: MonthlyAttendanceItem = {
        projectCode: '',
        projectName: '',
        originalEmployeeShiftId: '',
        employeeAttendanceResultId: '',
        workingDate: dateString,
        checkInTime: '',
        checkOutTime: '',
        totalWorkTime: 0,
        totalOverTime: 0,
        totalBreakTime: 0,
        totalMovingTime: 0,
        description: '',
        approverName: '',
        approvedDateTime: '',
        isApproved: false,
        breakTimes: [],
      }
      officeDataSource.value.push(emptyAttendanceItem)
      workingDateSet.value.add(dateString)
    }
  }
  workingDateSet.value.clear()
}

function validateBreakList(breakList?: BreakTimeItem[] | undefined) {
  if (!breakList || breakList.length === 0)
    return false
  for (let i = 0; i < breakList.length; i++) {
    const breakItem = breakList[i]
    if (!(HHMMRegex.test(breakItem.breakInTime!)
      || HHMMSSRegex.test(breakItem.breakInTime!)
      || HHMMRegex.test(breakItem.breakOutTime!)
      || HHMMSSRegex.test(breakItem.breakOutTime!)
    ))
      return false
  }
  return true
}

async function handleUpdatePersonalAttendance(attendanceRecord: AttendanceItem) {
  const shiftId = attendanceRecord.employeeShiftId
  const workingDate = attendanceRecord.workingDate ?? dayjs().format('YYYY-MM-DD')
  if (!shiftId)
    return
  try {
    const params: AttendanceUpdateParams = {
      checkInTime: `${workingDate} ${attendanceRecord.checkInTime}`,
      checkOutTime: `${workingDate} ${attendanceRecord.checkOutTime}`,
      breakList: [],
    }
    if (validateBreakList(attendanceRecord.breakList)) {
      params.breakList = [
        ...attendanceRecord.breakList!,
      ]
    }
    const { status, message } = await updateAttendanceApi(shiftId, params)
    if (status === 200)
      messageNotify.success(t('success.updateAttendance'))
    else
      messageNotify.error(message)
  }
  catch (error) {
    logger.error(error)
  }
}

// async function handleUpdateMonthlyAttendance(attendanceRecord: MonthlyAttendanceItem) {
//   const resultId = attendanceRecord.employeeAttendanceResultId
//   const workingDate = attendanceRecord.workingDate ?? dayjs().format('YYYY-MM-DD')
//   if (!resultId)
//     return
//   try {
//     const params: AttendanceRecordParams = {
//       checkInTime: `${workingDate} ${attendanceRecord.checkInTime}`,
//       checkOutTime: `${workingDate} ${attendanceRecord.checkOutTime}`,
//       breakTimes: [],
//     }
//     if (validateBreakList(attendanceRecord.breakTimes)) {
//       params.breakTimes = [
//         ...attendanceRecord.breakTimes,
//       ]
//     }

//     const { status, message } = await updateMonthlyAttendanceResultApi(resultId, params)
//     if (status === 200)
//       messageNotify.success(t('success.updateAttendance'))
//     else
//       messageNotify.error(message)
//   }
//   catch (error) {
//     logger.error(error)
//   }
// }

async function getPersonalAttendanceSummary() {
  try {
    const employeeId = route.params.id as string
    if (!employeeId)
      return
    const params: AttendanceEmployeeSummaryParams = {
      employeeId,
      fromDate: startOfMonth.value,
      toDate: endOfMonth.value,
    }
    const { data, status } = await getAttendanceEmployeeSummaryApi(params)
    if (status === 200) {
      totalWorkingDays.value = data?.totalWorkingDays ?? 0
      totalAbsentDays.value = data?.totalAbsentDays ?? 0
      totalWorkTime.value = data?.totalWorkTime ?? 0
      totalOverTime.value = data?.totalOverTime ?? 0
      totalPaidLeaveUsed.value = data?.totalPaidLeaveUsed ?? 0
      totalUnpaidLeaveUsed.value = data?.totalUnpaidLeaveUsed ?? 0
    }
  }
  catch (error) {
    console.error(error)
  }
}

async function getPersonalAttendance() {
  const employeeId = route.params.id as string
  if (!employeeId)
    return
  try {
    const params: QueryParams = {
      fromDate: startOfMonth.value,
      toDate: endOfMonth.value,
    }
    const { data, status } = await getAttendanceEmployeeApi(employeeId, params)
    if (status === 200) {
      employeeDataSource.value = data?.items ?? []
      initEmployeeDataSource(data?.items ?? [])
    }
    else {
      employeeDataSource.value = []
      initEmployeeDataSource(data?.items ?? [])
    }
  }
  catch (error) {
    logger.error(error)
  }
}

async function getMonthlyAttendanceResult() {
  try {
    const employeeId = route.params.id as string
    if (!employeeId)
      return
    const params: AttendanceQueryParams = {
      dateFrom: startOfMonth.value,
      dateTo: endOfMonth.value,
      pageNum: 1,
      pageSize: 100,
    }
    const { data, status } = await getMonthlyAttendanceResultApi(employeeId, params)
    if (status === 200) {
      officeDataSource.value = data?.items ?? []
      initMonthlyDataSource(data?.items ?? [])
    }
    else {
      officeDataSource.value = []
      initMonthlyDataSource(data?.items ?? [])
    }
  }
  catch (error) {
    messageNotify.error(t('error.loadData'))
  }
}

async function getMonthlyAttendanceSummary() {
  const employeeId = route.params.id as string
  if (!employeeId)
    return
  try {
    const params: QueryParams = {
      fromDate: startOfMonth.value,
      toDate: endOfMonth.value,
    }
    const { data, status } = await getMonthlyAttendanceSummaryApi(employeeId, params)
    if (status === 200) {
      totalWorkingDays.value = data?.totalWorkingDays ?? 0
      totalAbsentDays.value = data?.totalAbsentDays ?? 0
      totalWorkTime.value = data?.totalWorkTime ?? 0
      totalOverTime.value = data?.totalOverTime ?? 0
      totalPaidLeaveUsed.value = data?.totalPaidLeaveUsed ?? 0
      totalUnpaidLeaveUsed.value = data?.totalUnpaidLeaveUsed ?? 0
    }
    else {
      totalWorkingDays.value = 0
      totalAbsentDays.value = 0
      totalWorkTime.value = 0
      totalOverTime.value = 0
      totalPaidLeaveUsed.value = 0
      totalUnpaidLeaveUsed.value = 0
    }
  }
  catch (error) {
    logger.error(error)
  }
}

// CSV Export Function
function exportOfficeDataToCSV() {
  try {
    // Define CSV headers
    const headers = [
      t('workingDate'),
      t('projectName'),
      t('checkInTime'),
      t('checkOutTime'),
      t('breakTime'),
      t('totalWorkTime'),
      t('totalOverTime'),
      t('totalBreakTime'),
    ]

    // Prepare CSV data
    const csvData = [
      headers.join(','), // Header row
      ...officeDataSource.value.map((row) => {
        // Format break time as readable string
        const breakTimeStr = row.breakTimes?.map(breakItem =>
          `${formatTimeToHHMM(breakItem.breakInTime || '')} - ${formatTimeToHHMM(breakItem.breakOutTime || '')}`,
        ).join('/ ') || '-'

        return [
          row.workingDate || '',
          row.projectName || '',
          formatTimeToHHMM(row.checkInTime || '') || '00:00',
          formatTimeToHHMM(row.checkOutTime || '') || '00:00',
          `"${breakTimeStr}"`, // Wrap in quotes
          row.totalWorkTime ? convertTimeToHH_MM(row.totalWorkTime) : '00:00',
          row.totalOverTime ? convertTimeToHH_MM(row.totalOverTime) : '00:00',
          row.totalBreakTime ? convertTimeToHH_MM(row.totalBreakTime) : '00:00',
        ].join(',')
      }),
    ].join('\n')

    // Create and download CSV file
    const csvData_with_BOM = `\uFEFF${csvData}`
    const blob = new Blob([csvData_with_BOM], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)

    link.setAttribute('href', url)
    link.setAttribute('download', `${employeeCode}_${employeeName}_office_report_${dayjs().format('YYYY-MM-DD')}.csv`)
    link.style.visibility = 'hidden'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    messageNotify.success(t('success.exportCSV'))
  }
  catch (error) {
    messageNotify.error(t('error.exportCSV'))
  }
}

function exportEmployeeDataToCSV() {
  try {
    // Define CSV headers
    const headers = [
      t('workingDate'),
      t('projectName'),
      t('checkInTime'),
      t('checkOutTime'),
      t('breakTime'),
      t('totalWorkTime'),
      t('totalOverTime'),
      t('totalBreakTime'),
    ]

    // Prepare CSV data
    const csvData = [
      headers.join(','), // Header row
      ...employeeDataSource.value.map((row) => {
        // Format break time as readable string
        const breakTimeStr = row.breakList?.map(breakItem =>
          `${formatTimeToHHMM(breakItem.breakInTime || '')} - ${formatTimeToHHMM(breakItem.breakOutTime || '')}`,
        ).join('/ ') || '-'

        return [
          row.workingDate || '',
          row.projectName || '',
          formatTimeToHHMM(row.checkInTime || '') || '00:00',
          formatTimeToHHMM(row.checkOutTime || '') || '00:00',
          `"${breakTimeStr}"`, // Wrap in quotes
          row.totalWorkTime ? convertTimeToHH_MM(row.totalWorkTime) : '00:00',
          row.totalOverTime ? convertTimeToHH_MM(row.totalOverTime) : '00:00',
          row.totalBreakTime ? convertTimeToHH_MM(row.totalBreakTime) : '00:00',
        ].join(',')
      }),
    ].join('\n')

    // Create and download CSV file
    const csvData_with_BOM = `\uFEFF${csvData}`
    const blob = new Blob([csvData_with_BOM], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)

    link.setAttribute('href', url)
    link.setAttribute('download', `${employeeCode}_${employeeName}_employee_attendance_report_${dayjs().format('YYYY-MM-DD')}.csv`)
    link.style.visibility = 'hidden'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    messageNotify.success(t('success.exportCSV'))
  }
  catch (error) {
    messageNotify.error(t('error.exportCSV'))
  }
}

async function handleMonthChange(date: Dayjs | string) {
  if (!date)
    return
  selectedMonth.value = dayjs(date)
  route.query.month = selectedMonth.value.format('YYYY-MM')
  switch (activeTab.value) {
    case 'OFFICE':
      await getMonthlyAttendanceSummary()
      await getMonthlyAttendanceResult()
      break
    case 'EMPLOYEE':
      await getPersonalAttendanceSummary()
      await getPersonalAttendance()
      break
    default:
      break
  }
}

function convertTimeToHH_MM(time: number) {
  if (!time)
    return '00:00'
  const hours = Math.floor(time * 100 / 100)
  let minutes = (time * 100) % 100
  minutes = minutes * 60 / 100
  return `${hours}:${minutes.toString().padStart(2, '0')}`
}

async function handleSyncAttendance() {
  if (syncLoading.value)
    return
  syncLoading.value = true

  const employeeId = route.params.id as string
  if (!employeeId)
    return
  try {
    const params: UpdateMonthlyAttendanceParams = {
      employeeId,
      dateFrom: startOfMonth.value,
      dateTo: endOfMonth.value,
    }
    const { status, message } = await syncMonthlyAttendanceApi(params)
    if (status === 200) {
      messageNotify.success(t('success.syncAttendance'))
      await getMonthlyAttendanceResult()
      await getMonthlyAttendanceSummary()
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (error) {
    logger.error(error)
  }
  finally {
    syncLoading.value = false
  }
}

function handleExport() {
  if (activeTab.value === 'OFFICE')
    exportOfficeDataToCSV()
  else
    exportEmployeeDataToCSV()
}

// function openWorkTimeModal() {
//   workTimeModalIsVisible.value = true
// }

async function handleCreateNewAttendance(_: string, params: AttendanceUpdateParams) {
  if (loading.value)
    return
  loading.value = true

  const data: MonthlyAttendanceItemData = {
    employeeId: route.params.id as string,
    projectId: params.projectId ?? '',
    workingLocation: params.workingLocation ?? '',
    workingDate: params.workingDate ?? '',
    checkInTime: params.checkInTime ?? '',
    checkOutTime: params.checkOutTime ?? '',
    breakTimes: params.breakList ?? [],
  }
  try {
    switch (workTimeModalType.value) {
      case ModalType.ADD: {
        await createAttendanceRequest.execute(data)
        workTimeModalIsVisible.value = false
        await getMonthlyAttendanceSummary()
        await getMonthlyAttendanceResult()
        break
      }
      default:
        break
    }
  }
  catch (e) {
  }
  finally {
    loading.value = false
  }
}
watch(activeTab, async (newVal) => {
  switch (newVal) {
    case 'OFFICE':
      await getMonthlyAttendanceSummary()
      await getMonthlyAttendanceResult()
      break
    case 'EMPLOYEE':
      await getPersonalAttendanceSummary()
      await getPersonalAttendance()
      break
    default:
      break
  }
})

onMounted(async () => {
  await getMonthlyAttendanceSummary()
  await getPersonalAttendance()
  await getPersonalAttendanceSummary()
})
</script>

<template>
  <page-container>
    <div class="relative space-y-6">
      <!-- Attendance Statistics Section -->
      <div class="bg-white rounded-xl overflow-hidden border border-gray-200/50">
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 border-b border-gray-200 flex justify-between">
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-2">
              <div class="cursor-pointer flex items-center" @click="router.back()">
                <ArrowLeftOutlined class="text-xl mr-1" />
              </div>
            </div>
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0
                  012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
            </div>
            <div>
              <div class="text-lg font-semibold text-gray-900">
                {{ t('attendance.summary') }}
              </div>
              <div class="text-xs text-gray-600">
                {{ employeeCode }} {{ employeeName }}
              </div>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <!-- <a-button type="default">
              {{ t('button.confirm') }}
            </a-button>
            <a-button type="primary">
              {{ t('button.approve') }}
            </a-button>
            <a-button danger type="primary">
              {{ t('button.reject') }}
            </a-button> -->
            <div class="flex items-center gap-2">
              <a-date-picker v-model:value="selectedMonth" picker="month" value-format="YYYY-MM" @change="handleMonthChange" />
            </div>
            <a-button
              type="default"
              class="bg-green-50 border-green-200 text-green-700 hover:bg-green-100 hover:border-green-300"
              @click="handleExport"
            >
              {{ t('CSVDownload') }}
            </a-button>
            <a-button
              v-if="activeTab === 'OFFICE'"
              type="primary"
              :loading="syncLoading"
              @click="handleSyncAttendance"
            >
              {{ t('button.sync') }}
            </a-button>
          </div>
        </div>

        <div class="p-4">
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
            <!-- Working Days Card -->
            <div
              class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-3
            border border-green-200/50 hover:shadow-sm transition-all duration-200"
            >
              <div class="flex flex-col items-center text-center">
                <div class="w-6 h-6 bg-green-100 rounded-md flex items-center justify-center mb-2">
                  <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round" stroke-linejoin="round"
                      stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <div class="text-xl font-bold text-green-900">
                  {{ totalWorkingDays }}
                </div>
                <div class="text-xs text-green-600">
                  {{ t('workingDays') }}
                </div>
              </div>
            </div>

            <!-- Absent Days Card -->
            <div
              class="bg-gradient-to-br from-red-50 to-rose-50 rounded-lg p-3
            border border-red-200/50 hover:shadow-sm transition-all duration-200"
            >
              <div class="flex flex-col items-center text-center">
                <div class="w-6 h-6 bg-red-100 rounded-md flex items-center justify-center mb-2">
                  <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </div>
                <div class="text-xl font-bold text-red-900">
                  {{ totalAbsentDays }}
                </div>
                <div class="text-xs text-red-600">
                  {{ t('absentDays') }}
                </div>
              </div>
            </div>

            <!-- Total Work Time Card -->
            <div
              class="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-lg p-3
            border border-blue-200/50 hover:shadow-sm transition-all duration-200"
            >
              <div class="flex flex-col items-center text-center">
                <div class="w-6 h-6 bg-blue-100 rounded-md flex items-center justify-center mb-2">
                  <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div class="text-xl font-bold text-blue-900">
                  {{ totalWorkTime }}h
                </div>
                <div class="text-xs text-blue-600">
                  {{ t('workTime') }}
                </div>
              </div>
            </div>

            <!-- Overtime Card -->
            <div
              class="bg-gradient-to-br from-orange-50 to-amber-50 rounded-lg p-3
            border border-orange-200/50 hover:shadow-sm transition-all duration-200"
            >
              <div class="flex flex-col items-center text-center">
                <div class="w-6 h-6 bg-orange-100 rounded-md flex items-center justify-center mb-2">
                  <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div class="text-xl font-bold text-orange-900">
                  {{ totalOverTime }}h
                </div>
                <div class="text-xs text-orange-600">
                  {{ t('overtime') }}
                </div>
              </div>
            </div>

            <!-- Paid Leave Card -->
            <div
              class="bg-gradient-to-br from-purple-50 to-violet-50 rounded-lg p-3 border border-purple-200/50
            hover:shadow-sm transition-all duration-200"
            >
              <div class="flex flex-col items-center text-center">
                <div class="w-6 h-6 bg-purple-100 rounded-md flex items-center justify-center mb-2">
                  <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                    />
                  </svg>
                </div>
                <div class="text-xl font-bold text-purple-900">
                  {{ totalPaidLeaveUsed }}
                </div>
                <div class="text-xs text-purple-600">
                  {{ t('paidLeave') }}
                </div>
              </div>
            </div>

            <!-- Unpaid Leave Card -->
            <!-- <div
              class="bg-gradient-to-br from-gray-50 to-slate-50 rounded-lg p-3
              border border-gray-200/50 hover:shadow-sm transition-all duration-200"
            >
              <div class="flex flex-col items-center text-center">
                <div class="w-6 h-6 bg-gray-100 rounded-md flex items-center justify-center mb-2">
                  <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"
                    />
                  </svg>
                </div>
                <div class="text-xl font-bold text-gray-900">
                  {{ totalUnpaidLeaveUsed }}
                </div>
                <div class="text-xs text-gray-600">
                  {{ t('unpaidLeave') }}
                </div>
              </div>
            </div> -->
          </div>
        </div>
      </div>

      <!-- Loading Overlay -->
      <div
        v-if="loading"
        class="absolute inset-0 bg-white/80 backdrop-blur-sm z-10 flex items-center justify-center rounded-lg"
      >
        <div class="flex flex-col items-center gap-3">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
          <p class="text-sm text-gray-600">
            {{ t('loading') }}
          </p>
        </div>
      </div>

      <!-- Table Container -->
      <div class="relative">
        <EmployeeTab
          v-if="!showShiftInOneLine"
          :data-source="employeeDataSource"
          @handle-update-personal-attendance="handleUpdatePersonalAttendance"
        />
        <!-- Responsive Table Wrapper -->
        <!--
        <div class="overflow-x-auto">
          <a-tabs v-model:activeKey="activeTab" type="card">
            <a-tab-pane key="EMPLOYEE" :tab="t('tab.employeeData')">
              <EmployeeTab
                v-if="!showShiftInOneLine"
                :data-source="employeeDataSource"
                @handle-update-personal-attendance="handleUpdatePersonalAttendance"
              />
              <OneLineEmployeeTab
                v-else
                :data-source="employeeDataSource"
                @handle-update-personal-attendance="handleUpdatePersonalAttendance"
              />
            </a-tab-pane>
            <a-tab-pane key="OFFICE" :tab="t('tab.officeData')">
              <OfficeTab
                v-if="!showShiftInOneLine"
                :data-source="officeDataSource"
                @handle-update-monthly-attendance="handleUpdateMonthlyAttendance"
              />
              <OneLineOfficeTab
                v-else
                :data-source="officeDataSource"
                @handle-update-monthly-attendance="handleUpdateMonthlyAttendance"
              />
            </a-tab-pane>
            <template #rightExtra>
              <div class="flex items-center mr-4">
                <div class="flex items-center gap-2 mr-4">
                  <span>{{ t('showShiftInOneLine') }}</span>
                  <a-switch v-model:checked="showShiftInOneLine" />
                </div>
                <a-button
                  v-if="activeTab === 'OFFICE'"
                  type="primary"
                  class="ml-4 flex items-center"
                  @click="openWorkTimeModal"
                >
                  <template #icon>
                    <PlusOutlined />
                  </template>
                  {{ t('button.createNewAttendance') }}
                </a-button>
              </div>
            </template>
          </a-tabs>
        </div>
      -->
      </div>
    </div>

    <WorkTimeModal
      v-model:open="workTimeModalIsVisible"
      :type="workTimeModalType"
      @save-attendance="handleCreateNewAttendance"
    />
  </page-container>
</template>
