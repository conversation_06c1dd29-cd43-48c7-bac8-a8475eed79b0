<!-- src/views/CompanySetup.vue -->
<script setup lang="ts">
import { reactive } from 'vue'
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import type { ColumnType, TablePaginationConfig, TableProps } from 'ant-design-vue/es/table'
import { usePagination } from 'vue-request'
import type { PositionItem, PositionParams } from '~@/api/company/position'
import type { QueryParams } from '~@/api/common-params'
import { createPositionApi, deletePositionApi, getPositionListApi, updatePositionApi } from '~@/api/company/position'

const { t } = useI18n()

async function queryData(params: QueryParams) {
  const { data, status, message: mess } = await getPositionListApi(params)
  if (status === 200)
    return data
  else
    message.error(mess)
}
const {
  data,
  current,
  total,
  loading,
  pageSize,
  refresh,
  run,
} = usePagination(queryData, {
  defaultParams: [{
    pageNum: 1,
    pageSize: 10,
  }],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const pagination = computed(() => ({
  current: current.value,
  pageSize: pageSize.value,
  total: total.value,
}))

const handleTableChange: TableProps['onChange'] = (
  pagination: TablePaginationConfig,
) => {
  run({
    pageNum: pagination.current || 1,
    pageSize: pagination.pageSize || 10,
  })
}

// Position form
const positionForm = reactive<PositionParams & { positionId: string }>({
  positionId: '',
  positionCode: '',
  positionName: '',
  description: '',
})

const positionColumns = reactive<ColumnType<PositionItem>[]>([
  {
    title: t('position.code'),
    dataIndex: 'positionCode',
    key: 'positionCode',
    sorter: (a, b) => a.positionCode?.localeCompare(b.positionCode),
    defaultSortOrder: 'ascend',
  },
  {
    title: t('position.name'),
    dataIndex: 'positionName',
    key: 'positionName',
  },
  {
    title: t('position.description'),
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: t('action'),
    width: 120,
    key: 'action',
  },
])

const positionModal = reactive({
  loading: false,
  visible: false,
  isEdit: false,
})

// Position methods
function showPositionModal() {
  positionModal.isEdit = false
  positionModal.visible = true
}

function editPosition(position: PositionItem) {
  positionModal.isEdit = true
  positionModal.visible = true
  positionForm.positionId = position.positionId ?? ''
  positionForm.positionCode = position.positionCode ?? ''
  positionForm.positionName = position.positionName ?? ''
  positionForm.description = position.description ?? ''
}

async function handlePositionSubmit() {
  if (!positionForm.positionCode || !positionForm.positionName) {
    message.error(t('company.form.message'))
    return
  }

  if (positionModal.isEdit) {
    const { status, message: mess } = await updatePositionApi(positionForm.positionId, positionForm)
    if (status === 200) {
      positionModal.visible = false
      refresh()
      message.success(mess)
    }
    else {
      message.error(mess)
    }
  }
  else {
    const { status, message: mess } = await createPositionApi(positionForm)
    if (status === 200) {
      positionModal.visible = false
      message.success(mess)
    }
    else {
      message.error(mess)
    }
  }
}

async function deletePosition(positionId: string) {
  const { status, message: mess } = await deletePositionApi(positionId)
  if (status === 200) {
    message.success(mess)
    refresh()
  }
  else {
    message.error(mess)
  }
}
</script>

<template>
  <div class="mb-4">
    <a-button type="primary" @click="showPositionModal">
      <PlusOutlined /> {{ t('button.add') }}
    </a-button>
  </div>

  <a-table
    :columns="positionColumns"
    :data-source="data?.items ?? []"
    :row-key="(record: PositionItem) => record.positionId"
    :loading="loading"
    :pagination="pagination"
    @change="handleTableChange"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'action'">
        <a-space>
          <a-button type="link" @click="editPosition(record as PositionItem)">
            <EditOutlined />
          </a-button>
          <a-popconfirm
            :title="t('confirm.delete')"
            @confirm="deletePosition(record.positionId)"
          >
            <a-button type="link" danger>
              <DeleteOutlined />
            </a-button>
          </a-popconfirm>
        </a-space>
      </template>
    </template>
  </a-table>
  <!-- Position Modal -->
  <a-modal
    v-model:visible="positionModal.visible"
    :title="positionModal.isEdit ? t('position.edit') : t('position.add')"
    @ok="handlePositionSubmit"
  >
    <a-form :model="positionForm" layout="vertical">
      <a-form-item :label="t('position.code')" required name="positionCode">
        <a-input v-model:value="positionForm.positionCode" />
      </a-form-item>
      <a-form-item :label="t('position.name')" required name="positionName">
        <a-input v-model:value="positionForm.positionName" />
      </a-form-item>
      <a-form-item :label="t('position.description')" name="description">
        <a-textarea v-model:value="positionForm.description" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
