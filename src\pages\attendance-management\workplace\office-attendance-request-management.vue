<!-- eslint-disable antfu/top-level-function -->
<!-- eslint-disable curly -->
<script lang="ts" setup>
import {
  LeftOutlined,
  RightOutlined,
} from '@ant-design/icons-vue'
import { usePagination } from 'vue-request'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import {
  getRequestTypeList,
  getStatusList,
} from '~@/api/common/common'
import type {
  RequestTypeItem,
  StatusItem,
} from '~@/api/common/common'
import { getApproverRequestApi, rejectRequestApi } from '~@/api/dashboard/date-off-request'
import logger from '~@/utils/logger'

type RangeValue = [Dayjs, Dayjs]
const { t } = useI18n()
const rejectModalOpen = ref<boolean>(false)
const rejectReason = ref<string>('')
const currentRequestId = ref<string>()
const now = dayjs()
const messageNotify = useMessage()
const requestTypeData = ref<RequestTypeItem[]>([])
// const formFilterRef = ref()
// const visibleFilter = ref<boolean>(false)
// const initialFormFilterState = reactive<any>({
//   requestTypeId: undefined,
//   status: undefined,
// })
// const formFilterState = ref({ ...initialFormFilterState })
const statusData = ref<StatusItem[]>([])
// const items = ['attendance-request', 'timeshift-request']
// const activeIndex = ref<number>(0)
const currentMonth = ref(dayjs())
const dateRange = ref<RangeValue>([currentMonth.value.startOf('month'), currentMonth.value.endOf('month')])

const {
  data: listRequest,
  refresh,
  changeCurrent,
  current,
  totalPage,
  loading,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [
    {
      pageNum: 1,
      pageSize: 4,
    },
  ],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalPageKey: 'totalRecords',
  },
})

async function queryData(params: any) {
  try {
    params = {
      ...params,
      dateFrom: dateRange.value[0].format('YYYY-MM-DD'),
      dateTo: dateRange.value[1].format('YYYY-MM-DD'),
      requestType: 6,
      isAttendanceRequest: true,
    }

    const { data, status, code } = await getApproverRequestApi(params)
    if (status === 200) {
      logger.log('data', data)
      return data ?? []
    }
    else {
      logger.error(t(code))
    }
  }
  catch (error) {
    logger.error(error)
  }
}

// const approveRequest = async (requestId: string) => {
//   const params: ActionRequestParams = {
//     requestId,
//   }
//   try {
//     const { status, code, message } = await approveRequestApi(params)
//     if (status === 200) {
//       messageNotify.success(message)
//       refresh()
//     }
//     else {
//       logger.error(t(code))
//       messageNotify.error(t(code))
//     }
//   }
//   catch (e) {
//     logger.error(e)
//   }
// }

// const rejectRequest = (requestId: string) => {
//   currentRequestId.value = requestId
//   rejectModalOpen.value = true
// }

// const viewAttendanceMonthly = () => {

// }

const confirmReject = async () => {
  try {
    const id = currentRequestId.value ?? '';
    const { status, code, message } = await rejectRequestApi(id)
    if (status === 200) {
      messageNotify.success(message)
      rejectModalOpen.value = false
      rejectReason.value = ''
      refresh()
    }
    else {
      logger.error(t(code))
      messageNotify.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

async function getRequestType() {
  try {
    const { data, status, code } = await getRequestTypeList()
    if (status === 200) {
      requestTypeData.value = data?.items ?? []
    }
    else {
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

async function getRequestStatus() {
  try {
    const { data, status, code } = await getStatusList()
    if (status === 200) {
      statusData.value = data?.items ?? []
    }
    else {
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

// const setActive = (index: number) => {
//   activeIndex.value = index
//   changeCurrent(1)
// }

// const handleFieldFilter = () => {
//   formFilterState.value = { ...initialFormFilterState }
// }

// const handlefilter = () => {
//   visibleFilter.value = false
//   changeCurrent(1)
// }

// const handleClosefilter = () => {
//   visibleFilter.value = false
// }

// const handleClearFilter = () => {
//   handleFieldFilter()
//   changeCurrent(1)
// }

const nextMonth = () => {
  currentMonth.value = currentMonth.value.add(1, 'month')
  dateRange.value = [currentMonth.value.startOf('month'), currentMonth.value.endOf('month')]
  changeCurrent(1)
}

const prevMonth = () => {
  currentMonth.value = currentMonth.value.subtract(1, 'month')
  dateRange.value = [currentMonth.value.startOf('month'), currentMonth.value.endOf('month')]
  changeCurrent(1)
}

onMounted(async () => {
  const promises = [getRequestType(), getRequestStatus()]
  await Promise.all(promises)
})
</script>

<template>
  <page-container>
    <div class="flex justify-between mb-[34px]">
      <a-space :size="12" class="text-[20px] text-[#74797A] font-medium">
        <div>
          <span>{{ t('title.monthlyAttendanceRequest') }}</span>
        </div>
        <!-- <div
          v-for="(item, index) in items" :key="index" class="px-[2px] cursor-pointer" :class="{ 'text-[#101F23] dark:text-[#fff] position-relative isActive': activeIndex === index }"
          @click="setActive(index)"
        >
          {{ t(item) }}
        </div> -->
      </a-space>
      <a-space :size="12">
        <a-space :size="16">
          <CarbonCalendarFilter />
          <a-button class="w-[5px] flex items-center justify-center" @click="prevMonth">
            <LeftOutlined />
          </a-button>
          <div class="text-[20px] font-medium">
            {{ currentMonth.format('MMMM') }}
          </div>
          <div class="text-[20px] font-medium text-[#256CB5]">
            {{ currentMonth.format('YYYY') }}
          </div>
          <a-button class="w-[5px] flex items-center justify-center" @click="nextMonth">
            <RightOutlined />
          </a-button>
        </a-space>
        <!-- <a-popover v-model:open="visibleFilter" trigger="click" placement="bottomRight" overlay-class-name="filter-popover">
          <template #content>
            <div class="flex justify-between items-center h-[36px] px-[12px]">
              <a-space :size="4">
                <CarbonFilter size="12" />
                <div class="text-[12px]">
                  {{ t('filter') }}
                </div>
              </a-space>
              <div class="cursor-pointer" @click="handleClosefilter">
                <CarbonClose />
              </div>
            </div>
            <a-divider :style="{ margin: 0 }" />
            <a-form
              ref="formFilterRef" class="form-filter" layout="vertical" :model="formFilterState"
              @finish="handlefilter"
            >
              <a-form-item class="m-0 px-[12px] pt-[8px] pb-[16px]" :label="t('RequestType')" name="requestTypeId">
                <a-select
                  v-model:value="formFilterState.requestTypeId" :placeholder="t('RequestType')"
                  :options="requestTypeData" :field-names="{ label: 'requestTypeName', value: 'requestTypeId' }" @change="
                    formFilterState.leaveTypeId = formFilterState.requestTypeId === 1 ? 1 : undefined;
                  "
                />
              </a-form-item>
              <a-divider :style="{ margin: 0 }" />
              <a-form-item class="m-0 px-[12px] pt-[8px] pb-[16px]" :label="t('status')" name="status">
                <a-select
                  v-model:value="formFilterState.status" :placeholder="t('status')"
                  :field-names="{ label: 'statusName', value: 'statusId' }" :options="statusData"
                />
              </a-form-item>
              <a-divider :style="{ margin: 0 }" />
              <div class="flex justify-between p-[12px]">
                <a-button @click="handleFieldFilter">
                  {{ t('button.reset') }}
                </a-button>
                <a-button type="primary" html-type="submit">
                  {{ t('button.apply') }}
                </a-button>
              </div>
            </a-form>
          </template>
          <a-button>
            {{ t('button.filter') }}
            <DownOutlined />
          </a-button>
        </a-popover> -->
        <!-- <a-button @click="handleClearFilter">
          {{ t('button.reset') }}
        </a-button> -->
      </a-space>
    </div>
    <div class="flex flex-col gap-[24px]">
      <a-spin :spinning="loading">
        <template v-if="listRequest && (listRequest as any).items.length > 0">
          <template v-for="item in (listRequest as any).items" :key="item">
            <div class="w-full shadow-sm rounded-lg overflow-hidden mb-[24px] bg-[#fff] dark:bg-[#383838]">
              <div class="py-[12px] px-[48px]">
                <div class="flex justify-between items-center">
                  <div>
                    <div class="flex gap-[24px] items-center mb-[12px]">
                      <a-space :size="12">
                        <a-avatar :size="48" style="background-color: #D9D9D9;" />
                        <div class="font-medium text-[20px] text-[#256CB5]">
                          {{ item.createUserName }}
                        </div>
                      </a-space>
                      <h3 class="text-[20px] mb-0 text-[#101F23] dark:text-[#fff]">
                        {{ item.requestTypeName }}
                      </h3>
                      <div
                        class="text-[16px] py-[4px] px-[12px] rounded-lg" :class="{
                          'bg-[#FFE3E5] text-[#BD3D44]': item.statusCode === 'REJECTED',
                          'bg-[#DCF6E0] text-[#106B1E]': item.statusCode === 'APPROVED',
                          'bg-[#DEF0FF] text-[#24598E]': item.statusCode === 'PENDING',
                          'bg-[#E4E4E2] text-[#3A3B3C]': item.statusCode === 'CANCELLED',
                        }"
                      >
                        {{ item.statusName }}
                      </div>
                    </div>
                    <div class="flex gap-[24px]">
                      <div v-if="item.leaveTypeName" class="flex">
                        <div class="mr-[8px]">
                          {{ t('type') }}
                        </div>
                        <div>{{ item.leaveTypeName }}</div>
                      </div>
                      <div class="flex text-[#101F23] items-center">
                        <div class="mr-[5px]">
                          <carbon-calendar />
                        </div>
                        <div class="mr-[12px] dark:text-[#fff]">
                          {{ item.quantity }} {{ item.unitCounter }}
                        </div>
                        <div class="mr-[8px] dark:text-[#fff]">
                          {{ dayjs(item.requestFrom).format('YYYY/MM/DD') }}
                        </div>
                        <div class="mr-[8px]">
                          <carbon-arrow />
                        </div>
                        <div class="dark:text-[#fff]">
                          {{ dayjs(item.requestTo).format('YYYY/MM/DD') }}
                        </div>
                      </div>
                      <div class="flex">
                        <!-- <div class="mr-[8px]">
                          {{ t('time-request') }}
                        </div> -->
                        <div>
                          {{ now.diff(item.createTime, 'day') > 30 ? 30 : now.diff(item.createTime, 'day') }} days ago
                        </div>
                      </div>
                    </div>
                    <div class="flex">
                      <div class="mr-[8px] font-medium">
                        {{ t('reason') }}
                      </div>
                      <div class="dark:text-[#fff]">
                        {{ item.description }}
                      </div>
                    </div>
                  </div>
                  <div class="flex gap-x-[24px]">
                    <a-tooltip>
                      <template #title>{{ t('view-detail') }}</template>
                      <a-button>
                        <template #icon>
                          <carbon-view class="cursor-pointer" />
                        </template>
                      </a-button>
                    </a-tooltip>
                  </div>
                  <!-- <template v-if="item.statusCode === 'PENDING'">
                    <div class="flex gap-x-[24px]">
                      <carbon-reject class="cursor-pointer" @click="rejectRequest(item.requestId)" />
                      <carbon-approve class="cursor-pointer" @click="approveRequest(item.requestId)" />
                    </div>
                  </template> -->
                </div>
              </div>
            </div>
          </template>
        </template>
        <template v-else>
          <a-empty />
        </template>
      </a-spin>
      <div v-if="totalPage > 4" class="flex justify-center">
        <a-pagination
          v-model:current="current" :total="totalPage" :page-size="pageSize" show-less-items
          class="pagination-request"
        />
      </div>
    </div>
    <a-modal
      v-model:open="rejectModalOpen" width="300px" :title="t('title.reject')" :ok-text="t('button.confirm')" :cancel-text="t('button.cancel')"
      @ok="confirmReject"
    >
      <a-textarea v-model:value="rejectReason" :rows="3" :placeholder="t('placeholder.enter-reason')" />
    </a-modal>
  </page-container>
</template>

<style lang="less">
.isActive::before {
  content: '';
  display: block;
  position: absolute;
  bottom: -10px;
  left: 0;
  height: 4px;
  width: 100%;
  background-color: #3E93DC;
}
</style>
