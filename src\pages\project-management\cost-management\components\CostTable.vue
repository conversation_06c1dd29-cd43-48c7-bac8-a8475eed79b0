<script lang="ts" setup>
import type { CostItem } from '~@/api/construction'

defineProps<{
  items: CostItem[]
}>()

function formatNumber(value: number) {
  return value.toLocaleString()
}
</script>

<template>
  <div class="overflow-auto">
    <table class="min-w-full">
      <tbody>
        <tr v-for="(item, index) in items" :key="index" class="border-b">
          <td class="py-2">
            {{ item.sequenceNumber }}
          </td>
          <td class="py-2 text-right">
            {{ formatNumber(item.amount) }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
