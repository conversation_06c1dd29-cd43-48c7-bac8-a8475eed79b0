<script setup lang="ts">
import { ref } from 'vue'
import type { CostCategoryItem, GetCostCategoryParams } from '~@/api/company/cost-category'
import type { InputCostItem, InputCostItemQuery } from '~@/api/input-cost-item'
import { getInputCostItemListApi } from '~@/api/input-cost-item'
import { useCategory } from '~@/composables/category/useCategory'

const props = defineProps({
  constructionId: {
    type: String,
    required: true,
  },
  isPrimary: {
    type: Boolean,
    required: true,
  },
})

const { t } = useI18n()

const costData = ref<InputCostItem[]>([])
const maxLengthTableOne = ref(2)
const maxLengthTableTwo = ref(2)

// Map category names to their respective codes
const categoryMap: Record<string, string> = {}

// Get items for a specific category and position
function getCategoryItems(categoryName: string, position: number, index: number) {
  const categoryCode = categoryMap[categoryName]

  if (!categoryCode)
    return []

  // Filter items by category and get items at specific position
  const itemsInCategory = costData.value.filter(
    item => item.categoryCode === categoryCode,
  )

  if (index < 5)
    maxLengthTableOne.value = Math.max(maxLengthTableOne.value, itemsInCategory.length)
  else
    maxLengthTableTwo.value = Math.max(maxLengthTableTwo.value, itemsInCategory.length)

  // Return item at position or empty array if not exists
  return position < itemsInCategory.length ? [itemsInCategory[position]] : []
}

// Calculate total for a specific category
function getCategoryTotal(categoryName: string) {
  const categoryCode = categoryMap[categoryName]

  if (!categoryCode)
    return 0

  return costData.value
    .filter(item => item.categoryCode === categoryCode)
    .reduce((sum, item) => sum + (item.totalTaxed ?? 0), 0)
}

// Calculate total amount for all categories
function getTotalAmount() {
  return costData.value.reduce((sum, item) => sum + (item.totalTaxed ?? 0), 0)
}

// Format number with Japanese yen formatting
function formatNumber(value: number | null) {
  if (!value)
    return 0
  return value.toLocaleString('ja-JP')
}

async function fetchInputCostItem() {
  const params: InputCostItemQuery = {
    constructionId: props.constructionId,
    categoryId: undefined,
    inputCostId: undefined,
    pageNum: 1,
    pageSize: 100,
    projectId: undefined,
    vendorId: undefined,
  }
  const { data, status } = await getInputCostItemListApi(params)

  if (status === 200)
    costData.value = data?.items ?? []
}

const { categoryData, fetchCategory } = useCategory()

async function loadCategoryData() {
  const params: GetCostCategoryParams = {
    keyword: undefined,
    parentId: undefined,
    pageNum: 1,
    pageSize: 100,
  }
  await fetchCategory(params)
}

function createCategoryMap() {
  categoryData.value.forEach((item: CostCategoryItem) => {
    categoryMap[item.categoryName] = item.categoryCode
  })
}

onMounted(async () => {
  await loadCategoryData()
  createCategoryMap()
  await fetchInputCostItem()
})

// function handleExportExcel() {
//   exportConstructionCostSummary({
//     costData: costData.value,
//     categoryData: categoryData.value,
//     isPrimary: props.isPrimary,
//     getCategoryItems,
//     getCategoryTotal,
//     formatNumber,
//   })
// }
</script>

<template>
  <div class="p-4">
    <div class="bg-white shadow-md rounded-md overflow-hidden">
      <!-- Add export button -->
      <!-- <div class="flex justify-end p-2">
        <a-button
          type="primary"
          @click="handleExportExcel"
        >
          <template #icon>
            <FileExcelOutlined />
          </template>
          エクスポート
        </a-button>
      </div> -->
      <!-- Header -->
      <div class="bg-[#DEF0FF] text-[#24598E] p-3 text-center font-bold text-lg">
        <span v-if="isPrimary">{{ t('mainConstructionCost') }}</span>
        <span v-else>{{ t('subConstructionCost') }}</span>
      </div>

      <!-- Main content -->
      <div class="overflow-x-auto">
        <table class="w-full border-collapse">
          <thead>
            <!-- Category Headers -->
            <tr class="bg-gray-100">
              <template v-for="(item, index) in categoryData" :key="item.categoryCode">
                <th v-if="index < 5" class="border border-gray-300 p-2 w-1/8 border-solid">
                  {{ item.categoryName }}
                </th>
              </template>
            </tr>
          </thead>
          <tbody>
            <!-- Main content rows -->
            <tr v-for="i in maxLengthTableOne + 3" :key="i" class="hover:bg-gray-50">
              <template v-for="(categoryItem, index) in categoryData" :key="categoryItem.categoryCode">
                <td v-if="index < 5" class="border border-gray-300 p-2 border-solid">
                  <div v-if="getCategoryItems(categoryItem.categoryName, i - 1, index).length > 0">
                    <div v-for="item in getCategoryItems(categoryItem.categoryName, i - 1, index)" :key="item.itemId!">
                      <template v-if="categoryItem.categoryCode === 'EMPLOYEE' || categoryItem.categoryCode === 'OUTSOURCE'">
                        <div class="grid grid-cols-2 text-sm gap-2">
                          <div>
                            <span class="font-medium">{{ item.itemName }}</span>
                          </div>
                          <div class="flex justify-between">
                            <span>{{ t('price') }}</span>
                            <span>¥{{ formatNumber(item?.averagePrice) }}</span>
                          </div>
                          <div class="flex gap-2">
                            <span>{{ formatNumber(item?.quantity) }}</span>
                            <span>{{ t('manDay') }}</span>
                          </div>
                          <div class="flex justify-between">
                            <span>{{ t('summary-detail.amount') }}</span>
                            <span>¥{{ formatNumber(item?.totalAverageAmount) }}</span>
                          </div>
                        </div>
                      </template>
                      <template v-else>
                        <div class="flex justify-between text-sm">
                          <span class="font-medium">{{ item.itemName }}</span>
                          <span>{{ item.quantity }} {{ item.unit }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                          <span />
                          <span>¥{{ formatNumber(item.totalTaxed) }}</span>
                        </div>
                      </template>
                    </div>
                  </div>
                </td>
              </template>
            </tr>

            <!-- Subtotal row -->
            <tr class="bg-gray-100 font-medium">
              <template v-for="(categoryItem, index) in categoryData" :key="categoryItem.categoryCode">
                <td v-if="index < 5" class="border border-gray-300 border-solid p-2">
                  <div class="flex justify-between">
                    <span>{{ t('subtotal') }}</span>
                    <span>¥{{ formatNumber(getCategoryTotal(categoryItem.categoryName)) }}</span>
                  </div>
                </td>
              </template>
            </tr>
          </tbody>
        </table>

        <table class="w-full border-collapse">
          <thead>
            <!-- Category Headers -->
            <tr class="bg-gray-100">
              <template v-for="(item, index) in categoryData" :key="item.categoryCode">
                <th v-if="index >= 5" class="border border-gray-300 border-solid p-2 w-1/8">
                  {{ item.categoryName }}
                </th>
              </template>
            </tr>
          </thead>
          <tbody>
            <!-- Main content rows -->
            <tr v-for="i in maxLengthTableTwo + 3" :key="i" class="hover:bg-gray-50">
              <template v-for="(categoryItem, index) in categoryData" :key="categoryItem.categoryCode">
                <td v-if="index >= 5" class="border border-gray-300 border-solid p-2">
                  <div v-if="getCategoryItems(categoryItem.categoryName, i - 1, index).length > 0">
                    <div v-for="item in getCategoryItems(categoryItem.categoryName, i - 1, index)" :key="item.itemId!">
                      <template v-if="categoryItem.categoryCode === 'EMPLOYEE' || categoryItem.categoryCode === 'OUTSOURCE'">
                        <div class="grid grid-cols-2 text-sm gap-2">
                          <div>
                            <span class="font-medium">{{ item?.itemName }}</span>
                          </div>
                          <div class="flex justify-between">
                            <span>{{ t('price') }}</span>
                            <span>¥{{ formatNumber(item?.averagePrice) }}</span>
                          </div>
                          <div class="flex gap-2">
                            <span>{{ formatNumber(item?.quantity) }}</span>
                            <span>{{ t('manDay') }}</span>
                          </div>
                          <div class="flex justify-between">
                            <span>{{ t('summary-detail.amount') }}</span>
                            <span>¥{{ formatNumber(item?.totalAverageAmount) }}</span>
                          </div>
                        </div>
                      </template>
                      <template v-else>
                        <div class="flex justify-between text-sm">
                          <span class="font-medium">{{ item?.itemName }}</span>
                          <span>{{ item.quantity }} {{ item?.unit }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                          <span />
                          <span>¥{{ formatNumber(item?.totalTaxed) }}</span>
                        </div>
                      </template>
                    </div>
                  </div>
                </td>
              </template>
            </tr>
            <!-- Subtotal row -->
            <tr class="bg-gray-100 font-medium">
              <template v-for="(categoryItem, index) in categoryData" :key="categoryItem.categoryCode">
                <td v-if="index >= 5" class="border border-gray-300 border-solid p-2">
                  <div class="flex justify-between">
                    <span>{{ t('subtotal') }}</span>
                    <span>¥{{ formatNumber(getCategoryTotal(categoryItem?.categoryName)) }}</span>
                  </div>
                </td>
              </template>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Total -->
      <div class="p-4 bg-gray-100">
        <div class="flex justify-end space-x-6">
          <span class="font-bold">{{ t('grandTotal') }}:</span>
          <span class="font-bold text-xl">¥{{ formatNumber(getTotalAmount()) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
