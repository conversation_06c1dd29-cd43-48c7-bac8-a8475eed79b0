import type { QueryParams } from '../common-params';

export interface RoleParams {
  roleId?: number;
  orgId?: number;
  roleName?: string;
  description?: string;
  assignedUserIds?: number[];
  structureId?: string;
}

export interface SimpleEmployeeInfo {
  employeeId: string;
  employeeName: string;
}

export interface Role {
  roleId: string;
  roleName: string;
  description?: string;
  isDefault?: boolean;
  assignedEmployees?: SimpleEmployeeInfo[];
}

export interface RoleResponse {
  items: Role[];
  pageIndex: number;
  pageSize: number;
  totalRow?: number;
  pageCount?: number;
}

export interface RoleListParams extends QueryParams {
  keyword?: string;
  structureId?: string;
}

export function createRoleApi(role: RoleParams) {
  return usePost<any>('v1/role', role);
}

export function updateRoleApi(roleId: string, role: RoleParams) {
  return usePut<any>(`v1/role/${roleId}`, role);
}

export function deleteRoleApi(id: string) {
  return useDelete<any>(`v1/role/${id}`);
}
export function getListRolesApi(params?: RoleListParams) {
  return useGet<RoleResponse>('v1/role', params);
}

export function getRoleHierarchyApi() {
  return useGet<RoleResponse>('v1/role/hierarchy');
}
