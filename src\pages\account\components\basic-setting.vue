<script setup lang="ts">
import { UploadOutlined } from '@ant-design/icons-vue'
import logger from '~@/utils/logger'
import type { UserInfoItem, UserInfoParams } from '~@/types/company/user-info'
import { getUserInfoAvatarApi, updateUserInfoApi } from '~@/api/company/user-info'
import { RuleObject } from 'ant-design-vue/es/form'
import axios from 'axios'

const userStore = useUserStore()
const { t } = useI18n()
const messageNotify = useMessage()

const formRef = ref()
const labelCol = { span: 0 }
const wrapperCol = { span: 13 }
const loading = ref(false)
const token = useAuthorization()
const baseUrl = import.meta.env.VITE_APP_BASE_API
const imageSrc = ref<string>('')
const formState = reactive<UserInfoItem>({
  userName: '',
  email: '',
  address: '',
  phone: '',
  gender: false,
  birthday: '',
  avatarUrl: null,
})

const rules = computed<Record<string, RuleObject[]>>(() => ({
  userName: [
    { required: true, message: t('account.settings.form-rule-name'), trigger: 'change' },
  ],
  phone: [
    { required: true, message: t('account.settings.form-rule-phoneNumber'), trigger: 'change' },
  ],
  gender: [
    { required: true, message: t('account.settings.form-rule-gender'), trigger: 'change' },
  ],
  address: [
    { required: true, message: t('account.settings.form-rule-address'), trigger: 'change' },
  ],
  email: [
    { required: true, message: t('account.settings.form-rule-email'), trigger: 'change' },
    { type: 'email', message: t('account.settings.form-rule-email-format'), trigger: 'change' },
  ],
  birthday: [
    { required: true, message: t('account.settings.form-rule-birthday'), trigger: 'change' },
  ],
}))

// Fetch user info when component mounts
async function fetchUserInfo() {
  userStore.getEmployeeInfo()
  // try {
  //   loading.value = true
  //   const { data, status, message } = await getUserInfoApi()
    
  //   if (status === 200 && data) {
  //     // Update reactive form state with fetched data
  //     Object.assign(formState, data)
  //     formState.avatarUrl = `${baseUrl}/v1/${data.avatarUrl}`
  //   } else {
  //     messageNotify.error(message || t('account.settings.fetch-failed'))
  //   }
  // } catch (error) {
  //   console.error('Error fetching user info:', error)
  //   messageNotify.error(t('account.settings.fetch-failed'))
  // } finally {
  //   loading.value = false
  // }
}

async function fetchUserAvatar() {
  try {
    const { data, status } = await getUserInfoAvatarApi()
    if (status === 200 && data) {
      imageSrc.value = `data:image/jpeg;base64,${data.avatarBase64}`
    } else {
    }
  } catch (error) {
    messageNotify.error(t('account.settings.avatar-fetch-failed'))
  }
}

// Handle form submission
async function onSubmit() {
  try {
    // Validate form first
    await formRef.value.validate()
    
    loading.value = true
    const params: UserInfoParams = {
      name: formState.userName,
      email: formState.email,
      address: formState.address,
      phone: formState.phone,
      gender: formState.gender,
      birthday: formState.birthday,
    }
    const { status, message } = await updateUserInfoApi(params)

    if (status === 200) {
      messageNotify.success(t('account.settings.update-success'))
      await fetchUserInfo() // Refresh data after successful update
    } else {
      messageNotify.error(message || t('account.settings.update-failed'))
    }
  } catch (error) {
    if (error instanceof Error) {
      messageNotify.error(error.message)
    }
  } finally {
    loading.value = false
  }
}

// Custom upload handler
const handleCustomUpload = async (options: any) => {
  logger.log('options', options)
  const { file, onSuccess, onError } = options
  const formData = new FormData()
  formData.append('file', file)

  try {
    await axios.put(`${baseUrl}/v1/userinfo/avatar`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'Authorization': token.value
      }
    })
    onSuccess()
    fetchUserAvatar()
    messageNotify.success(t('uploadAvatarSuccess'))
  } catch (error) {
    onError()
  }
}

// Handle avatar upload
function handleAvatarChange(info: any) {
  logger.log('info', info)
  if (info.file.status === 'done') {
    messageNotify.success(t('account.settings.avatar-upload-success'))
    fetchUserInfo()
    fetchUserAvatar()
  } else if (info.file.status === 'error') {
    messageNotify.error(t('account.settings.avatar-upload-failed'))
  }
}

// Fetch user info when component mounts
onMounted(() => {
  Object.assign(formState, userStore.userInfo)
  fetchUserAvatar()
})
</script>

<template>
  <a-card :title="t('account.settings.basic-setting')" :bordered="false">
    <a-spin :spinning="loading">
      <a-row>
        <a-col :span="12">
          <a-form
            ref="formRef"
            :model="formState"
            :rules="rules"
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
          >
            <a-form-item :label-col="{ span: 24 }" :label="t('name')" name="userName">
              <a-input
                v-model:value="formState.userName"
                :placeholder="t('account.settings.form-input-plac')"
                style="width: 320px;"
              />
            </a-form-item>
            <div class="flex">
              <a-form-item :label-col="{ span: 24 }" :label="t('birthday')" name="birthday">
                <a-date-picker
                  v-model:value="formState.birthday"
                  :placeholder="t('account.settings.form-input-plac')"
                  value-format="YYYY-MM-DD"
                />
              </a-form-item>
              <a-form-item :label-col="{ span: 24 }" :label="t('gender')" name="gender">
                <a-radio-group v-model:value="formState.gender" class="flex">
                  <a-radio :value="true">
                    {{ t('account.settings.form-male') }}
                  </a-radio>
                  <a-radio :value="false">
                    {{ t('account.settings.form-female') }}
                  </a-radio>
                </a-radio-group>
              </a-form-item>
            </div>
            <a-form-item :label-col="{ span: 24 }" :label="t('account.settings.form-email')" name="email">
              <a-input
                v-model:value="formState.email"
                :placeholder="t('account.settings.form-input-plac')"
                style="width: 320px;"
              />
            </a-form-item>
            <a-form-item :label-col="{ span: 24 }" :label="t('account.settings.form-phoneNumber')" name="phone">
              <a-input
                v-model:value="formState.phone"
                :placeholder="t('account.settings.form-input-plac')"
              />
            </a-form-item>
            <a-form-item :label-col="{ span: 24 }" :label="t('account.settings.form-address')" name="address">
              <a-input
                v-model:value="formState.address"
                :placeholder="t('account.settings.form-input-plac')"
                style="width: 320px;"
              />
            </a-form-item>
           
            <a-form-item>
              <a-button type="primary" :loading="loading" @click="onSubmit">
                {{ t('account.settings.form-submit') }}
              </a-button>
            </a-form-item>
          </a-form>
        </a-col>
        <a-col :span="4">
          <p>{{ t('account.settings.basic-avatar') }}</p>
          <div class="flex flex-col items-center">
            <a-avatar :size="100">
              <template #icon>
                <img
                  :src="imageSrc"
                  alt="avatar"
                >
              </template>
            </a-avatar>
            <a-upload
              name="file"
              :file-list="[]"
              :show-upload-list="false"
              :custom-request="handleCustomUpload"
              @change="handleAvatarChange"
            >
              <a-button class="mt-4">
                <UploadOutlined />
                {{ t('account.settings.basic-avatar.upload') }}
              </a-button>
            </a-upload>
          </div>
        </a-col>
      </a-row>
    </a-spin>
  </a-card>
</template>
