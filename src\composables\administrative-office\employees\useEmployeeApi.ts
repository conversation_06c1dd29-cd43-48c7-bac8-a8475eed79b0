import type { QueryParams } from '~@/api/common-params'
import { getPositionListApi } from '~@/api/company/position'
import { getListRolesApi } from '~@/api/company/role'
import { getStructureListApi } from '~@/api/company/struct'

export function useEmployeeApi() {
  // Fetch chức vụ
  const fetchPositions = async (params: QueryParams) => {
    const { data } = await getPositionListApi(params)
    return data?.items ?? []
  }

  // Fetch danh sach phong ban
  const fetchStructures = async (params: QueryParams) => {
    const { data } = await getStructureListApi(params)
    return data?.items ?? []
  }

  // Fetch vai trò
  const fetchRoles = async (params: QueryParams) => {
    const { data } = await getListRolesApi(params)
    return data?.items ?? []
  }

  //

  return {
    fetchPositions,
    fetchStructures,
    fetchRoles,
  }
}
