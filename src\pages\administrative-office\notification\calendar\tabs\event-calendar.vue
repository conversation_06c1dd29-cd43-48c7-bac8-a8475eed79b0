<script lang="ts" setup>
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import * as holiday_jp from '@holiday-jp/holiday_jp';
import type { CSSProperties } from 'vue';
import {
  CloseOutlined,
  LeftOutlined,
  RightOutlined,
} from '@ant-design/icons-vue';
import { Empty } from 'ant-design-vue';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import type { Holiday } from '@holiday-jp/holiday_jp/lib/types';
import logger from '~@/utils/logger';
import type { EventCalendarRuleItem } from '~@/api/company/event-calendar';
import { getEventCalendarDate } from '~@/api/company/event-calendar';
import { ResponseStatusEnum } from '~@/enums/response-status-enum';

dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);

const selectedValue = ref<Dayjs>(dayjs());
const currentDate = ref<Dayjs>();
const publicHolidays = ref<Holiday<Date>[]>([]);
const companyHolidays = ref<EventCalendarRuleItem[]>([]);
const { t } = useI18n();
const selectedYear = ref(dayjs().year());
const calendarRef = ref();
const { width } = useElementSize(calendarRef);
const openModal = ref<boolean>(false);

const getMonthFromYear = computed(() => {
  const year = selectedYear.value;
  return Array.from({ length: 12 }, (_, i) => {
    const startOfMonth = dayjs().year(year).month(i).startOf('month');
    const endOfMonth = startOfMonth.endOf('month');
    return {
      id: i,
      initDateValue: startOfMonth,
      startOfMonth,
      endOfMonth,
    };
  });
});

const getCurrentStyle = computed(() => {
  return (current: Dayjs, validRange: Dayjs[]) => {
    currentDate.value = current;
    const [start, end] = validRange;
    const style: CSSProperties = {};

    if (
      current.format('YYYY-MM-DD')
        === selectedValue.value.format('YYYY-MM-DD')
      && current.isSameOrBefore(end, 'date')
      && current.isSameOrAfter(start, 'date')
    ) {
      style['background-color'] = '#1890ff';
      style.color = 'white';
      style['border-radius'] = '50%';
    }

    if (
      getPublicHoliday.value(current.format('YYYY-MM-DD')).show
      && current.isSameOrBefore(end, 'date')
      && current.isSameOrAfter(start, 'date')
    ) {
      style.color = 'white';
      style['background-color'] = 'red';
      style['border-radius'] = '50%';
    }

    if (
      getCompanyHolidays.value(current.format('YYYY-MM-DD')).length > 0
      && current.isSameOrBefore(end, 'date')
      && current.isSameOrAfter(start, 'date')
    ) {
      style.color = 'white';
      style['background-color'] = 'blue';
      style['border-radius'] = '50%';
    }

    return style;
  };
});

async function getListCompanyHolidays() {
  const fromDate = dayjs().year(selectedYear.value).startOf('year');
  const toDate = fromDate.endOf('year');
  const eventData = await getEventCalendarDate({
    from: fromDate.format('YYYY-MM-DD'),
    to: toDate.format('YYYY-MM-DD'),
  });
  if (eventData.status === ResponseStatusEnum.SUCCESS)
    companyHolidays.value = eventData.data?.eventCalendars ?? [];
}

const getCompanyHolidays = computed(() => {
  return (value: string) => {
    const items = companyHolidays.value.filter(
      (holiday: EventCalendarRuleItem) => holiday.applyDate === value,
    );
    return items;
  };
});

function getListPublicHolidays() {
  const startOfYear = dayjs().year(selectedYear.value).startOf('year');
  const endOfYear = startOfYear.endOf('year');
  publicHolidays.value = holiday_jp.between(
    startOfYear.toDate(),
    endOfYear.toDate(),
  );
  logger.log('public holidays:', publicHolidays.value);
}

const getPublicHoliday = computed(() => {
  return (value: string) => {
    const item = publicHolidays.value.find(
      (holiday: Holiday<Date>) =>
        dayjs(holiday.date).format('YYYY-MM-DD') === value,
    );
    const label = t('locale') === 'ja' ? item?.name : item?.name_en;
    return { ...item, show: !!item, label };
  };
});

function onDateSelect(
  date: Dayjs,
  info: { source: 'year' | 'month' | 'date' | 'customize' },
) {
  if (info.source === 'date') {
    selectedValue.value = date;
    openModal.value = true;
    selectedYear.value = date.year();
  }
}

function decrementYear() {
  selectedYear.value -= 1;
}

function incrementYear() {
  selectedYear.value += 1;
}

const getColSpan = computed(() => {
  if (width.value < 520)
    return { span: 24 };
  return {
    span: 6,
    xxl: 6,
    xl: 8,
    lg: 8,
    md: 12,
    sm: 12,
    xs: 24,
  };
});

const showTooltip = computed(() => {
  return (current: string) => {
    return (
      getPublicHoliday.value(current).show
      || getCompanyHolidays.value(current).length > 0
    );
  };
});

onMounted(async () => {
  await getListCompanyHolidays();
  getListPublicHolidays();
});

watch(selectedYear, async () => {
  await getListCompanyHolidays();
  getListPublicHolidays();
});
</script>

<template>
  <page-container>
    <a-row :gutter="[2, 2]" class="event-calendar">
      <a-col flex="1">
        <a-card>
          <template #title>
            <div class="flex justify-between">
              <div>
                <div class="flex justify-center items-center gap-2">
                  <a-button
                    class="flex items-center justify-center"
                    size="small"
                    @click="decrementYear"
                  >
                    <LeftOutlined />
                  </a-button>
                  <a-typography-text class="text-base" color="primary">
                    {{ selectedYear }}
                  </a-typography-text>
                  <a-button
                    class="flex items-center justify-center"
                    size="small"
                    @click="incrementYear"
                  >
                    <RightOutlined />
                  </a-button>
                  <img
                    width="20"
                    height="20"
                    src="/icon/calendar.svg"
                    alt="calendar"
                  >
                </div>
              </div>
              <div>
                <a-button
                  size="small"
                  type="primary"
                  @click="onDateSelect(dayjs(), { source: 'date' })"
                >
                  {{ t('title.today') }}
                </a-button>
              </div>
            </div>
          </template>

          <a-row ref="calendarRef" :gutter="[24, 24]">
            <template v-for="item in getMonthFromYear" :key="item.id">
              <a-col :="getColSpan">
                <a-calendar
                  :value="item.initDateValue"
                  :fullscreen="false"
                  :valid-range="[item.startOfMonth, item.endOfMonth]"
                  @select="onDateSelect"
                >
                  <template #headerRender>
                    <div class="flex justify-center font-bold">
                      {{
                        item.initDateValue.locale(t('locale')).format('MMMM')
                      }}
                    </div>
                  </template>
                  <template #dateFullCellRender="{ current }">
                    <a-tooltip
                      v-if="showTooltip(current.format('YYYY-MM-DD'))"
                      color="white"
                      class="cursor-pointer"
                    >
                      <template #title>
                        <a-card
                          border-style="none"
                          :body-style="{ padding: '10px' }"
                        >
                          <a-timeline class="timeline">
                            <a-timeline-item
                              v-if="
                                getPublicHoliday(current.format('YYYY-MM-DD'))
                                  .show
                              "
                              color="red"
                            >
                              <div>{{ current.format('YYYY-MM-DD') }}</div>
                              <div color="red">
                                {{
                                  getPublicHoliday(current.format('YYYY-MM-DD'))
                                    .label
                                }}
                              </div>
                            </a-timeline-item>
                            <a-timeline-item
                              v-for="holiday in getCompanyHolidays(
                                current.format('YYYY-MM-DD'),
                              )"
                              :key="holiday.eventId"
                              :color="holiday.isDayOff ? 'orange' : 'blue'"
                            >
                              <div class="flex gap-2">
                                {{ current.format('YYYY-MM-DD') }}
                                <a-tag v-if="holiday.isDayOff" color="red">
                                  {{ t('holiday') }}
                                </a-tag>
                              </div>
                              <div
                                :color="holiday.isDayOff ? 'orange' : 'blue'"
                              >
                                {{ holiday.eventName }}
                              </div>
                            </a-timeline-item>
                          </a-timeline>
                        </a-card>
                      </template>
                      <div
                        class="ant-picker-cell-inner"
                        :style="
                          getCurrentStyle(current, [
                            item.startOfMonth,
                            item.endOfMonth,
                          ])
                        "
                      >
                        {{ current.date() }}
                      </div>
                    </a-tooltip>
                    <div
                      v-else
                      class="ant-picker-cell-inner cursor-pointer"
                      :style="
                        getCurrentStyle(current, [
                          item.startOfMonth,
                          item.endOfMonth,
                        ])
                      "
                    >
                      {{ current.date() }}
                    </div>
                  </template>
                </a-calendar>
              </a-col>
            </template>
          </a-row>
        </a-card>
      </a-col>
      <a-col v-if="openModal" class="sm:flex-[0_0_300px]">
        <a-card size="small" class="fixed w-[300px]" :bordered="false">
          <template #title>
            <div class="flex items-center gap-2">
              <CarbonCalendar color="#74797a" />
              <a-typography-text style="font-size: 15px; color: #256cb5">
                {{ selectedValue.format('DD.MM.YYYY') }}
              </a-typography-text>
              <a-typography-text style="font-size: 14px; color: #101f23">
                {{ selectedValue.format('ddd') }}
              </a-typography-text>
            </div>
          </template>
          <template #extra>
            <a-button
              type="text"
              size="small"
              class="flex justify-center items-center"
              @click="openModal = false"
            >
              <CloseOutlined />
            </a-button>
          </template>
          <div>
            <a-timeline v-if="showTooltip(selectedValue.format('YYYY-MM-DD'))">
              <a-timeline-item
                v-if="getPublicHoliday(selectedValue.format('YYYY-MM-DD')).show"
                color="red"
              >
                <div>{{ selectedValue.format('YYYY-MM-DD') }}</div>
                <div>
                  {{
                    getPublicHoliday(selectedValue.format('YYYY-MM-DD')).label
                  }}
                </div>
              </a-timeline-item>
              <a-timeline-item
                v-for="holiday in getCompanyHolidays(
                  selectedValue.format('YYYY-MM-DD'),
                )"
                :key="holiday.eventId"
                :color="holiday.isDayOff ? 'orange' : 'blue'"
              >
                <a-collapse ghost expand-icon-position="end">
                  <a-collapse-panel key="1">
                    <template #header>
                      <div class="flex gap-2">
                        {{ selectedValue.format('YYYY-MM-DD') }}
                        <a-tag v-if="holiday.isDayOff" color="red">
                          {{ t('holiday') }}
                        </a-tag>
                      </div>
                      <div :color="holiday.isDayOff ? 'orange' : 'blue'">
                        {{ holiday.eventName }}
                      </div>
                    </template>
                    <div>
                      <span color="blue">
                        {{ t('form.event-start-time') }} </span>:
                      {{ holiday.eventStartTime }}
                    </div>
                    <div>
                      <span color="blue"> {{ t('form.event-end-time') }} </span>:
                      {{ holiday.eventEndTime }}
                    </div>
                    <div>
                      <span color="blue"> {{ t('form.description') }} </span>:
                      {{ holiday.description }}
                    </div>
                  </a-collapse-panel>
                </a-collapse>
              </a-timeline-item>
            </a-timeline>
            <a-empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
          </div>
        </a-card>
      </a-col>
    </a-row>
  </page-container>
</template>

<style lang="less" scoped>
.timeline {
  :deep(.ant-timeline-item-last) {
    padding-bottom: 0;
  }
  :deep(.ant-timeline-item-content) {
    min-height: auto;
  }
}
.event-calendar {
  :deep(.ant-collapse-header) {
    padding: 0;
  }
  // :deep(.ant-picker-content) {
  // height: 176px;
  // }
  :deep(td) {
    padding: 0 !important;
  }
}
</style>
