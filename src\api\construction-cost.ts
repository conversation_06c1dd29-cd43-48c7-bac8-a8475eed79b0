import type { ConstructionType } from '~@/utils/constant'

// src/types/construction.ts
export interface ConstructionPaymentRequest {
  constructionId: string
  requestAmount: number
  retentionAmount: number
  releasedAmount: number
  totalClaimedAmount: number
}

export interface SubCategory {
  subCategoryId: string
  subCategoryName: string
  workload: number
  amount: number
  avgAmount: number
}

export interface HumanCategorizedCost {
  categoryId: string
  categoryCode: string
  categoryName: string
  subCategories?: SubCategory[]
  totalAmount: number
  totalAvgAmount: number
}
export interface InHumanCategorizedCost {
  categoryId: string
  categoryCode: string
  categoryName: string
  subCategories?: SubCategory[]
  totalAmount: number
  totalAvgAmount: number
}

export interface CostData {
  constructionCostId: string
  totalCost: number
  riskAmount: number
  reportFrom?: string
  reportTo?: string
  requestAmount: number
  retentionAmount: number
  releasedAmount: number
  totalClaimedAmount: number
  profitByRequestedAmount?: number
  profitByClaimedAmount?: number
  humanCategorizedCosts: HumanCategorizedCost[]
  inHumanCategorizedCosts: InHumanCategorizedCost[]
}

export interface ConstructionCostItem {
  constructionId: string
  isPrimary: boolean
  currentCost: CostData
  currentAccumulateCost: CostData
  lastAccumulateCost: CostData
}

export interface CostAmountItem {
  previousAmount: number
  previousProgressPercentage: number
  currentAmount: number
  currentProgressPercentage: number
  accumulatedAmount: number
  accumulatedProgressPercentage: number
  remainingBalance: number
  type: ConstructionType
}

export interface EstimateBudgetItem {
  budgetAccordingToProgress: number
  againstBudget: number
  actualProfitMargin: number
  estimatedProfitMargin: number
  type: ConstructionType
}

export interface ConstructionResponse {
  items: ConstructionCostItem[]
}

export interface SessionItem {
  label: string
  value: number
}

export interface ConstructionCostQueryParams {
  startDate: string
  endDate: string
}

export interface ConstructionCostParams {
  riskAmount: number
  requestAmount: number
  retentionAmount: number
  releaseAmount: number
}

export async function updateConstructionCostApi(constructionId: string, params: ConstructionCostParams) {
  return usePut<any>(`v1/constructioncost/${constructionId}`, params)
}

export async function getConstructionCostByProjectApi(projectId: string, queryParams: ConstructionCostQueryParams) {
  return useGet<ConstructionResponse>(`v1/constructioncost/project/${projectId}`, queryParams)
}
