<script lang="ts" setup>
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons-vue'
import { type InputCost, fetchInputCostImage } from '~@/api/invoice'

const props = defineProps({
  invoice: {
    type: Object as PropType<InputCost>,
    required: true,
  },
})

const emit = defineEmits<{
  (event: 'showDetail', invoice: InputCost): void
  (event: 'deleteInvoice', inputCostId: string): void
}>()

const orgId = useOrg()
const { t } = useI18n()
const localInvoice = ref<InputCost>()

function formatNumber(num: number): string {
  return num.toLocaleString()
}

// const imageSrc = computed(() => {
//   const imgSrc = fetchInputCostImage(props.invoice.imageUrls[0], orgId.value ?? '')
//   console.log('imgSrc', imgSrc)
//   return imgSrc
// })

// const loading = ref<boolean>(false)
// const error = ref<string | null>(null)
// const token = useAuthorization()
// const baseUrl = import.meta.env.VITE_APP_BASE_API ?? '/'

// Hàm gọi API để lấy hình ảnh
// async function fetchImage(imageUrlParam: string): Promise<void> {
//   try {
//     loading.value = true
//     error.value = null
//     const apiUrl = `${baseUrl}/cost/inputcost/images?imageUrl=${imageUrlParam}`
//     const response = await axios.get(apiUrl, {
//       params: {
//         imageUrl: imageUrlParam,
//       },
//       headers: {
//         'Accept': '*/*',
//         'Authorization': token.value,
//       },
//       responseType: 'blob', // Nhận dữ liệu dạng binary (hình ảnh)
//     })

//     // Chuyển Blob thành URL để hiển thị
//     const imageObjectURL = URL.createObjectURL(response.data)
//     imageSrc.value = imageObjectURL
//     loading.value = false
//   }
//   catch (error) {
//     console.error('Error fetching image:', error)
//     loading.value = false
//   }
// }

function showDetail(invoice: InputCost) {
  emit('showDetail', invoice)
}

function deleteInvoice(inputCostId: string) {
  emit('deleteInvoice', inputCostId)
}

watch (
  () => props.invoice,
  (newValue) => {
    if (newValue)
      Object.assign(localInvoice, newValue)
  },
  {
    deep: true,
    immediate: true,
  },
)

// Goi ham fetchImage khi component được tạo
onMounted(async () => {
  if (!props.invoice || !props.invoice.imageUrls || props.invoice.imageUrls.length === 0)
    return
  await fetchInputCostImage(props.invoice.imageUrls?.[0], orgId.value ?? '')
})
</script>

<template>
  <div class="flex items-start">
    <div class="mr-4">
      <!-- <div class="w-16 h-16 bg-gray-200 flex items-center justify-center rounded">
        <FileOutlined style="font-size: 24px; color: #999;" />
        </div> -->
      <!-- <NImage
        width="100"
        :src="imageSrc"
      /> -->
      <!-- <div v-else class="w-20 h-20 bg-gray-200 flex items-center justify-center rounded">
        <FileOutlined style="font-size: 24px; color: #999;" />
      </div> -->
    </div>

    <div class="flex-grow">
      <div class="flex flex-col md:flex-row md:justify-between">
        <div class="mb-2 md:mb-0">
          <h3 class="text-lg font-medium">
            {{ invoice.title }}
          </h3>
          <div class="grid grid-cols-2 gap-x-8 text-sm">
            <div>{{ t('invoice-number') }}</div>
            <div>{{ invoice.originalNumber }}</div>
          </div>
        </div>

        <div class="md:w-1/7">
          <div class="grid grid-cols-1 gap-x-2 text-sm">
            <div class="text-gray-500">
              {{ t('construction') }}
            </div>
            <div class="">
              {{ invoice.constructionName }}
            </div>
            <div class="text-gray-500">
              {{ t('supplier') }}
            </div>
            <div>{{ invoice.vendorName }}</div>
          </div>
        </div>
        <div class="md:w-1/7">
          <div class="grid grid-cols-1 gap-x-2 text-sm">
            <div class="text-gray-500">
              {{ t('type') }}
            </div>
            <div>{{ invoice.entryTypeName }}</div>
            <div class="text-gray-500">
              {{ t('release-date') }}
            </div>
            <div>{{ invoice.issueDate }}</div>
          </div>
        </div>

        <div class="md:w-1/7">
          <div class="grid grid-cols-1 gap-x-2 text-sm">
            <div class="text-gray-500">
              {{ t('payment-method') }}
            </div>
            <div>{{ invoice.paymentTypeCode }}</div>
            <div class="text-gray-500">
              {{ t('payment-term') }}
            </div>
            <div>{{ invoice.paymentDate }}</div>
          </div>
        </div>

        <div class="mt-2 md:mt-0 text-right">
          <div class="text-gray-500">
            {{ t('total') }}
          </div>
          <div class="font-bold">
            ¥ {{ formatNumber(invoice?.totalAmount ?? 0) }}
          </div>
        </div>
      </div>
    </div>

    <div class="ml-4 flex gap-2">
      <a-button type="text" class="flex items-center justify-center">
        <template #icon>
          <EyeOutlined />
        </template>
        {{ t('button.note') }}
      </a-button>
      <a-button type="text" class="flex items-center justify-center">
        <template #icon>
          <InfoCircleOutlined />
        </template>
        {{ t('button.detail') }}
      </a-button>
      <a-button type="text" class="flex items-center justify-center" @click="showDetail(invoice)">
        <template #icon>
          <EditOutlined />
        </template>
      </a-button>
      <a-button type="text" class="flex items-center justify-center" @click="deleteInvoice(invoice.inputCostId)">
        <template #icon>
          <DeleteOutlined />
        </template>
      </a-button>
    </div>
  </div>
</template>
