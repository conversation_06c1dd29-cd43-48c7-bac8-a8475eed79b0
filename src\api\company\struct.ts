import type { QueryParams } from '../common-params'

export interface StructureItem {
  structureId: string
  structureCode: string
  structureName: string
  structureParentId: string | null
  children: StructureItem[]
  roles: any[]
  description: string
  isOutSource: boolean
  createTime: string
  updateTime: string | null
}

export interface StructureParams {
  structureCode: string
  structureName: string
  description?: string
  isOutSource: boolean
  structureParentId?: string
}

// Interface cho cấu trúc chính
export interface StructureResponse {
  items: StructureItem[]
  pageIndex: number
  pageSize: number
  totalRecords: number
}

export interface SimpleStructureInfo {
  id: number
  name: string
}

export interface SimpleStructureResponse {
  items: SimpleStructureInfo[]
  pageIndex: number
  pageSize: number
  totalRow: number
  pageCount: number
}

export interface SimpleStructureInfoResponse {
  items: SimpleStructureInfo[]
  pageNum: number
  pageSize: number
  totalRecords: number
}

export interface StructureQueryParams extends QueryParams {
  isOutSource?: boolean
  keyword?: string
}

export async function getStructureListApi(params?: StructureQueryParams) {
  return useGet<StructureResponse>('v1/structure', params)
}

export async function getStructureByIdApi(structureId: string) {
  return useGet<StructureItem>(`v1/structure/${structureId}`)
}

export async function getStructureComboApi(params: QueryParams) {
  return useGet<SimpleStructureResponse>('v1/structure/simple-structure-info', params)
}

export async function createStructureApi(params: StructureParams) {
  return usePost<StructureItem>('v1/structure', params)
}

export async function updateStructureApi(structureId: string, params: StructureParams) {
  return usePut<StructureItem>(`v1/structure/${structureId}`, params)
}

export async function deleteStructureApi(structureId: string) {
  return useDelete<StructureItem>(`v1/structure/${structureId}`)
}
