<script lang="ts" setup>
import Ranking from './tabs/ranking.vue';
import OutsourceCost from './tabs/outsource-cost.vue';
import EmployeeCost from './tabs/employee-cost.vue';

const { t } = useI18n();
</script>

<template>
  <a-tabs destroy-inactive-tab-pane>
    <a-tab-pane key="1" :tab="t('form.ranking')">
      <Ranking />
    </a-tab-pane>
    <a-tab-pane key="2" :tab="t('form.employee-cost')">
      <EmployeeCost />
    </a-tab-pane>
    <a-tab-pane key="3" :tab="t('form.outsource-cost')">
      <OutsourceCost />
    </a-tab-pane>
  </a-tabs>
</template>
