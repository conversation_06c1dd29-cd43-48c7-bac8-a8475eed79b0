import type { DateRangeParams } from '../common-params'

export interface UserLeaveInfo {
  employeeLeaveId: string
  employeeId: string
  employeeName: string
  employeeCode: string
  items: LeaveDetails[]
}
interface LeaveDetails {
  baseLeave: number
  baseLeaveExpire: string
  lastRemainLeave: number
  lastRemainLeaveExpire: string
  totalUsedLeave: number
  totalSelfUsedLeave: number
  totalOrgUsedLeave: number
  createTime: string
  updateTime: string
}

export interface LeaveInfo {
  employeeLeaveId: string
  employeeId: string
  employeeName: string
  employeeCode: string
  baseLeave: number
  baseLeaveExpire: string
  lastRemainLeave: number
  lastRemainLeaveExpire: string
  totalUsedLeave: number
  totalSelfUsedLeave: number
  totalOrgUsedLeave: number
  createTime: string
  updateTime: string
}

export interface LeaveResponse {
  items: LeaveInfo[]
  pageNum: number
  pageSize: number
  totalRecords: number
}

export interface LeaveQueryParams {
  pageNum: number
  pageSize: number
  queryFrom?: string
  queryTo?: string
  baseLeaveMin?: number
  baseLeaveMax?: number
  baseLeaveValidFrom?: string
  baseLeaveValidTo?: string
  lastLeaveRemainingValidFrom?: string
  lastLeaveRemainingValidTo?: string
  lastLeaveRemainingMin?: number
  lastLeaveRemainingMax?: number
  usedMin?: number
  usedMax?: number
  personalUsedMin?: number
  personalUsedMax?: number
  orgUsedMin?: number
  orgUsedMax?: number
  orgId?: number
  active?: boolean
  userInfo?: string
}

export interface UsedLeaveResponse {
  items: UsedLeaveTime[]
}

export interface UsedLeaveTime {
  requestFrom: string
  requestTo: string
  isPersonalUse: boolean
}

export interface BaseLeaveParams {
  baseLeave: number
  baseLeaveExpire: string
}

export async function getEmployeeLeaveApi(params: LeaveQueryParams) {
  return useGet<LeaveResponse>('v1/employeeleave', params)
}

export async function getLeaveInfoByUserApi(employeeId: string, params: DateRangeParams) {
  return useGet<UserLeaveInfo>(`v1/employeeleave/${employeeId}`, params)
}

export async function getEmployeeLeaveUsageApi(employeeId: string, params: DateRangeParams) {
  return useGet<UsedLeaveResponse>(`v1/employeeleave/usage/${employeeId}`, params)
}

export async function updateBaseLeaveApi(employeeLeaveId: string, params: BaseLeaveParams) {
  return usePut<any>(`v1/employeeleave/${employeeLeaveId}`, params)
}

export async function createBaseLeaveApi(employeeId: string, params: BaseLeaveParams) {
  return usePost<any>(`v1/employeeleave/${employeeId}`, params)
}
