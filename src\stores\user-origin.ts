// /* eslint-disable curly */
// import { v4 as uuidv4 } from 'uuid'
// import type { EmployeeInfo } from '~@/api/common/user'

// import { getEmployeeInfoApi } from '~@/api/common/user'
// import type { MenuData, MenuDataItem } from '~@/layouts/basic-layout/typing'
// import { rootRoute } from '~@/router/dynamic-routes'
// import {
//   generateFlatRoutes,
//   generateRoutes,
//   generateTreeRoutes,
// } from '~@/router/generate-route'
// import { DYNAMIC_LOAD_WAY, DynamicLoadEnum } from '~@/utils/constant'
// import { getMenuFunc } from '~@/api/common/menu.ts'
// import { ResponseStatusEnum } from '~@/enums/response-status-enum'
// import logger from '~@/utils/logger'

// export const useUserStore = defineStore('user', () => {
//   const routerData = shallowRef()
//   const menuData = shallowRef<MenuData>([])
//   const userInfo = shallowRef<EmployeeInfo>()
//   const token = useAuthorization()
//   const avatar = ''
//   const nickname = computed(
//     () => userInfo.value?.employeeCode ?? userInfo.value?.employeeId,
//   )
//   const roles = computed(() => userInfo.value?.roles)

//   const profileMenu: MenuDataItem = {
//     component: '/profile/basic',
//     hideInBreadcrumb: undefined,
//     hideInMenu: true,
//     icon: 'UserOutlined',
//     id: uuidv4(),
//     locale: 'menu.profile.basic',
//     name: undefined,
//     parentId: undefined,
//     path: '/profile/basic',
//     redirect: undefined,
//     title: 'ProfileBasic',
//   }
//   const accountSettingMenu: MenuDataItem = {
//     component: '/account/settings',
//     hideInBreadcrumb: undefined,
//     hideInMenu: true,
//     icon: 'SettingOutlined',
//     id: uuidv4(),
//     locale: 'menu.account.settings',
//     name: 'AccountSetting',
//     parentId: undefined,
//     path: '/account/settings',
//     redirect: undefined,
//     title: 'AccountSetting',
//   }

//   // Lấy thông tin menu từ API
//   const getMenuRoutes = async () => {
//     const { data, status, code } = await getMenuFunc()
//     if (status === ResponseStatusEnum.SUCCESS) {
//       menuData.value = data ?? []
//       // Thêm router tĩnh vào menu
//       menuData.value.push(profileMenu)
//       menuData.value.push(accountSettingMenu)
//       console.log('menuData1', menuData.value)
//     }
//     else {
//       logger.error(code)
//     }
//     return generateTreeRoutes(menuData.value)
//   }

//   const generateDynamicRoutes = async () => {
//     const dynamicLoadWay
//       = DYNAMIC_LOAD_WAY === DynamicLoadEnum.BACKEND
//         ? getMenuRoutes
//         : generateRoutes
//     const { menuData: treeMenuData, routeData } = await dynamicLoadWay()

//     menuData.value = treeMenuData

//     logger.log('menuData2', menuData.value)

//     routerData.value = {
//       ...rootRoute,
//       children: generateFlatRoutes(routeData),
//     }
//     logger.log(routerData.value)
//     return routerData.value
//   }

//   const getEmployeeInfo = async () => {
//     try {
//       const { data, status, message } = await getEmployeeInfoApi()
//       if (status === 200) {
//         userInfo.value = data ?? undefined
//         console.log('userInfo', userInfo.value)
//       }
//       else {
//         console.error(message)
//       }
//     }
//     catch (e) {
//       logger.error(e)
//     }
//   }

//   function checkPermission() {
//     logger.log('menuData', menuData.value)
//     if (menuData.value.length === 0)
//       return false
//     return true
//   }

//   const logout = async () => {
//     try {
//       //
//     }
//     finally {
//       token.value = null
//       userInfo.value = undefined
//       // routerData.value = undefined
//       // menuData.value = []
//     }
//   }

//   return {
//     userInfo,
//     roles,
//     getEmployeeInfo,
//     logout,
//     routerData,
//     menuData,
//     generateDynamicRoutes,
//     avatar,
//     nickname,
//     checkPermission,
//   }
// })
