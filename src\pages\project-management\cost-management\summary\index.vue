<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { usePagination } from 'vue-request'
import dayjs from 'dayjs'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import { CalendarOutlined, ProjectOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons-vue'
import CostTable from '../components/CostTable.vue'
import ExpenseBreakdown from '../components/ExpenseBreakdown.vue'
import type { ConstructionProject } from '~@/api/construction'
import { getProjectComboApi } from '~@/api/company/project'

dayjs.extend(isSameOrAfter)
dayjs.extend(isSameOrBefore)

const { t } = useI18n()

const route = useRoute()
const selectedProjectId = ref(route.params.id)
const BASE_URL = import.meta.env.VITE_APP_BASE_API
const selectedDateFrom = ref(dayjs().startOf('month').format('YYYY-MM-DD'))
const selectedDateTo = ref(dayjs().endOf('month').format('YYYY-MM-DD'))
const isShowProductcost = ref(false)
const { roles } = useUserStore()
const { checkEmployeeCostVisibleAll } = useMenuPermission()
const router = useRouter()
const REDIRECT_NAME = 'ProjectCostSummaryDetail'

const isRouteExist = computed(() => {
  return router.getRoutes().some(route => route.name === REDIRECT_NAME)
})

const url = computed(() => {
  return `${BASE_URL}/v1/construction/project/${selectedProjectId.value}/overview?dateFrom=${selectedDateFrom.value}&dateTo=${selectedDateTo.value}`
})

async function queryData(params: any) {
  const { data, status } = await getProjectComboApi(params)
  if (status === 200)
    return data
}

const {
  data: projectData,
} = usePagination(queryData, {
  defaultParams: [{
    pageNum: 1,
    pageSize: 100,
  }],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const projectComboData = computed(() => {
  return projectData.value?.items ?? []
})
const {
  data,
  execute,
} = useFetch(url, {
  immediate: true,
  refetch: true,
  async beforeFetch({ options, cancel }) {
    const token = useAuthorization()
    if (!token.value) {
      cancel()
      return
    }
    options.headers = {
      ...options.headers,
      Authorization: token.value,
      'Accept-Language': lsLocaleState.value ?? 'ja-JP',
      'Content-Type': 'application/json',
    }
    return { options }
  },
  onFetchError({ error, data }) {
    if (error.message === 'signal is aborted without reason')
      return { error: new Error('Yêu cầu bị hủy do thiếu token'), data }

    return { error, data }
  },
}).get().json()

const constructionProjectData = computed(() => {
  return data?.value?.data as ConstructionProject
})

const mainContractItems = computed(() => {
  return constructionProjectData.value?.contractCosts.mainConstructionContractCost.initialCostItems
})
const estimateContractItems = computed(() => {
  return constructionProjectData.value?.estimateCosts.mainConstructionEstimateCost.estimateCostItems
})

const mainChangesItems = computed(() => {
  return constructionProjectData.value?.contractCosts.mainConstructionContractCost.modifiedCostItems
})

const subContractItems = computed(() => {
  return constructionProjectData.value?.contractCosts.subConstructionContractCost.initialCostItems
})
const estimateSubContractItems = computed(() => {
  return constructionProjectData.value?.estimateCosts.subConstructionEstimateCost.estimateCostItems
})

const subChangesItems = computed(() => {
  return constructionProjectData.value?.contractCosts.subConstructionContractCost.modifiedCostItems
})

const overallContractItems = computed(() => {
  return constructionProjectData.value?.contractCosts.overallConstructionContractCost.initialCostItems
})
const estimateOverallContractItems = computed(() => {
  return constructionProjectData.value?.estimateCosts.overallConstructionEstimateCost.estimateCostItems
})

const overallChangesItems = computed(() => {
  return constructionProjectData.value?.contractCosts.overallConstructionContractCost.modifiedCostItems
})

// Employee costs with worker count
const mainCategorizedCosts = computed(() => {
  return constructionProjectData.value?.accumulatedCosts.mainConstructionAccumulatedCost.categorizedCosts
})

const separateCategorizedCosts = computed(() => {
  return constructionProjectData.value?.accumulatedCosts.subConstructionAccumulatedCost.categorizedCosts
})

const overallCategorizedCosts = computed(() => {
  return constructionProjectData.value?.accumulatedCosts.overallConstructionAccumulatedCost.categorizedCosts
})

const totalMainAccumulatedCost = computed(() => {
  if (isShowProductcost.value)
    return constructionProjectData.value?.accumulatedCosts.mainConstructionAccumulatedCost.totalAccumulatedCost

  return constructionProjectData.value?.accumulatedCosts.mainConstructionAccumulatedCost.totalAccumulatedAvgCost
})

const totalSeparateAccumulatedCost = computed(() => {
  if (isShowProductcost.value)
    return constructionProjectData.value?.accumulatedCosts.subConstructionAccumulatedCost.totalAccumulatedCost

  return constructionProjectData.value?.accumulatedCosts.subConstructionAccumulatedCost.totalAccumulatedAvgCost
})

const totalOverallAccumulatedCost = computed(() => {
  if (isShowProductcost.value)
    return constructionProjectData.value?.accumulatedCosts.overallConstructionAccumulatedCost.totalAccumulatedCost

  return constructionProjectData.value?.accumulatedCosts.overallConstructionAccumulatedCost.totalAccumulatedAvgCost
})

const mainRiskAmount = computed(() => {
  return constructionProjectData.value?.accumulatedCosts.mainConstructionAccumulatedCost.riskAmount
})

const separateRiskAmount = computed(() => {
  return constructionProjectData.value?.accumulatedCosts.subConstructionAccumulatedCost.riskAmount
})

const overallRiskAmount = computed(() => {
  return constructionProjectData.value?.accumulatedCosts.overallConstructionAccumulatedCost.riskAmount
})

// Contract costs
const totalMainInitialCost = computed(() => {
  return constructionProjectData.value?.contractCosts.mainConstructionContractCost.totalInitialCost
})
const totalSubInitialCost = computed(() => {
  return constructionProjectData.value?.contractCosts.subConstructionContractCost.totalInitialCost
})
const totalOverallInitialCost = computed(() => {
  return constructionProjectData.value?.contractCosts.overallConstructionContractCost.totalInitialCost
})
const totalMainConstructionCost = computed(() => {
  return constructionProjectData.value?.contractCosts.totalMainConstructionCost
})
const totalSubConstructionCost = computed(() => {
  return constructionProjectData.value?.contractCosts.totalSubConstructionCost
})
const totalOverallConstructionCost = computed(() => {
  return constructionProjectData.value?.contractCosts.totalOverallConstructionCost
})

// Estimate costs
const totalEstimateMainCost = computed(() => {
  return constructionProjectData.value?.estimateCosts.mainConstructionEstimateCost.totalEstimateCost
})
const totalEstimateSubCost = computed(() => {
  return constructionProjectData.value?.estimateCosts.subConstructionEstimateCost.totalEstimateCost
})
const totalEstimateOverallCost = computed(() => {
  return constructionProjectData.value?.estimateCosts.overallConstructionEstimateCost.totalEstimateCost
})

// Format currency
function formatCurrency(value: number) {
  if (!value)
    return 0
  return value.toLocaleString()
}

// Reset filters to default values
function handleReset() {
  selectedDateFrom.value = dayjs().startOf('month').format('YYYY-MM-DD')
  selectedDateTo.value = dayjs().endOf('month').format('YYYY-MM-DD')
  selectedProjectId.value = route.params.id
  execute()
}

// Handle search button click
function handleSearch() {
  execute()
}

// Ngay thang trong filter bao gồm ngày tháng cảu tháng này
function isBetweenFilterDate() {
  const currentStartDate = dayjs().startOf('month')
  const currentEndDate = dayjs().endOf('month')
  const filterStartDate = selectedDateFrom.value
  const filterEndDate = selectedDateTo.value
  if (filterStartDate && filterEndDate)
    return currentStartDate.isSameOrAfter(dayjs(filterStartDate), 'day') && currentEndDate.isSameOrBefore(dayjs(filterEndDate), 'day')

  return false
}

const isEmployeeCostVisibleAll = computed(() => {
  const rule = isBetweenFilterDate() ? 'RULE_1' : 'RULE_2'
  return roles.some(role => checkEmployeeCostVisibleAll(role.roleId, rule))
})

const handleShowOriginalCost = () => {
  console.log('isShowOriginalCost', isShowProductcost.value)
}

watch(
  () => route.params.id,
  (newId) => {
    selectedProjectId.value = newId // Cập nhật selectedProjectId khi id thay đổi
  },
  { immediate: true },
)

// Watch for changes in date range and project selection
watch(
  [selectedDateFrom, selectedDateTo, selectedProjectId],
  () => {
    execute()
  },
  { deep: true },
)

onMounted(async () => {
  if (selectedProjectId.value)
    execute()
})
</script>

<template>
  <page-container>
    <div class="bg-gray-100 min-h-screen p-4">
      <!-- Header Section -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="flex flex-col lg:flex-row lg:items-end gap-4">
          <!-- Date Range Section -->
          <div class="flex flex-col sm:flex-row gap-4 flex-1">
            <div class="flex-1">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                <CalendarOutlined class="mr-2" />
                {{ t('dateFrom') }}
              </label>
              <a-date-picker
                v-model:value="selectedDateFrom"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full"
                size="large"
                :placeholder="t('selectStartDate')"
              />
            </div>
            <div class="flex-1">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                <CalendarOutlined class="mr-2" />
                {{ t('dateTo') }}
              </label>
              <a-date-picker
                v-model:value="selectedDateTo"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full"
                size="large"
                :placeholder="t('selectEndDate')"
              />
            </div>
          </div>

          <!-- Project Selection Section -->
          <div class="flex-1 lg:max-w-sm">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <ProjectOutlined class="mr-2" />
              {{ t('project') }}
            </label>
            <a-select
              v-model:value="selectedProjectId"
              class="w-full"
              size="large"
              :placeholder="t('selectProject')"
              show-search
              :filter-option="(input: string, option: any) => {
                const project = projectComboData.find(p => p.id === option?.key);
                return project ? `${project.code} - ${project.name}`.toLowerCase().includes(input.toLowerCase()) : false;
              }"
            >
              <a-select-option v-for="project in projectComboData" :key="project.id" :value="project.id">
                <div class="flex items-center">
                  <span class="font-medium text-blue-600">{{ project.code }}</span>
                  <span class="mx-2 text-gray-400">-</span>
                  <span class="text-gray-700">{{ project.name }}</span>
                </div>
              </a-select-option>
            </a-select>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-2 items-center">
            <a-button
              type="primary"
              size="large"
              class="flex items-center"
              :loading="data === null"
              @click="handleSearch"
            >
              <SearchOutlined class="mr-2" />
              {{ t('search') }}
            </a-button>
            <a-button
              size="large"
              class="flex items-center"
              @click="handleReset"
            >
              <ReloadOutlined class="mr-2" />
              {{ t('reset') }}
            </a-button>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg p-4 mb-4 shadow">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <div class="mb-4 md:mb-0">
            <h1 class="text-lg font-medium">
              {{ constructionProjectData?.projectName }}
            </h1>
            <div class="text-sm text-gray-600">
              <p>{{ t("customer") }}: {{ constructionProjectData?.customerName }}</p>
              <p>{{ t("contractor") }}: {{ constructionProjectData?.contractorName }}</p>
              <p>{{ t("contract-construction-period") }}: {{ constructionProjectData?.contractualStartDate }} ~ {{ constructionProjectData?.contractualEndDate }}</p>
            </div>
          </div>
          <div class="flex space-x-2">
            <router-link
              v-if="isRouteExist"
              :to="{
                name: REDIRECT_NAME,
                query: { id: selectedProjectId },
              }"
            >
              <a-button type="primary">
                {{ t("view-cost-summary-detail") }}
              </a-button>
            </router-link>
          </div>
        </div>
      </div>

      <!-- Main Construction Section -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
        <!-- Main Construction Cost -->
        <a-card :title="t('main-construction-cost')" :bordered="false" class="shadow">
          <template #extra>
            <span class="font-bold">{{ t("total") }}: ¥ {{ formatCurrency(totalMainConstructionCost) }}</span>
          </template>

          <a-tabs>
            <a-tab-pane key="contract" :tab="t('contract')">
              <CostTable :items="mainContractItems" />
            </a-tab-pane>
            <a-tab-pane key="changes" :tab="t('changes')">
              <CostTable :items="mainChangesItems" />
            </a-tab-pane>
          </a-tabs>
          <div class="border-t-1 border-b-0 border-l-0 border-r-0 border-gray-200 border-solid pt-2 text-right">
            <span class="font-bold">{{ t("subtotal") }}: {{ formatCurrency(totalMainInitialCost) }}</span>
          </div>
        </a-card>

        <!-- Separate Construction Costs -->
        <a-card :title="t('separate-construction-costs')" :bordered="false" class="shadow">
          <template #extra>
            <span class="font-bold">{{ t("total") }}: ¥ {{ formatCurrency(totalSubConstructionCost) }}</span>
          </template>

          <a-tabs>
            <a-tab-pane key="contract" :tab="t('contract')">
              <CostTable :items="subContractItems" />
            </a-tab-pane>
            <a-tab-pane key="changes" :tab="t('changes')">
              <CostTable :items="subChangesItems" />
            </a-tab-pane>
          </a-tabs>
          <div class="border-t-1 border-b-0 border-l-0 border-r-0 border-gray-200 border-solid pt-2 text-right">
            <span class="font-bold">{{ t("subtotal") }}: {{ formatCurrency(totalSubInitialCost) }}</span>
          </div>
        </a-card>

        <!-- Construction Cost Total -->
        <a-card :title="t('construction-cost-total')" :bordered="false" class="shadow">
          <template #extra>
            <span class="font-bold">{{ t("total") }}: ¥ {{ formatCurrency(totalOverallConstructionCost) }}</span>
          </template>

          <a-tabs>
            <a-tab-pane key="contract" :tab="t('contract')">
              <CostTable :items="overallContractItems" />
            </a-tab-pane>
            <a-tab-pane key="changes" :tab="t('changes')">
              <CostTable :items="overallChangesItems" />
            </a-tab-pane>
          </a-tabs>
          <div class="border-t-1 border-b-0 border-l-0 border-r-0 border-gray-200 border-solid pt-2 text-right">
            <span class="font-bold">{{ t("subtotal") }}: {{ formatCurrency(totalOverallInitialCost) }}</span>
          </div>
        </a-card>
      </div>

      <!-- Estimate Construction Cost -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
        <!-- Estimate Construction Cost -->
        <a-card :title="t('estimate-construction-cost')" :bordered="false" class="shadow">
          <template #extra>
            <span class="font-bold">{{ t("total") }}: ¥ {{ formatCurrency(totalEstimateMainCost) }}</span>
          </template>
          <CostTable :items="estimateContractItems" />
          <div class="border-t-1 border-b-0 border-l-0 border-r-0 border-gray-200 border-solid pt-2 text-right">
            <span class="font-bold">{{ t("subtotal") }}: {{ formatCurrency(totalEstimateMainCost) }}</span>
          </div>
        </a-card>

        <!-- Separate Construction Costs -->
        <a-card :title="t('separate-construction-costs')" :bordered="false" class="shadow">
          <template #extra>
            <span class="font-bold">{{ t("total") }}: ¥ {{ formatCurrency(totalEstimateSubCost) }}</span>
          </template>

          <CostTable :items="estimateSubContractItems" />
          <div class="border-t-1 border-b-0 border-l-0 border-r-0 border-gray-200 border-solid pt-2 text-right">
            <span class="font-bold">{{ t("subtotal") }}: {{ formatCurrency(totalEstimateSubCost) }}</span>
          </div>
        </a-card>

        <!-- Construction Cost Total -->
        <a-card :title="t('construction-cost-total')" :bordered="false" class="shadow">
          <template #extra>
            <span class="font-bold">{{ t("total") }}: ¥ {{ formatCurrency(totalEstimateOverallCost) }}</span>
          </template>

          <CostTable :items="estimateOverallContractItems" />
          <div class="border-t-1 border-b-0 border-l-0 border-r-0 border-gray-200 border-solid pt-2 text-right">
            <span class="font-bold">{{ t("subtotal") }}: {{ formatCurrency(totalEstimateOverallCost) }}</span>
          </div>
        </a-card>
      </div>

      <!-- Expenses Section -->
      <div class="bg-white rounded-lg p-4 mb-4 shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="text-lg font-medium mb-2">
            {{ t("expenses") }}
          </div>
          <div class="flex items-center gap-2">
            <a-switch
              v-if="isEmployeeCostVisibleAll"
              v-model:checked="isShowProductcost"
              :checked-children="t('productionCost')"
              :un-checked-children="t('averageCost')"
              @change="handleShowOriginalCost"
            />
          </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <!-- Main Construction Cost Expenses -->
          <div class="border border-red-200 rounded-lg p-4">
            <h3 class="font-bold mb-3">
              {{ t('main-construction-cost') }}
            </h3>
            <ExpenseBreakdown
              :total-cost="totalMainAccumulatedCost"
              :categorized-costs="mainCategorizedCosts"
              :benefit="mainRiskAmount"
              :is-between-filter-date="isBetweenFilterDate()"
              :is-show-product-cost="isShowProductcost"
            />
          </div>

          <!-- Separate Construction Expenses -->
          <div class="border border-yellow-200 rounded-lg p-4">
            <h3 class="font-bold mb-3">
              {{ t('separate-construction-cost') }}
            </h3>
            <ExpenseBreakdown
              :total-cost="totalSeparateAccumulatedCost"
              :categorized-costs="separateCategorizedCosts"
              :benefit="separateRiskAmount"
              :is-between-filter-date="isBetweenFilterDate()"
              :is-show-product-cost="isShowProductcost"
            />
          </div>

          <!-- Combined Expenses -->
          <div class="border border-blue-200 rounded-lg p-4">
            <h3 class="font-bold mb-3">
              {{ t('main-construction-cost-and-separate-construction-cost') }}
            </h3>
            <ExpenseBreakdown
              :total-cost="totalOverallAccumulatedCost"
              :categorized-costs="overallCategorizedCosts"
              :benefit="overallRiskAmount"
              :is-between-filter-date="isBetweenFilterDate()"
              :is-show-product-cost="isShowProductcost"
            />
          </div>
        </div>
      </div>
    </div>
  </page-container>
</template>

<style scoped>
:deep(.ant-tabs-tab) {
  padding: 8px 12px;
}

:deep(.ant-collapse-header) {
  font-weight: 500;
}
</style>
