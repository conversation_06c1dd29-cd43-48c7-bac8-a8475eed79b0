import FingerprintJS from '@fingerprintjs/fingerprintjs';
import { v4 as uuidv4 } from 'uuid';
import { useDeviceId } from '~@/composables/org';

export async function generateDeviceFingerprint(): Promise<string> {
  try {
    const fp = await FingerprintJS.load();

    const result = await fp.get();

    return result.visitorId;
  } catch (error) {
    return uuidv4();
  }
}

export async function getDeviceId(): Promise<string> {
  const deviceId = useDeviceId();
  if (!deviceId.value) {
    deviceId.value = await generateDeviceFingerprint();
  }
  return deviceId.value;
}
