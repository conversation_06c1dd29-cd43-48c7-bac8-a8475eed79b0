<script lang="ts" setup>
import {
  CloseOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import {
  ColumnGroupType,
  ColumnType,
  TablePaginationConfig,
} from 'ant-design-vue/es/table';
import { FilterValue } from 'ant-design-vue/es/table/interface';
import { debounce } from 'lodash';
import { UnwrapRef } from 'vue';
import { usePagination } from 'vue-request';
import {
  createRankingApi,
  deleteRankingApi,
  getRankingListApi,
  RankingDataResponse,
  RankingItem,
  updateRankingApi,
} from '~@/api/company/ranking';
import { ResponseStatusEnum } from '~@/enums/response-status-enum';
import logger from '~@/utils/logger';

type ColumnItemType<T> = ColumnGroupType<T> | ColumnType<T>;

type RankingItemType = {
  isEdit?: boolean;
  isAdd?: boolean;
  rankingNameStatus?: '' | 'error' | 'warning';
  descriptionStatus?: '';
  minValue?: number;
  maxValue?: number;
  averageValue?: number;
  minValueStatus?: '' | 'error' | 'warning';
  maxValueStatus?: '' | 'error' | 'warning';
  averageValueStatus?: '' | 'error' | 'warning';
} & RankingItem;

interface Params {
  pageNum?: number;
  pageSize?: number;
  keyword?: string;
}

const { t } = useI18n();
const editableData: UnwrapRef<Record<string, RankingItemType>> = reactive({});
const searchForm = ref<Params>({
  pageSize: 10,
  pageNum: 1,
});

const queryData = async (params?: Params) => {
  const { data } = await getRankingListApi(params);
  if (!data) return ref<RankingDataResponse>({ items: [] }).value;

  return ref(data).value;
};

const {
  data: dataSource,
  loading,
  refresh,
  total,
  current,
  run,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [searchForm.value],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
});

const pagination = computed(() => ({
  total: total.value ?? 10,
  current: current.value,
  pageSize: pageSize.value,
  showSizeChanger: true,
}));

const columns = computed<ColumnItemType<RankingItemType>[]>(() => [
  { title: t('form.name'), dataIndex: 'name', width: 300 },
  { title: t('form.min-value'), dataIndex: 'min-value', width: 200 },
  { title: t('form.max-value'), dataIndex: 'max-value', width: 200 },
  { title: t('form.average-value'), dataIndex: 'average-value', width: 200 },
  { title: t('form.description'), dataIndex: 'description', width: 200 },
  {
    title: t('action'),
    dataIndex: 'action',
    width: 150,
    align: 'center',
    fixed: 'right',
  },
]);

const handleTableChange = (
  pagination: TablePaginationConfig,
  filters: Record<string, FilterValue>
) => {
  searchForm.value.pageSize = pagination.pageSize;
  searchForm.value.pageNum = pagination.current;
  run({ ...searchForm.value, ...filters });
};

const handlePaginationChange = (page: number, pageSize: number) => {
  searchForm.value.pageSize = pageSize;
  searchForm.value.pageNum = page;
  run(searchForm.value);
};

const onSearch = () => {
  handleTableChange(
    { pageSize: searchForm.value.pageSize ?? 10, current: 1 },
    {}
  );
};

const handleAdd = () => {
  const newData: RankingItemType = {
    rankingId: `${Date.now()}`,
    rankingName: undefined,
    description: undefined,
    minValue: undefined,
    maxValue: undefined,
    averageValue: undefined,
    isAdd: true,
  };
  if (dataSource.value) dataSource.value.items.unshift(newData);
  editableData[newData.rankingId] = newData;
};

const handleEdit = (key: string) => {
  const findRanking = dataSource.value?.items.find(
    (item) => item.rankingId === key
  ) as RankingItemType;
  if (!findRanking) return;
  editableData[key] = { ...findRanking, isEdit: true, isAdd: false };
};

const validateFields = (key: string) => {
  let check = true;

  if (!editableData[key].rankingName) {
    editableData[key].rankingNameStatus = 'error';
    if (check) check = false;
  } else {
    editableData[key].rankingNameStatus = undefined;
  }

  if (!editableData[key].minValue && !editableData[key].maxValue) {
    editableData[key].minValueStatus = 'error';
    editableData[key].maxValueStatus = 'error';
    if (check) check = false;
  } else {
    editableData[key].minValueStatus = undefined;
    editableData[key].maxValueStatus = undefined;
  }

  return check;
};

const handleCreate = async (key: string) => {
  try {
    if (!validateFields(key)) return;

    const create = await createRankingApi({
      rankingName: editableData[key]?.rankingName,
      minValue: editableData[key]?.minValue || null,
      maxValue: editableData[key]?.maxValue || null,
      averageValue: editableData[key]?.averageValue || null,
      description: editableData[key]?.description,
    });
    if (create.status !== ResponseStatusEnum.SUCCESS) return;

    if (dataSource.value) {
      Object.assign(
        dataSource.value.items.filter((item) => key === item.rankingId)[0],
        create.data
      );
    }
    delete editableData[key];
    message.success(create.message);
  } catch (error) {
    logger.error(error);
  }
};

const handleUpdate = async (key: string) => {
  try {
    if (!validateFields(key)) return;

    const update = await updateRankingApi(key, {
      rankingName: editableData[key]?.rankingName,
      minValue: editableData[key]?.minValue || null,
      maxValue: editableData[key]?.maxValue || null,
      averageValue: editableData[key]?.averageValue || null,
      description: editableData[key]?.description,
    });
    if (update.status !== ResponseStatusEnum.SUCCESS) return;

    if (dataSource.value) {
      Object.assign(
        dataSource.value.items.filter((item) => key === item.rankingId)[0],
        update.data
      );
    }
    delete editableData[key];
    message.success(update.message);
  } catch (error) {
    logger.error(error);
  }
};

const handleDelete = async (key: string) => {
  try {
    const deleted = await deleteRankingApi(key);
    if (deleted.status !== ResponseStatusEnum.SUCCESS) return;

    refresh();
  } catch (error) {
    logger.error(error);
  }
};

const handleCancel = (key: string) => {
  if (dataSource.value) {
    dataSource.value.items = dataSource.value.items.filter((item) => {
      if (key !== item.rankingId) return true;
      if (editableData[key].isEdit) return true;
      return false;
    });
  }
  delete editableData[key];
};
</script>

<template>
  <page-container>
    <a-row :wrap="false" :gutter="[12, 12]" class="h-[calc(100vh-160px)] flex-col">
      <a-col flex="none" span="24">
        <a-row :gutter="[12, 12]">
          <a-col span="24">
            <a-row :gutter="[12, 12]">
              <a-col>
                <a-button class="flex flex-items-center" type="primary" @click="handleAdd">
                  <PlusOutlined />
                  {{ `${t('button.new')}` }}
                </a-button>
              </a-col>
              <a-col>
                <a-input v-model:value="searchForm.keyword" :placeholder="t('search')" style="width: 25rem" allow-clear
                  @press-enter="onSearch">
                  <template #prefix>
                    <SearchOutlined class="text-gray-500" />
                  </template>
                </a-input>
              </a-col>
            </a-row>
          </a-col>
          <a-col span="24">
            <a-table class="tableRanking" :scroll="{ x: 'max-content', y: 'calc(100vh - 320px)' }" :columns="columns"
              :data-source="dataSource?.items" :loading="loading" :pagination="false" row-key="rankingId"
              @change="handleTableChange">
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'name'">
                  <div>
                    <a-input v-if="editableData[record.rankingId]"
                      v-model:value="editableData[record.rankingId].rankingName"
                      :placeholder="t('form.name.placeholder')"
                      :status="editableData[record.rankingId].rankingNameStatus"
                      @change="debounce(validateFields, 500)(record.rankingId)">
                      <template #suffix>
                        <a-tooltip v-if="
                          editableData[record.rankingId].rankingNameStatus
                        " placement="top" :title="t('form.name.placeholder')">
                          <InfoCircleOutlined color="red" />
                        </a-tooltip>
                      </template>
                    </a-input>
                    <template v-else>
                      {{ record.rankingName }}
                    </template>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'min-value'">
                  <div>
                    <a-input v-if="editableData[record.rankingId]"
                      v-model:value="editableData[record.rankingId].minValue" :placeholder="t('form.min-value')"
                      type="number" min="0" :status="editableData[record.rankingId].minValueStatus"
                      @change="debounce(validateFields, 500)(record.rankingId)">
                      <template #suffix>
                        <a-tooltip v-if="editableData[record.rankingId].minValueStatus" placement="top" :title="t('form.min-value-and-max-value-cannot-be-empty')
                          ">
                          <InfoCircleOutlined color="red" />
                        </a-tooltip>
                      </template>
                    </a-input>
                    <template v-else>
                      {{ record.minValue }}
                    </template>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'max-value'">
                  <div>
                    <a-input v-if="editableData[record.rankingId]"
                      v-model:value="editableData[record.rankingId].maxValue" :placeholder="t('form.max-value')"
                      type="number" min="0" :status="editableData[record.rankingId].maxValueStatus"
                      @change="debounce(validateFields, 500)(record.rankingId)">
                      <template #suffix>
                        <a-tooltip v-if="editableData[record.rankingId].maxValueStatus" placement="top" :title="t('form.min-value-and-max-value-cannot-be-empty')
                          ">
                          <InfoCircleOutlined color="red" />
                        </a-tooltip>
                      </template>
                    </a-input>
                    <template v-else>
                      {{ record.maxValue }}
                    </template>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'average-value'">
                  <div>
                    <a-input v-if="editableData[record.rankingId]"
                      v-model:value="editableData[record.rankingId].averageValue" :placeholder="t('form.average-value')"
                      type="number" min="0" :status="editableData[record.rankingId].averageValueStatus"
                      @change="debounce(validateFields, 500)(record.rankingId)">
                      <template #suffix>
                        <a-tooltip v-if="editableData[record.rankingId].averageValueStatus" placement="top"
                          :title="t('form.average-value')">
                          <InfoCircleOutlined color="red" />
                        </a-tooltip>
                      </template>
                    </a-input>
                    <template v-else>
                      {{ record.averageValue }}
                    </template>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'description'">
                  <div>
                    <a-input v-if="editableData[record.rankingId]"
                      v-model:value="editableData[record.rankingId].description"
                      :placeholder="t('form.description.placeholder')"
                      :status="editableData[record.rankingId].descriptionStatus"
                      @change="debounce(validateFields, 500)(record.rankingId)">
                      <template #suffix>
                        <a-tooltip v-if="
                          editableData[record.rankingId].descriptionStatus
                        " placement="top" :title="t('form.description.placeholder')">
                          <InfoCircleOutlined color="red" />
                        </a-tooltip>
                      </template>
                    </a-input>
                    <template v-else>
                      {{ record.description }}
                    </template>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'action'">
                  <div v-if="editableData[record.rankingId]" class="flex flex-justify-center gap-2">
                    <a-button class="flex items-center" size="small" type="primary"
                      @click="handleCreate(record.rankingId)" v-if="editableData[record.rankingId].isAdd">
                      {{ t('button.save') }}
                    </a-button>
                    <a-button class="flex items-center" size="small" type="primary"
                      @click="handleUpdate(record.rankingId)" v-if="editableData[record.rankingId].isEdit">
                      {{ t('button.save') }}
                    </a-button>
                    <a-button v-if="editableData[record.rankingId].isAdd" class="flex items-center" size="small" danger
                      @click="handleCancel(record.rankingId)">
                      <CloseOutlined />
                    </a-button>
                    <a-popconfirm v-if="editableData[record.rankingId].isEdit" :title="t('message.cancel-confirmation')"
                      @confirm="handleCancel(record.rankingId)">
                      <a-button class="flex items-center" size="small" danger>
                        <CloseOutlined />
                      </a-button>
                    </a-popconfirm>
                  </div>
                  <div v-else class="flex flex-justify-center gap-2">
                    <a-button class="flex items-center" size="small" ghost @click="handleEdit(record.rankingId)">
                      <img src="/icon/edit.svg" class="w-[20px]" />
                    </a-button>

                    <a-popconfirm :title="t('message.delete-confirmation')">
                      <a-button class="flex items-center" size="small" ghost @click="handleDelete(record.rankingId)">
                        <img src="/icon/delete.svg" class="w-[20px]" />
                      </a-button>
                    </a-popconfirm>
                  </div>
                </template>
              </template>
            </a-table>
          </a-col>
        </a-row>
      </a-col>
      <a-col flex="auto" span="24">
        <div class="h-full flex items-end">
          <a-row justify="space-between" class="mt-4 w-full">
            <a-col>
              <a-pagination class="pagination" :total="pagination.total" :current="pagination.current"
                :pageSize="pagination.pageSize" @change="handlePaginationChange" />
            </a-col>
            <a-col>
              <a-row :gutter="[12, 12]" justify="center" align="middle">
                <a-col>{{ t('show') }}</a-col>
                <a-col>
                  <a-pagination class="pagination pagination-right" :total="pagination.total"
                    :current="pagination.current" :pageSize="pagination.pageSize" showSizeChanger
                    :buildOptionText="(props: any) => props.value" @change="handlePaginationChange" />
                </a-col>
                <a-col>{{ t('entries') }}</a-col>
              </a-row>
            </a-col>
          </a-row>
        </div>
      </a-col>
    </a-row>
  </page-container>
</template>

<style lang="less" scoped>
.pagination {
  :deep(.ant-pagination-item-active) {
    background: #f99649;
    border-color: #f99649;

    a {
      color: #fff;
    }
  }

  :deep(.ant-select-selector) {
    background: #f99649;
    border-color: #f99649 !important;

    .ant-select-selection-item {
      color: #fff;
    }
  }

  :deep(.ant-select-arrow) {
    color: #fff;
  }
}

.pagination-right {
  :deep(.ant-pagination-prev) {
    display: none;
  }

  :deep(.ant-pagination-next) {
    display: none;
  }

  :deep(.ant-pagination-item) {
    display: none;
  }

  :deep(.ant-pagination-options) {
    margin: 0;
  }

  :deep(.ant-pagination-jump-next) {
    display: none;
  }

  :deep(.ant-pagination-jump-prev) {
    display: none;
  }
}
</style>
