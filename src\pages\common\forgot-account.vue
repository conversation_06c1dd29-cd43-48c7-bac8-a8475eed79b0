<!-- eslint-disable antfu/top-level-function -->
<script setup lang="ts">
import { AxiosError } from 'axios'
import type { Rule } from 'ant-design-vue/es/form'
import { type FormInstance, message } from 'ant-design-vue'
import type { ForgotPasswordParams } from '~/api/common/login'
import { recoverPasswordApi } from '~/api/common/login'
import { getQueryParam } from '~/utils/tools'
import logger from '~@/utils/logger'

// const message = useMessage();
const notification = useNotification()
const router = useRouter()
const { t } = useI18n()
const formRef = ref<FormInstance>()
const submitLoading = shallowRef(false)
const errorAlert = shallowRef(false)

const formState = reactive<ForgotPasswordParams>({
  accountLoginInfo: '',
})

async function recoverPassword(params: ForgotPasswordParams) {
  submitLoading.value = true
  try {
    const { status } = await recoverPasswordApi(params)
    return status
  }
  catch (e) {
    logger.error(e)
  }
}

async function submit() {
  submitLoading.value = true
  try {
    await formRef.value?.validate()
    const params = {
      accountLoginInfo: formState.accountLoginInfo,
    } as unknown as ForgotPasswordParams
    const status = await recoverPassword(params)
    if (status === 200) {
      // token.value = Data?.AccessToken;
      notification.success({
        message: 'メールを送信しました',
        duration: 3,
      })
      const redirect = getQueryParam('redirect', '/login')
      router.push({
        path: redirect,
        replace: true,
      })
    }
    else {
      errorAlert.value = true
      submitLoading.value = false
      message.error('電子メール/ユーザー名が存在しない')
      logger.log('error')
    }
  }
  catch (e) {
    if (e instanceof AxiosError) {
      errorAlert.value = true
      formRef.value?.validate()
      submitLoading.value = false
    }
    submitLoading.value = false
  }
}

// Hàm validator phải là hàm async và return một Promise
const validateEmailAndUsername = async (_rule: Rule, value: string) => {
  if (value === '')
    return Promise.reject(new Error('電子メール/ユーザー名を入力してください'))
}

const rules: Record<string, Rule[]> = {
  accountLoginInfo: [{ required: true, validator: validateEmailAndUsername, trigger: 'blur' }],
}
</script>

<template>
  <div class="login-container">
    <div class="login-content flex-center">
      <div class="box-border flex min-h-[520px]">
        <div class="ant-pro-form-login-main-right px-5 w-[400px] flex-center flex-col relative z-11">
          <div class="text-center py-6 text-2xl">
            {{ t("ResetYourAccount") }}
          </div>
          <a-form ref="formRef" layout="vertical" class="w-full" :model="formState" :rules="rules">
            <a-form-item has-feedback name="accountLoginInfo">
              <a-input v-model:value="formState.accountLoginInfo" allow-clear :placeholder="t('EmailOrUsername')" size="large"/>
            </a-form-item>
            <a-form-item>
              <a-button
                type="primary" block size="large" :loading="submitLoading"
                @click="submit"
              >
                {{ t("button.sendLink") }}
              </a-button>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped></style>
