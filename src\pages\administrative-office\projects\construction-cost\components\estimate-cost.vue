<script setup lang="ts">
import FormulaInlineTooltip from './formula-inline-tooltip.vue'
import FormulaTooltip from './fomula-tooltip.vue'
import type { EstimateBudgetItem } from '~@/api/construction-cost'
import type { ConstructionType } from '~@/utils/constant'

defineProps({
  estimateBudget: {
    type: Object as () => EstimateBudgetItem,
    required: false,
  },
})

const { t } = useI18n()

// Định dạng số tiền theo kiểu Nhật Bản
function formatCurrency(value?: number): string {
  if (!value)
    return '¥0'
  return new Intl.NumberFormat('ja-JP', {
    style: 'currency',
    currency: 'JPY',
    currencyDisplay: 'symbol',
    maximumFractionDigits: 0,
  }).format(value).replace('¥', '¥')
}

// Định dạng phần trăm với dấu
function formatPercentage(value?: number): string {
  if (!value)
    return '0.00%'
  const sign = value >= 0 ? '' : '-'
  const absValue = Math.abs(value)
  return `${sign}${absValue.toFixed(2)}%`
}

function getTitleProcessBudget(type?: ConstructionType) {
  switch (type) {
    case 'MAIN':
      return '工事進捗に応じた予算（本工事費）'
    case 'SUB':
      return '工事進捗に応じた予算（別途工事費）'
    case 'OVERALL':
      return '工事進捗に応じた予算（総合工事費）'
    default:
      return ''
  }
}

function getTitleAgainstBudget(type?: ConstructionType) {
  switch (type) {
    case 'MAIN':
      return '対予算金額（本工事費）'
    case 'SUB':
      return '対予算金額（別途工事費）'
    case 'OVERALL':
      return '対予算金額（総合工事費）'
    default:
      return ''
  }
}

function getTitleActualProfitMargin(type?: ConstructionType) {
  switch (type) {
    case 'MAIN':
      return '本工事費の実績利益率'
    case 'SUB':
      return '別途工事費の実績利益率'
    case 'OVERALL':
      return '総合工事費の実績利益率'
    default:
      return ''
  }
}

function getTitleEstimatedProfitMargin(type?: ConstructionType) {
  switch (type) {
    case 'MAIN':
      return '本工事費の対予算（利益率）'
    case 'SUB':
      return '別途工事費の対予算（利益率）'
    case 'OVERALL':
      return '総合工事費の対予算（利益率）'
    default:
      return ''
  }
}

function getContentEstimatedProfitMargin(type?: ConstructionType) {
  switch (type) {
    case 'MAIN':
      return '= 本工事の実際の利益率 - 本工事の利益率予想'
    case 'SUB':
      return '= 別途工事の実際の利益率 - 別途工事の利益率予想'
    case 'OVERALL':
      return '= 総合工事の実際の利益率 - 総合工事の利益率予想'
    default:
      return ''
  }
}

function getNumberatorActualProfitMargin(type?: ConstructionType) {
  switch (type) {
    case 'MAIN':
      return '累計出来高金額(本工） - 本工事の工事原価合計（累計）'
    case 'SUB':
      return '累計出来高金額(分工） - 別途工事の工事原価合計（累計）'
    case 'OVERALL':
      return '累計出来高金額(総合） - 総合工事の工事原価合計（累計）'
    default:
      return ''
  }
}

function getDenominatorActualProfitMargin(type?: ConstructionType) {
  switch (type) {
    case 'MAIN':
      return '累計出来高金額(本工）'
    case 'SUB':
      return '累計出来高金額(分工）'
    case 'OVERALL':
      return '累計出来高金額(総合）'
    default:
      return ''
  }
}

function getContentAgainstBudget(type?: ConstructionType) {
  switch (type) {
    case 'MAIN':
      return '= 累計出来高金額(本工） - 工事進捗に応じた予算（本工事費）'
    case 'SUB':
      return '= 累計出来高金額(分工） - 工事進捗に応じた予算（別途工事費）'
    case 'OVERALL':
      return '= 累計出来高金額(総合） - 工事進捗に応じた予算（総合工事費）'
    default:
      return ''
  }
}

function getContentProcessBudget(type?: ConstructionType) {
  switch (type) {
    case 'MAIN':
      return '= 本工事予算小計 x 累計進捗率(本工）'
    case 'SUB':
      return '= 別途工事予算小計 x 累計進捗率(分工）'
    case 'OVERALL':
      return '= 総合工事予算小計 x 累計進捗率(総合）'
    default:
      return ''
  }
}

onMounted(() => {
})
</script>

<template>
  <div class="w-full">
    <table class="w-full border-collapse border border-gray-400 border-solid">
      <!-- Header -->
      <thead>
        <tr>
          <th colspan="4" class="bg-white border border-gray-400 border-solid p-2 text-center font-bold">
            <span v-if="estimateBudget?.type === 'MAIN'"> {{ t('construction-cost.main.estimateCost') }}</span>
            <span v-else-if="estimateBudget?.type === 'SUB'">{{ t('construction-cost.sub.estimateCost') }}</span>
            <span v-else-if="estimateBudget?.type === 'OVERALL'">{{ t('construction-cost.overall.estimateCost') }}</span>
          </th>
        </tr>
      </thead>

      <!-- Column Headers -->
      <thead>
        <tr>
          <th class="p-2 bg-blue-50 border border-gray-400 border-solid">
            {{ t('budgetBasedOnConstructionProgress ') }}
          </th>
          <th class="p-2 bg-blue-50 border border-gray-400 border-solid">
            {{ t('againstBudget') }}
          </th>
          <th class="p-2 bg-blue-50 border border-gray-400 border-solid">
            {{ t('actualProfitMargin') }}
          </th>
          <th class="p-2 bg-blue-50 border border-gray-400 border-solid">
            {{ t('mcEstimatedProfitMargin') }}
          </th>
        </tr>
      </thead>

      <!-- Data Row -->
      <tbody>
        <tr>
          <td class="p-2 bg-blue-200 border border-gray-400 border-solid text-center font-mono">
            <FormulaInlineTooltip
              :title="getTitleProcessBudget(estimateBudget?.type)"
              :content="getContentProcessBudget(estimateBudget?.type)"
              :value="formatCurrency(estimateBudget?.budgetAccordingToProgress)"
            />
          </td>
          <td class="p-2 bg-blue-200 border border-gray-400 border-solid text-center font-mono">
            <FormulaInlineTooltip
              :title="getTitleAgainstBudget(estimateBudget?.type)"
              :content="getContentAgainstBudget(estimateBudget?.type)"
              :value="formatCurrency(estimateBudget?.budgetAccordingToProgress)"
            />
          </td>
          <td class="p-2 bg-blue-200 border border-gray-400 border-solid text-center font-mono">
            <FormulaTooltip
              :title="getTitleActualProfitMargin(estimateBudget?.type)"
              :numerator="getNumberatorActualProfitMargin(estimateBudget?.type)"
              :denominator="getDenominatorActualProfitMargin(estimateBudget?.type)"
              :value-percentage="formatPercentage(estimateBudget?.actualProfitMargin)"
            />
          </td>
          <td class="p-2 bg-blue-200 border border-gray-400 border-solid text-center font-mono">
            <FormulaInlineTooltip
              :title="getTitleEstimatedProfitMargin(estimateBudget?.type)"
              :content="getContentEstimatedProfitMargin(estimateBudget?.type)"
              :value="formatPercentage(estimateBudget?.estimatedProfitMargin)"
            />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
