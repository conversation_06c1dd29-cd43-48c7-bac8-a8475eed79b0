<script lang="ts" setup>
import { reactive, ref } from 'vue'

import {
  SearchOutlined,
} from '@ant-design/icons-vue'
import { usePagination } from 'vue-request'
import InvoiceDetailModal from './InvoiceDetailModal.vue'
import CardItem from './components/CardItem.vue'
import type { InputCost, InputCostFilterRequest } from '~@/api/invoice'
import { getInputCostListApi } from '~@/api/invoice'
import type { GetVendorParams } from '~@/api/company/vendor'
import { getVendor } from '~@/api/company/vendor'
import type { FilterParams } from '~@/api/company/project'
import { getPaginatedProjectListApi } from '~@/api/company/project'
import { getEntryType } from '~@/api/company/entry-type'

const { t } = useI18n()

type DateType = [string, string] | undefined
const issueDateRange = ref<DateType>(undefined)
const paymentDateRange = ref<DateType>(undefined)

// Types
const filterFormState = reactive<InputCostFilterRequest>({
  pageNum: 1,
  pageSize: 50,
  vendorId: undefined,
  projectId: undefined,
  entryTypeId: undefined,
  issueDateFrom: undefined,
  issueDateTo: undefined,
  paymentDateFrom: undefined,
  paymentDateTo: undefined,
})

// State
const detailModalVisible = ref(false)
const selectedInvoice = ref<InputCost | null>(null)

// Sample data

// // Methods
// function showDetail(invoice: Invoice) {
//   selectedInvoice.value = invoice
//   detailModalVisible.value = true
// }

function formatNumber(num: number): string {
  return num.toLocaleString()
}

const detailVisible = ref(false)
const currentInvoice = ref<InputCost>()
const isFiltering = ref(false)

// function handleSaveInvoice(updatedInvoice: InputCost) {
//   console.log('Invoice saved:', updatedInvoice)
//   // Update your state or call API to save changes
// }

const inputCostFilterState = reactive<InputCostFilterRequest>({
  pageNum: 1,
  pageSize: 5,
})
const vendorParams = reactive<GetVendorParams>({
  pageNum: 1,
  pageSize: 50,
})

const projectParams = reactive<FilterParams>({
  pageNum: 1,
  pageSize: 50,
})

async function queryInvoice(params: InputCostFilterRequest) {
  const { data } = await getInputCostListApi(params)
  isFiltering.value = false
  return data
}

async function queryVendor(params: GetVendorParams) {
  const { data } = await getVendor(params)
  return data
}

async function queryProject(params: FilterParams) {
  const { data } = await getPaginatedProjectListApi(params)
  return data
}

const {
  data: invoiceData,
  refresh: refreshInvoices,
  run: runInvoice,
  current,
  pageSize,
  total,
} = usePagination(
  queryInvoice,
  {
    defaultParams: [inputCostFilterState],
    pagination: {
      currentKey: 'pageNum',
      pageSizeKey: 'pageSize',
      totalKey: 'totalRecords',
    },
  },
)
const {
  data: vendorData,
} = usePagination(queryVendor, {
  defaultParams: [vendorParams],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const {
  data: projectData,
} = usePagination(queryProject, {
  defaultParams: [projectParams],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const entryTypeParams = reactive<any>({
  pageNum: 1,
  pageSize: 50,
})
async function queryEntryType(params: any) {
  const { data } = await getEntryType(params)
  return data
}
const {
  data: entryTypeData,
} = usePagination(queryEntryType, {
  defaultParams: [entryTypeParams],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})
const entryTypes = computed(() => entryTypeData.value?.entryTypes ?? [])

const invoices = computed(() => invoiceData.value?.items ?? [])
const vendors = computed(() => vendorData.value?.items ?? [])
const projects = computed(() => projectData.value?.items ?? [])

function refreshInputCost() {
  refreshInvoices()
  const idx = invoices.value.findIndex((item: InputCost) => item.inputCostId === currentInvoice.value?.inputCostId)
  if (idx !== -1)
    currentInvoice.value = invoices.value[idx]
}

function onFilterInvoice() {
  if (isFiltering.value)
    return

  isFiltering.value = true
  filterFormState.issueDateFrom = issueDateRange.value?.[0]
  filterFormState.issueDateTo = issueDateRange.value?.[1]
  filterFormState.paymentDateFrom = paymentDateRange.value?.[0]
  filterFormState.paymentDateTo = paymentDateRange.value?.[1]
  runInvoice(filterFormState)
}

function showDetail(invoice: InputCost) {
  currentInvoice.value = invoice
  detailVisible.value = true
}

// async function deleteInvoice(inputCostId: string) {
//   const { status } = await deleteInputCostApi(inputCostId)
//   if (status === 200) {
//     messageNotify.success('Delete successfully')
//     refreshInvoices()
//   }
//   else {
//     messageNotify.error('Delete failed')
//   }
// }

const pageSizeOptions = ref([
  {
    label: '5',
    value: '5',
  },
  {
    label: '10',
    value: '10',
  },
  {
    label: '20',
    value: '20',
  },
  {
    label: '50',
    value: '50',
  },
  {
    label: '100',
    value: '100',
  },
])
</script>

<template>
  <div class="flex flex-col h-screen bg-gray-100 p-4">
    <!-- Filter Section -->
    <a-card class="mb-4 shadow-sm">
      <div class="flex flex-col md:flex-row items-start gap-4">
        <!-- <div class="flex gap-2 mb-4 md:mb-0">
          <a-button type="primary" class="bg-blue-500">
            Day
          </a-button>
          <a-button>Month</a-button>
          <a-button>Year</a-button>
          <a-button>All</a-button>
        </div> -->

        <!-- <div class="flex items-center gap-2 ml-auto">
          <a-date-picker
            v-model:value="searchDate"
            placeholder="Date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="onFilterInvoice"
          />
          <a-input-search
            v-model:value="filterFormState.title"
            placeholder="Search..."
            style="width: 200px;"
            @press-enter="onFilterInvoice"
            @search="onFilterInvoice"
          />
        </div> -->
      </div>

      <!-- Advanced Filter -->
      <div class="mt-4 grid grid-cols-1 md:grid-cols-6 gap-4">
        <div>
          <label class="text-sm text-gray-500">{{ t('project') }}</label>
          <a-select
            v-model:value="filterFormState.projectId"
            :options="projects"
            :field-names="{ label: 'projectName', value: 'projectId' }"
            style="width: 100%"
            allow-clear
            @change="onFilterInvoice"
          />
        </div>
        <div>
          <label class="text-sm text-gray-500">{{ t('supplier') }}</label>
          <a-select
            v-model:value="filterFormState.vendorId"
            :options="vendors"
            :field-names="{ label: 'vendorName', value: 'vendorId' }"
            style="width: 100%"
            allow-clear
            @change="onFilterInvoice"
          />
        </div>
        <div>
          <label class="text-sm text-gray-500">{{ t('invoice-type') }}</label>
          <a-select
            v-model:value="filterFormState.entryTypeId"
            :options="entryTypes"
            :field-names="{ label: 'entryTypeName', value: 'entryTypeId' }"
            style="width: 100%"
            allow-clear
            @change="onFilterInvoice"
          />
        </div>
        <div>
          <label class="text-sm text-gray-500">{{ t('release-date') }}</label>
          <a-range-picker
            v-model:value="issueDateRange"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </div>
        <div>
          <label class="text-sm text-gray-500">{{ t('payment-term') }}</label>
          <a-range-picker
            v-model:value="paymentDateRange"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            style="width: 100%"
          />
        </div>
        <div class="flex items-end gap-2">
          <!-- <div class="flex-grow">
            <label class="text-sm text-gray-500">Total from</label>
            <a-input placeholder="from" />
          </div>
          <div class="flex-grow">
            <label class="text-sm text-gray-500">to</label>
            <a-input placeholder="to" />
          </div> -->
          <a-button type="primary" class="bg-blue-500 flex items-center justify-center" @click="onFilterInvoice">
            <template #icon>
              <SearchOutlined />
            </template>
            {{ t('button.search') }}
          </a-button>
          <a-button class="flex items-center justify-center">
            <template #icon>
              <CarbonReload />
            </template>
            {{ t('button.reset') }}
          </a-button>
        </div>
      </div>
    </a-card>

    <!-- Invoices List -->
    <div class="flex-grow overflow-auto mb-[24px]">
      <div class="space-y-4">
        <a-card v-for="invoice in invoices" :key="invoice.inputCostId" class="shadow-sm hover:shadow-md transition-shadow">
          <CardItem
            :invoice="invoice"
            @show-detail="showDetail"
          />
        </a-card>
        <a-empty v-if="!invoices.length" />
        <a-skeleton v-if="isFiltering" active />
      </div>
    </div>
    <div class="flex justify-between w-full">
      <a-pagination
        v-model:current="current"
        :total="total"
        :page-size="pageSize"
        class="text-[0.875rem] project-pagination"
        :show-size-changer="false"
      />
      <div
        class="flex items-center gap-x-[8px] text-[#74797A] project-select-page-size"
      >
        <span>{{ t("button.show") }}</span>
        <a-select
          v-model:value="pageSize"
          :options="pageSizeOptions"
        >
          <template #suffixIcon>
            <CarbonPagninationArrowDown />
          </template>
        </a-select>
        <span>{{ t("button.entries") }}</span>
      </div>
    </div>

    <!-- Detail Modal -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="Invoice Details"
      :footer="null"
      width="700px"
    >
      <div v-if="selectedInvoice">
        <h2 class="text-xl font-semibold mb-4">
          {{ selectedInvoice.title }}
        </h2>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <h3 class="font-medium">
              General Information
            </h3>
            <div class="grid grid-cols-2 gap-2 mt-2">
              <div class="text-gray-500">
                Invoice Number:
              </div>
              <div>{{ selectedInvoice.originalNumber }}</div>
              <div class="text-gray-500">
                Project:
              </div>
              <div>{{ selectedInvoice.projectName }}</div>
              <div class="text-gray-500">
                Type:
              </div>
              <div>{{ selectedInvoice.entryTypeName }}</div>
              <div class="text-gray-500">
                Total:
              </div>
              <div class="font-bold">
                ¥ {{ formatNumber(selectedInvoice.totalAmount) }}
              </div>
            </div>
          </div>

          <div>
            <h3 class="font-medium">
              Payment Details
            </h3>
            <div class="grid grid-cols-2 gap-2 mt-2">
              <div class="text-gray-500">
                Supplier:
              </div>
              <div>{{ selectedInvoice.vendorName }}</div>
              <div class="text-gray-500">
                Payment Method:
              </div>
              <div>{{ selectedInvoice.paymentTypeName }}</div>
              <div class="text-gray-500">
                Release Date:
              </div>
              <div>{{ selectedInvoice.issueDate }}</div>
              <div class="text-gray-500">
                Payment Term:
              </div>
              <div>{{ selectedInvoice.paymentDate }}</div>
            </div>
          </div>
        </div>

        <div class="mt-6">
          <h3 class="font-medium">
            Notes
          </h3>
          <p class="mt-2 text-gray-600">
            Additional notes for invoice {{ selectedInvoice.originalNumber }}. This section can contain
            any comments or special instructions related to this invoice.
          </p>
        </div>
      </div>

      <div class="flex justify-end mt-6">
        <a-button @click="detailModalVisible = false">
          Close
        </a-button>
        <a-button type="primary" class="ml-2 bg-blue-500">
          Download
        </a-button>
      </div>
    </a-modal>

    <InvoiceDetailModal
      v-model:visible="detailVisible"
      v-model:invoice="currentInvoice as InputCost"
      @refresh-input-cost="refreshInputCost"
    />
  </div>
</template>

<style scoped>
/* Additional custom styles if needed */
:deep(.ant-btn-primary) {
  background-color: #1890ff;
}
</style>
