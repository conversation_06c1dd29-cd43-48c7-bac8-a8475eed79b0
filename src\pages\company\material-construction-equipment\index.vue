<script lang="ts" setup>
import type { ColumnGroupType, ColumnType } from 'ant-design-vue/es/table'

const { t } = useI18n()
const visible = ref<boolean>(false)
const isOpenModal = ref<boolean>(false)

const columns: (ColumnGroupType<any> | ColumnType<any>)[] = [
  {
    title: 'Code',
    dataIndex: 'code',
    key: 'code',
    width: 150,
    align: 'center',
  },
  {
    title: 'Category',
    dataIndex: 'category',
    key: 'category',
    width: 150,
    align: 'center',
  },
  {
    title: 'Item Name',
    dataIndex: 'item_name',
    key: 'item_name',
    width: 150,
    align: 'center',
  },
  {
    title: 'Payment Method',
    dataIndex: 'payment_method',
    key: 'payment_method',
    width: 150,
    align: 'center',
  },
  {
    title: 'Provider',
    dataIndex: 'provider',
    key: 'provider',
    width: 150,
    align: 'center',
  },
  {
    title: 'Manufacturer',
    dataIndex: 'manufacturer',
    key: 'manufacturer',
    width: 150,
    align: 'center',
  },
  {
    title: 'Unit',
    dataIndex: 'unit',
    key: 'unit',
    width: 150,
    align: 'center',
  },
  {
    title: 'Unit price',
    dataIndex: 'unit_price',
    key: 'unit_price',
    width: 150,
    align: 'center',
  },
  {
    title: 'Valid from',
    dataIndex: 'valid_from',
    key: 'valid_from',
    width: 150,
    align: 'center',
  },
  {
    title: 'Valid to',
    dataIndex: 'valid_to',
    key: 'valid_to',
    width: 150,
    align: 'center',
  },
  {
    title: 'Description',
    dataIndex: 'description',
    key: 'description',
    width: 150,
    align: 'center',
  },
]

const detailColumns: (ColumnGroupType<any> | ColumnType<any>)[] = [
  {
    title: 'Code',
    dataIndex: 'code',
    key: 'code',
    width: 150,
    align: 'center',
  },
  {
    title: 'Category',
    dataIndex: 'category',
    key: 'category',
    width: 150,
    align: 'center',
  },
  {
    title: 'Payment Type',
    dataIndex: 'payment_type',
    key: 'payment_type',
    width: 150,
    align: 'center',
  },
  {
    title: 'Item Name',
    dataIndex: 'item_name',
    key: 'item_name',
    width: 150,
    align: 'center',
  },
  {
    title: 'Mechanic Name',
    dataIndex: 'mechanic_name',
    key: 'mechanic_name',
    width: 150,
    align: 'center',
  },
  {
    title: 'Unit',
    dataIndex: 'unit',
    key: 'unit',
    width: 150,
    align: 'center',
  },
  {
    title: 'Unit price',
    dataIndex: 'unit_price',
    key: 'unit_price',
    width: 150,
    align: 'center',
  },
  {
    title: 'Description',
    dataIndex: 'description',
    key: 'description',
    width: 150,
    align: 'center',
  },
  {
    title: 'Valid from',
    dataIndex: 'valid_from',
    key: 'valid_from',
    width: 150,
    align: 'center',
  },
  {
    title: 'Valid to',
    dataIndex: 'valid_to',
    key: 'valid_to',
    width: 150,
    align: 'center',
  },
]

async function openModal() {
  isOpenModal.value = true
}
</script>

<template>
  <page-container>
    <a-card :bordered="false">
      <template #title>
        <a-divider orientation="left" orientation-margin="0px">
          <span class="font-bold text-base">
            List of materials, construction equipment, and Employee
          </span>
        </a-divider>
        <a-form>
          <a-row :gutter="[12, 12]">
            <a-col :span="14">
              <a-input-search
                :placeholder="t('input.placeholder')"
                style="width: 270px"
              />
            </a-col>
            <a-col :span="10" class="flex flex-justify-end">
              <a-space>
                <a-popover
                  v-model:open="visible"
                  trigger="click"
                  placement="bottomRight"
                  :arrow="false"
                  overlay-class-name="filter-popover"
                >
                  <template #title>
                    <div
                      class="flex flex-items-center flex-justify-between p-2"
                    >
                      <span class="flex flex-items-center">
                        <CarbonFilterNew size="14" class="mr-2" />
                        {{ t("button.filter") }}
                      </span>
                      <a-button type="text" @click="visible = false">
                        <close-outlined />
                      </a-button>
                    </div>
                    <a-divider class="m-0" />
                  </template>
                  <template #content>
                    <a-form class="w-[300px]" layout="vertical">
                      <a-row :gutter="12" class="px-2">
                        <a-col span="24">
                          <a-form-item
                            label="Effective Date"
                            name="effective_date"
                          >
                            <a-range-picker
                              class="w-full"
                              value-format="YYYY-MM-DD"
                            />
                          </a-form-item>
                        </a-col>
                        <a-col span="24">
                          <a-form-item label="Code Type" name="code_type">
                            <a-select
                              class="w-full"
                              placeholder="Select Code Type"
                            />
                          </a-form-item>
                        </a-col>
                        <a-col span="24">
                          <a-form-item
                            label="Procurement Route"
                            name="procurement_route"
                          >
                            <a-select
                              class="w-full"
                              placeholder="Select Procurement Route"
                            />
                          </a-form-item>
                        </a-col>
                        <a-col span="24">
                          <a-form-item label="Vendor" name="vendor">
                            <a-select
                              class="w-full"
                              placeholder="Select Vendor"
                            />
                          </a-form-item>
                        </a-col>
                        <a-col span="24">
                          <a-form-item label="Manufacturer" name="manufacturer">
                            <a-select
                              class="w-full"
                              placeholder="Select Manufacturer"
                            />
                          </a-form-item>
                        </a-col>
                        <a-divider class="mb-2 mt-0" />
                        <a-col span="24" class="px-2 text-right">
                          <a-space>
                            <a-button @click="visible = false">
                              {{ $t("button.cancel") }}
                            </a-button>
                            <a-button type="primary">
                              {{ $t("button.filter") }}
                            </a-button>
                          </a-space>
                        </a-col>
                      </a-row>
                    </a-form>
                  </template>
                  <a-button class="flex flex-items-center">
                    <CarbonFilterNew size="14" class="mr-2" />
                    {{ t("button.filter") }} <down-outlined />
                  </a-button>
                </a-popover>
                <a-button>
                  {{ t("button.resetFilter") }}
                </a-button>

                <a-button
                  class="flex flex-items-center"
                  type="primary"
                  @click="openModal()"
                >
                  <plus-outlined /> Add new
                </a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-form>
      </template>
      <a-table :columns="columns" :data-source="[]" />
    </a-card>

    <a-modal v-model:open="isOpenModal" title="" :footer="false" width="100%">
      <a-card class="ant-pro-basicLayout">
        <a-form autocomplete="off" layout="vertical">
          <a-row :gutter="[12, 12]" class="flex-col">
            <a-col>
              <a-row :gutter="[12, 12]">
                <a-col :span="8">
                  <a-form-item label="Item Name">
                    <a-input class="w-full" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="Vendor Name">
                    <a-input class="w-full" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="Mechanic Name">
                    <a-input class="w-full" />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-col>
            <a-col>
              <a-table :columns="detailColumns" :data-source="[]" />
            </a-col>
            <a-col>
              <a-form-item label="Description">
                <a-textarea :rows="4" />
              </a-form-item>
            </a-col>
            <a-col>
              <a-form-item label="Images">
                <a-upload
                  action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                  list-type="picture-card"
                >
                  <div>
                    <plus-outlined />
                    <div style="margin-top: 8px">
                      Upload
                    </div>
                  </div>
                </a-upload>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </a-modal>
  </page-container>
</template>
