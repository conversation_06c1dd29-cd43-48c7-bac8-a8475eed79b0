# Changelog


## v1.0.0-beta.3

[compare changes](https://github.com/antdv-pro/antdv-pro/compare/v1.0.0-beta.2...v1.0.0-beta.3)

### 🩹 Fixes

- Locale error ([df1293d](https://github.com/antdv-pro/antdv-pro/commit/df1293d))

### 🏡 Chore

- Update readme ([27ab14e](https://github.com/antdv-pro/antdv-pro/commit/27ab14e))
- Update deps ([9946ea4](https://github.com/antdv-pro/antdv-pro/commit/9946ea4))
- Update deps ([aae9b3a](https://github.com/antdv-pro/antdv-pro/commit/aae9b3a))
- Unified build output directory #118 ([#118](https://github.com/antdv-pro/antdv-pro/issues/118))
- Update deps ([75937d6](https://github.com/antdv-pro/antdv-pro/commit/75937d6))

### ❤️ Contributors

- <PERSON><PERSON><PERSON>u <<EMAIL>>
- Aibayanyu20 <<EMAIL>>
- <PERSON> ([@kirklin](http://github.com/kirklin))

## v1.0.0-beta.2

[compare changes](https://github.com/antdv-pro/antdv-pro/compare/v1.0.0-beta.1...v1.0.0-beta.2)

### 🚀 Enhancements

- Add vitest ([ec66360](https://github.com/antdv-pro/antdv-pro/commit/ec66360))
- Add icp ([ced7249](https://github.com/antdv-pro/antdv-pro/commit/ced7249))

### 🩹 Fixes

- Fix the page not scrolling to the top when switching ([d6a2406](https://github.com/antdv-pro/antdv-pro/commit/d6a2406))
- Fix locale auto load error ([e441349](https://github.com/antdv-pro/antdv-pro/commit/e441349))
- Using vite's outDir to unify staticPath ([d5f5448](https://github.com/antdv-pro/antdv-pro/commit/d5f5448))
- Fix typo ([e68bb8c](https://github.com/antdv-pro/antdv-pro/commit/e68bb8c))
- Fix i18n hot accept error ([e1da2a8](https://github.com/antdv-pro/antdv-pro/commit/e1da2a8))
- Card layout misalignment ([1944820](https://github.com/antdv-pro/antdv-pro/commit/1944820))
- Remove tabs default margin ([8de0518](https://github.com/antdv-pro/antdv-pro/commit/8de0518))
- Not has multitab prcessing pagecontainer spacing ([a604f9d](https://github.com/antdv-pro/antdv-pro/commit/a604f9d))
- Icp open blank page ([f0739e2](https://github.com/antdv-pro/antdv-pro/commit/f0739e2))
- Page show not right close #122 ([#122](https://github.com/antdv-pro/antdv-pro/issues/122))

### 🏡 Chore

- Change version ([742270e](https://github.com/antdv-pro/antdv-pro/commit/742270e))
- Update deps ([d291e93](https://github.com/antdv-pro/antdv-pro/commit/d291e93))
- Update deps ([00b693e](https://github.com/antdv-pro/antdv-pro/commit/00b693e))

### ❤️ Contributors

- Aibayanyu <<EMAIL>>
- Jiabochao ([@jiabochao](http://github.com/jiabochao))
- Undefined ([@undefined-moe](http://github.com/undefined-moe))
- 萍萍 ([@jiabochao](http://github.com/jiabochao))
- Aibayanyu20 <<EMAIL>>
- Unknown ([@aibayanyu20](http://github.com/aibayanyu20))

## v1.0.0-beta.1

[compare changes](https://github.com/antdv-pro/antdv-pro/compare/v0.2.0...v1.0.0-beta.1)

### 🚀 Enhancements

- Add antdv-style ([329690f](https://github.com/antdv-pro/antdv-pro/commit/329690f))
- Mist replace vite ([df6be41](https://github.com/antdv-pro/antdv-pro/commit/df6be41))
- Delete file ([327ae33](https://github.com/antdv-pro/antdv-pro/commit/327ae33))
- **setting:** Supported watermark switch ([a441950](https://github.com/antdv-pro/antdv-pro/commit/a441950))
- Support eager mode ([32d9340](https://github.com/antdv-pro/antdv-pro/commit/32d9340))

### 🩹 Fixes

- Add component name ([a444650](https://github.com/antdv-pro/antdv-pro/commit/a444650))
- Support vercel deploy ([c78010f](https://github.com/antdv-pro/antdv-pro/commit/c78010f))
- Support vercel deploy ([c2294aa](https://github.com/antdv-pro/antdv-pro/commit/c2294aa))
- Change vercel config ([8af11a8](https://github.com/antdv-pro/antdv-pro/commit/8af11a8))
- Change vercel config ([6f828e5](https://github.com/antdv-pro/antdv-pro/commit/6f828e5))
- Dev server error ([7ef7e00](https://github.com/antdv-pro/antdv-pro/commit/7ef7e00))

### 🏡 Chore

- Update deps ([524df05](https://github.com/antdv-pro/antdv-pro/commit/524df05))
- Update `unplugin-config` ([403f3bb](https://github.com/antdv-pro/antdv-pro/commit/403f3bb))
- Delete multimenu ([5673f06](https://github.com/antdv-pro/antdv-pro/commit/5673f06))
- Resolve conflict ([5cac11e](https://github.com/antdv-pro/antdv-pro/commit/5cac11e))
- Change version ([20bb03f](https://github.com/antdv-pro/antdv-pro/commit/20bb03f))
- Change ([fcac6a0](https://github.com/antdv-pro/antdv-pro/commit/fcac6a0))
- Change ([550a341](https://github.com/antdv-pro/antdv-pro/commit/550a341))
- Change ([c2e4cc5](https://github.com/antdv-pro/antdv-pro/commit/c2e4cc5))
- Change ([1826431](https://github.com/antdv-pro/antdv-pro/commit/1826431))
- **release:** V1.0.0 ([699a870](https://github.com/antdv-pro/antdv-pro/commit/699a870))

### 🤖 CI

- Support deploy ([5f7cd06](https://github.com/antdv-pro/antdv-pro/commit/5f7cd06))
- Support deploy ([e53528e](https://github.com/antdv-pro/antdv-pro/commit/e53528e))
- Change local-dir ([4c278e4](https://github.com/antdv-pro/antdv-pro/commit/4c278e4))
- Change local-dir ([415fa6a](https://github.com/antdv-pro/antdv-pro/commit/415fa6a))
- Change deploy ([259b768](https://github.com/antdv-pro/antdv-pro/commit/259b768))
- Change deploy ([0cc948d](https://github.com/antdv-pro/antdv-pro/commit/0cc948d))

### ❤️ Contributors

- Aibayanyu <<EMAIL>>
- Aibayanyu20 <<EMAIL>>
- 246859 ([@CQUT-Programmer](http://github.com/CQUT-Programmer))
- Kirk Lin ([@kirklin](http://github.com/kirklin))

## v0.2.0

[compare changes](https://github.com/antdv-pro/antdv-pro/compare/v0.0.19...v0.2.0)

### 🚀 Enhancements

- Add exception page ([e857135](https://github.com/antdv-pro/antdv-pro/commit/e857135))
- Add mock server ([2fbecaf](https://github.com/antdv-pro/antdv-pro/commit/2fbecaf))
- 增加结果页路由 ([14d20f8](https://github.com/antdv-pro/antdv-pro/commit/14d20f8))
- 增加结果页i18n配置 ([0f51c7d](https://github.com/antdv-pro/antdv-pro/commit/0f51c7d))
- 同步结果页成功页 #28 ([#28](https://github.com/antdv-pro/antdv-pro/issues/28))
- 增加结果页路由 #29 ([#29](https://github.com/antdv-pro/antdv-pro/issues/29))
- Add new result page ([ad6463e](https://github.com/antdv-pro/antdv-pro/commit/ad6463e))
- Add spinning loader to iframe ([c75a3b8](https://github.com/antdv-pro/antdv-pro/commit/c75a3b8))
- Add spinning loader to iframe ([888d183](https://github.com/antdv-pro/antdv-pro/commit/888d183))
- Add login redirect ([70293ed](https://github.com/antdv-pro/antdv-pro/commit/70293ed))
- 增加路由 ([ef72da5](https://github.com/antdv-pro/antdv-pro/commit/ef72da5))
- 页面 ([39ff6d5](https://github.com/antdv-pro/antdv-pro/commit/39ff6d5))
- 路由配置 ([2af830c](https://github.com/antdv-pro/antdv-pro/commit/2af830c))
- Picocolors custom terminal ([2aca5fd](https://github.com/antdv-pro/antdv-pro/commit/2aca5fd))
- Add basic profile i18n config ([fb2ac66](https://github.com/antdv-pro/antdv-pro/commit/fb2ac66))
- Add basic profile router ([1e8b9e4](https://github.com/antdv-pro/antdv-pro/commit/1e8b9e4))
- Add basic profile i18n config ([cba4ba1](https://github.com/antdv-pro/antdv-pro/commit/cba4ba1))
- 新增列表数据接口 ([11b7c89](https://github.com/antdv-pro/antdv-pro/commit/11b7c89))
- 新增列表数据请求方法 ([47bbd73](https://github.com/antdv-pro/antdv-pro/commit/47bbd73))
- Add a tsconfig rule ([ebaee8d](https://github.com/antdv-pro/antdv-pro/commit/ebaee8d))
- Basic-list page ([dea1394](https://github.com/antdv-pro/antdv-pro/commit/dea1394))
- Add search list ([c95c6aa](https://github.com/antdv-pro/antdv-pro/commit/c95c6aa))
- Add dynamic routes ([766b04e](https://github.com/antdv-pro/antdv-pro/commit/766b04e))
- Init route config ([e9eb147](https://github.com/antdv-pro/antdv-pro/commit/e9eb147))
- New page ([ea00f70](https://github.com/antdv-pro/antdv-pro/commit/ea00f70))
- Login view ([b270521](https://github.com/antdv-pro/antdv-pro/commit/b270521))
- I18n config ([f292469](https://github.com/antdv-pro/antdv-pro/commit/f292469))
- Page ([89fca03](https://github.com/antdv-pro/antdv-pro/commit/89fca03))
- Axios loading ([f60c4d7](https://github.com/antdv-pro/antdv-pro/commit/f60c4d7))
- Loading text prop ([26d96b4](https://github.com/antdv-pro/antdv-pro/commit/26d96b4))
- Route config ([51cba5d](https://github.com/antdv-pro/antdv-pro/commit/51cba5d))
- Update version ([5222e8c](https://github.com/antdv-pro/antdv-pro/commit/5222e8c))
- Account center page ([dfeaf09](https://github.com/antdv-pro/antdv-pro/commit/dfeaf09))
- Account-center i18n config ([0c38c00](https://github.com/antdv-pro/antdv-pro/commit/0c38c00))
- Add basic table routes ([d445c08](https://github.com/antdv-pro/antdv-pro/commit/d445c08))
- Add basic table i18n config ([d0e3385](https://github.com/antdv-pro/antdv-pro/commit/d0e3385))
- Add basic table demo ([ddb7c13](https://github.com/antdv-pro/antdv-pro/commit/ddb7c13))
- Add static routes ([9f42e28](https://github.com/antdv-pro/antdv-pro/commit/9f42e28))
- Account-settings page ([b5f1360](https://github.com/antdv-pro/antdv-pro/commit/b5f1360))
- I18n config ([c50b840](https://github.com/antdv-pro/antdv-pro/commit/c50b840))
- Add step-form router ([23603f2](https://github.com/antdv-pro/antdv-pro/commit/23603f2))
- Add step-form pages ([40baabe](https://github.com/antdv-pro/antdv-pro/commit/40baabe))
- Add footer-tool-bar components ([7552a18](https://github.com/antdv-pro/antdv-pro/commit/7552a18))
- Add advanced form router ([3bf0624](https://github.com/antdv-pro/antdv-pro/commit/3bf0624))
- Add task form ([a4c1c51](https://github.com/antdv-pro/antdv-pro/commit/a4c1c51))
- Add advanced form ([2fc8cc2](https://github.com/antdv-pro/antdv-pro/commit/2fc8cc2))
- Add components parent ([fc47099](https://github.com/antdv-pro/antdv-pro/commit/fc47099))
- Add parent layout ([75f4fd7](https://github.com/antdv-pro/antdv-pro/commit/75f4fd7))
- Feat list ([412da2d](https://github.com/antdv-pro/antdv-pro/commit/412da2d))
- Add `unocss-preset-chinese`, `unocss-preset-ease` ([7e5b706](https://github.com/antdv-pro/antdv-pro/commit/7e5b706))
- Add some files ([431b1eb](https://github.com/antdv-pro/antdv-pro/commit/431b1eb))
- Add consult-table page ([41ef497](https://github.com/antdv-pro/antdv-pro/commit/41ef497))
- Add field trend components ([a2ecae7](https://github.com/antdv-pro/antdv-pro/commit/a2ecae7))
- Add introduce-row first demo ([7a7e540](https://github.com/antdv-pro/antdv-pro/commit/7a7e540))
- Complete introduce-row demo ([659bcaf](https://github.com/antdv-pro/antdv-pro/commit/659bcaf))
- Add antv/g2plot package ([41c742e](https://github.com/antdv-pro/antdv-pro/commit/41c742e))
- Add part of sales-card demo ([ac18ff6](https://github.com/antdv-pro/antdv-pro/commit/ac18ff6))
- Delete useless code ([acbf99b](https://github.com/antdv-pro/antdv-pro/commit/acbf99b))
- Complete sales-card demo ([ea9d0cd](https://github.com/antdv-pro/antdv-pro/commit/ea9d0cd))
- Add 修改侧边栏可伸缩按钮位置 ([12cc61d](https://github.com/antdv-pro/antdv-pro/commit/12cc61d))
- Add 修改侧边伸缩默认显示 ([4aebe57](https://github.com/antdv-pro/antdv-pro/commit/4aebe57))
- Add 控制伸缩功能只在侧边布局生效 ([df0738c](https://github.com/antdv-pro/antdv-pro/commit/df0738c))
- Complete number-info and top-search demo ([b0ceb48](https://github.com/antdv-pro/antdv-pro/commit/b0ceb48))
- Complete proportion-sales demo ([be3b597](https://github.com/antdv-pro/antdv-pro/commit/be3b597))
- Complete offline-data demo ([97cf972](https://github.com/antdv-pro/antdv-pro/commit/97cf972))
- Reconstruct file location ([181e9ba](https://github.com/antdv-pro/antdv-pro/commit/181e9ba))
- Add part of monitor demo ([0f3ca9d](https://github.com/antdv-pro/antdv-pro/commit/0f3ca9d))
- Complete part of monitor demo ([f671f56](https://github.com/antdv-pro/antdv-pro/commit/f671f56))
- Add part of workplace demo ([32958ac](https://github.com/antdv-pro/antdv-pro/commit/32958ac))
- Complete workplace demo ([257c6b5](https://github.com/antdv-pro/antdv-pro/commit/257c6b5))
- Complete monitor demo ([7b84c28](https://github.com/antdv-pro/antdv-pro/commit/7b84c28))
- Change filename ([57a9bb0](https://github.com/antdv-pro/antdv-pro/commit/57a9bb0))
- Add storage locale ([bf52bbb](https://github.com/antdv-pro/antdv-pro/commit/bf52bbb))
- Support dynamic router close #93 ([#93](https://github.com/antdv-pro/antdv-pro/issues/93))
- Add virtual list ([069f27f](https://github.com/antdv-pro/antdv-pro/commit/069f27f))
- Change text ([4dccc14](https://github.com/antdv-pro/antdv-pro/commit/4dccc14))
- Add `unplugin-config` ([c6107cf](https://github.com/antdv-pro/antdv-pro/commit/c6107cf))

### 🔥 Performance

- 优化单独监听路由浪费渲染性能 ([05a3d79](https://github.com/antdv-pro/antdv-pro/commit/05a3d79))

### 🩹 Fixes

- Fix iframe transition error ([57311cc](https://github.com/antdv-pro/antdv-pro/commit/57311cc))
- Delete scroll ([a373517](https://github.com/antdv-pro/antdv-pro/commit/a373517))
- Add script toJS ([1601334](https://github.com/antdv-pro/antdv-pro/commit/1601334))
- Result page i18n step2-operator ([c900087](https://github.com/antdv-pro/antdv-pro/commit/c900087))
- Success result i18n ([412dfeb](https://github.com/antdv-pro/antdv-pro/commit/412dfeb))
- Router i18n ([d00cf4e](https://github.com/antdv-pro/antdv-pro/commit/d00cf4e))
- Success page type error ([71aa9ec](https://github.com/antdv-pro/antdv-pro/commit/71aa9ec))
- Success page type error ([451afd0](https://github.com/antdv-pro/antdv-pro/commit/451afd0))
- Remove console log ([8c8a2db](https://github.com/antdv-pro/antdv-pro/commit/8c8a2db))
- 修改路由错误 ([0d5e75c](https://github.com/antdv-pro/antdv-pro/commit/0d5e75c))
- Dark theme error ([2c08422](https://github.com/antdv-pro/antdv-pro/commit/2c08422))
- Commit-msg-lint ([2a91560](https://github.com/antdv-pro/antdv-pro/commit/2a91560))
- Commit-msg-lint ([8e47aae](https://github.com/antdv-pro/antdv-pro/commit/8e47aae))
- Commit-msg-lint, remove trailing comma ([e578774](https://github.com/antdv-pro/antdv-pro/commit/e578774))
- 修改错误配置 ([0dc9048](https://github.com/antdv-pro/antdv-pro/commit/0dc9048))
- Change dir name ([288e11c](https://github.com/antdv-pro/antdv-pro/commit/288e11c))
- Fix qrcode ([1086a8d](https://github.com/antdv-pro/antdv-pro/commit/1086a8d))
- Dark mode change error ([67ad773](https://github.com/antdv-pro/antdv-pro/commit/67ad773))
- Lint error ([f064f70](https://github.com/antdv-pro/antdv-pro/commit/f064f70))
- Build error ([1c13da1](https://github.com/antdv-pro/antdv-pro/commit/1c13da1))
- Fix top mode header appear scroll && iframe is not fullscreen ([d07e846](https://github.com/antdv-pro/antdv-pro/commit/d07e846))
- Change ([6e07288](https://github.com/antdv-pro/antdv-pro/commit/6e07288))
- Build error ([8563a69](https://github.com/antdv-pro/antdv-pro/commit/8563a69))
- Menu icon #53 ([#53](https://github.com/antdv-pro/antdv-pro/issues/53))
- Login theme layout ([63e26a7](https://github.com/antdv-pro/antdv-pro/commit/63e26a7))
- Rename enum file ([49edbc0](https://github.com/antdv-pro/antdv-pro/commit/49edbc0))
- Login view change ([e861e47](https://github.com/antdv-pro/antdv-pro/commit/e861e47))
- Fix page not full ([0caac29](https://github.com/antdv-pro/antdv-pro/commit/0caac29))
- Router component ([a6e0fcf](https://github.com/antdv-pro/antdv-pro/commit/a6e0fcf))
- Optimization level ([219646c](https://github.com/antdv-pro/antdv-pro/commit/219646c))
- I18n symbol #60 ([#60](https://github.com/antdv-pro/antdv-pro/issues/60))
- Login panel ([34cf8ae](https://github.com/antdv-pro/antdv-pro/commit/34cf8ae))
- Validate not take effect ([baae131](https://github.com/antdv-pro/antdv-pro/commit/baae131))
- Validate not take effect ([d132d04](https://github.com/antdv-pro/antdv-pro/commit/d132d04))
- Add page container ([706087c](https://github.com/antdv-pro/antdv-pro/commit/706087c))
- Type ([7d8ccde](https://github.com/antdv-pro/antdv-pro/commit/7d8ccde))
- 将新消息通知中的三个切换按钮的checked状态单独定义 ([c7a44b7](https://github.com/antdv-pro/antdv-pro/commit/c7a44b7))
- Footer tool bar support dark mode ([aae8cb3](https://github.com/antdv-pro/antdv-pro/commit/aae8cb3))
- Fix radio-group ([e1a75b1](https://github.com/antdv-pro/antdv-pro/commit/e1a75b1))
- Fix generate route ([b033198](https://github.com/antdv-pro/antdv-pro/commit/b033198))
- Fix generate route ([41b50a1](https://github.com/antdv-pro/antdv-pro/commit/41b50a1))
- Menu hidden header is not full close #71 ([#71](https://github.com/antdv-pro/antdv-pro/issues/71))
- Build error close #75 ([#75](https://github.com/antdv-pro/antdv-pro/issues/75))
- Collapsed trigger ([ff8ae6c](https://github.com/antdv-pro/antdv-pro/commit/ff8ae6c))
- Account-setting avatar size ([069ad16](https://github.com/antdv-pro/antdv-pro/commit/069ad16))
- Base-loading ui ([dfed9a9](https://github.com/antdv-pro/antdv-pro/commit/dfed9a9))
- Modify route name to keep unique and basic form path ([69be62f](https://github.com/antdv-pro/antdv-pro/commit/69be62f))
- Issues#68 ([#68](https://github.com/antdv-pro/antdv-pro/issues/68))
- Add 删除打印和修正伸缩图标大小 ([22e74a8](https://github.com/antdv-pro/antdv-pro/commit/22e74a8))
- Add pagination ([a04e223](https://github.com/antdv-pro/antdv-pro/commit/a04e223))
- Issue#81 ([#81](https://github.com/antdv-pro/antdv-pro/issues/81))
- Mock server error close #86 ([#86](https://github.com/antdv-pro/antdv-pro/issues/86))
- Fix win and macos server env ([91bc884](https://github.com/antdv-pro/antdv-pro/commit/91bc884))
- Dark mode ([1e31431](https://github.com/antdv-pro/antdv-pro/commit/1e31431))
- Build error ([65bda0e](https://github.com/antdv-pro/antdv-pro/commit/65bda0e))
- Build error ([3c5bc4b](https://github.com/antdv-pro/antdv-pro/commit/3c5bc4b))
- Locale storage optimize ([a49d090](https://github.com/antdv-pro/antdv-pro/commit/a49d090))
- Chart support destroy ([65bdfc7](https://github.com/antdv-pro/antdv-pro/commit/65bdfc7))
- Change i18n ([8c3f517](https://github.com/antdv-pro/antdv-pro/commit/8c3f517))
- Build error ([50b5773](https://github.com/antdv-pro/antdv-pro/commit/50b5773))
- Chart auto fit ([f03bb48](https://github.com/antdv-pro/antdv-pro/commit/f03bb48))
- Login form logo container layout ([a87df0d](https://github.com/antdv-pro/antdv-pro/commit/a87df0d))
- Set autocomplete to off ([8ae0024](https://github.com/antdv-pro/antdv-pro/commit/8ae0024))
- Switch menu stuck & stopped ([8173341](https://github.com/antdv-pro/antdv-pro/commit/8173341))
- Add router key ([91256e3](https://github.com/antdv-pro/antdv-pro/commit/91256e3))
- Change route view ([411a4b5](https://github.com/antdv-pro/antdv-pro/commit/411a4b5))
- Change ([e2d046e](https://github.com/antdv-pro/antdv-pro/commit/e2d046e))
- Add `defineOptions` to index file ([52e4053](https://github.com/antdv-pro/antdv-pro/commit/52e4053))

### 💅 Refactors

- Change files name ([16f7940](https://github.com/antdv-pro/antdv-pro/commit/16f7940))

### 🏡 Chore

- Add wx group jpg ([fc6481c](https://github.com/antdv-pro/antdv-pro/commit/fc6481c))
- Update wx group ([2b32fa5](https://github.com/antdv-pro/antdv-pro/commit/2b32fa5))
- Update dependencies for Nitro as devDependencies ([8480b27](https://github.com/antdv-pro/antdv-pro/commit/8480b27))
- Update dependencies for Nitro as devDependencies ([6fbafac](https://github.com/antdv-pro/antdv-pro/commit/6fbafac))
- Remove docs script ([18cf8f5](https://github.com/antdv-pro/antdv-pro/commit/18cf8f5))
- Add deps ([a014040](https://github.com/antdv-pro/antdv-pro/commit/a014040))
- Add commit check ([ad43dc1](https://github.com/antdv-pro/antdv-pro/commit/ad43dc1))
- Add .gitattributes ([cc821f4](https://github.com/antdv-pro/antdv-pro/commit/cc821f4))
- 删减路径多余空格 ([1617c99](https://github.com/antdv-pro/antdv-pro/commit/1617c99))
- Change ci ([e4638d8](https://github.com/antdv-pro/antdv-pro/commit/e4638d8))
- Add start date ([3de8ae4](https://github.com/antdv-pro/antdv-pro/commit/3de8ae4))
- Types ([fd2f05f](https://github.com/antdv-pro/antdv-pro/commit/fd2f05f))
- Chore ([6838cab](https://github.com/antdv-pro/antdv-pro/commit/6838cab))
- Change ([d26935d](https://github.com/antdv-pro/antdv-pro/commit/d26935d))
- Add current route ([1322f42](https://github.com/antdv-pro/antdv-pro/commit/1322f42))
- Change components ([29d86c2](https://github.com/antdv-pro/antdv-pro/commit/29d86c2))
- Change name ([85cabdb](https://github.com/antdv-pro/antdv-pro/commit/85cabdb))
- **composables/loading:** Loading param note ([e81849a](https://github.com/antdv-pro/antdv-pro/commit/e81849a))
- Add gitee ([371e5ea](https://github.com/antdv-pro/antdv-pro/commit/371e5ea))
- BubbleCanvas ts type ([3fdd93b](https://github.com/antdv-pro/antdv-pro/commit/3fdd93b))
- Change ([49a95a4](https://github.com/antdv-pro/antdv-pro/commit/49a95a4))
- Change ([70a8034](https://github.com/antdv-pro/antdv-pro/commit/70a8034))
- Ignore component.d.ts ([4bbd074](https://github.com/antdv-pro/antdv-pro/commit/4bbd074))
- Ignore component.d.ts ([2ecae5b](https://github.com/antdv-pro/antdv-pro/commit/2ecae5b))
- Delete excess ([aae0a7f](https://github.com/antdv-pro/antdv-pro/commit/aae0a7f))
- Del basic table demo ([211e0a4](https://github.com/antdv-pro/antdv-pro/commit/211e0a4))
- Add account routerlink ([403d3a5](https://github.com/antdv-pro/antdv-pro/commit/403d3a5))
- Add space to advance form ([80faa7e](https://github.com/antdv-pro/antdv-pro/commit/80faa7e))
- Change format ([4af5f56](https://github.com/antdv-pro/antdv-pro/commit/4af5f56))
- Change ([ac15c2a](https://github.com/antdv-pro/antdv-pro/commit/ac15c2a))
- Font optimization ([18a31b7](https://github.com/antdv-pro/antdv-pro/commit/18a31b7))
- Change version ([692bd7c](https://github.com/antdv-pro/antdv-pro/commit/692bd7c))
- Change version ([d45e443](https://github.com/antdv-pro/antdv-pro/commit/d45e443))
- Change version ([bdafa15](https://github.com/antdv-pro/antdv-pro/commit/bdafa15))
- Change version ([199bf5d](https://github.com/antdv-pro/antdv-pro/commit/199bf5d))
- Change version ([6eee4de](https://github.com/antdv-pro/antdv-pro/commit/6eee4de))
- Change version ([c696770](https://github.com/antdv-pro/antdv-pro/commit/c696770))
- Change pnpm-lock version ([caedc74](https://github.com/antdv-pro/antdv-pro/commit/caedc74))
- Change ([747b6c2](https://github.com/antdv-pro/antdv-pro/commit/747b6c2))
- 修改侧边菜单超出显示样式 ([368ffe0](https://github.com/antdv-pro/antdv-pro/commit/368ffe0))
- Fix console error ([a08f428](https://github.com/antdv-pro/antdv-pro/commit/a08f428))
- Use auto import ([4be2127](https://github.com/antdv-pro/antdv-pro/commit/4be2127))
- **release:** V0.1.0 ([f6f3781](https://github.com/antdv-pro/antdv-pro/commit/f6f3781))
- Change ([290413e](https://github.com/antdv-pro/antdv-pro/commit/290413e))
- Change ([80b5b74](https://github.com/antdv-pro/antdv-pro/commit/80b5b74))
- Change version ([c31dea8](https://github.com/antdv-pro/antdv-pro/commit/c31dea8))

### 🎨 Styles

- Fix card-list style ([6b68f8d](https://github.com/antdv-pro/antdv-pro/commit/6b68f8d))
- Login change ([4e74769](https://github.com/antdv-pro/antdv-pro/commit/4e74769))
- Fix style ([8d8a96e](https://github.com/antdv-pro/antdv-pro/commit/8d8a96e))
- Fix style ([05d072e](https://github.com/antdv-pro/antdv-pro/commit/05d072e))

### 🤖 CI

- Add lint ([a6cbed7](https://github.com/antdv-pro/antdv-pro/commit/a6cbed7))

### ❤️ Contributors

- Aibayanyu <<EMAIL>>
- Kirk Lin ([@kirklin](http://github.com/kirklin))
- Liosummer <<EMAIL>>
- Windlil ([@windlil](http://github.com/windlil))
- Aibayanyu20 <<EMAIL>>
- Konv Suu <<EMAIL>>
- AutismSuperman <<EMAIL>>
- 杜天宇 <<EMAIL>>
- AShu-guo <<EMAIL>>
- Sun1090 ([@Sun1090](http://github.com/Sun1090))
- Undefined ([@undefined-moe](http://github.com/undefined-moe))
- LC1 <<EMAIL>>
- Yizhankui <<EMAIL>>
- 张浩杰 ([@HavocZhang](http://github.com/HavocZhang))
- 一个小瘪三 <<EMAIL>>
- Qi Yuhang <<EMAIL>>
- Qyh <<EMAIL>>
- Zev Zhu 
- Adekang <<EMAIL>>

## v0.1.0

[compare changes](https://github.com/antdv-pro/antdv-pro/compare/v0.0.19...v0.1.0)

### 🚀 Enhancements

- Add exception page ([e857135](https://github.com/antdv-pro/antdv-pro/commit/e857135))
- Add mock server ([2fbecaf](https://github.com/antdv-pro/antdv-pro/commit/2fbecaf))
- 增加结果页路由 ([14d20f8](https://github.com/antdv-pro/antdv-pro/commit/14d20f8))
- 增加结果页i18n配置 ([0f51c7d](https://github.com/antdv-pro/antdv-pro/commit/0f51c7d))
- 同步结果页成功页 #28 ([#28](https://github.com/antdv-pro/antdv-pro/issues/28))
- 增加结果页路由 #29 ([#29](https://github.com/antdv-pro/antdv-pro/issues/29))
- Add new result page ([ad6463e](https://github.com/antdv-pro/antdv-pro/commit/ad6463e))
- Add spinning loader to iframe ([c75a3b8](https://github.com/antdv-pro/antdv-pro/commit/c75a3b8))
- Add spinning loader to iframe ([888d183](https://github.com/antdv-pro/antdv-pro/commit/888d183))
- Add login redirect ([70293ed](https://github.com/antdv-pro/antdv-pro/commit/70293ed))
- 增加路由 ([ef72da5](https://github.com/antdv-pro/antdv-pro/commit/ef72da5))
- 页面 ([39ff6d5](https://github.com/antdv-pro/antdv-pro/commit/39ff6d5))
- 路由配置 ([2af830c](https://github.com/antdv-pro/antdv-pro/commit/2af830c))
- Picocolors custom terminal ([2aca5fd](https://github.com/antdv-pro/antdv-pro/commit/2aca5fd))
- Add basic profile i18n config ([fb2ac66](https://github.com/antdv-pro/antdv-pro/commit/fb2ac66))
- Add basic profile router ([1e8b9e4](https://github.com/antdv-pro/antdv-pro/commit/1e8b9e4))
- Add basic profile i18n config ([cba4ba1](https://github.com/antdv-pro/antdv-pro/commit/cba4ba1))
- 新增列表数据接口 ([11b7c89](https://github.com/antdv-pro/antdv-pro/commit/11b7c89))
- 新增列表数据请求方法 ([47bbd73](https://github.com/antdv-pro/antdv-pro/commit/47bbd73))
- Add a tsconfig rule ([ebaee8d](https://github.com/antdv-pro/antdv-pro/commit/ebaee8d))
- Basic-list page ([dea1394](https://github.com/antdv-pro/antdv-pro/commit/dea1394))
- Add search list ([c95c6aa](https://github.com/antdv-pro/antdv-pro/commit/c95c6aa))
- Add dynamic routes ([766b04e](https://github.com/antdv-pro/antdv-pro/commit/766b04e))
- Init route config ([e9eb147](https://github.com/antdv-pro/antdv-pro/commit/e9eb147))
- New page ([ea00f70](https://github.com/antdv-pro/antdv-pro/commit/ea00f70))
- Login view ([b270521](https://github.com/antdv-pro/antdv-pro/commit/b270521))
- I18n config ([f292469](https://github.com/antdv-pro/antdv-pro/commit/f292469))
- Page ([89fca03](https://github.com/antdv-pro/antdv-pro/commit/89fca03))
- Axios loading ([f60c4d7](https://github.com/antdv-pro/antdv-pro/commit/f60c4d7))
- Loading text prop ([26d96b4](https://github.com/antdv-pro/antdv-pro/commit/26d96b4))
- Route config ([51cba5d](https://github.com/antdv-pro/antdv-pro/commit/51cba5d))
- Update version ([5222e8c](https://github.com/antdv-pro/antdv-pro/commit/5222e8c))
- Account center page ([dfeaf09](https://github.com/antdv-pro/antdv-pro/commit/dfeaf09))
- Account-center i18n config ([0c38c00](https://github.com/antdv-pro/antdv-pro/commit/0c38c00))
- Add basic table routes ([d445c08](https://github.com/antdv-pro/antdv-pro/commit/d445c08))
- Add basic table i18n config ([d0e3385](https://github.com/antdv-pro/antdv-pro/commit/d0e3385))
- Add basic table demo ([ddb7c13](https://github.com/antdv-pro/antdv-pro/commit/ddb7c13))
- Add static routes ([9f42e28](https://github.com/antdv-pro/antdv-pro/commit/9f42e28))
- Account-settings page ([b5f1360](https://github.com/antdv-pro/antdv-pro/commit/b5f1360))
- I18n config ([c50b840](https://github.com/antdv-pro/antdv-pro/commit/c50b840))
- Add step-form router ([23603f2](https://github.com/antdv-pro/antdv-pro/commit/23603f2))
- Add step-form pages ([40baabe](https://github.com/antdv-pro/antdv-pro/commit/40baabe))
- Add footer-tool-bar components ([7552a18](https://github.com/antdv-pro/antdv-pro/commit/7552a18))
- Add advanced form router ([3bf0624](https://github.com/antdv-pro/antdv-pro/commit/3bf0624))
- Add task form ([a4c1c51](https://github.com/antdv-pro/antdv-pro/commit/a4c1c51))
- Add advanced form ([2fc8cc2](https://github.com/antdv-pro/antdv-pro/commit/2fc8cc2))
- Add components parent ([fc47099](https://github.com/antdv-pro/antdv-pro/commit/fc47099))
- Add parent layout ([75f4fd7](https://github.com/antdv-pro/antdv-pro/commit/75f4fd7))
- Feat list ([412da2d](https://github.com/antdv-pro/antdv-pro/commit/412da2d))
- Add `unocss-preset-chinese`, `unocss-preset-ease` ([7e5b706](https://github.com/antdv-pro/antdv-pro/commit/7e5b706))
- Add some files ([431b1eb](https://github.com/antdv-pro/antdv-pro/commit/431b1eb))
- Add consult-table page ([41ef497](https://github.com/antdv-pro/antdv-pro/commit/41ef497))
- Add field trend components ([a2ecae7](https://github.com/antdv-pro/antdv-pro/commit/a2ecae7))
- Add introduce-row first demo ([7a7e540](https://github.com/antdv-pro/antdv-pro/commit/7a7e540))
- Complete introduce-row demo ([659bcaf](https://github.com/antdv-pro/antdv-pro/commit/659bcaf))
- Add antv/g2plot package ([41c742e](https://github.com/antdv-pro/antdv-pro/commit/41c742e))
- Add part of sales-card demo ([ac18ff6](https://github.com/antdv-pro/antdv-pro/commit/ac18ff6))
- Delete useless code ([acbf99b](https://github.com/antdv-pro/antdv-pro/commit/acbf99b))
- Complete sales-card demo ([ea9d0cd](https://github.com/antdv-pro/antdv-pro/commit/ea9d0cd))
- Add 修改侧边栏可伸缩按钮位置 ([12cc61d](https://github.com/antdv-pro/antdv-pro/commit/12cc61d))
- Add 修改侧边伸缩默认显示 ([4aebe57](https://github.com/antdv-pro/antdv-pro/commit/4aebe57))
- Add 控制伸缩功能只在侧边布局生效 ([df0738c](https://github.com/antdv-pro/antdv-pro/commit/df0738c))
- Complete number-info and top-search demo ([b0ceb48](https://github.com/antdv-pro/antdv-pro/commit/b0ceb48))
- Complete proportion-sales demo ([be3b597](https://github.com/antdv-pro/antdv-pro/commit/be3b597))
- Complete offline-data demo ([97cf972](https://github.com/antdv-pro/antdv-pro/commit/97cf972))
- Reconstruct file location ([181e9ba](https://github.com/antdv-pro/antdv-pro/commit/181e9ba))
- Add part of monitor demo ([0f3ca9d](https://github.com/antdv-pro/antdv-pro/commit/0f3ca9d))
- Complete part of monitor demo ([f671f56](https://github.com/antdv-pro/antdv-pro/commit/f671f56))
- Add part of workplace demo ([32958ac](https://github.com/antdv-pro/antdv-pro/commit/32958ac))
- Complete workplace demo ([257c6b5](https://github.com/antdv-pro/antdv-pro/commit/257c6b5))
- Complete monitor demo ([7b84c28](https://github.com/antdv-pro/antdv-pro/commit/7b84c28))
- Change filename ([57a9bb0](https://github.com/antdv-pro/antdv-pro/commit/57a9bb0))
- Add storage locale ([bf52bbb](https://github.com/antdv-pro/antdv-pro/commit/bf52bbb))
- Support dynamic router close #93 ([#93](https://github.com/antdv-pro/antdv-pro/issues/93))
- Add virtual list ([069f27f](https://github.com/antdv-pro/antdv-pro/commit/069f27f))
- Change text ([4dccc14](https://github.com/antdv-pro/antdv-pro/commit/4dccc14))

### 🔥 Performance

- 优化单独监听路由浪费渲染性能 ([05a3d79](https://github.com/antdv-pro/antdv-pro/commit/05a3d79))

### 🩹 Fixes

- Fix iframe transition error ([57311cc](https://github.com/antdv-pro/antdv-pro/commit/57311cc))
- Delete scroll ([a373517](https://github.com/antdv-pro/antdv-pro/commit/a373517))
- Add script toJS ([1601334](https://github.com/antdv-pro/antdv-pro/commit/1601334))
- Result page i18n step2-operator ([c900087](https://github.com/antdv-pro/antdv-pro/commit/c900087))
- Success result i18n ([412dfeb](https://github.com/antdv-pro/antdv-pro/commit/412dfeb))
- Router i18n ([d00cf4e](https://github.com/antdv-pro/antdv-pro/commit/d00cf4e))
- Success page type error ([71aa9ec](https://github.com/antdv-pro/antdv-pro/commit/71aa9ec))
- Success page type error ([451afd0](https://github.com/antdv-pro/antdv-pro/commit/451afd0))
- Remove console log ([8c8a2db](https://github.com/antdv-pro/antdv-pro/commit/8c8a2db))
- 修改路由错误 ([0d5e75c](https://github.com/antdv-pro/antdv-pro/commit/0d5e75c))
- Dark theme error ([2c08422](https://github.com/antdv-pro/antdv-pro/commit/2c08422))
- Commit-msg-lint ([2a91560](https://github.com/antdv-pro/antdv-pro/commit/2a91560))
- Commit-msg-lint ([8e47aae](https://github.com/antdv-pro/antdv-pro/commit/8e47aae))
- Commit-msg-lint, remove trailing comma ([e578774](https://github.com/antdv-pro/antdv-pro/commit/e578774))
- 修改错误配置 ([0dc9048](https://github.com/antdv-pro/antdv-pro/commit/0dc9048))
- Change dir name ([288e11c](https://github.com/antdv-pro/antdv-pro/commit/288e11c))
- Fix qrcode ([1086a8d](https://github.com/antdv-pro/antdv-pro/commit/1086a8d))
- Dark mode change error ([67ad773](https://github.com/antdv-pro/antdv-pro/commit/67ad773))
- Lint error ([f064f70](https://github.com/antdv-pro/antdv-pro/commit/f064f70))
- Build error ([1c13da1](https://github.com/antdv-pro/antdv-pro/commit/1c13da1))
- Fix top mode header appear scroll && iframe is not fullscreen ([d07e846](https://github.com/antdv-pro/antdv-pro/commit/d07e846))
- Change ([6e07288](https://github.com/antdv-pro/antdv-pro/commit/6e07288))
- Build error ([8563a69](https://github.com/antdv-pro/antdv-pro/commit/8563a69))
- Menu icon #53 ([#53](https://github.com/antdv-pro/antdv-pro/issues/53))
- Login theme layout ([63e26a7](https://github.com/antdv-pro/antdv-pro/commit/63e26a7))
- Rename enum file ([49edbc0](https://github.com/antdv-pro/antdv-pro/commit/49edbc0))
- Login view change ([e861e47](https://github.com/antdv-pro/antdv-pro/commit/e861e47))
- Fix page not full ([0caac29](https://github.com/antdv-pro/antdv-pro/commit/0caac29))
- Router component ([a6e0fcf](https://github.com/antdv-pro/antdv-pro/commit/a6e0fcf))
- Optimization level ([219646c](https://github.com/antdv-pro/antdv-pro/commit/219646c))
- I18n symbol #60 ([#60](https://github.com/antdv-pro/antdv-pro/issues/60))
- Login panel ([34cf8ae](https://github.com/antdv-pro/antdv-pro/commit/34cf8ae))
- Validate not take effect ([baae131](https://github.com/antdv-pro/antdv-pro/commit/baae131))
- Validate not take effect ([d132d04](https://github.com/antdv-pro/antdv-pro/commit/d132d04))
- Add page container ([706087c](https://github.com/antdv-pro/antdv-pro/commit/706087c))
- Type ([7d8ccde](https://github.com/antdv-pro/antdv-pro/commit/7d8ccde))
- 将新消息通知中的三个切换按钮的checked状态单独定义 ([c7a44b7](https://github.com/antdv-pro/antdv-pro/commit/c7a44b7))
- Footer tool bar support dark mode ([aae8cb3](https://github.com/antdv-pro/antdv-pro/commit/aae8cb3))
- Fix radio-group ([e1a75b1](https://github.com/antdv-pro/antdv-pro/commit/e1a75b1))
- Fix generate route ([b033198](https://github.com/antdv-pro/antdv-pro/commit/b033198))
- Fix generate route ([41b50a1](https://github.com/antdv-pro/antdv-pro/commit/41b50a1))
- Menu hidden header is not full close #71 ([#71](https://github.com/antdv-pro/antdv-pro/issues/71))
- Build error close #75 ([#75](https://github.com/antdv-pro/antdv-pro/issues/75))
- Collapsed trigger ([ff8ae6c](https://github.com/antdv-pro/antdv-pro/commit/ff8ae6c))
- Account-setting avatar size ([069ad16](https://github.com/antdv-pro/antdv-pro/commit/069ad16))
- Base-loading ui ([dfed9a9](https://github.com/antdv-pro/antdv-pro/commit/dfed9a9))
- Modify route name to keep unique and basic form path ([69be62f](https://github.com/antdv-pro/antdv-pro/commit/69be62f))
- Issues#68 ([#68](https://github.com/antdv-pro/antdv-pro/issues/68))
- Add 删除打印和修正伸缩图标大小 ([22e74a8](https://github.com/antdv-pro/antdv-pro/commit/22e74a8))
- Add pagination ([a04e223](https://github.com/antdv-pro/antdv-pro/commit/a04e223))
- Issue#81 ([#81](https://github.com/antdv-pro/antdv-pro/issues/81))
- Mock server error close #86 ([#86](https://github.com/antdv-pro/antdv-pro/issues/86))
- Fix win and macos server env ([91bc884](https://github.com/antdv-pro/antdv-pro/commit/91bc884))
- Dark mode ([1e31431](https://github.com/antdv-pro/antdv-pro/commit/1e31431))
- Build error ([65bda0e](https://github.com/antdv-pro/antdv-pro/commit/65bda0e))
- Build error ([3c5bc4b](https://github.com/antdv-pro/antdv-pro/commit/3c5bc4b))
- Locale storage optimize ([a49d090](https://github.com/antdv-pro/antdv-pro/commit/a49d090))
- Chart support destroy ([65bdfc7](https://github.com/antdv-pro/antdv-pro/commit/65bdfc7))
- Change i18n ([8c3f517](https://github.com/antdv-pro/antdv-pro/commit/8c3f517))
- Build error ([50b5773](https://github.com/antdv-pro/antdv-pro/commit/50b5773))
- Chart auto fit ([f03bb48](https://github.com/antdv-pro/antdv-pro/commit/f03bb48))
- Login form logo container layout ([a87df0d](https://github.com/antdv-pro/antdv-pro/commit/a87df0d))
- Set autocomplete to off ([8ae0024](https://github.com/antdv-pro/antdv-pro/commit/8ae0024))
- Switch menu stuck & stopped ([8173341](https://github.com/antdv-pro/antdv-pro/commit/8173341))
- Add router key ([91256e3](https://github.com/antdv-pro/antdv-pro/commit/91256e3))
- Change route view ([411a4b5](https://github.com/antdv-pro/antdv-pro/commit/411a4b5))
- Change ([e2d046e](https://github.com/antdv-pro/antdv-pro/commit/e2d046e))

### 💅 Refactors

- Change files name ([16f7940](https://github.com/antdv-pro/antdv-pro/commit/16f7940))

### 🏡 Chore

- Add wx group jpg ([fc6481c](https://github.com/antdv-pro/antdv-pro/commit/fc6481c))
- Update wx group ([2b32fa5](https://github.com/antdv-pro/antdv-pro/commit/2b32fa5))
- Update dependencies for Nitro as devDependencies ([8480b27](https://github.com/antdv-pro/antdv-pro/commit/8480b27))
- Update dependencies for Nitro as devDependencies ([6fbafac](https://github.com/antdv-pro/antdv-pro/commit/6fbafac))
- Remove docs script ([18cf8f5](https://github.com/antdv-pro/antdv-pro/commit/18cf8f5))
- Add deps ([a014040](https://github.com/antdv-pro/antdv-pro/commit/a014040))
- Add commit check ([ad43dc1](https://github.com/antdv-pro/antdv-pro/commit/ad43dc1))
- Add .gitattributes ([cc821f4](https://github.com/antdv-pro/antdv-pro/commit/cc821f4))
- 删减路径多余空格 ([1617c99](https://github.com/antdv-pro/antdv-pro/commit/1617c99))
- Change ci ([e4638d8](https://github.com/antdv-pro/antdv-pro/commit/e4638d8))
- Add start date ([3de8ae4](https://github.com/antdv-pro/antdv-pro/commit/3de8ae4))
- Types ([fd2f05f](https://github.com/antdv-pro/antdv-pro/commit/fd2f05f))
- Chore ([6838cab](https://github.com/antdv-pro/antdv-pro/commit/6838cab))
- Change ([d26935d](https://github.com/antdv-pro/antdv-pro/commit/d26935d))
- Add current route ([1322f42](https://github.com/antdv-pro/antdv-pro/commit/1322f42))
- Change components ([29d86c2](https://github.com/antdv-pro/antdv-pro/commit/29d86c2))
- Change name ([85cabdb](https://github.com/antdv-pro/antdv-pro/commit/85cabdb))
- **composables/loading:** Loading param note ([e81849a](https://github.com/antdv-pro/antdv-pro/commit/e81849a))
- Add gitee ([371e5ea](https://github.com/antdv-pro/antdv-pro/commit/371e5ea))
- BubbleCanvas ts type ([3fdd93b](https://github.com/antdv-pro/antdv-pro/commit/3fdd93b))
- Change ([49a95a4](https://github.com/antdv-pro/antdv-pro/commit/49a95a4))
- Change ([70a8034](https://github.com/antdv-pro/antdv-pro/commit/70a8034))
- Ignore component.d.ts ([4bbd074](https://github.com/antdv-pro/antdv-pro/commit/4bbd074))
- Ignore component.d.ts ([2ecae5b](https://github.com/antdv-pro/antdv-pro/commit/2ecae5b))
- Delete excess ([aae0a7f](https://github.com/antdv-pro/antdv-pro/commit/aae0a7f))
- Del basic table demo ([211e0a4](https://github.com/antdv-pro/antdv-pro/commit/211e0a4))
- Add account routerlink ([403d3a5](https://github.com/antdv-pro/antdv-pro/commit/403d3a5))
- Add space to advance form ([80faa7e](https://github.com/antdv-pro/antdv-pro/commit/80faa7e))
- Change format ([4af5f56](https://github.com/antdv-pro/antdv-pro/commit/4af5f56))
- Change ([ac15c2a](https://github.com/antdv-pro/antdv-pro/commit/ac15c2a))
- Font optimization ([18a31b7](https://github.com/antdv-pro/antdv-pro/commit/18a31b7))
- Change version ([692bd7c](https://github.com/antdv-pro/antdv-pro/commit/692bd7c))
- Change version ([d45e443](https://github.com/antdv-pro/antdv-pro/commit/d45e443))
- Change version ([bdafa15](https://github.com/antdv-pro/antdv-pro/commit/bdafa15))
- Change version ([199bf5d](https://github.com/antdv-pro/antdv-pro/commit/199bf5d))
- Change version ([6eee4de](https://github.com/antdv-pro/antdv-pro/commit/6eee4de))
- Change version ([c696770](https://github.com/antdv-pro/antdv-pro/commit/c696770))
- Change pnpm-lock version ([caedc74](https://github.com/antdv-pro/antdv-pro/commit/caedc74))
- Change ([747b6c2](https://github.com/antdv-pro/antdv-pro/commit/747b6c2))
- 修改侧边菜单超出显示样式 ([368ffe0](https://github.com/antdv-pro/antdv-pro/commit/368ffe0))
- Fix console error ([a08f428](https://github.com/antdv-pro/antdv-pro/commit/a08f428))
- Use auto import ([4be2127](https://github.com/antdv-pro/antdv-pro/commit/4be2127))

### 🎨 Styles

- Fix card-list style ([6b68f8d](https://github.com/antdv-pro/antdv-pro/commit/6b68f8d))
- Login change ([4e74769](https://github.com/antdv-pro/antdv-pro/commit/4e74769))
- Fix style ([8d8a96e](https://github.com/antdv-pro/antdv-pro/commit/8d8a96e))
- Fix style ([05d072e](https://github.com/antdv-pro/antdv-pro/commit/05d072e))

### 🤖 CI

- Add lint ([a6cbed7](https://github.com/antdv-pro/antdv-pro/commit/a6cbed7))

### ❤️ Contributors

- Liosummer <<EMAIL>>
- Windlil ([@windlil](http://github.com/windlil))
- Aibayanyu20 <<EMAIL>>
- Aibayanyu <<EMAIL>>
- Konv Suu <<EMAIL>>
- AutismSuperman <<EMAIL>>
- 杜天宇 <<EMAIL>>
- AShu-guo <<EMAIL>>
- Sun1090 ([@Sun1090](http://github.com/Sun1090))
- Undefined ([@undefined-moe](http://github.com/undefined-moe))
- LC1 <<EMAIL>>
- Kirk Lin ([@kirklin](http://github.com/kirklin))
- Yizhankui <<EMAIL>>
- 张浩杰 ([@HavocZhang](http://github.com/HavocZhang))
- 一个小瘪三 <<EMAIL>>
- Qi Yuhang <<EMAIL>>
- Qyh <<EMAIL>>
- Zev Zhu 
- Adekang <<EMAIL>>

## v0.0.19

[compare changes](https://undefined/undefined/compare/v0.0.18...v0.0.19)

### 🚀 Enhancements

- Add copy setting config (7677dbf)
- Add LICENSE (97dadb0)
- Support does not depend on name to keep alive (82394bb)
- Add github and doc link (d80f78a)

### 🩹 Fixes

- Eslint warning close #13 (#13)

### 🏡 Chore

- Change version (215b0a0)
- Change version (37b121c)
- Fix conflict (5b6463b)
- Change eslint ignore (38cad2c)

### ❤️  Contributors

- Aibayanyu20 <<EMAIL>>
- Aibayanyu <<EMAIL>>

## v0.0.18

[compare changes](https://undefined/undefined/compare/v0.0.17...v0.0.18)

### 🚀 Enhancements

- Global config (e314b34)
- Support customDev mode (11c6c06)
- Add dynamic animation list close #10 (#10)
- Add Dockerfile (0619785)

### 🩹 Fixes

- Loading error (94efff8)
- Delete modal typo (0c3f7c3)
- Header content overflow issue (e7dd087)
- Change dockerfile (4ba22e5)
- Change api (059549e)

### 🏡 Chore

- Change version (46b422b)
- Change version (af2549e)
- Change readme (4d9221c)

### ❤️  Contributors

- Aibayanyu20 <<EMAIL>>
- Kai <<EMAIL>>

## v0.0.17

[compare changes](https://undefined/undefined/compare/v0.0.16...v0.0.17)

### 🚀 Enhancements

- Add useMessage && useModal && useNotifaction replace message && modal && notifaction (d0c031a)
- Replace request config (4be8824)

### 🩹 Fixes

- Typo (14c93ef)
- Typo (ca14d96)

### ❤️  Contributors

- Aibayanyu20 <<EMAIL>>

## v0.0.16

[compare changes](https://undefined/undefined/compare/v0.0.15...v0.0.16)

### 🚀 Enhancements

- Update version (2d7162d)

### 🩹 Fixes

- Remove tab more (4b4b092)
- Style warning (f6a863c)
- Fix split error (ad77483)

### 🏡 Chore

- Update version (e231627)
- Change version (4223747)

### ❤️  Contributors

- Aibayanyu <<EMAIL>>
- Aibayanyu20 <<EMAIL>>

## v0.0.15

[compare changes](https://undefined/undefined/compare/v0.0.14...v0.0.15)


### 🚀 Enhancements

  - Add locale (7adbce1)
  - Menu support i18n (74baf00)
  - Title support title (aea9fd9)

### 🩹 Fixes

  - Fix split menu change error (da34522)
  - Menu title error (a13462e)
  - Menu title error (5a70263)

### 🏡 Chore

  - Change version (f327491)

### ❤️  Contributors

- Aibayanyu20 <<EMAIL>>

## v0.0.14

[compare changes](https://undefined/undefined/compare/v0.0.13...v0.0.14)


### 🚀 Enhancements

  - Support split menu mode (86a9273)

### 🩹 Fixes

  - Context menu refresh current (2d8dca4)
  - Optimize the animation effect (f0ed9a3)
  - HeaderHeight is not take effect (73ca87b)
  - Fix css error (6fe1276)

### ❤️  Contributors

- Aibayanyu20 <<EMAIL>>
- Aibayanyu <<EMAIL>>

## v0.0.13

[compare changes](https://undefined/undefined/compare/v0.0.12...v0.0.13)


### 🚀 Enhancements

  - Support dynamic routes (2164119)
  - Support dynamic load way in env (5dd538d)

### 🏡 Chore

  - Add readme (5d048e3)

### ❤️  Contributors

- Aibayanyu <<EMAIL>>

## v0.0.12

[compare changes](https://undefined/undefined/compare/v0.0.11...v0.0.12)


### 🚀 Enhancements

  - Add access mode (e89acfe)

### 🩹 Fixes

  - Loading experience optimization (136aa7b)
  - Change loading in router guard (8479d60)
  - Typo (29430fa)
  - Typo (6358062)
  - Loading delay (c842062)
  - Query dom (46cfb14)
  - Logout cache (1e18c85)

### 🏡 Chore

  - Bump version (b20d4b1)
  - Change version (afe7e7f)
  - Fix auto import components ts types (a68d41c)
  - Change plugin version (32edfae)

### ❤️  Contributors

- Aibayanyu <<EMAIL>>
- Aibayanyu20 <<EMAIL>>
- Kai <<EMAIL>>

## v0.0.11

[compare changes](https://undefined/undefined/compare/v0.0.10...v0.0.11)


### 🚀 Enhancements

  - Add request control token (43bc593)
  - Add request control token (f71c2e4)
  - Support accordion mode (6d9c8df)

### 🩹 Fixes

  - Typo (96f8220)
  - Store state error (aa6c605)

### 🏡 Chore

  - Migrate package version (6486f57)
  - Change (a1ee980)
  - Update version (6863493)
  - Update version (69e8b1f)
  - Change vue-i18n (7923e2b)
  - Update version (5d74cd6)

### 🤖 CI

  - Add issue  close workflow (5278a99)

### ❤️  Contributors

- Zev Zhu 
- Aibayanyu <<EMAIL>>
- 杜天宇 <<EMAIL>>
- Aibayanyu20 <<EMAIL>>

## v0.0.10

[compare changes](https://undefined/undefined/compare/v0.0.9...v0.0.10)


### 🩹 Fixes

  - Fix close left and right error (eea8854)

### ❤️  Contributors

- Aibayanyu <<EMAIL>>

## v0.0.9

[compare changes](https://undefined/undefined/compare/v0.0.8...v0.0.9)


### 🚀 Enhancements

  - Add context right menu (390022d)
  - Use size small (ac962b0)
  - Add contenxt menu (f720551)

### 🩹 Fixes

  - Height error (ae945b9)

### 🏡 Chore

  - Change author (3faac73)

### 🤖 CI

  - Rename package.json (4bd1cde)

### ❤️  Contributors

- Aibayanyu <<EMAIL>>
- Zhuzhengjian <<EMAIL>>
- Zev Zhu

## v0.0.8

[compare changes](https://undefined/undefined/compare/v0.0.7...v0.0.8)


### 🚀 Enhancements

  - Add pagecontainer (f3ecd48)

### 🩹 Fixes

  - Color primary (661f7b3)

### ❤️  Contributors

- Aibayanyu <<EMAIL>>

## v0.0.7

[compare changes](https://undefined/undefined/compare/v0.0.6...v0.0.7)


### 🩹 Fixes

  - Import error (1ba59d5)
  - Rename (9362480)

### 🏡 Chore

  - Migrate version (1eebf00)

### 🤖 CI

  - Change (5a12b81)

### ❤️  Contributors

- Aibayanyu <<EMAIL>>

## v0.0.6

[compare changes](https://undefined/undefined/compare/v0.0.5...v0.0.6)


### 🏡 Chore

  - Migrate ant-design-vue (0d02def)
  - Migrate version (946b304)

### ❤️  Contributors

- Aibayanyu <<EMAIL>>

## v0.0.5

[compare changes](https://undefined/undefined/compare/v0.0.4...v0.0.5)


### 🚀 Enhancements

  - Add select lang (0bc02ae)

### 🏡 Chore

  - Change (68464cb)

### ❤️  Contributors

- Aibayanyu <<EMAIL>>

## v0.0.4

[compare changes](https://undefined/undefined/compare/v0.0.3...v0.0.4)


### 🏡 Chore

  - Change pkg (65d4d49)
  - Change (45b44fb)
  - Change (7608a96)

### ❤️  Contributors

- Aibayanyu ([@mist-design](http://github.com/mist-design))

## v0.0.3

[compare changes](https://undefined/undefined/compare/v0.0.2...v0.0.3)


### 🚀 Enhancements

  - Add keepalive config (0021ed4)

### 🩹 Fixes

  - Remove transition (6e1b836)
  - Transition error (25bed59)
  - Analysis is not take effect (ef0e5ec)

### 🏡 Chore

  - Change version (03787ae)
  - Change version (5e161ce)

### 🤖 CI

  - Add workflow (438b594)

### ❤️  Contributors

- Aibayanyu ([@mist-design](http://github.com/mist-design))

## vtrue

[compare changes](https://undefined/undefined/compare/v0.0.2...vtrue)


### 🚀 Enhancements

  - Add keepalive config (0021ed4)

### 🩹 Fixes

  - Remove transition (6e1b836)
  - Transition error (25bed59)
  - Analysis is not take effect (ef0e5ec)

### 🤖 CI

  - Add workflow (438b594)

### ❤️  Contributors

- Aibayanyu ([@mist-design](http://github.com/mist-design))

