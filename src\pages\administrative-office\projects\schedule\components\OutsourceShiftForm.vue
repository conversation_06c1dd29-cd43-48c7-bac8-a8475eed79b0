<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
import { DeleteOutlined } from '@ant-design/icons-vue'
// import type { Dayjs } from 'dayjs'
import type { OutsourceShift, UpdateScheduleShiftParams } from '~@/api/company/schedule'
import { formatTimeToHHMMSS } from '~@/utils/apiTimer'
import { Skills } from '~@/utils/constant'

const props = defineProps({
  projectId: {
    type: String,
    required: true,
  },
  selectedOutsourceShift: {
    type: Object as PropType<OutsourceShift>,
    required: true,
  },
})

const emit = defineEmits<{
  (event: 'closeShiftInfo'): void
  (event: 'updateOutsourceShift', outsourceShiftId: string, projectId: string, params: UpdateScheduleShiftParams): void
  (event: 'deleteOutsourceShift', outsourceShiftId: string): void
}>()

const { t } = useI18n()

interface ShiftFormState {
  outsourceId?: string
  scheduledStartTime?: string
  scheduledEndTime?: string
  assignedWorkload?: number
  workingRole: string
}

const shiftFormState = reactive<ShiftFormState>({
  outsourceId: undefined,
  scheduledStartTime: undefined,
  scheduledEndTime: undefined,
  assignedWorkload: undefined,
  workingRole: '',
})

function closeShiftInfo() {
  emit('closeShiftInfo')
}

function updateOutsourceShift() {
  if (!props.selectedOutsourceShift?.outSourceShiftId)
    return

  const params: UpdateScheduleShiftParams = {
    startTime: shiftFormState.scheduledStartTime ?? '00:00',
    endTime: shiftFormState.scheduledEndTime ?? '00:00',
    assignedRole: shiftFormState.workingRole,
    assignedWorkload: shiftFormState.assignedWorkload ?? 0,
  }
  emit('updateOutsourceShift', props.selectedOutsourceShift.outSourceShiftId, props.projectId, params)
}

function deleteOutsourceShift() {
  if (!props.selectedOutsourceShift?.outSourceShiftId)
    return
  emit('deleteOutsourceShift', props.selectedOutsourceShift.outSourceShiftId)
}

watch(() => props.selectedOutsourceShift, () => {
  if (props.selectedOutsourceShift) {
    shiftFormState.outsourceId = props.selectedOutsourceShift.outSourceShiftId
    shiftFormState.scheduledStartTime = formatTimeToHHMMSS(props.selectedOutsourceShift.scheduledStartTime)
    shiftFormState.scheduledEndTime = formatTimeToHHMMSS(props.selectedOutsourceShift.scheduledEndTime)
    shiftFormState.workingRole = props.selectedOutsourceShift.workingRole
    shiftFormState.assignedWorkload = props.selectedOutsourceShift.assignedWorkload
  }
  else {
    shiftFormState.outsourceId = undefined
    shiftFormState.scheduledStartTime = undefined
    shiftFormState.scheduledEndTime = undefined
    shiftFormState.workingRole = ''
    shiftFormState.assignedWorkload = 0
  }
}, { immediate: true })

// const handleSelectTime = (time: Dayjs, type: 'scheduledStartTime' | 'scheduledEndTime') => {
//   shiftFormState[type] = time.format('HH:mm')
// }

onMounted(() => {
})
</script>

<template>
  <div class="flex justify-end items-center cursor-pointer" @click="closeShiftInfo">
    <CarbonClose />
  </div>
  <div class="mt-12">
    <div class="border-b-1 border-t-0 border-l-0 border-r-0 border-gray-300 border-solid mb-4">
      <a-typography-title :level="5">
        {{ t('title.outsourceShiftInfo') }}
      </a-typography-title>
    </div>
    <a-form
      layout="vertical"
      name="shiftFormState"
    >
      <div class="flex flex-col border-b-1 border-t-0 border-l-0 border-r-0 border-gray-300 border-solid pt-4 mb-4">
        <a-form-item :label="t('startTime')" name="scheduledStartTime'">
          <!-- <a-time-picker
            v-model:value="shiftFormState.scheduledStartTime"
            format="HH:mm"
            value-format="HH:mm"
            class="w-full"
            @select="(time: Dayjs) => handleSelectTime(time, 'scheduledStartTime')"
          /> -->
          <TimePicker 
            v-model:value="shiftFormState.scheduledStartTime"
            value-type="string"
            value-format="HH:mm"
          />
        </a-form-item>
        <a-form-item :label="t('endTime')" name="scheduledEndTime'">
          <!-- <a-time-picker
            v-model:value="shiftFormState.scheduledEndTime"
            format="HH:mm"
            value-format="HH:mm"
            class="w-full"
            @select="(time: Dayjs) => handleSelectTime(time, 'scheduledEndTime')"
          /> -->
          <TimePicker 
            v-model:value="shiftFormState.scheduledEndTime"
            value-type="string"
            value-format="HH:mm"
          />
        </a-form-item>
        <a-form-item :label="t('workload')" name="assignedWorkload'">
          <a-input-number v-model:value="shiftFormState.assignedWorkload" class="w-full" />
        </a-form-item>
      </div>
      <div class="flex gap-x-12 mb-4">
        <a-radio-group v-model:value="shiftFormState.workingRole" name="roleRadioGroup" class="flex">
          <div class="flex flex-col gap-y-2">
            <a-radio :value="Skills.SUPERVISOR">
              {{ t('supervisor') }}
            </a-radio>
            <a-radio :value="Skills.FOREMAN">
              {{ t('foreman') }}
            </a-radio>
            <a-radio :value="Skills.WORKER">
              {{ t('worker') }}
            </a-radio>
          </div>
          <div class="flex flex-col gap-y-2">
            <a-radio :value="Skills.HEAVY_MACHINERY_OPERATOR">
              {{ t('heavyMachineryOperator') }}
            </a-radio>
            <a-radio :value="Skills.FORMWORK_WORKER">
              {{ t('formworkWorker') }}
            </a-radio>
            <a-radio :value="Skills.REBAR_WORKER">
              {{ t('rebarWorker') }}
            </a-radio>
          </div>
        </a-radio-group>
      </div>
      <a-form-item>
        <div class="flex justify-between gap-x-4">
          <a-button danger type="primary" class="flex items-center justify-center" @click="deleteOutsourceShift">
            <template #icon>
              <DeleteOutlined />
            </template>
            {{ t('button.delete') }}
          </a-button>
          <a-button type="primary" @click="updateOutsourceShift">
            {{ t('button.update') }}
          </a-button>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>
