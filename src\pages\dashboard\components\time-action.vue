<!-- eslint-disable curly -->
<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
import { ref } from 'vue'
import { EditFilled } from '@ant-design/icons-vue'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import type { AttendanceUpdateParams, BreakTimeItem } from '~@/api/attendance'
import logger from '~@/utils/logger'

const props = defineProps<{
  valueOne: string
  valueTwo: string
  employeeShiftId?: string
  type?: string
  breakItem?: BreakTimeItem
  breakList?: BreakTimeItem[]
}>()
const emit = defineEmits<{
  (event: 'onUpdateAttendance', employeeShiftId: string, params: AttendanceUpdateParams): void
}>()

const isDisabledTimePicker = ref(true)
const _valueOne = ref(dayjs(props.valueOne, 'HH:mm'))
const _valueTwo = ref(dayjs(props.valueTwo, 'HH:mm'))

// const initData = () => {
//   if (!props.breakItem)
//     return
//   _breakList.value = [
//     {
//       breakIn: '00:00:00',
//       breakOut: '00:00:00',
//     },
//     {
//       breakIn: '00:00:00',
//       breakOut: '00:00:00',
//     },
//     {
//       breakIn: '00:00:00',
//       breakOut: '00:00:00',
//     },
//   ]
//   props.breakList.forEach((item: BreakTimeItem, index) => {
//     _breakList.value[index] = item
//   })

//   logger.log('_breakList: ', _breakList)
// }
const onSave = () => {
  logger.log('onSave')
  if (!props.employeeShiftId || !props.type)
    return
  const timeValueOne = _valueOne.value.format('HH:mm:ss')
  const timeValueTwo = _valueTwo.value.format('HH:mm:ss')
  const params: AttendanceUpdateParams = {}
  if (props.type === 'checkInOut') {
    params.checkInTime = timeValueOne
    params.checkOutTime = timeValueTwo
  }
  else {
    if (!props.breakItem || !props.breakList)
      return
    const _breakList: BreakTimeItem[] = props.breakList.map((item: BreakTimeItem) => ({
      breakInTime: item.breakInTime,
      breakOutTime: item.breakOutTime,
    }))
    switch (props.type) {
      case 'breakInOut_1':
        _breakList[0] = {
          breakInTime: timeValueOne,
          breakOutTime: timeValueTwo,
        }
        break
      case 'breakInOut_2':
        _breakList[1] = {
          breakInTime: timeValueOne,
          breakOutTime: timeValueTwo,
        }
        break
      case 'breakInOut_3':
        _breakList[2] = {
          breakInTime: timeValueOne,
          breakOutTime: timeValueTwo,
        }
        break
      default:
        break
    }
    params.breakList = _breakList
  }
  // if (!isWorkingTimeValid(params.breakList ?? [], params.checkInTime || '00:00:00', params.checkOutTime || '00:00:00'))
  //   return
  emit('onUpdateAttendance', props.employeeShiftId, params)
  logger.log('params: ', params)
  isDisabledTimePicker.value = true
}

const handleSelect = (time: Dayjs, type: string) => {
  if (type === 'TimeOne') {
    if (time.format('mm') !== _valueOne.value.format('mm')) {
      _valueOne.value = time
      onSave()
    }
  }
  else if (type === 'TimeTwo') {
    if (time.format('mm') !== _valueTwo.value.format('mm')) {
      _valueTwo.value = time
      onSave()
    }
  }
}

const onEdit = () => {
  isDisabledTimePicker.value = false
}

onMounted (() => {
  // initData()
})
</script>

<template>
  <a-avatar v-if="type === 'breakInOut_1'" src="/icon/break_one.svg" :size="24" class="hidden xl:hidden 2xl:block mr-1 ml-1" />
  <a-avatar v-if="type === 'breakInOut_2'" src="/icon/break_two.svg" :size="20.5" class="hidden xl:hidden 2xl:block mr-1.4 ml-1.7" />
  <a-avatar v-if="type === 'breakInOut_3'" src="/icon/break_three.svg" :size="20.5" class="hidden xl:hidden 2xl:block mr-1.4 ml-1.7" />
  <a-time-picker
    v-model:value="_valueOne"
    class="w-[3rem] p-0 dashboard-time-picker"
    :bordered="false"
    :disabled="isDisabledTimePicker"
    :clear-icon="true"
    show-time
    format="HH:mm"
    size="small"
    placeholder="00:00"
    @select="handleSelect($event, 'TimeOne')"
    @change="onSave"
    @ok="onSave"
  >
    <template #suffixIcon />
  </a-time-picker>
  <span class="text-[#1570EF]">~</span>
  <a-time-picker
    v-model:value="_valueTwo"
    class="w-[3.125rem] p-0 ml-1 dashboard-time-picker"
    :bordered="false"
    :disabled="isDisabledTimePicker"
    :clear-icon="true"
    show-time
    format="HH:mm"
    size="small"
    placeholder="00:00"
    @select="handleSelect($event, 'TimeTwo')"
    @ok="onSave"
    @change="onSave"
  >
    <template #suffixIcon />
  </a-time-picker>
  <span
    v-if="isDisabledTimePicker && (breakItem || type === 'checkInOut')"
    class="text-[#B7B9B8] cursor-pointer hover:text-[#1570EF]"
    @click="onEdit"
  >
    <EditFilled style="font-size: 1rem;" />
  </span>
  <span
    v-else
    class="text-[#B7B9B8] cursor-pointer hover:text-[#1570EF] w-[1rem]"
    @click="onEdit"
  />
</template>

<style scoped>

</style>
