<script setup lang="ts">
import { DownOutlined, FilterFilled } from '@ant-design/icons-vue'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  buttonText: {
    type: String,
    default: 'filter',
  },
})

const emit = defineEmits<Emits>()

const { t } = useI18n()

interface Emits {
  (e: 'update:isOpen', value: boolean): void
  (e: 'onClose'): void
  (e: 'onApply', values: any): void
}

function handleClose() {
  emit('update:isOpen', false)
  emit('onClose')
}

function handleApply(values: any) {
  emit('onApply', values)
  emit('update:isOpen', false)
}
</script>

<template>
  <a-popover
    :open="isOpen"
    trigger="click"
    placement="bottomRight"
    overlay-class-name="filter-popover"
    @update:open="(val) => emit('update:isOpen', val)"
  >
    <template #content>
      <div class="flex justify-between items-center h-[36px] px-[12px]">
        <a-space :size="4">
          <FilterFilled class="text-primary text-sm" />
          <div class="text-[12px]">
            {{ t('filter') }}
          </div>
        </a-space>
        <div class="cursor-pointer" @click="handleClose">
          <CarbonClose />
        </div>
      </div>
      <a-divider :style="{ margin: 0 }" />
      <div class="p-3">
        <slot name="filter-content" />
      </div>
      <div class="flex justify-end p-3">
        <a-button type="primary" @click="handleApply">
          {{ t('button.apply') }}
        </a-button>
      </div>
    </template>
    <a-button
      class="filter-button flex items-center gap-x-2 h-[40px] px-4"
      :class="{ 'filter-button-active': props.isOpen }"
    >
      <FilterFilled class="filter-icon" />
      <span class="font-medium">{{ t(props.buttonText) }}</span>
      <DownOutlined
        :class="{ 'rotate-180': props.isOpen }"
        class="transition-transform duration-300"
      />
    </a-button>
  </a-popover>
</template>

<style scoped>
.filter-button {
  background: linear-gradient(145deg, #3B82F6 0%, #1D4ED8 100%);
  color: white;
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.25);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.filter-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.filter-button:hover {
  background: linear-gradient(145deg, #2563EB 0%, #1E40AF 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(37, 99, 235, 0.3);
  color: white;
}

.filter-button:hover::before {
  opacity: 1;
}

.filter-button-active {
  background: linear-gradient(145deg, #1E40AF 0%, #1E3A8A 100%);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(1px);
}

.filter-icon {
  font-size: 18px;
  color: #38BDF8;
  filter: drop-shadow(0 0 3px rgba(56, 189, 248, 0.5));
  transition: all 0.3s ease;
}

.filter-button:hover .filter-icon {
  color: #7DD3FC;
  filter: drop-shadow(0 0 4px rgba(125, 211, 252, 0.6));
}

:deep(.filter-popover) {
  min-width: 300px;
}

:deep(.ant-popover-arrow) {
  display: none;
}

:deep(.ant-popover-inner) {
  border-radius: 8px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.1);
  overflow: hidden;
}

:deep(.ant-popover-inner-content) {
  padding: 0;
}

:deep(.ant-form-item-label > label) {
  color: #1E40AF;
  font-weight: 500;
}

:deep(.ant-btn-primary) {
  background: #2563EB;
  border-color: #2563EB;
}

:deep(.ant-btn-primary:hover) {
  background: #1D4ED8;
  border-color: #1D4ED8;
}
</style>
