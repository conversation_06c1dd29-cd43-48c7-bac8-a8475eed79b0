<script setup lang="ts">
import EstimateCostCard from './estimate-cost-card.vue'
import TableDate from './components/table-data.vue'
import type { ConstructionCostItem } from '~@/api/construction-cost'

defineProps({
  constructionCosts: {
    type: Array as () => ConstructionCostItem[],
    required: true,
  },
})

const { t } = useI18n()
</script>

<template>
  <div class="p-4">
    <div class="flex justify-between items-center mb-4">
      <h1 class="text-xl font-bold">
        {{ t('siteProgressSummary') }}
      </h1>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Progress Rate Card -->
      <div
        v-for="constructionCost in constructionCosts" :key="constructionCost.constructionId"
        class="bg-white shadow-lg rounded-xl p-6"
      >
        <TableDate
          :construction-cost="constructionCost"
        />
      </div>

      <!-- Budget Comparison Card -->
      <div v-for="constructionCost in constructionCosts" :key="constructionCost.constructionId">
        <EstimateCostCard
          :construction-cost="constructionCost"
        />
      </div>
    </div>
  </div>
</template>
