<script lang="ts">
export default {
  name: '<PERSON><PERSON><PERSON><PERSON>',
}
</script>

<template>
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 10C10.4945 10 10.9778 9.85338 11.3889 9.57867C11.8001 9.30397 12.1205 8.91352 12.3097 8.45671C12.4989 7.99989 12.5484 7.49723 12.452 7.01227C12.3555 6.52732 12.1174 6.08186 11.7678 5.73223C11.4181 5.3826 10.9727 5.1445 10.4877 5.04804C10.0028 4.95157 9.50012 5.00108 9.04331 5.1903C8.5865 5.37952 8.19605 5.69995 7.92135 6.11107C7.64665 6.5222 7.50002 7.00555 7.50002 7.5C7.50002 8.16304 7.76341 8.79892 8.23225 9.26777C8.70109 9.73661 9.33698 10 10 10ZM10 6.25C10.2472 6.25 10.4889 6.32331 10.6945 6.46066C10.9 6.59801 11.0603 6.79324 11.1549 7.02164C11.2495 7.25005 11.2742 7.50139 11.226 7.74386C11.1778 7.98634 11.0587 8.20907 10.8839 8.38388C10.7091 8.5587 10.4864 8.67775 10.2439 8.72598C10.0014 8.77421 9.75007 8.74946 9.52166 8.65485C9.29326 8.56024 9.09803 8.40002 8.96068 8.19446C8.82333 7.9889 8.75002 7.74723 8.75002 7.5C8.75002 7.16848 8.88172 6.85054 9.11614 6.61612C9.35056 6.38169 9.6685 6.25 10 6.25ZM15.4125 8.22312L14.9631 8.07375C15.044 7.3737 14.976 6.66447 14.7635 5.99255C14.551 5.32064 14.199 4.70123 13.7303 4.17493C13.2617 3.64864 12.6871 3.22735 12.0442 2.9387C11.4013 2.65004 10.7047 2.50054 10 2.5C9.28612 2.50028 8.58057 2.65356 7.93091 2.94952C7.28125 3.24548 6.70257 3.67725 6.23385 4.21573C5.76514 4.75422 5.41728 5.3869 5.21372 6.07117C5.01016 6.75543 4.95562 7.47538 5.05378 8.1825C4.45568 8.29402 3.90261 8.5762 3.46128 8.995C3.15577 9.28546 2.91289 9.63535 2.74758 10.0231C2.58228 10.4109 2.49803 10.8284 2.50003 11.25V13.7969C2.50135 14.4727 2.72115 15.13 3.12665 15.6707C3.53215 16.2113 4.1016 16.6064 4.75003 16.7969L6.54378 17.3594C6.84506 17.4531 7.15886 17.5006 7.4744 17.5C7.76064 17.4996 8.04552 17.4605 8.32127 17.3837L11.9338 16.315C12.2556 16.2272 12.5951 16.2272 12.9169 16.315L14.4088 16.815C14.7764 16.9045 15.1596 16.9094 15.5295 16.8292C15.8993 16.7491 16.2461 16.586 16.5438 16.3523C16.8414 16.1186 17.0822 15.8204 17.2478 15.4802C17.4134 15.1399 17.4997 14.7665 17.5 14.3881V11.17C17.4986 10.5234 17.2972 9.89302 16.9235 9.36534C16.5498 8.83765 16.022 8.43847 15.4125 8.2225V8.22312ZM7.34877 4.85125C7.69643 4.50218 8.1096 4.2252 8.56457 4.03621C9.01954 3.84721 9.50736 3.74992 10 3.74992C10.4927 3.74992 10.9805 3.84721 11.4355 4.03621C11.8904 4.2252 12.3036 4.50218 12.6513 4.85125C13.3526 5.55696 13.7468 6.51113 13.7481 7.50608C13.7494 8.50103 13.3577 9.45622 12.6581 10.1637L10.4375 12.3231C10.3216 12.4368 10.1658 12.5004 10.0035 12.5004C9.84114 12.5004 9.68529 12.4368 9.56939 12.3231L7.34877 10.1731C6.64594 9.46587 6.25147 8.50927 6.25147 7.51219C6.25147 6.5151 6.64594 5.55851 7.34877 4.85125ZM16.25 14.3881C16.2504 14.5774 16.2076 14.7643 16.1248 14.9346C16.0421 15.1048 15.9216 15.254 15.7725 15.3706C15.6316 15.4839 15.4676 15.5647 15.2919 15.6074C15.1163 15.6501 14.9334 15.6535 14.7563 15.6175L13.2906 15.125C12.7363 14.9657 12.1487 14.9623 11.5925 15.115L7.97752 16.1825C7.6302 16.2783 7.26262 16.272 6.91878 16.1644L5.11003 15.6019C4.71856 15.4887 4.37438 15.2515 4.12916 14.9261C3.88393 14.6006 3.7509 14.2044 3.75003 13.7969V11.25C3.74863 10.9974 3.79891 10.7471 3.8978 10.5146C3.99668 10.2821 4.14207 10.0723 4.32503 9.89812C4.61144 9.62516 4.97629 9.44897 5.36815 9.39437C5.62009 10.0189 5.99526 10.5862 6.47128 11.0625L8.70064 13.2206C9.04909 13.5618 9.51735 13.7529 10.005 13.7529C10.4927 13.7529 10.9609 13.5618 11.3094 13.2206L13.5363 11.0562C14.0349 10.5556 14.4211 9.95461 14.6694 9.29312L15.005 9.40437C15.369 9.5348 15.6839 9.77434 15.9067 10.0903C16.1295 10.4063 16.2494 10.7834 16.25 11.17V14.3881Z" fill="#74797A" />
  </svg>
</template>
