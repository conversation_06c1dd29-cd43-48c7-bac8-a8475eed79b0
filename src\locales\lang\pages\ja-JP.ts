export default {
  // result page
  'result.success.title': '提出成功',
  'result.success.description':
        '提出結果ページは、一連の操作タスクの結果をフィードバックするために使用されます。簡単な操作の場合は、Message グローバルプロンプトフィードバックを使用してください。このテキストエリアは簡単な補足説明を表示できます。 "文書"の表示に類似した要件がある場合、以下のグレーのエリアではより複雑なコンテンツを表示できます。',
  'result.success.operate-title': '工事名',
  'result.success.operate-id': '工事ID',
  'result.success.principal': '責任者',
  'result.success.operate-time': '有効な時間',
  'result.success.step1-title': 'プロジェクトの作成',
  'result.success.step1-operator': '<PERSON>',
  'result.success.step2-title': '部門初審査',
  'result.success.step2-operator': 'Zev Zhu',
  'result.success.step2-extra': '促進',
  'result.success.step3-title': '財務審査',
  'result.success.step4-title': '終了',
  'result.success.btn-return': '一覧に戻る',
  'result.success.btn-project': 'プロジェクトを表示',
  'result.success.btn-print': '印刷',
  'result.fail.error.title': '提出失敗',
  'result.fail.error.description':
        '再提出する前に以下の情報を確認して修正してください。',
  'result.fail.error.hint-title': '提出したコンテンツには次のエラーがあります：',
  'result.fail.error.hint-text1': 'アカウントが凍結されています',
  'result.fail.error.hint-btn1': '即時解凍',
  'result.fail.error.hint-text2': 'アカウントはまだ申請の対象ではありません',
  'result.fail.error.hint-btn2': '即時アップグレード',
  'result.fail.error.btn-text': '修正して戻る',

  'profile.basic.orderDetailTitle': '注文の詳細',
  'profile.basic.orderNumber': '注文番号',
  'profile.basic.orderStatus': '注文ステータス',
  'profile.basic.orderStatusValue': '完了',
  'profile.basic.transactionNumber': '取引番号',
  'profile.basic.subOrderNumber': 'サブオーダー番号',
  'profile.basic.customerInfoTitle': '顧客情報',
  'profile.basic.customerName': '顧客名',
  'profile.basic.contactNumber': '連絡先番号',
  'profile.basic.deliveryMethod': '配送方法',
  'profile.basic.deliveryAddress': '配送先住所',
  'profile.basic.remarks': '備考',
  'profile.basic.productInfoTitle': '製品情報',
  'profile.basic.productName': '製品名',
  'profile.basic.unitPrice': '単価',
  'profile.basic.quantity': '数量',
  'profile.basic.subtotal': '小計',
  'profile.basic.paymentInfoTitle': '支払い情報',
  'profile.basic.paymentMethod': '支払い方法',
  'profile.basic.paymentTime': '支払い時刻',
  'profile.basic.paymentAmount': '支払い金額',
  'profile.basic.paymentStatus': '支払いステータス',

  'profile.advanced.creater': '作成者',
  'profile.advanced.create-time': '作成日時',
  'profile.advanced.create-effective-date': '有効な日付',
  'profile.advanced.create-product': '製品',
  'profile.advanced.create-id': '製品文書',
  'profile.advanced.create-info': 'メモ',
  'profile.advanced.create-status': 'ステータス',
  'profile.advanced.create-status-finshed': '完了',
  'profile.advanced.create-price': '価格',
  'profile.advanced.create-do1': 'Opt1',
  'profile.advanced.create-do2': 'Opt1',
  'profile.advanced.create-do3': 'Opt1',
  'profile.advanced.tab1': '詳細',
  'profile.advanced.tab2': 'ルール',
  'profile.advanced.card': '顧客カード',
  'profile.advanced.id': 'IDカード',
  'profile.advanced.group': 'データグループ',
  'profile.advanced.group-data': 'データID',
  'profile.advanced.group-data-update': '更新時間',
  'profile.advanced.call-log': '連絡履歴',
  'profile.advanced.call-spent': '連絡時間',
  'profile.advanced.call-date': '連絡日',
  'profile.advanced.log': 'ログ1',
  'profile.advanced.log1': 'ログ1',
  'profile.advanced.log2': 'ログ3',
  'profile.advanced.log-type': 'タイプ',
  'profile.advanced.log-owner': 'オペレータ',
  'profile.advanced.log-result': '結果',
  'profile.advanced.log-time': '開始時間',
  'profile.advanced.log-info': 'メモ',
  'profile.advanced.remove': '削除',
  'profile.advanced.step-title': '進捗',
  'profile.advanced.step-notice': 'リマインダー',

  'form.basic-form.basic.title': '基本フォーム',
  'form.basic-form.basic.description':
        'フォームページは、ユーザーに情報を収集または確認するために使用され、基本フォームはデータ項目が少ないシナリオで一般的です。',
  'form.basic-form.title.label': 'タイトル',
  'form.basic-form.title.placeholder': '対象に名前を付けてください',
  'form.basic-form.title.required': 'タイトルを入力してください',
  'form.basic-form.date.label': '開始日と終了日',
  'form.basic-form.placeholder.start': '開始日',
  'form.basic-form.placeholder.end': '終了日',
  'form.basic-form.date.required': '開始日と終了日を選択してください',
  'form.basic-form.goal.label': '目標の説明',
  'form.basic-form.goal.placeholder': '業務目標を入力してください',
  'form.basic-form.goal.required': '目標の説明を入力してください',
  'form.basic-form.standard.label': 'メトリクス',
  'form.basic-form.standard.placeholder': 'メトリクスを入力してください',
  'form.basic-form.standard.required': 'メトリクスを入力してください',
  'form.basic-form.client.label': 'クライアント',
  'form.basic-form.label.tooltip': '対象サービスオブジェクト',
  'form.basic-form.client.placeholder':
        'カスタマーサービスを説明してください、内部のお客様は直接{\'@\'}名前/ジョブ番号',
  'form.basic-form.client.required': '担当するお客様を説明してください',
  'form.basic-form.invites.label': '招待批評家',
  'form.basic-form.invites.placeholder':
        '{\'@\'}名前/ジョブ番号を直接指定してください、最大5人まで招待できます',
  'form.basic-form.weight.label': '重さ',
  'form.basic-form.weight.placeholder': '重さを入力してください',
  'form.basic-form.public.label': '対象の公開',
  'form.basic-form.label.help': '顧客と招待者はデフォルトで共有されています',
  'form.basic-form.radio.public': '公共',
  'form.basic-form.radio.partially-public': '部分的に公開',
  'form.basic-form.radio.private': 'プライベート',
  'form.basic-form.publicUsers.placeholder': '公開先',
  'form.basic-form.option.A': '同僚A',
  'form.basic-form.option.B': '同僚B',
  'form.basic-form.option.C': '同僚C',
  'form.basic-form.email.required': 'メールアドレスを入力してください！',
  'form.basic-form.email.wrong-format': 'メールアドレスの形式が正しくありません！',
  'form.basic-form.userName.required': 'ユーザー名を入力してください！',
  'form.basic-form.password.required': 'パスワードを入力してください！',
  'form.basic-form.password.twice': '2回入力したパスワードが一致しません！',
  'form.basic-form.strength.msg':
        '少なくとも6文字を入力し、推測しやすいパスワードは使用しないでください。',
  'form.basic-form.strength.strong': '強さ: 強い',
  'form.basic-form.strength.medium': '強さ: 中程度',
  'form.basic-form.strength.short': '強さ: 短すぎる',
  'form.basic-form.confirm-password.required': 'パスワードを確認してください！',
  'form.basic-form.phone-number.required': '電話番号を入力してください！',
  'form.basic-form.phone-number.wrong-format': '不正な電話番号！',
  'form.basic-form.verification-code.required': '検証コードを入力してください！',
  'form.basic-form.form.get-captcha': 'キャプチャを取得',
  'form.basic-form.captcha.second': '秒',
  'form.basic-form.form.optional': '（オプション）',
  'form.basic-form.form.submit': '送信',
  'form.basic-form.form.save': '保存',
  'form.basic-form.email.placeholder': 'メールアドレス',
  'form.basic-form.password.placeholder': 'パスワード',
  'form.basic-form.confirm-password.placeholder': 'パスワードを確認',
  'form.basic-form.phone-number.placeholder': '電話番号',
  'form.basic-form.verification-code.placeholder': '検証コード',
  // account page
  'account.center.tags': 'タグ',
  'account.center.team': 'チーム',
  'account.center.article': '記事',
  'account.center.application': 'アプリケーション',
  'account.center.project': 'プロジェクト',
  'account.center.posted': '投稿された',
  'account.center.activity-user': 'アクティビティユーザー',
  'account.center.new-user': '新しいユーザー',
  'account.center.updated': '数分前に更新',

  'account.settings.basic-setting': '基本設定',
  'account.settings.security-setting': 'セキュリティ設定',
  'account.settings.account-setting': 'アカウントバインディング',
  'account.settings.message-setting': '新しいメッセージ',
  'account.settings.form-email': '電子メール',
  'account.settings.form-name': 'ニックネーム',
  'account.settings.form-region': '国/地域',
  'account.settings.form-address': '住所',
  'account.settings.form-phoneNumber': '電話番号',
  'account.settings.form-desc': '個人プロフィール',
  'account.settings.form-region-China': '中国',
  'account.settings.form-input-plac': '入力してください',
  'account.settings.form-select-plac': '選んでください',
  'account.settings.form-rule-name': 'ニックネームを入力してください',
  'account.settings.form-rule-phoneNumber': '電話番号を入力してください',
  'account.settings.form-rule-address': '住所を入力してください',
  'account.settings.form-rule-region': '選んでください',
  'account.settings.form-rule-email': 'メールアドレスを入力してください',
  'account.settings.form-rule-desc': 'プロフィールを入力してください',
  'account.settings.basic-avatar': 'アバター',
  'account.settings.basic-avatar.upload': '画像をアップロード',
  'account.settings.form-submit': '送信',

  'account.settings.security.account-password': 'アカウントのパスワード',
  'account.settings.security.account-password-desc': '現在のパスワード強度: 強力',
  'account.settings.security.phone': 'セキュリティ電話',
  'account.settings.security.phone-desc': '添付電話: 131****8888',
  'account.settings.security.email': '予備のメール',
  'account.settings.security.email-desc': 'バインドされたメール: ant**.com',
  'account.settings.security.MFA': 'MFA',
  'account.settings.security.MFA-desc': 'バインドされたMFAデバイスはありません',
  'account.settings.modify': '編集',
  'account.settings.security-problem': 'セキュリティの問題',
  'account.settings.security-problem-desc': '設定済み',
  'account.settings.account.taobao': '淘宝をバインド',
  'account.settings.account.alipay': 'アリペイをバインド',
  'account.settings.account.dingding': 'ディンディングをバインド',
  'account.settings.account.not.bind': '現在バインドされていません',
  'account.settings.account.bind': 'バインド',
  'account.settings.message.title1': 'その他のメッセージ',
  'account.settings.message.title2': 'システムメッセージ',
  'account.settings.message.title3': 'やるべき仕事',
  'account.settings.message.desc1': '他のユーザーからのメッセージは、内部メッセージの形で通知されます',
  'account.settings.message.desc2': '他のシステムからのメッセージは、内部メッセージの形で通知されます',
  'account.settings.message.desc3': '他のタスクからのメッセージは、内部メッセージの形で通知されます',

  // Common
  'table.loginId': 'ログインID',
  'table.checkIn': '作業開始',
  'table.checkInOut': '時間',
  'table.workdayType': '形態',
  'table.checkinGPS': '位置(GPS)',
  'table.checkOut': '作業終了',
  'table.breakIn': '休憩開始',
  'table.breakOut': '休憩終了',
  'table.checkoutGPS': '位置(GPS)',
  'table.fixHours': '固定時間',
  'table.overtime': '残業時間',
  'table.breakTime': '休憩',
  'table.breakInOut': '休憩',
  'table.comment': '備考',
  'table.name': '名前',
  'table.userID': 'ユーザーID',
  'table.email': 'メール',
  'table.birthday': '生年月日',
  'table.phone': '電話番号',
  'table.status': 'ステータス',
  'table.gender': '性別',
  'table.userRule': 'ルール',
  'table.totalHours': '所定作業時間',
  'table.action': '操作',
  'table.note': '備考',
  'table.title.role-management': '権限管理',
  'table.title.role-name': '権限名',
  'table.title.description': '詳細',
  'table.title.list-of-user': 'ユーザー一覧',
  'table.title.action': '操作',
  'table.title.screen': '画面',
  'table.userType': '勤務体制',
  'table.daytimeApproval': '申請状況',
  'updatedBy': '更新者',
  'updatedAt': '更新日時',

  // Dashboard page
  'dashboard.workplace.tableTitle': '時間表示',
  'dashboard.workplace.locationOutside': '作業場選択',
  'dashboard.workplace.locationInside': '社内選択',
  'dashboard.workplace.timeKeepingData.date': '日付',
  'dashboard.workplace.timeKeepingData.title': '勤怠管理表',
  'dashboard.workplace.timeKeepingData.schedule': 'スケジュール',
  'dashboard.workplace.timeKeepingData.weekDay': '平日',
  'dashboard.workplace.timeKeepingData.weekend': '休日',
  'dashboard.workplace.timeKeepingData.approval': '承認終了',
  'dashboard.workplace.timeKeepingData.cancel': '取り消し',
  'dashboard.workplace.timeKeepingData.pending': '承認待ち',
  'locale': 'ja',
  'dashboard.workplace.button.checkIn': '作業開始',
  'dashboard.workplace.button.checkOut': '作業終了',
  'dashboard.workplace.button.startBreak': '休憩開始',
  'dashboard.workplace.button.endBreak': '休憩終了',
  'attendance': '勤怠',

  'dashboard.workplace.project': 'プロジェクト',
  'dashboard.workplace.project-menu': 'メニュー',
  'dashboard.workplace.attendanceDetail': '勤務詳細',
  'dashboard.workplace.location': '作業場名',
  'dashboard.workplace.address': '住所',
  'dashboard.workplace.attendance.textArea': 'ここにメモを書きます',
  'dashboard.workplace.notification': 'お知らせ',
  'dashboard.workplace.notification.recordedWorkingHours': '勤怠時間を記録しました',
  'dashboard.workplace.notification.just': 'たった今',
  'dashboard.workplace.notification.user': 'ユーザー',
  'totalWorkTime': '合計勤務時間',
  'totalOvertime': '合計残業時間',
  'dashboard.totalTime': '勤務時間',
  'dashboard.overtime': '残業時間',
  'dashboard.attendance.title': '今日の勤怠詳細',
  'select.current-project': '選択中のプロジェクト',
  'select.other-project': 'その他のプロジェクト',
  'select.business': '出張',
  'select.remote': 'リモート',
  'workplace': '場所',
  'enterWorkplace': '場所入力',

  'total-worktime-of-day': '合計勤務時間',
  'total-overtime-of-day': '合計残業時間',
  'total-worktime-of-week': '合計勤務時間',
  'total-overtime-of-week': '合計残業時間',
  'worktime': '勤務時間',

  'project-management': 'プロジェクト管理',
  'attendance-detail': '出勤詳細',
  'prime-contractor': '主元請け',
  'person-in-charge': '担当者',
  'expected-date': '予定日',
  'actual-date': '実績日',
  'main-construction': '主要工事',
  'sub-construction': '副工事',
  'overall': '全体',
  'project.sum': '合計',
  'worksite-attendance': '現場出勤',
  'cost': '費用',
  'presigned': '事前承認',
  'check-time': 'チェック時間',
  'break-time': '休憩時間',
  'button.add-break': '休憩追加',
  'note': '備考',
  'total-hours': '合計時間',
  'button.request-approval': '承認申請',
  'button.check-in': '作業開始',
  'notification.checkInTimeRequired': '作業開始時間を入力してください',
  'invalidTime': '無効な時間',
  'dashboard.clock': '時計',
  'dashboard.todayAttendance': '今日の勤怠',
  'dashboard.attendanceTable': '勤怠表',
  'dashboard.tour': 'このページの使い方',
  'tour.dashboard.clock.description': 'このセクションは現在の時刻と勤怠状況を表示します。出勤、休憩、退勤を行うために使用します。',
  'tour.dashboard.todayAttendance.description': 'このセクションは今日の勤怠記録を表示します。出勤、休憩、退勤の時間を確認できます。また、勤怠を更新したり、承認を申請することもできます。',
  'tour.dashboard.totalTime.description': 'このセクションは今日の合計勤務時間と残業時間をまとめて表示します。一日の勤務時間を管理するのに役立ちます。',
  'tour.dashboard.attendanceTable.description': 'この表は選択された週の勤怠記録を表示します。日々の勤怠詳細を確認したり、管理することができます。',
  'tour': 'ページの使い方',
  'requireCheckInTimeBeforeCurrentTime': '出勤時間は現在の時刻より前にする必要があります',
  'requireCheckOutTimeBeforeCurrentTime': '退勤時間は現在の時刻より前にする必要があります',
  'requireCheckOutTimeAfterCheckInTime': '退勤時間は出勤時間より後にする必要があります',

  'productionCost': '生産原価',
  'averageCost': '平均原価',

  // Manager/team Page
  'manager.team.worksite.attendanceRequest': '申請',
  'manager.team.worksite.username': '社員名',
  'manager.team.worksite.workStatus': '勤務状態',

  // Settings Page
  'settings.title': '設定',
  'company.title': '会社情報',
  'structure.title': '組織',
  'position.title': '役職',

  // Position
  'position.code': 'ポジションコード',
  'position.name': 'ポジション名',
  'position.description': '説明',
  'position.edit': '編集',
  'position.add': '追加',
  'position.form.message': 'ポジションコードと名前を入力してください',
  'position.status': 'ステータス',

  // Company Page
  'company.project.projectCode': 'プロジェクトコード',
  'company.project.projectName': 'プロジェクト名',
  'company.project.orgStructureKey': 'キー',
  'company.project.address': '住所',
  'company.position.orgPositionCode': 'コード',
  'company.position.orgPositionName': '現場名',
  'company.position.status': 'ステータス',

  // Organization
  'company.name': '会社名',
  'company.code': '会社コード',
  'company.address': '住所',
  'company.postalCode': '郵便番号',
  'company.legalTaxNumber': '法人税番号',
  'company.description': '説明',
  'company.edit': '編集',
  'company.add': '追加',

  // Structure
  'structureName': '属所名称',
  'structure.code': 'コード',
  'structure.name': '名前',
  'structure.parent': '親',
  'structure.source': 'ソース',
  'structure.description': '説明',
  'structure.roles': '役割',
  'structure.children': '子',
  'structure.isOutSource': '外部',

  // Button
  'button.add': '新規追加',
  'button.confirm': '確認',
  'button.sendLink': 'リンクを送信',
  'EmailOrUsername': '電子メール/ユーザー名',
  'button.edit': '編集',
  'button.delete': '削除',
  'button.update': '変更',
  'button.cancel': 'キャンセル',
  'button.create': '作成',
  'button.ok': 'Ok',
  'button.save': '保存',
  'button.cancelDaily': '取り消し',
  'button.requestDaily': '勤怠連絡',
  'button.sendNote': '送信',
  'button.showMore': '表示',
  'button.showLess': '隠し',
  'button.requestApproval': '日次出勤申請',
  'button.editAttendaceData': 'データ編集',
  'button.createDailyReport': '日報作成',
  'button.requestTimeOfWorking': '作業時間申請',
  'button.viewRequest': '申請一覧',
  'button.reject': '拒否',
  'button.addSchedule': 'スケジュール追加',
  'button.download': 'ダウンロード',
  'button.approve': '承認',
  'button.send': '送信',
  'button.reset-change': 'リセット',
  'button.decentralize': '権限設定',
  'button.sendReport': '報告送付',
  'button.copy': 'コピー',
  'button.paste': '貼り付け',
  'button.resetPass': 'パスワードの再設定',
  'button.view': '表示',
  'button.view-detail': '詳細表示',
  'button.search': '検索',
  'button.reset': '再設定',
  'button.filter': 'フィルター',
  'button.resetFilter': '条件クリア',
  'button.new': '追加',
  'button.show': '表示',
  'button.entries': 'エントリー',
  'button.manager': '担当者',
  'button.description': '説明',
  'button.apply': '適用',
  'button.editHistory': '編集履歴',
  'button.manuallyImport': '手動インポート',
  'button.importFromInvoiceSlip': '伝票からインポート',
  'button.createDefaultBaseLeave': 'デフォルトの休日を作成',
  'title.baseLeaveChange': '休日変更',
  'button.access': 'アクセス',
  'button.login': 'ログイン',
  'button.invite': '招待',
  'button.invoice-list': '伝票一覧',
  'button.filters': 'フィルター',
  'button.createNewAttendance': '新規勤怠作成',

  'form.code': 'コード',
  'form.name': '名前',
  'form.userID': 'ユーザーID',
  'form.email': 'メール',
  'form.birthday': '生年月日',
  'form.phone': '電話番号',
  'form.status': 'ステータス',
  'form.gender': '性別',
  'form.userRule': '権限',
  'form.parent-category': '親カテゴリ',
  'form.unit': '単位',
  'form.category': 'カテゴリ',
  'form.sub-name': 'サブ名',
  'form.approval-status': '承認状態',
  'common.am': '午前',
  'common.pm': '午後',

  // Project page
  'projectInfo': 'プロジェクト情報',
  'title.edit-project': 'プロジェクト編集',
  'title.add-project': 'プロジェクトの追加',
  'initialBudget': '初期予算',
  'actualBudget': '実績予算',
  'managersInfo': '担当者情報',
  'action': '操作',
  'expectedPlan': '予定計画',
  'actualPlan': '実績計画',
  'projectStructure': '構造',
  'workplaceName': '作業場名',
  'expectedDate': '予定日',
  'actualDate': '実績日',
  'mainManager': '担当者',
  'subManager': '副担当者',
  'description': '説明',
  'status.planed': '未着手',
  'status.started': '進行中',
  'status.ended': '竣工',
  'costRange': 'コスト範囲',
  'budgetRange': '予算範囲',
  'isOffice': '事務所',
  'isHeadOffice': '本社',
  'isWorksite': '現場',
  'form.managerUserName': '担当者名',
  'form.subManagerUserName': '副担当者名',
  'projectCode': 'プロジェクトコード',
  'projectType': 'プロジェクトタイプ',
  'form.projectCode': 'プロジェクトコード',
  'form.projectName': 'プロジェクト名',
  'form.orgStructureKey': '組織所属キー',
  'form.StructureName': '所属名',
  'form.address': '住所',
  'form.description': '説明',
  'form.jurisdiction': '管轄機関',
  'form.mainContractor': '主元請け',
  'form.companyRole': '自社の役割',
  'form.mainManager': '担当者',
  'form.endUserID': 'ユーザーID',
  'form.subManagerList': '副担任者',
  'form.startDatePlan': '予定開始日',
  'form.endDatePlan': '予定終了日',
  'form.startDateReal': '実際の開始日',
  'form.endDateReal': '実際の終了日',
  'form.note': 'メモ',
  'form.effective': '有効',
  'form.addressType': '住所タイプ',

  'form.shiftWorkCode': '勤務形態コード',
  'form.shiftWorkName': '勤務形態名',
  'form.checkInTime': '出勤時間',
  'form.checkOutTime': '退勤時間',
  'button.addBreak': '休憩を追加',
  'form.breakStart': '休憩開始時間',
  'form.breakEnd': '休憩終了時間',
  'form.isNextDay': '翌日に移動',
  'form.orderNumber': '順番',
  'form.officeCode': 'オフィスコード',
  'form.officeName': 'オフィス名',
  'form.totalRow': '合計行',

  'validate.mainManager': '社員はシステム内でメインマネージャーとサブマネージャーの両方として存在することはできません。',
  'validate.subManager': '社員はシステム内でメインマネージャーとサブマネージャーの両方として存在することはできません。',

  // input
  'input.placeholder': 'キーワードを入力してください...',
  'work-shift': '勤務形態',
  'detail': '詳細',
  'TotalTitle': '一日の合計',
  'TotalWorkingTime': '出勤時間',
  'TotalWorkingDays': '作業日数',
  'TotalBreakTime': '休憩時間数',
  'TotalWorkDays': '勤務日数',
  'TotalPaidLeaveRemainDays': '有給残日数',
  'TotalUnpaidLeaveRemainDays': '有休消化数',
  'TotalOvertime': '残業時数',
  'TotalDaysOff': '欠勤日数',
  'TotalRemainingPaidLeaveDays': '有給残日数',
  'unpaidLeaveRemain': '有給残日数',
  'Date': '日付',
  'RequestType': '申請種類',
  'Quantity': '数量',
  'LeaveType': '休暇タイプ',
  'Unit': '単位名',
  'UserApprove1': '承認者①',
  'UserApprove2': '承認者②',
  'Note': '備考',
  'RequestName': '申請名',
  'UnitName': '単位名',
  'StatusName': '状態',
  'Hours': '時間',
  'Days': '日付',
  'RequestList': '申請一覧',
  'TypeOfRequest': '申請種類',
  'StartTime': '開始時間',
  'EndTime': '終了時間',
  'Reason': '理由',
  'ReasonForRequest': '申請理由',
  'WorkingEarly': '早出勤務',
  'WorkingLate': '残業勤務',
  'Overtime': '残業',
  'SendRequestToOffice': '提出',

  // Invoice
  'invoice-detail': '請求書詳細',

  // Role management
  'functionName': '機能名前',
  'company.work-shift.detail': '勤務形態詳細',
  'RolesConfig': '権限設定',
  'Screen': '画面',
  'PageUrl': 'ページURL',
  'UserRulesName': '権限名',
  'OrderNumber': '順位番号',
  'ResetYourAccount': 'アカウントを再設定',
  'title.delete': '削除',
  'isWorksiteManager': '作業場管理者',
  'worksiteAuth': '作業場権限',
  'officeAuth': 'オフィス権限',
  'role-management': '権限管理',
  'role-decentralize': '権限設定',
  'title.add-role': '権限追加',
  'title.edit-role': '権限編集',
  'type': '種類',
  'roleName': '権限名',
  'roleParent': '権限親',
  'employees': '社員',
  'users': 'ユーザー',
  'canWrite': '書込',
  'canCreate': '作成',
  'canRead': '読込',
  'canUpdate': '更新',
  'canDelete': '削除',
  'assignPermissFor': '権限割り当て',
  'search': '検索...',
  'structure': '所属',
  'page.profile.basic': '基本プロフィール',
  'menu.company.work-shift.detail': '勤務形態詳細',

  // Schedule management
  'title.edit-schedule': 'スケジュール編集',
  'title.add-schedule': 'スケジュール登録',
  'title.ScheduleManagement': 'スケジュール管理',
  'Manager': '担当者',
  'TotalUser': 'ユーザー総数',
  'expectedEmpNum': '予定社員数',
  'expectedEmpIds': '予定社員',
  'projectName': 'プロジェクト名',
  'title.add-new-shift': 'シフト作成',
  'title.edit-shift': 'シフト編集',
  'workshift': 'シフト',
  'default-workshift': 'デフォルトシフト',
  'startTime': '時間開始',
  'endTime': '時間終了',
  'presignedWorkload': '予定社員数',
  'assignUsers': 'ユーザー',
  'type.day': '日',
  'type.week': '週',
  'type.month': '月',
  'title.copyWorkSchedule': '予定コピー',
  'title.sourceDate': '元の日付',
  'title.targetDate': '先の日付',
  'title.download-schedule': 'スケジュールダウンロード',
  'timeRange': '日付範囲',
  'title.scheduleInfo': 'スケジュール情報',
  'estimated': '予定',
  'planned': '計画',
  'title.userShiftInfo': 'ユーザーシフト情報',
  'employee.internal': '社員',
  'employee.outsource': '下請け/外注先',
  'workload': '工数',
  'showAllEmployeeShifts': '全シフト',
  'schedule.plan': '予定',
  'schedule.estimate': '見積',
  'schedule.actual': '実績',
  'title.outsourceShiftInfo': '下請け/外注先シフト情報',
  'officeOperation': '事務所業務',
  'schedule.plannedWorkload': '予定工数',
  'workingDate': '作業日',
  'schedule.estimatedWorkload': '実績工数',
  'employeeShift': '社員シフト',
  'outsourceShift': '下請け/外注先シフト',
  'message.noEmployeeShift': '社員シフトがありません',
  'overview': '概要',
  'outsourcingShift': '下請け/外注先シフト',
  'notSelected': '未選択',
  'notAssigned': '未割り当て',
  'message.noOutsourceShift': '下請け/外注先シフトがありません',
  'title.createSchedule': 'スケジュール作成',
  'role': 'ロール',
  'totalScheduledWorkTime': '予定作業時間',
  'oclock': '時',
  'outsourcing': '下請け/外注先',
  'message.please-select': '{msg}を選択してください',
  'label.exportFormat': 'エクスポート形式',

  // Workday
  'SUN': '日',
  'MON': '月',
  'TUE': '火',
  'WED': '水',
  'THU': '木',
  'FRI': '金',
  'SAT': '土',

  // Skills
  'rebarWorker': '鉄筋工',
  'supervisor': '監督',
  'heavyMachineryOperator': '重機OP',
  'unassigned': '未割り当て',
  'contractType': '契約タイプ',
  'engineer': '土工',
  'foreman': '職長',
  'formworkWorker': '型枠工',
  'worker': '土工',

  // Attendance management
  'unRanked': 'ランク外',
  'Approve1StatusName': '承認者⓵の状態',
  'Approve2StatusName': '承認者⓶の状態',
  'EndUserName': '申請者',

  // Calendar
  'tab.calendar': '勤怠カレンダー',
  'tab.timeTable': '勤怠表',
  'tab.schedule': '勤怠リスト',
  'link.dayoffRequest': '申請',
  'link.attendanceDetail': '勤怠詳細',
  'title.workingInformation': '勤怠情報',
  'startWorkingTime': '開始時間',
  'endWorkingTime': '終了時間',
  'work-status.absent': '勤怠なし',
  'work-status.scheduled': '勤務予定',
  'work-status.dayoff': '休暇申請',
  'work-status.request': '申請',
  'timekeeping-request-status.new': '申請なし',
  'timekeeping-request-status.requested': '申請',
  'timekeeping-request-status.approved': '承認',
  'timekeeping-request-status.rejected': '拒否',
  'button.monthlyAttendanceRequest': '月次勤怠申請',
  'monthlyAttendanceRequest': '月次勤怠申請',
  'form.working-location': '作業場',
  'form.break-time': '休憩時間',
  'form.break-in-time': '休憩開始時間',
  'form.break-out-time': '休憩終了時間',
  'form.approval': '承認',

  'address': '住所',
  'project': 'プロジェクト',
  'check_in_time': '作業開始',
  'check_out_time': '作業終了',
  'check_in_gps': '開始位置',
  'check_out_gps': '終了位置',
  'working_time': '勤務時間',
  'break_time': '休憩時間',
  'no_break_time': '休憩なし',
  'break_time_1': '休憩時間⓵',
  'break_time_2': '休憩時間⓶',
  'break_time_3': '休憩時間⓷',
  'time': '時間',
  'total_work_time': '合計勤務時間',
  'total_overtime': '残業時間',
  'request-type': '申請タイプ',
  'reason': '備考',
  'approver': '承認者',

  'calendar.request': '申請',
  'calendar.absent': '欠勤',
  'calendar.absent-days': '欠勤日数',
  'calendar.attendance': '出勤',
  'calendar.working-day': '勤務日数',
  'calendar.working-days': '勤務日数',
  'calendar.working-time': '勤務時間',
  'calendar.overtime': '残業時間',
  'calendar.days-off': '休日',
  'calendar.paid-leave': '休暇日数',
  'calendar.scheduled': '予定済み',
  'error.projectRequired': 'プロジェクトは必須です',
  'error.checkInTimeRequired': '作業開始時間は必須です',
  'error.checkOutTimeRequired': '作業終了時間は必須です',
  'error.checkInTimeMustBeBeforeCheckOutTime': '作業開始時間は作業終了時間より前にしてください',
  'error.checkOutTimeMustBeBefore2359OfNextOneDayOfCheckInDate': '作業終了時間は翌日の23:59までにしてください',
  'error.checkOutTimeMustBeBeforeCurrentTime': '作業終了時間は現在時刻より前にしてください',

  // holiday
  'title.holiday-info': '休暇情報',
  'title.today': '本日',
  'holiday-code': '休暇コード',
  'holiday-name': '休暇名前',
  'notes': '備考',
  'salary-coefficient': '休日出勤割増',
  'annual-event': '年間イベント',
  'button.add-new-event': '新しいイベントを追加',
  'calendar-empty': 'イベントなし',
  'form.event-name': 'イベント名',
  'form.event-name.required': 'イベント名を入力してください',
  'form.event-date': 'イベント日',
  'form.event-start-date': 'イベント開始日',
  'form.event-end-date': 'イベント終了日',
  'form.event-time': 'イベント時間',
  'form.event-start-time': 'イベント開始時間',
  'form.event-end-time': 'イベント終了時間',
  'form.is-recurring': '繰り返し',
  'form.recurring-type': '繰り返しタイプ',
  'form.recurring-type.daily': '毎日',
  'form.recurring-type.weekly': '毎週',
  'form.recurring-type.monthly': '毎月',
  'form.recurring-type.annually': '毎年',
  'form.is-day-off': '休日',
  'form.recurring-date': '繰り返し日',
  'form.recurring-from': '繰り返し開始日',
  'form.recurring-to': '繰り返し終了日',
  'form.recurring-day': '繰り返し日',
  'form.recurring-day.monday': '月曜日',
  'form.recurring-day.tuesday': '火曜日',
  'form.recurring-day.wednesday': '水曜日',
  'form.recurring-day.thursday': '木曜日',
  'form.recurring-day.friday': '金曜日',
  'form.recurring-day.saturday': '土曜日',
  'form.recurring-day.sunday': '日曜日',
  'form.recurring-week': '繰り返し週',
  'form.recurring-week.first': '第1週',
  'form.recurring-week.second': '第2週',
  'form.recurring-week.third': '第3週',
  'form.recurring-week.fourth': '第4週',
  'form.recurring-week.last': '最終週',
  'form.recurring-month': '繰り返し月',
  'form.recurring-month.january': '1月',
  'form.recurring-month.february': '2月',
  'form.recurring-month.march': '3月',
  'form.recurring-month.april': '4月',
  'form.recurring-month.may': '5月',
  'form.recurring-month.june': '6月',
  'form.recurring-month.july': '7月',
  'form.recurring-month.august': '8月',
  'form.recurring-month.september': '9月',
  'form.recurring-month.october': '10月',
  'form.recurring-month.november': '11月',
  'form.recurring-month.december': '12月',
  'add-event-calendar': 'イベントカレンダー追加',
  'edit-event-calendar': 'イベントカレンダー編集',
  'log-event-calendar': 'イベントカレンダーログ',
  'log.CREATE': '作成',
  'log.UPDATE': '更新',
  'log.DELETE': '削除',
  'log.lastmodifiedtime': '最終更新日',
  'log.lastmodifiedby': '最終更新者',
  'log.changed': '<span color="blue">{field}</span>を<span color="warning">{oldValue}</span>から<span color="warning">{newValue}</span>に変更しました',
  'button.yes': 'はい',
  'button.no': 'いいえ',
  'event-calendar': 'イベントカレンダー',
  'event-calendar-rule-setting': 'イベントカレンダールール設定',
  'holiday': '休日',

  // User management
  'ranking': '給与係数',
  'rankingName': '給与係数名',
  'active-only': 'Active',
  'table.person': 'ユーザー',
  'table.contact': '連絡先',
  'table.struct-rank': '所属&ランキング',
  'employeeType': '社員タイプ',
  'office': '事業所',
  'construction': '工事',
  'form.rankingDate': '給与係数日',
  'edit-user': 'ユーザー編集',
  'add-user': 'ユーザー追加',
  'set-up-ranking': '社内ランキング設定',
  'male': '男性',
  'female': '女性',
  'form.loginId': 'ログインID',
  'no-data': '未入力',
  'form.address.placeholder': '住所を入力してください',
  'form.orgStructureName.placeholder': '組織名を入力してください',
  'form.loginId.placeholder': 'ログインIDを入力してください',
  'form.name.placeholder': '名前を入力してください',
  'form.min-value-and-max-value-cannot-be-empty': '最大値と最小値を同時に空白にすることはできません。',
  'form.email.placeholder': 'メールを入力してください',
  'form.phone.placeholder': '電話番号を入力してください',
  'form.gender.placeholder': '性別を選択してください',
  'form.birthday.placeholder': '生年月日を選択してください',
  'form.baseLeave': '基本休暇',
  'form.baseLeaveExpire': '基本休暇有効期限',
  'gender.male': '男性',
  'gender.female': '女性',
  'title.edit-employee': '社員編集',
  'steps.userInfoSetting': 'ユーザー情報設定',
  'steps.employeeInfoSetting': '社員情報設定',
  'steps.complete': '完了',
  'steps.confirm': '確認',
  'button.next': '次へ',
  'button.back': '戻る',
  'form.employeeMails': '社員メール',
  'form.employeePhones': '社員電話番号',
  'form.employeeAddress': '社員住所',
  'form.employeeType': '社員タイプ',
  'form.employeeRole': '社員役割',
  'form.rankingStartDate': '給与係数開始日',
  'form.rankingEndDate': '給与係数終了日',
  'form.rankingName': '給与係数名',
  'form.workingStatus': '勤務状態',
  'form.employeeCode': '社員コード',
  'form.orgStructureName': '組織名',
  'form.orgPositionName': '役職名',
  'form.isEmployee': '社員',
  'form.salaryInHour': '時給',
  'form.salaryInDay': '日給',
  'form.standardWorkingHours': '標準作業時間',
  'form.salaryInMonth': '月給',
  'form.workingFromDate': '勤務開始日',
  'form.workingToDate': '勤務終了日',
  'userInfo': 'ユーザー情報',
  'employeeInfo': '社員情報',
  'birthday': '生年月日',
  'gender': '性別',
  'steps.employeeInfo': '社員情報',
  'title.invite-employee': '社員招待',
  'success.invite-employee': '社員招待成功',
  'success.invite-employee-sub-title': '招待リンクをメールで確認してください',
  'button.close': '閉じる',
  'button.continue': '続ける',
  'type.employee': '社員',
  'type.outsource': '下請け/外注先',
  'form.positionName': '役職名',
  'alert.confirmResetPass': 'パスワードをリセットしますか？',
  'alert.confirmDelete': '{msg}を削除しますか？',
  'employee': '社員',
  'positionName': '役職名',
  'workingStatus': '勤務状態',

  // Cost summary
  'subtotal': '小計',
  'total': '合計',
  'expenses': '経費',
  'benefit': '利益',
  'main-construction-cost': '主要工事費',
  'separate-construction-cost': '別工事費',
  'overall-construction-cost': '総工事費',
  'contract-construction-period': '契約工事期間',
  'cost-management-entry': 'コスト管理エントリー',
  'check-details': '詳細を確認',
  'edit-cost-summary': 'コスト概要を編集',
  'construction-cost-total': '工事費合計',
  'estimate-construction-cost': '工事費見積もり',
  'separate-construction-costs': '別工事費',
  'contract': '契約',
  'contract-period': '契約期間',
  'changes': '変更',
  'total-construction-cost': '総工事費',
  'rich-amount': 'リッチ額',
  'contractor': '元請け',
  'customer': '顧客',
  'main-construction-cost-and-separate-construction-cost': '主要工事費と別工事費',
  'view-cost-summary-detail': 'コスト概要詳細を表示',

  // Cost Category
  'add-cost-category': 'コストカテゴリの追加',
  'edit-cost-category': 'コストカテゴリの編集',
  'log-cost-category': 'コストカテゴリログ',
  'categories-management': 'カテゴリ管理',
  'form.products': '製品',
  'button.add-more': '追加',

  // Vendor
  'add-vendor': 'サプライヤーの追加',
  'edit-vendor': 'サプライヤーの編集',
  'log-vendor': 'サプライヤーログ',
  'form.corporate': '法人',
  'company-information': '会社情報',
  'contact-person': '担当者',
  'invoice': '請求書',
  'form.original-number': '原本番号',
  'form.invoice-title': '請求書タイトル',
  'form.issue-date': '発行日',
  'form.contractor': '元請け',
  'form.customer': '顧客',
  'form.outsource': '下請け/外注先',
  'form.vendor': 'サプライヤー',
  'form.vendor-common-name': 'サプライヤー通称',
  'form.other-name': 'その他の名前',
  'form.corporate-number': '法人番号',
  'form.tax-code': '税コード',
  'representative-information': '代表者情報',
  'form.representative-name': '代表者名',
  'form.phone-number': '電話番号',
  'show': '表示',
  'entries': 'エントリー',
  'invoice-list-of': '請求書一覧',
  'button.detail': '詳細',
  'add-representative': 'メンバーの追加',

  // Contractor
  'add-contractor': '元請けの追加',
  'edit-contractor': '元請けの編集',
  'log-contractor': '元請けログ',
  'form.contractor-common-name': '元請け通称',
  'button.project-list': 'プロジェクト一覧',
  'project-list-of': 'プロジェクト一覧',

  // Customer
  'add-customer': '顧客の追加',
  'edit-customer': '顧客の編集',
  'log-customer': '顧客ログ',
  'form.type-of-customer': '顧客タイプ',
  'form.customer-common-name': '顧客通称',

  // Outsource
  'add-outsource': '下請け/外注先の追加',
  'edit-outsource': '下請け/外注先の編集',
  'log-outsource': '下請け/外注先ログ',

  // Cost Item
  'add-cost-item': 'コスト項目の追加',
  'edit-cost-item': 'コスト項目の編集',
  'form.item': '項目',
  'form.item-list': '取引項目一覧',
  'form.seri': 'シリアル',
  'form.main-category': 'カテゴリー',
  'form.time_period': '時間帯',
  'form.time-period-from': '時間帯開始',
  'form.to': 'から',
  'form.price-from': '価格開始',
  'suppliers-comparison': 'サプライヤー比較',
  'monthly-price-comparison': '月間価格比較',
  'form.subcontractor': '下請け',

  // Cost Manufacturer
  'add-manufacturer': 'メーカーの追加',
  'edit-manufacturer': 'メーカーの編集',
  'log-manufacturer': 'メーカーログ',
  'form.size': 'サイズ',
  'form.serial-number': 'シリアル番号',
  'form.model': 'モデル',
  'form.fuel-usage': '燃料使用量',
  'form.equipment-status': '機器状態',
  'form.last-maintain-date': '最終メンテナンス日',
  'equipment': '機器',
  'form.manufacturer-common-name': 'メーカー通称',

  // Office management
  'userName': 'ユーザー名',
  'code': 'コード',
  'workTime': '勤務時間',
  'overtime': '残業時間',
  'workDays': '勤務日数',
  'offdays': '休日',
  'usedLeaves': '使用休暇',
  'remainLeaves': '残休暇',
  'comment': '備考',

  // Outsource
  'outsource.name': '下請け/外注先名',
  'outsource.code': '下請け/外注先コード',
  'outsource.description': '下請け/外注先説明',
  'outsource.edit': '下請け/外注先編集',
  'outsource.add': '下請け/外注先追加',
  'outsource.form.message': '下請け/外注先情報を入力してください',
  'outsource.status': '下請け/外注先ステータス',
  'outsource.corporate-number': '会社番号',
  'outsource.contact-person': '連絡人',
  'outsource.contact-person-email': '連絡人メール',
  'outsource.contact-person-phone-number': '連絡人電話番号',
  'outsource.expertise': '専門分野',
  'outsource.address': '住所',
  'outsource.email': 'メール',
  'outsource.phone-number': '電話番号',
  'outsource.edit-outsource': '下請け/外注先編集',

  // Cost Config
  'payment-type': '支払タイプ',
  'entry-type': '入力タイプ',
  'add-payment-type': '支払タイプの追加',
  'edit-payment-type': '支払タイプの編集',
  'form.payment-type-name': '支払タイプ名',
  'add-entry-type': '入力タイプの追加',
  'edit-entry-type': '入力タイプの編集',
  'form.entry-type-name': '入力タイプ名',
  'log-payment-type': '支払タイプログ',
  'log-entry-type': '入力タイプログ',

  // Construction cost
  'requestAmount': '出来高金額',
  'retentionAmount': '保留金',
  'releasedAmount': '保留金解除額',
  'totalClaimedAmount': '保留金除外の請求金額',
  'profitByRequestedAmount': '出来高に対する利益費',
  'profitByClaimedAmount': '請求に対する利益金額',
  'construction-cost.totalCost': '工事原価合計',
  'riskAmount': '相殺',
  'mainConstruction': '本工',
  'subConstruction': '分工',
  'estimateBudgetAmount': '予算金額',
  'againstBudget': '予算対比',
  'actualProfitMargin': '実績利益率',
  'mcEstimatedProfitMargin': '予測利益率',
  'previousAmount': '前回まで',
  'currentAmount': '今回',
  'accumulatedAmount': '累計',
  'remainingBalance': '残金',
  'processRate': '進捗率',
  'construction-cost.main.estimateCost': '対予算（本工事費）集計',
  'construction-cost.sub.estimateCost': '対予算 (別途工事費) 集計',
  'construction-cost.overall.estimateCost': '対予算 (総合計) 集計',
  'mainConstructionCostAmount': '出来高書 (本工事費) 金額',
  'subConstructionCostAmount': '出来高書 (別途工事費) 金額',
  'overallConstructionCostAmount': '出来高書 (総合計) 金額',
  'mainConstructionCostSummary': '本工事費のサマリー',
  'subConstructionCostSummary': '別途工事費のサマリー',
  'mainConstructionCost': '本工事費',
  'subConstructionCost': '別途工事費',

  'previousMiscellaneousExpenses': '前回までの諸費用',
  'currentMiscellaneousExpenses': '今回の諸費用',
  'currentMonthEstimatedAmount': '出来高予測金額',
  'currentMonthConfirmedAmount': '出来高確定金額',
  'amountExcludingRetention': '保留金除外の金額',
  'accumulatedMiscellaneousCosts': '累計諸費用',
  'accumulatedRetentionAmount': '累計保留金',
  'siteProgressSummary': '現場進捗の集計',

  'budgetBasedOnConstructionProgress ': '工事進捗に応じた予算',
  'constructionInfoDetail': '工事情報詳細',

  'contractualCost': '契約金額',
  'estimatedCost': '予算金額',
  'initialCost': '当初',
  'modifiedCost': '変更増減',
  'profitRatio': '本工事の利益率予想',

  // Input Cost
  'form.original-document-number': '原本番号',
  'form.original-document-number.required': '原本番号を入力してください',
  'form.original-document-date': '原本日付',
  'form.original-document-date.required': '原本日付を入力してください',
  'form.process-type': '処理タイプ',
  'form.process-type.required': '処理タイプを入力してください',
  'form.entry-type': '入力タイプ',
  'form.entry-type.required': '入力タイプを入力してください',
  'form.payment-date': '支払日',
  'form.payment-date.required': '支払日を入力してください',
  'form.payment-type': '支払タイプ',
  'form.payment-type.required': '支払タイプを入力してください',
  'form.provider': '提供者',
  'form.provider.required': '提供者を入力してください',
  'form.project': '工事',
  'form.project.required': '工事を入力してください',
  'form.item-name': '項目名',
  'form.item-name.required': '項目名を入力してください',
  'form.transaction-date': '取引日',
  'form.transaction-date.required': '取引日を入力してください',
  'form.quantity': '数量',
  'form.quantity.required': '数量を入力してください',
  'form.manufacturer': 'メーカー',
  'form.manufacturer.required': 'メーカーを入力してください',
  'form.price': '価格',
  'form.price.required': '価格を入力してください',
  'form.tax-rate': '税率',
  'form.tax-rate.required': '税率を入力してください',
  'form.price-excluding-tax': '税抜価格',
  'form.price-excluding-tax.required': '税抜価格を入力してください',
  'form.price-included-tax': '税込価格',
  'form.price-included-tax.required': '税込価格を入力してください',
  'form.total': '合計',
  'form.total.required': '合計を入力してください',
  'form.description.placeholder': '説明を入力してください',
  'form.description.required': '説明を入力してください',
  'employee-costs': '社員コスト',
  'outsourcing-costs': '下請け/外注先コスト',
  'material-costs': '材料コスト',
  'lease-costs': 'リースコスト',
  'machine-costs': '機械コスト',
  'other-costs': 'その他コスト',
  'cancellation': 'キャンセル',
  'form.from-date': '開始日',
  'form.to-date': '終了日',
  'form.site-name': 'サイト名',
  'message.delete-confirmation': '削除しますか？',
  'message.edit-confirmation': '編集しますか？',
  'message.add-confirmation': '追加しますか？',
  'message.cancel-confirmation': 'キャンセルしますか？',
  'message.required': '入力してください{field}',
  'rankName': 'ランク名',
  'manHours': '工数',
  'approximatePrice': '概算金額',
  'fixedPrice': '固定金額',
  'averagePrice': '平均金額',
  'amount': '合計金額',
  'outsourceName': '下請け/外注先名',
  'message.process-data-confirmation': '数分以内にデータを処理します。',
  'title.input-cost-item-delete': '入力コストアイテムを削除',
  'confirm-delete-item': '削除しますか?',
  'manufacturer': 'メーカー',

  // Project management
  'form.structureName': '所属名',
  'form.expectedStartDate': '予定開始日',
  'form.expectedEndDate': '予定終了日',
  'form.actualStartDate': '実績開始日',
  'form.actualEndDate': '実績終了日',
  'project.status': 'プロジェクトの状態',

  // Worksite management
  'form.primaryManager': '担当者',
  'form.subManagers': '副担当者',
  'title.add-workplace': '作業場の追加',
  'add-workplace': '作業場の追加',
  'edit-workplace': '作業場の編集',
  'status.working': '勤務中',
  'deactive': '非活動',
  'table.office': '• 事業所',
  'table.construction': '• 工事',
  'form.workTime': '勤務時間',
  'form.breakTime': '休憩時間',
  'selectDate': '日付を選択',

  // Workshift
  'confirm.updateStatus': 'ステータス変更の確認',
  'warning.updateStatus': 'ワークサイトのステータスを {msg1} から {msg2} に変更してもよろしいですか?',
  'form.workShiftName': '勤務シフト名',
  'form.inTime': '勤務開始時間',
  'form.outTime': '勤務終了時間',
  'status.active': '有効',
  'status.deactive': '無効',
  'workShiftBreaks': '休憩',
  'workshift.error.the-break-time-is-not-within-the-working-hours.': '休憩時間は作業時間内に設定してください。',
  'workshift.error.break-times-cannot-overlap.': '休憩時間は重複しないようにしてください。',
  'workshift.error.the-actual-working-time-calculation-is-unreasonable.': '実際の作業時間計算は不正確です。',
  'workshift.error.break-out-time-is-later-than-the-break-in-time-of-the-break.': '休憩終了時間は休憩開始時間より後に設定してください。',
  'workshift.error.check-out-time-is-later-than-the-check-in-time.': '勤務終了時間は勤務開始時間より後に設定してください。',
  'form.workShiftCode': '勤務シフトコード',
  'workshift.error.check-in-time-is-required.': '勤務開始時間を入力してください。',
  'workshift.error.check-out-time-is-required.': '勤務終了時間を入力してください。',

  'error.checkInTimeMustBeBeforeCurrentTime': '出勤時間は現在時刻より前に設定してください。',

  // Mornitoring page
  'title.reject': '拒否',
  'attendanceStatus': '勤怠状況',
  'workplaceAddress': '作業場住所',
  'isApproved': '承認済み',
  'isRequested': '申請済み',
  'request.isRequested': '申請済み',
  'request.isNotRequested': '申請なし',
  'request.isApproved': '承認済み',
  'request.isRejected': '拒否済み',
  'status.scheduled': '予定あり',
  'status.checkIn': '作業開始済',
  'status.break': '休憩中',
  'status.checkOut': '作業終了済',
  'weekday': '平日',
  'weekkend': '休日',
  'attendance-list': '勤怠リスト',
  'employee-name': '従業員名',
  'work-time': '作業時間',
  'totalBreakTime': '合計休憩時間',
  'total-over-time': '残業時間',
  'coefficient': '工数分配 ',
  'attendance-status': '勤怠状況',
  'work-notes': '作業内容',
  'button.export': 'エクスポート',
  'ranking-name': 'ランキング名',
  'outsource-name': '外注名',
  'workloadOnMainConstruction': '本工事工数',
  'workloadOnSubConstruction': '別途工事工数',
  'title.editAttendance': '勤怠の編集',
  'title.addAttendance': '勤怠の追加',
  'workingTime': '作業時間',
  'totalWorkingTime': '所定労働時間',
  'totalOverTime': '残業時間',
  'checkInTime': '出勤時間',
  'checkOutTime': '退勤時間',
  'breakTime': '休憩時間',
  'noBreakTime': '休憩なし',
  'standardHours': '所定労働時間',
  'realHours': '実働時間',
  'lateHours': '遅刻時間',
  'earlyHours': '早退時間',
  'duration': ' 継続時間',
  'totalWorkload': '作業量合計',
  'button.dailyReportCreated': '日報作成済み',
  'title.timecard': '出勤/打刻管理',
  'date': '日付',
  'placeholder.rejectReason': '理由を入力してください',
  'requestStatus': '申請状況',

  // Change password page
  'oldPassword': '現在のパスワード',
  'newPassword': '新しいパスワード',
  'confirmNewPassword': '新しいパスワード(確認)',

  // Request center
  'requestFromTo': '申請日',
  'your-request': 'あなたの申請',
  'filter': 'フィルター',
  'apply-by': '申請者',
  'reject-by': '拒否者',
  'approve-by': '承認者',
  'add-new-request': '新しい申請を追加',
  'attendance-request': '勤怠申請',
  'timeshift-request': '休暇申請',
  'time-request': '勤怠申請',
  'title.monthlyAttendanceRequest': '月次勤怠申請',
  'view-detail': '詳細一覧',
  'form.type': '種別',
  'form.days': '日',
  'form.rejected-by': '拒否者',
  'form.approved-by': '承認者',
  'form.reason': '備考',
  'form.request': '申請',
  'form.author': '申請者',
  'form.your-request': 'あなたの申請',
  'form.start-date': '開始日',
  'form.end-date': '終了日',
  'form.request-type': '申請種別',
  'form.leave-type': '休暇種別',
  'form.minutes': '分',
  'form.hours': '時間',
  'NOT_APPROVED': '未承認',
  'APPROVED': '承認済み',
  'REJECTED': '拒否済み',
  'PENDING': '確認待ち',
  'CANCELLED': '取下げ',
  'NOT_REQUESTED': '未申請',
  'form.request-duration': '申請期間',
  'request-management': '申請管理',
  'message.approve-confirmation': '承認しますか？',
  'message.reject-confirmation': '拒否しますか？',
  'placeholder.select-time': '時間を選択',
  'placeholder.select': '{msg}選択',
  'placeholder.enter': '{msg}入力',
  'on-site-approver': '現場承認者',
  'office-approver': '事務所承認者',
  'representative-approver': '代表承認者',
  'day': '日',
  'half-day': '半日',
  'custom-day': 'カスタム日',
  'morning': '午前',
  'afternoon': '午後',
  'leave-request-description': '休暇申請は、従業員が休暇を取得するための申請です。申請内容には、休暇の種類、開始日、終了日、理由などが含まれます。',
  'work-request-description': '勤怠申請は、従業員が勤務時間や休暇を申請するためのものです。申請内容には、勤務時間の変更、休暇の取得、遅刻や早退の理由などが含まれます。',
  'overtime-request-description': '残業申請は、従業員が通常の勤務時間を超えて働くことを申請するためのものです。申請内容には、残業の理由、開始時間、終了時間などが含まれます。',
  'in-out-request-description': '出退勤申請は、従業員が出勤や退勤の時間を申請するためのものです。申請内容には、出勤時間、退勤時間、理由などが含まれます。',

  // Human cost
  'form.ranking': '社内ランク',
  'form.rank-name': 'ランク名',
  'form.employee-name': '社員名',
  'form.employee-cost': '社員単価',
  'form.cost-amount': '日用金額',
  'form.avarage-cost-amount': '平均値金額',
  'form.ranking-matching-method': 'ランキング判断方法',
  'form.outsource-cost': '下請け/外注先費',
  'form.direct': '直径判断する',
  'form.indirect': '月給の上判断する',
  'form.min-value': '最小値',
  'form.max-value': '最大値',
  'form.average-value': '平均値',
  'form.rank': 'ランク',
  'form.cost-by-day': '費用（日）',
  'form.valid-date': '有効日',
  'form.empty': '未確定',
  // Common
  'placeholder.enter-data': '{msg}を入力してください',
  'placeholder.select-data': '{msg}を選択してください',
  'placeholder.note': '備考',
  'placeholder.write-here': 'ここに入力してください',

  // Profile basic
  'profile.basic.contactInfo': '連絡情報',
  'profile.basic.roleInfo': '役割情報',
  'profile.username': 'ユーザー名',
  'profile.email': 'メールアドレス',
  'profile.phone': '携帯電話',
  'profile.address': '住所',
  'profile.birthday': '生年月日',
  'profile.company': '会社',
  'profile.position': 'ポジション',
  'profile.structure': '属所',
  'profile.level': 'ランキング',

  'profile': 'プロフィール',
  'settings': '設定',
  'logout': 'ログアウト',
  'setNewAvatar': '新しいプロフィール画像を設定',
  'cropYourNewAvatar': '新しいプロフィール画像をクロップ',
  'changePhoto': 'プロフィール画像を変更',

  // Leave setting
  'employeeId': '社員ID',
  'employeeName': '社員名',
  'employeeCode': '社員コード',
  'baseLeave': '基本休暇',
  'baseLeaveExpire': '基本休暇の有効期限',
  'lastRemainLeave': '残りの休暇',
  'lastRemainLeaveExpire': '残りの休暇の有効期限',
  'totalUsedLeave': '使用済み休暇合計',
  'totalSelfUsedLeave': '自己使用済み休暇合計',
  'totalOrgUsedLeave': '組織使用済み休暇合計',
  'createTime': '作成日時',
  'updateTime': '更新日時',
  'from': 'から',
  'to': 'まで',
  'baseLeaveValidDateRange': '基本休暇の有効期限',
  'lastLeaveRemainingValidDateRange': '残りの休暇の有効期限',
  'lastLeaveRemaining': '残りの休暇',
  'totalPersonalUsedLeave': '使用済み個人休暇合計',
  'personalUsed': '個人使用',
  'forceUsed': '強制使用',
  'expire': '有効期限',

  // Notification
  'notification': '通知',
  'post': '役職',
  'form.title': 'タイトル',
  'form.body': '本文',
  'form.target': 'ターゲット',
  'form.target-type': 'ターゲットタイプ',
  'form.target-id': 'ターゲットID',
  'target-type.individual': 'プライベート',
  'target-type.role': '役割',
  'target-type.all': '全員',
  'all': '全員',
  'unread': '未読',
  'mark-all-as-read': '全て既読にする',
  'noTarget': 'ターゲットなし',
  'form.notification-type': '通知タイプ',
  'notification-type.push': 'プッシュ通知',
  'notification-type.email': 'メール通知',
  'test': 'テスト',
  'FAILED': '失敗',
  'PUBLISHED': '公開済み',
  'PARTIALLY_PUBLISHED': '部分的に公開済み',
  'PARTIALLY_FAILED': '部分的に失敗',

  // Account
  'title.invite-to-organization': '組織へ招待',
  'description.invite-to-organization': '組織招待を管理します',
  'message.no-invitations': '招待がありません',
  'message.expire-time': '有効期限',
  'button.accept': '承認',
  'message.accept-invitation': '招待を承認しました',
  'message.reject-invitation': '招待を拒否しました',
  'message.error-accept-invitation': '招待の承認に失敗しました',
  'button.signUp': '新規登録',
  'account.settings.password.change': 'パスワードを変更',
  'placeholder.oldPassword': '現在のパスワード',
  'placeholder.newPassword': '新しいパスワード',
  'placeholder.confirmNewPassword': '新しいパスワード(確認)',
  'validation.oldPassword.required': '現在のパスワードを入力してください',
  'validation.password.required': 'パスワードを入力してください',
  'validation.confirmPassword.required': 'パスワード確認を入力してください',
  'button.submit': '送信',
  'validation.confirmPassword.notMatch': 'パスワードが一致しません',
  'validation.password.pattern': 'パスワードは大文字、小文字、数字を含む必要があります',
  'account.settings.password.changeSuccess': 'パスワード変更成功',

  // Invoice
  'supplier': '仕入先',
  'invoice-type': '請求書タイプ',
  'release-date': '発行日',
  'payment-term': '支払期間',
  'payment-method': '支払方法',
  'payment-date': '支払日',
  'invoice-number': '請求書番号',
  'button.note': '備考',
  'item-name': '品目名',
  'unit': '単位',
  'quantity': '数量',
  'price': '価格',
  'unitPrice': '単価',
  'tax-rate': '税率',
  'button.save-change': '変更を保存',
  'phone-number': '電話番号',
  'email': '電子メール',
  'click-or-drag-file-to-this-area-to-upload': 'クリックまたはドラグファイルをこのエリアにドラグしてアップロード',
  'upload-image': '画像のアップロード',
  'support-for-a-single-upload': '単一アップロードに対応',
  'vendor': 'サプライヤー',
  'transaction-date': '取引日',
  'total-nontaxed': '非課税合計',
  'total-taxed': '税抜合計',
  'button.upload': 'アップロード',
  'input-cost-item-list': '入力コスト品目リスト',
  'add-new-item': '新しい品目を追加',
  'noMore': 'これ以上ありません',
  'invoiceInput': '請求書入力',
  'no-images': '画像はありません',
  'placeholder-search': '検索',
  'currency.unit': '¥',

  // Summary detail
  'price-after-tax': '税込価格',
  'price-before-tax': '税抜価格',
  'original-input-cost-number': '元の入力コスト番号',
  'actions': '操作',
  'button.manual-import': '手動インポート',
  'button.image-import': '画像インポート',
  'invoice-image': '請求書画像',
  'constructure': '構成',
  'invoice-title': '請求書タイトル',
  'unit-price': '単価',
  'tax-rate-percent': '税率(%)',
  'category': 'カデゴリ',
  'button.save-all': '全て保存',
  'update-time': '更新日時',
  'summary-detail.amount': '金額',
  'manDay': '人工',
  'grandTotal': '総合計',
  'show-original-cost': '生産原価表示 ／ 単価平均値',

  // Basic-setting
  'account.settings.form-male': '男',
  'account.settings.form-female': '女',
  'account.settings.form-birthday.placeholder': '生年月日を選択してください',
  'account.settings.form-baseLeave': '基本休暇',
  'name': '名前',
  'avatar.updateSuccess': 'アバター更新成功',
  'profile.role': 'ロール',

  // Sign up page
  'title.registration': '新規登録',
  'title.otp_verification': 'OTP 検証',
  'title.complete': '完了',
  'form.password': 'パスワード',
  'form.confirmPassword': 'パスワード確認',
  'button.verify': '検証',
  'button.resend': '再送信',
  'message.registration_success': '新規登録成功',
  'message.registration_success_sub': 'ログインできます',
  'message.otp_sent_to_email': 'OTP が {email} に送信されました',
  'message.otp_verification_success': 'OTP 検証成功',
  'message.otp_verification_error': 'OTP 検証に失敗しました',
  'message.resend_otp_error': 'OTP の再送信に失敗しました',
  'message.password_min': 'パスワードは6文字以上で入力してください',
  'message.password_not_match': 'パスワードが一致しません',
  'message.email_invalid': 'メールアドレスが無効です',
  'message.loginId': 'ログインIDを入力してください',
  'message.password': 'パスワードを入力してください',
  'message.confirmPassword': 'パスワード確認を入力してください',
  'form.male': '男性',
  'form.female': '女性',
  'placeholder.name': '名前を入力してください',
  'placeholder.address': '住所を入力してください',
  'placeholder.phone': '携帯電話を入力してください',
  'placeholder.loginId': 'ログインIDを入力してください',
  'placeholder.password': 'パスワードを入力してください',
  'placeholder.confirmPassword': 'パスワード確認を入力してください',
  'placeholder.birthday': '生年月日を入力してください',
  'placeholder.email': 'メールアドレスを入力してください',

  // Monthly attendance closing detail
  'attendance.summary': '社員の月次勤怠の詳細',
  'attendance.summary.description': '月次勤怠統計概要',
  'workingDays': '勤務日数',
  'absentDays': '欠勤日数',
  'paidLeave': '休暇日数',
  'lastUpdated': '最終更新日',
  'chooseAction': '操作を選択',
  'CSVDownload': 'CSVダウンロード',
  'success.updateAttendance': '変更が確認されました！',
  'error.updateAttendance': '変更の確認に失敗しました',
  'error.invalidTimeFormat': '無効な時間形式',
  'error.loadData': 'データの読み込みに失敗しました',
  'success.exportCSV': 'CSVファイルが正常にエクスポートされました！',
  'error.exportCSV': 'CSVファイルのエクスポートに失敗しました',
  'status': '状態',
  'dateFrom': '日付から',
  'dateTo': '日付まで',
  'ago': '前',
  'error.isApprovedRecord': 'この勤怠記録は承認されているため、編集できません',
  'status.approved': '承認済み',
  'status.notApproved': '未承認',
  'error.invalidTimeFormatHHMM': '無効な時間形式（HH:MM）',
  'approvalStatus': '承認状況',
  'button.sync': '同期',
  'success.syncAttendance': '勤怠同期が完了しました',
  'error.syncAttendance': '勤怠同期に失敗しました',
  'tab.officeData': '事業所データ',
  'tab.employeeData': '社員データ',
  'showShiftInOneLine': 'シフトを1行で表示',

  // Employee shift table
  'fontSize': 'フォントサイズ',
  'rowHeight': '行の高さ',

  // Simulation report
  'simulationReport': 'シミュレーションレポート',
  'export': 'エクスポート',

  // Placeholder
  'Please Select': '選択してください',

  // Invite page
  'button.back-to-login-page': 'ログインページに戻る',

  // Error page
  'message.noPermission': 'システム画面へのアクセス権限がありません。',
  'error.apiError': 'APIエラー',

  // Alert message
  'confirm.delete': '削除確認',

  // Form
  'form.required': '{msg}を入力してください',

  // Danh sach ma lỗi
  'KAN500': '不明なエラー',
  'KAN00': '操作成功',
  'KAN01': '見つかりません',
  'KAN02': '変更なし',
  'KAN03': '不正なリクエスト',
  'KAN04': '認証されていません',
  'KAN05': '禁止されています',
  'AUTH01': 'パスワードが正しくありません',
  'AUTH02': 'パスワードの変更に失敗しました',
  'AUTH03': 'パスワード確認が一致しません',
  'AUTH04': 'パスワードが要求を満たしていません',
  'AUTH05': 'パスワードのリセットに失敗しました',
  'AUTH06': '外部委託がブロックされています',
  'TKN01': 'トークンが期限切れです',
  'TKN02': 'トークンが無効です',
  'TKN03': 'トークンの検証に失敗しました',
  'USR01': 'ユーザーが見つかりません',
  'USR02': '組織内でユーザーが見つかりません',
  'USR03': 'ユーザーの作成に失敗しました',
  'USR04': 'ユーザーの更新に失敗しました',
  'USR05': '役割の割り当てに失敗しました',
  'USR06': 'ユーザーが既に存在します',
  'USR07': '組織内にユーザーが既に存在します',
  'USR08': '管理者の作成は禁じられています',
  'USR09': '組織の割り当てに失敗しました',
  'CRT01': '契約の作成に失敗しました',
  'CRT02': '契約の更新に失敗しました',
  'ORG01': '組織が見つかりません',
  'ORG02': '組織の作成に失敗しました',
  'ORG03': '組織の更新に失敗しました',
  'STR01': '所属が見つかりません',
  'STR02': '所属の作成に失敗しました',
  'STR03': '所属の更新に失敗しました',
  'POS01': 'ポジションが見つかりません',
  'POS02': 'ポジションの作成に失敗しました',
  'POS03': 'ポジションの更新に失敗しました',
  'RNK01': 'ランキングが見つかりません',
  'RNK02': 'ランキングの作成に失敗しました',
  'RNK03': 'ランキングの更新に失敗しました',
  'WPL01': '勤務地が見つかりません',
  'WPL02': '勤務地の作成に失敗しました',
  'WPL03': '勤務地の更新に失敗しました',
  'WPL04': '管理者の割り当てに失敗しました',
  'GLC01': 'グローバル設定が見つかりません',
  'MAIL01': 'メールテンプレートが見つかりません',
  'MAIL02': 'メールの送信に失敗しました',
  'ROLE01': '役割が見つかりません',
  'ROLE02': '役割の作成に失敗しました',
  'ROLE03': '役割の更新に失敗しました',
  'UNIT01': '単位が見つかりません',
  'RQT01': 'リクエストタイプが見つかりません',
  'LVT01': '休暇タイプが見つかりません',
  'REQ01': 'リクエストが見つかりません',
  'REQ02': 'リクエストの作成に失敗しました',
  'REQ03': 'リクエストの更新に失敗しました',
  'REQ04': '出席の承認に失敗しました',
  'REQ05': 'リクエストの承認に失敗しました',
  'REQ06': 'リクエストの拒否に失敗しました',
  'REQ07': 'リクエストの詳細が無効です',
  'TRL01': '翻訳者が見つかりません',
  'SFT01': 'シフトが見つかりません',
  'SFT02': 'シフトの作成に失敗しました',
  'SFT03': 'シフトの更新に失敗しました',
  'SFT04': 'シフトの削除に失敗しました',
  'SFT05': 'チェックインに失敗しました',
  'SFT06': 'チェックアウトに失敗しました',
  'SFT07': '休憩開始に失敗しました',
  'SFT08': '休憩終了に失敗しました',
  'SFT09': '既にチェックイン済みです',
  'SFT10': '既に休憩開始済みです',
  'SFT11': 'まだチェックインしていません',
  'SFT12': 'シフトはすでに終了しています',
  'SFT13': 'まだ休憩開始していません',
  'SFT14': 'まだ休憩終了していません',
  'SFT15': '既に休憩終了済みです',
  'SCH01': 'スケジュールが見つかりません',
  'SCH02': 'スケジュールの作成に失敗しました',
  'SCH03': 'スケジュールの更新に失敗しました',
  'SCH04': '作業者情報が見つかりません',
  'PRJ01': 'プロジェクトが見つかりません',
  'PRJ02': 'プロジェクトの作成に失敗しました',
  'PRJ03': 'プロジェクトの更新に失敗しました',
  'PRJ04': '進行中のプロジェクトがありません',
  'GPS01': '位置情報が見つかりません',
  'GPS02': '位置情報が無効です',
  'HOL01': '休日が見つかりません',
  'HOL02': '休日の作成に失敗しました',
  'HOL03': '休日の更新に失敗しました',
  'URF01': '機能的役割が見つかりません',
  'URF02': '機能的役割の作成に失敗しました',
  'URF03': '機能的役割の更新に失敗しました',
  'FNC01': '機能が見つかりません',
  'FNC02': '機能の作成に失敗しました',
  'FNC03': '機能の更新に失敗しました',
  'HST01': '履歴の取得に失敗しました',
  'HST02': '履歴の作成に失敗しました',
}
