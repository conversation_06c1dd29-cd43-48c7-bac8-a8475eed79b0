<script lang="ts" setup>
import {
  EyeOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';
import { Empty, message } from 'ant-design-vue';
import type {
  ColumnGroupType,
  ColumnType,
  TablePaginationConfig,
} from 'ant-design-vue/es/table';
import { FilterValue } from 'ant-design-vue/es/table/interface';
import { FileType } from 'ant-design-vue/es/upload/interface';
import dayjs from 'dayjs';
import _ from 'lodash';
import { cloneDeep, isEmpty, isEqual } from 'lodash';
import { usePagination } from 'vue-request';
import type {
  VendorChangedListItem,
  VendorItem,
  VendorLogItem,
} from '~@/api/company/vendor';
import {
  createVendor,
  deleteVendor,
  getOneVendor,
  getVendor,
  getVendorLogo,
  getVendorLogs,
  updateVendor,
} from '~@/api/company/vendor';
import { ResponseStatusEnum } from '~@/enums/response-status-enum';
import { ModalType, TimeLineColor } from '~@/enums/system-status-enum';
import logger from '~@/utils/logger';

type ColumnItemType<T> = ColumnGroupType<T> | ColumnType<T>;

interface Params {
  pageNum?: number;
  pageSize?: number;
  keyword?: string;
}

interface LogState {
  logId: string;
  pageSize: number;
  pageNum: number;
  hasMore: boolean;
}

interface VendorState {
  vendorId: string;
  pageSize: number;
  pageNum: number;
  hasMore: boolean;
  keyword?: string;
}

const initFormState: VendorItem = {
  logo: undefined,
  logoUrl: '',
  vendorId: '',
  vendorCode: '',
  vendorName: '',
  vendorSubName: undefined,
  corporateNumber: undefined,
  address: undefined,
  phoneNumber: undefined,
  email: undefined,
  contactPerson: {
    name: '',
    phoneNumber: '',
    email: '',
  },
  description: undefined,
  vendorInvoices: [],
};

const modalType = ref<ModalType>(ModalType.ADD);
const formRef = ref();
const uploadRef = ref();
const modalLoading = ref<boolean>(false);
const logLoading = ref<boolean>(false);
const invoiceLoading = ref<boolean>(false);
const invoiceData = ref<any[]>([]);
const openLog = ref<boolean>(false);
const openInvoice = ref<boolean>(false);
const invoiceRef = ref();
const { arrivedState: invoiceArrived } = useScroll(invoiceRef);
const logRef = ref();
const { arrivedState: logArrived } = useScroll(logRef);
const isOpenModal = ref<boolean>(false);
const { t } = useI18n();
const formState = reactive<VendorItem>({ ...cloneDeep(initFormState) });
const searchForm = ref<Params>({
  pageSize: 10,
  pageNum: 1,
});
const logState = ref<LogState>({
  logId: '',
  pageSize: 20,
  pageNum: 1,
  hasMore: true,
});
const logData = ref<VendorLogItem[]>([]);
const vendorState = ref<VendorState>({
  vendorId: '',
  pageSize: 20,
  pageNum: 1,
  hasMore: true,
});

const queryData = async (params?: Params) => {
  const { data } = await getVendor(params);
  return data;
};

const {
  data: dataSource,
  loading,
  refresh,
  total,
  current,
  run,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [searchForm.value],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
});

const pagination = computed(() => ({
  total: total.value ?? 10,
  current: current.value,
  pageSize: pageSize.value,
  showSizeChanger: true,
}));

const columns = computed<ColumnItemType<VendorItem>[]>(() => [
  { dataIndex: 'image', width: 120, align: 'center' },
  { dataIndex: 'vendor', width: 250 },
  { dataIndex: 'corporate', width: 200 },
  { dataIndex: 'divider', width: 2 },
  { dataIndex: 'representative', width: 250 },
  { dataIndex: 'phone', width: 250 },
  { dataIndex: 'invoice', width: 150, align: 'center' },
  { dataIndex: 'action', width: 150, align: 'center', fixed: 'right' },
]);

const onFinish = async () => {
  try {
    await formRef.value.validate();

    switch (modalType.value) {
      case ModalType.ADD:
        const create = await createVendor({
          vendorCode: formState.vendorCode,
          vendorName: formState.vendorName,
          vendorSubName: formState.vendorSubName,
          corporateNumber: formState.corporateNumber,
          address: formState.address,
          // phoneNumber: formState.phoneNumber,
          // email: formState.email,
          contactPerson: {
            name: formState.contactPerson.name,
            phoneNumber: formState.contactPerson.phoneNumber,
            email: formState.contactPerson.email,
          },
          logo: formState.logo,
          // description: formState.description,
        });
        if (create.status === ResponseStatusEnum.SUCCESS) {
          message.success(create.message);
        } else {
          message.error(create.message);
          return;
        }

        break;
      case ModalType.EDIT:
        const update = await updateVendor(formState.vendorId, {
          vendorCode: formState.vendorCode,
          vendorName: formState.vendorName,
          vendorSubName: formState.vendorSubName,
          corporateNumber: formState.corporateNumber,
          address: formState.address,
          // phoneNumber: formState.phoneNumber,
          // email: formState.email,
          contactPerson: {
            name: formState.contactPerson.name,
            phoneNumber: formState.contactPerson.phoneNumber,
            email: formState.contactPerson.email,
          },
          logo: formState.logo,
          // description: formState.description,
        });
        if (update.status === ResponseStatusEnum.SUCCESS) {
          message.success(update.message);
        } else {
          message.error(update.message);
          return;
        }

        break;
      default:
        break;
    }

    isOpenModal.value = false;
    onReset();
    refresh();
  } catch (error) {
    logger.error(error);
  }
};

const beforeUpload = (file: FileType) => {
  formState.logo = file;
  formState.logoUrl = URL.createObjectURL(file);
  return false;
};

const openModal = async (id: string, type: ModalType) => {
  switch (type) {
    case ModalType.ADD:
      modalType.value = type;
      isOpenModal.value = true;
      break;
    case ModalType.COPY:
    case ModalType.EDIT: {
      isOpenModal.value = true;
      modalLoading.value = true;
      modalType.value = type;

      const update = await getOneVendor(id);
      formState.logoUrl = update.data?.logoUrl && getVendorLogo(id);
      formState.vendorId = id;
      formState.vendorCode = update.data?.vendorCode ?? '';
      formState.vendorName = update.data?.vendorName ?? '';
      formState.vendorSubName = update.data?.vendorSubName ?? '';
      formState.corporateNumber = update.data?.corporateNumber ?? '';
      formState.address = update.data?.address ?? '';
      formState.phoneNumber = update.data?.phoneNumber ?? '';
      formState.email = update.data?.email ?? '';
      formState.contactPerson.name = update.data?.contactPerson?.name ?? '';
      formState.contactPerson.phoneNumber =
        update.data?.contactPerson?.phoneNumber ?? '';
      formState.contactPerson.email = update.data?.contactPerson?.email ?? '';
      formState.description = update.data?.description ?? '';
      formState.vendorInvoices = update.data?.vendorInvoices ?? [];

      modalLoading.value = false;
      break;
    }
    case ModalType.LOG: {
      logLoading.value = true;
      openLog.value = true;
      modalType.value = type;
      logState.value.pageNum = 1;

      const logs = await getVendorLogs(id, {
        pageSize: logState.value.pageSize,
        pageNum: logState.value.pageNum,
      });
      logData.value = logs.data?.entityChanges ?? [];
      logState.value.logId = id;
      logState.value.hasMore = logData.value.length === logState.value.pageSize;
      logLoading.value = false;
      break;
    }
    case ModalType.INVOICE: {
      invoiceLoading.value = true;
      openInvoice.value = true;
      modalType.value = type;
      vendorState.value.pageNum = 1;
      vendorState.value.keyword = '';

      const invoices = await getOneVendor(id, {
        pageSize: vendorState.value.pageSize,
        pageNum: vendorState.value.pageNum,
      });
      invoiceData.value = invoices.data?.vendorInvoices ?? [];
      invoiceLoading.value = false;
      vendorState.value.vendorId = id;
      vendorState.value.hasMore =
        invoiceData.value.length === vendorState.value.pageSize;
      break;
    }
    default:
      break;
  }
};

const onSearchInvoice = async () => {
  invoiceLoading.value = true;
  vendorState.value.pageNum = 1;

  const projects = await getOneVendor(vendorState.value.vendorId, {
    pageSize: vendorState.value.pageSize,
    pageNum: vendorState.value.pageNum,
    keyword: vendorState.value.keyword,
  });
  invoiceData.value = projects.data?.vendorInvoices ?? [];
  invoiceLoading.value = false;
  vendorState.value.hasMore =
    invoiceData.value.length === vendorState.value.pageSize;
};

const handleDeleteCategory = async (id: string) => {
  try {
    const del = await deleteVendor(id);
    if (del.status === ResponseStatusEnum.SUCCESS) {
      message.success(del.message);
    } else {
      message.error(del.message);
      return;
    }
  } catch (error) {
  } finally {
    refresh();
  }
};

const handleTableChange = (
  pagination: TablePaginationConfig,
  filters: Record<string, FilterValue>
) => {
  searchForm.value.pageSize = pagination.pageSize;
  searchForm.value.pageNum = pagination.current;
  run({ ...searchForm.value, ...filters });
};

const handlePaginationChange = (page: number, pageSize: number) => {
  searchForm.value.pageSize = pageSize;
  searchForm.value.pageNum = page;
  run(searchForm.value);
};

const onReset = () => {
  Object.assign(formState, cloneDeep(initFormState));
};

const renderTitle = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
    case ModalType.COPY:
      return _.startCase(_.toLower(`${t('button.add')} ${t('form.vendor')}`));
    case ModalType.EDIT:
      return `${t('button.edit')} ${t('form.vendor')}`;
    case ModalType.LOG:
      return t('log-vendor');
    default:
      return '';
  }
});

const renderOkConfirm = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
    case ModalType.COPY:
      return t('message.add-confirmation');
    case ModalType.EDIT:
      return t('message.edit-confirmation');
    default:
      return '';
  }
});

const onSearch = () => {
  handleTableChange(
    { pageSize: searchForm.value.pageSize ?? 10, current: 1 },
    {}
  );
};

const getTimeLineColor = computed(() => {
  return (action: keyof typeof TimeLineColor) => {
    return TimeLineColor[action];
  };
});

const getTimeLineTitle = computed(() => {
  return (field: string) => {
    switch (field.toLowerCase()) {
      case 'vendorcode':
        return t('form.code');
      case 'vendorname':
        return t('form.name');
      case 'vendorsubname':
        return t('form.sub-name');
      case 'description':
        return t('form.description');
      case 'corporatenumber':
        return t('form.corporate');
      case 'address':
        return t('form.address');
      case 'phonenumber':
        return t('form.phone');
      case 'email':
        return t('form.email');
      case 'lastmodifiedtime':
        return t('log.lastmodifiedtime');
      case 'lastmodifiedby':
        return t('log.lastmodifiedby');
      default:
        return field;
    }
  };
});

const getTimeLineValue = computed(() => {
  return (
    field: string,
    value: string | number | boolean | number[] | string[]
  ) => {
    switch (field.toLowerCase()) {
      case 'lastmodifiedtime': {
        if (!value) return `''`;
        return dayjs(value as string).format('YYYY-MM-DD HH:mm:ss');
      }
      default:
        if (!value) return `''`;
        return value;
    }
  };
});

const showChangedItem = computed(() => {
  return (changedItem: VendorChangedListItem) => {
    const valueBefore = isEmpty(changedItem.valueBefore)
      ? ''
      : changedItem.valueBefore;
    const valueAfter = isEmpty(changedItem.valueAfter)
      ? ''
      : changedItem.valueAfter;

    return !isEqual(valueBefore, valueAfter);
  };
});

const handleLogScroll = async () => {
  if (!logState.value.hasMore) return;
  logState.value.pageNum += 1;
  const logs = await getVendorLogs(logState.value.logId, {
    pageSize: logState.value.pageSize,
    pageNum: logState.value.pageNum,
  });
  const entityChanges = logs.data?.entityChanges ?? [];
  logData.value = [...logData.value, ...entityChanges];
  logState.value.hasMore = entityChanges.length === logState.value.pageSize;
};

watch(
  () => logArrived.bottom,
  async (value: boolean) => {
    if (value) await handleLogScroll();
  }
);

const handleInvoiceScroll = async () => {
  if (!vendorState.value.hasMore) return;
  vendorState.value.pageNum += 1;
  const invoices = await getOneVendor(vendorState.value.vendorId, {
    pageSize: vendorState.value.pageSize,
    pageNum: vendorState.value.pageNum,
  });
  const data = invoices.data?.vendorInvoices ?? [];
  invoiceData.value = [...invoiceData.value, ...data];
  vendorState.value.hasMore = data.length === vendorState.value.pageSize;
};

watch(
  () => invoiceArrived.bottom,
  async (value: boolean) => {
    if (value) await handleInvoiceScroll();
  }
);
</script>

<template>
  <page-container>
    <a-row
      :wrap="false"
      :gutter="[12, 12]"
      class="h-[calc(100vh-165px)] flex-col"
    >
      <a-col flex="none" span="24">
        <a-row :gutter="[12, 12]">
          <a-col span="24">
            <a-row :gutter="[12, 12]">
              <a-col>
                <a-button
                  class="flex flex-items-center"
                  type="primary"
                  @click="openModal('', ModalType.ADD)"
                >
                  <PlusOutlined />
                  {{ `${t('button.new')} ${t('form.vendor')}` }}
                </a-button>
              </a-col>
              <a-col>
                <a-input
                  v-model:value="searchForm.keyword"
                  :placeholder="t('search')"
                  style="width: 25rem"
                  allow-clear
                  @press-enter="onSearch"
                >
                  <template #prefix>
                    <SearchOutlined class="text-gray-500" />
                  </template>
                </a-input>
              </a-col>
            </a-row>
          </a-col>
          <a-col span="24">
            <a-table
              class="tableVendor"
              :scroll="{ x: 'max-content', y: 'calc(100vh - 265px)' }"
              :columns="columns"
              :data-source="dataSource?.items"
              :loading="loading"
              :pagination="false"
              row-key="vendorId"
              @change="handleTableChange"
              :show-header="false"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'image'">
                  <div class="flex items-center justify-center">
                    <img
                      v-if="record.logoUrl"
                      :src="getVendorLogo(record.vendorId)"
                      class="h-[80px] w-[80px] rounded-md"
                    />
                  </div>
                </template>
                <template v-if="column.dataIndex === 'vendor'">
                  <div class="flex flex-col justify-center gap-0.5 2xl:gap-2">
                    <span class="text-base font-bold">
                      {{ record.vendorName }}
                    </span>
                    <div>
                      <span class="text-gray-500"> {{ t('form.code') }}: </span>
                      <span class="font-medium">
                        {{ record.vendorCode }}
                      </span>
                    </div>
                    <div>
                      <span class="text-gray-500">
                        {{ t('form.other-name') }}:
                      </span>
                      <span class="font-medium">
                        {{ record.vendorSubName }}
                      </span>
                    </div>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'corporate'">
                  <div class="flex flex-col justify-center gap-0.5 2xl:gap-2">
                    <div class="flex flex-col">
                      <span class="text-gray-500">
                        {{ t('form.corporate-number') }}:
                      </span>
                      <span class="font-medium">
                        {{ record.corporateNumber }}
                      </span>
                    </div>
                    <div class="flex flex-col">
                      <span class="text-gray-500">
                        {{ t('form.tax-code') }}:
                      </span>
                      <span class="font-medium">
                        {{ record.corporateNumber }}
                      </span>
                    </div>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'representative'">
                  <div class="flex flex-col justify-center gap-0.5 2xl:gap-2">
                    <div class="flex flex-col">
                      <span class="text-gray-500 flex gap-1">
                        <img src="/icon/user.svg" />
                        {{ t('representative-information') }}:
                      </span>
                      <span class="font-medium">
                        {{ record.contactPerson?.name }}
                      </span>
                    </div>
                    <div class="flex flex-col">
                      <span class="text-gray-500 flex gap-1">
                        <img src="/icon/location.svg" />
                        {{ t('form.address') }}:
                      </span>
                      <span class="font-medium">{{ record.address }}</span>
                    </div>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'phone'">
                  <div class="flex flex-col justify-center gap-0.5 2xl:gap-2">
                    <div class="flex flex-col">
                      <span class="text-gray-500 flex gap-1">
                        <img src="/icon/phone.svg" />
                        {{ t('form.phone') }}:
                      </span>
                      <span class="font-medium">
                        {{ record.contactPerson?.phoneNumber }}
                      </span>
                    </div>
                    <div class="flex flex-col">
                      <span class="text-gray-500 flex gap-1">
                        <img src="/icon/email.svg" />
                        {{ t('form.email') }}:
                      </span>
                      <span class="font-medium text-ellipsis overflow-hidden">
                        {{ record.contactPerson?.email }}
                      </span>
                    </div>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'divider'">
                  <a-divider type="vertical" class="h-24 border-2" />
                </template>
                <template v-if="column.dataIndex === 'invoice'">
                  <a-button
                    class="flex items-center justify-center"
                    type="text"
                    @click="openModal(record.vendorId, ModalType.INVOICE)"
                    color="primary"
                    size="small"
                  >
                    <EyeOutlined class="text-lg" />
                    {{ t('button.invoice-list') }}
                  </a-button>
                </template>
                <template v-if="column.dataIndex === 'action'">
                  <div class="flex flex-justify-center gap-2">
                    <a-button
                      class="flex items-center bg-[#DEF0FF]"
                      @click="openModal(record.vendorId, ModalType.EDIT)"
                      size="small"
                    >
                      <img src="/icon/edit.svg" class="w-[20px]" />
                    </a-button>
                    <!-- <a-button
                  class="flex items-center justify-center"
                  type="text"
                  @click="openModal(record.vendorId, ModalType.COPY)"
                  color="orange"
                  size="small"
                >
                  <CopyOutlined />
                </a-button>
                <a-button
                  class="flex items-center justify-center"
                  type="text"
                  @click="openModal(record.vendorId, ModalType.LOG)"
                  color="warning"
                  size="small"
                >
                  <FileSearchOutlined />
                </a-button> -->
                    <a-popconfirm
                      :title="t('message.delete-confirmation')"
                      @confirm="() => handleDeleteCategory(record.vendorId)"
                    >
                      <a-button
                        class="flex items-center bg-[#DEF0FF]"
                        size="small"
                      >
                        <img src="/icon/delete.svg" class="w-[20px]" />
                      </a-button>
                    </a-popconfirm>
                  </div>
                </template>
              </template>
            </a-table>
          </a-col>
        </a-row>
      </a-col>
      <a-col flex="auto" span="24">
        <div class="h-full flex items-end">
          <a-row justify="space-between" class="mt-4 w-full">
            <a-col>
              <a-pagination
                class="pagination"
                :total="pagination.total"
                :current="pagination.current"
                :pageSize="pagination.pageSize"
                @change="handlePaginationChange"
              />
            </a-col>
            <a-col>
              <a-row :gutter="[12, 12]" justify="center" align="middle">
                <a-col>{{ t('show') }}</a-col>
                <a-col>
                  <a-pagination
                    class="pagination pagination-right"
                    :total="pagination.total"
                    :current="pagination.current"
                    :pageSize="pagination.pageSize"
                    showSizeChanger
                    :buildOptionText="(props: any) => props.value"
                    @change="handlePaginationChange"
                  />
                </a-col>
                <a-col>{{ t('entries') }}</a-col>
              </a-row>
            </a-col>
          </a-row>
        </div>
      </a-col>
    </a-row>

    <a-modal
      v-model:open="isOpenModal"
      width="800px"
      :footer="false"
      @cancel="onReset"
      :closable="false"
      :maskClosable="false"
    >
      <template #title>
        <div class="flex justify-center items-center">
          <a-typography-title :level="4" class="!text-[#256CB5]">
            {{ renderTitle }}
          </a-typography-title>
        </div>
      </template>
      <a-card border-style="none" :loading="modalLoading" class="card">
        <a-form
          ref="formRef"
          :model="formState"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
          autocomplete="off"
          @finish="onFinish"
        >
          <a-row :gutter="[12, 12]" class="flex-nowrap">
            <a-col flex="none">
              <div class="w-32 flex flex-col justify-center items-center gap-2">
                <div>
                  <a-form-item :label="t('form.logo')" name="logo" no-style>
                    <a-upload
                      name="logo"
                      list-type="picture-card"
                      :show-upload-list="false"
                      :before-upload="beforeUpload"
                    >
                      <img
                        v-if="formState.logoUrl"
                        :src="formState.logoUrl"
                        class="h-full w-full rounded-md"
                        ref="uploadRef"
                      />
                      <div v-else ref="uploadRef"><PlusOutlined /></div>
                    </a-upload>
                  </a-form-item>
                </div>
                <a-row
                  :gutter="[4, 4]"
                  class="w-full justify-center items-center"
                  v-if="formState.logoUrl"
                >
                  <a-col>
                    <a-button
                      class="flex items-center"
                      type="text"
                      size="small"
                      @click="uploadRef.click()"
                    >
                      <img src="/icon/refresh.svg" class="w-[20px]" />
                    </a-button>
                  </a-col>
                  <a-col>
                    <a-popconfirm
                      :title="t('message.delete-confirmation')"
                      @confirm="
                        () => {
                          formState.logoUrl = '';
                          formState.logo = undefined;
                        }
                      "
                    >
                      <a-button
                        class="flex items-center"
                        type="text"
                        size="small"
                      >
                        <img src="/icon/delete.svg" class="w-[20px]" />
                      </a-button>
                    </a-popconfirm>
                  </a-col>
                </a-row>
              </div>
            </a-col>
            <a-col flex="auto">
              <a-row :gutter="[12, 12]">
                <a-col span="24">
                  <a-form-item
                    :label="t('form.vendor-common-name')"
                    name="vendorName"
                    :rules="[{ required: true }]"
                  >
                    <a-input
                      v-model:value="formState.vendorName"
                      :placeholder="t('form.vendor-common-name')"
                    />
                  </a-form-item>
                </a-col>
                <a-col span="12">
                  <a-form-item
                    :label="t('form.code')"
                    name="vendorCode"
                    :rules="[{ required: true }]"
                  >
                    <a-input
                      v-model:value="formState.vendorCode"
                      :placeholder="t('form.code')"
                    />
                  </a-form-item>
                </a-col>
                <a-col span="12">
                  <a-form-item
                    :label="t('form.other-name')"
                    name="vendorSubName"
                  >
                    <a-input
                      v-model:value="formState.vendorSubName"
                      :placeholder="t('form.other-name')"
                    />
                  </a-form-item>
                </a-col>
                <a-col span="12">
                  <a-form-item
                    :label="t('form.corporate-number')"
                    name="corporateNumber"
                  >
                    <a-input
                      v-model:value="formState.corporateNumber"
                      :placeholder="t('form.corporate-number')"
                    />
                  </a-form-item>
                </a-col>
                <a-col span="12">
                  <a-form-item
                    :label="t('form.tax-code')"
                    name="corporateNumber"
                  >
                    <a-input
                      v-model:value="formState.corporateNumber"
                      :placeholder="t('form.tax-code')"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-col>
          </a-row>

          <a-divider />

          <div class="flex justify-center items-center">
            <a-typography-title :level="5">
              {{ t('representative-information') }}
            </a-typography-title>
          </div>

          <a-row :gutter="[12, 12]">
            <a-col span="12">
              <a-form-item
                :label="t('form.representative-name')"
                name="contactPerson.name"
              >
                <a-input
                  v-model:value="formState.contactPerson.name"
                  :placeholder="t('form.representative-name')"
                />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item :label="t('form.address')" name="address">
                <a-input
                  v-model:value="formState.address"
                  :placeholder="t('form.address')"
                />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item
                :label="t('form.phone-number')"
                name="contactPerson.phoneNumber"
              >
                <a-input
                  v-model:value="formState.contactPerson.phoneNumber"
                  :placeholder="t('form.phone-number')"
                />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item :label="t('form.email')" name="contactPerson.email">
                <a-input
                  v-model:value="formState.contactPerson.email"
                  :placeholder="t('form.email')"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row justify="end">
            <a-row :gutter="[4, 4]">
              <a-col>
                <a-button
                  @click="
                    () => {
                      isOpenModal = false;
                      onReset();
                    }
                  "
                >
                  {{ t('button.cancel') }}
                </a-button>
              </a-col>
              <a-col>
                <a-popconfirm :title="renderOkConfirm" @confirm="onFinish">
                  <a-button type="primary">{{ t('button.save') }}</a-button>
                </a-popconfirm>
              </a-col>
            </a-row>
          </a-row>
        </a-form>
      </a-card>
    </a-modal>

    <a-modal
      :title="t('invoice-list-of')"
      width="600px"
      v-model:open="openInvoice"
      :footer="false"
      @cancel="openInvoice = false"
    >
      <div>
        <a-input
          v-model:value="vendorState.keyword"
          :placeholder="t('search')"
          allow-clear
          @press-enter="onSearchInvoice"
        >
          <template #prefix>
            <SearchOutlined class="text-gray-500" />
          </template>
        </a-input>
      </div>
      <br />
      <a-card
        :loading="invoiceLoading"
        border-style="none"
        class="overflow-y-auto overflow-x-hidden h-[70vh] invoice-list"
        ref="invoiceRef"
      >
        <a-row :gutter="[12, 12]">
          <a-col span="24" v-for="item in invoiceData" :key="item.inputCostId">
            <a-row :gutter="[4, 4]" class="bg-[#FAF9F7] p-4 rounded-md">
              <a-col flex="none">
                <div class="w-20">
                  <img
                    v-if="item.logoUrl"
                    :src="item.logoUrl"
                    class="h-full w-full rounded-md"
                  />
                </div>
              </a-col>
              <a-col flex="auto">
                <a-row :gutter="[4, 4]">
                  <a-col flex="auto">
                    <a-typography-title :level="5">
                      {{ item.invoiceTitle }}
                    </a-typography-title>
                    <div>
                      <span class="text-gray-500">Code: </span>
                      <span class="font-medium">
                        {{ item.originalNumber }}
                      </span>
                    </div>
                    <div>
                      <span class="text-gray-500">Invoice number: </span>
                      <span class="font-medium">
                        {{ item.originalNumber }}
                      </span>
                    </div>
                  </a-col>
                  <a-col flex="none" class="flex justify-center items-center">
                    <a-button
                      size="small"
                      type="primary"
                      class="flex items-center justify-center"
                    >
                      <EyeOutlined />
                      {{ t('button.detail') }}
                    </a-button>
                  </a-col>
                </a-row>
              </a-col>
            </a-row>
          </a-col>
          <a-col span="24" v-if="invoiceData.length === 0">
            <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
          </a-col>
        </a-row>
        <div
          class="flex justify-center items-center"
          v-if="vendorState.hasMore"
        >
          <a-spin />
        </div>
      </a-card>
    </a-modal>

    <a-drawer
      :title="renderTitle"
      size="large"
      :open="openLog"
      @close="openLog = false"
      :body-style="{ padding: 0, overflow: 'hidden' }"
    >
      <a-card
        :loading="logLoading"
        class="overflow-auto h-full log-vendor"
        border-style="none"
        ref="logRef"
      >
        <a-timeline>
          <a-timeline-item
            v-for="item in logData"
            :key="item.auditLogId"
            :color="getTimeLineColor(item.action as keyof typeof TimeLineColor)"
          >
            <a-collapse ghost expandIconPosition="end">
              <a-collapse-panel key="1">
                <template #header>
                  <span color="blue">{{ item.modifiedUserName }}</span>
                  {{ t(`log.${item.action}`) }}
                  <div class="text-gray-400">
                    {{ dayjs(item.modifiedTime).format('YYYY-MM-DD HH:mm:ss') }}
                  </div>
                </template>
                <div
                  class="text-gray-400"
                  v-for="changedItem in item.changedList"
                  :key="changedItem.fieldName"
                >
                  <div
                    v-if="showChangedItem(changedItem)"
                    v-html="
                      t('log.changed', {
                        field: getTimeLineTitle(changedItem.fieldName),
                        oldValue: getTimeLineValue(
                          changedItem.fieldName,
                          changedItem.valueBefore
                        ),
                        newValue: getTimeLineValue(
                          changedItem.fieldName,
                          changedItem.valueAfter
                        ),
                      })
                    "
                  />
                </div>
              </a-collapse-panel>
            </a-collapse>
          </a-timeline-item>
        </a-timeline>
        <div class="flex justify-center items-center" v-if="logState.hasMore">
          <a-spin />
        </div>
      </a-card>
    </a-drawer>
  </page-container>
</template>

<style lang="less" scoped>
.log-vendor {
  :deep(.ant-collapse-header) {
    padding: 0;
  }
}
.invoice-list {
  :deep(.ant-card-body) {
    padding: 0;
  }
}
.card {
  :deep(.ant-upload) {
    margin: 0 !important;
  }
}
.tableVendor {
  :deep(.ant-table) {
    background: transparent;
  }
  :deep(table) {
    border-collapse: separate;
    border-spacing: 0 16px;
    margin-top: -16px;
    margin-right: 10px;
  }
  :deep(.ant-table-container) {
    padding-top: 16px;
  }
  :deep(.ant-table-tbody > tr) {
    background: #fff;
    box-shadow: 0px 2px 4px 0px #0000001a;
    border-radius: 8px;
  }
  :deep(.ant-table-tbody > tr > td:first-child) {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }
  :deep(.ant-table-tbody > tr > td:last-child) {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }
  :deep(.ant-table-tbody > tr > td) {
    background: #fff !important;
    transition: none;
  }
}
.pagination {
  :deep(.ant-pagination-item-active) {
    background: #f99649;
    border-color: #f99649;
    a {
      color: #fff;
    }
  }
  :deep(.ant-select-selector) {
    background: #f99649;
    border-color: #f99649 !important;
    .ant-select-selection-item {
      color: #fff;
    }
  }
  :deep(.ant-select-arrow) {
    color: #fff;
  }
}
.pagination-right {
  :deep(.ant-pagination-prev) {
    display: none;
  }
  :deep(.ant-pagination-next) {
    display: none;
  }
  :deep(.ant-pagination-item) {
    display: none;
  }
  :deep(.ant-pagination-options) {
    margin: 0;
  }
  :deep(.ant-pagination-jump-next) {
    display: none;
  }
  :deep(.ant-pagination-jump-prev) {
    display: none;
  }
}
</style>
