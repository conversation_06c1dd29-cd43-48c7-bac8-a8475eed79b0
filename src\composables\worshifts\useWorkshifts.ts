import type { WorkshiftItem } from '~@/api/company/work-shift'
import { getWorkshiftApi } from '~@/api/company/work-shift'

export function useWorkshifts() {
  const workshifts = ref<WorkshiftItem[]>()
  const loading = ref(true)
  const error = ref()
  const messageNotify = useMessage()

  const fetchWorkshifts = async (queryParams?: any) => {
    loading.value = true
    try {
      const { data, status, message } = await getWorkshiftApi(queryParams)
      if (status === 200) {
        workshifts.value = data?.items ?? []
      }
      else {
        messageNotify.error(message)
        error.value = message
      }
    }
    catch (error) {
    }
    finally {
      loading.value = false
    }
  }
  return {
    workshifts,
    fetchWorkshifts,
    loading,
    error,
  }
}
