<script setup lang="ts">
import type { UnwrapRef } from 'vue'
import type { AttendanceItem, AttendanceUpdateParams } from '~@/api/attendance'
import { getWorkingLocationDisplayText } from '~@/api/attendance'

import TimeAction from '~/pages/dashboard/components/time-action.vue'

const props = defineProps<{
  todayAttendaceData: AttendanceItem[]
  noteState: UnwrapRef<Record<string, string>>
}>()

const emit = defineEmits<{
  (event: 'onUpdateAttendance', employeeShiftId: string, params: AttendanceUpdateParams): void
  (event: 'onAddNotes', employeeShiftId: string): void
  (event: 'onSendAttendanceDailyRequest', employeeShiftId: string): void
  (event: 'attendanceDailyRequest', employeeShiftId: string): void
}>()

const timeActionComponentKey = ref(0)

function onUpdateAttendance(employeeShiftId: string, params: AttendanceUpdateParams) {
  emit('onUpdateAttendance', employeeShiftId, params)
}

function attendanceDailyRequest(employeeShiftId: string) {
  emit('attendanceDailyRequest', employeeShiftId)
}

const noteState = ref<Record<string, string>>(props.noteState)

const { t } = useI18n()

watch(() => props.todayAttendaceData, (_) => {
  timeActionComponentKey.value++
}, { deep: true })
</script>

<template>
  <!-- Attendence detail start -->
  <div class="xl:col-span-2 shadow-lg rounded-lg bg-white">
    <!-- Trường hợp todayAttendaceData không có dữ liệu -->
    <template v-if="!todayAttendaceData.length">
      <div class="overflow-y-scroll snap-y snap-mandatory h-[15rem] w-full">
        <div
          class="snap-start w-full"
        >
          <div class="flex flex-col gap-y-[8px] items-center xl:p-[16px]">
            <!-- Title section  -->
            <div class="flex xl:flex-self-start">
              <span style="font-size: 1.25rem; line-height: 1.8rem;">
                {{ t('dashboard.attendance.title') }}
              </span>
            </div>
            <!-- Content section  -->
            <div class="flex flex-col xl:w-full xl:flex-row xl:justify-between">
              <div class="flex items-center">
                <a-typography-text class="detail-title">
                  {{ `${t("table.checkIn")}: ` }}
                </a-typography-text>
                <TimeAction
                  :key="timeActionComponentKey"
                  value-one="00:00:00"
                  value-two="00:00:00"
                  type="checkInOut"
                />
              </div>
              <div class="flex">
                <div>
                  <a-typography-text class="detail-title">
                    {{ t("table.breakInOut") }}:
                  </a-typography-text>
                </div>
                <div class="flex flex-col items-center">
                  <div class="flex items-center">
                    <TimeAction
                      :key="timeActionComponentKey"
                      value-one="00:00:00"
                      value-two="00:00:00"
                      type="breakInOut_1"
                    />
                  </div>
                  <div class="flex items-center">
                    <div class="xl:hidden">
                      <TimeAction
                        :key="timeActionComponentKey"
                        value-one="00:00:00"
                        value-two="00:00:00"
                        type="breakInOut_2"
                      />
                    </div>
                  </div>
                  <div class="flex items-center">
                    <div class="xl:hidden">
                      <TimeAction
                        :key="timeActionComponentKey"
                        value-one="00:00:00"
                        value-two="00:00:00"
                        type="breakInOut_3"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex gap-x-[2px] xl:gap-x-16 xl:w-full xl:justify-between">
              <div>
                <a-typography-text class="detail-title">
                  {{ t("totalWorkTime") }}:
                </a-typography-text>
                <span style="font-size: 1rem;">
                  00.00
                </span>
              </div>
              <div class="flex">
                <div class="hidden xl:flex">
                  <TimeAction
                    :key="timeActionComponentKey"
                    value-one="00:00:00"
                    value-two="00:00:00"
                    type="breakInOut_2"
                  />
                </div>
              </div>
            </div>
            <div class="flex justify-center xl:w-full xl:justify-between">
              <div class="flex justify-center">
                <a-typography-text class="detail-title">
                  {{ t("totalOvertime") }}:
                </a-typography-text>
                <span style="font-size: 1rem;">
                  00.00
                </span>
              </div>
              <div class="flex">
                <div class="hidden xl:flex">
                  <TimeAction
                    :key="timeActionComponentKey"
                    value-one="00:00:00"
                    value-two="00:00:00"
                    type="breakInOut_3"
                  />
                </div>
              </div>
            </div>
            <div class="flex flex-col gap-y-[8px] xl:w-full xl:flex-row xl:justify-between">
              <div class="flex">
                <a-typography-text class="detail-title w-10">
                  {{ `${t('Note')}: ` }}
                </a-typography-text>
                <div class="flex">
                  <div class="border-y-1 border-l-1 rounded-l">
                    <a-input
                      class="2xl:w-84 w-40"
                      :bordered="false"
                      :placeholder="t('dashboard.workplace.attendance.textArea')"
                    />
                  </div>
                  <div class="flex items-center border-y-1 border-r-1 p-2 rounded-r">
                    <CarbonPlus />
                  </div>
                </div>
              </div>
              <a-button
                type="primary"
              >
                {{ t("button.requestApproval") }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- Trường hợp todayAttendaceData có dữ liệu -->
    <template v-else>
      <div class="overflow-y-scroll snap-y snap-mandatory h-[15rem] w-full">
        <template v-for="item in todayAttendaceData" :key="item.employeeShiftId">
          <div class="snap-start w-full">
            <div class="flex flex-col gap-y-[8px] items-center xl:px-[14px] xl:py-[28px]">
              <!-- Title section  -->
              <div class="flex xl:flex-self-start">
                <span style="font-size: 1.25rem; line-height: 1.8rem;">
                  {{ item.projectName }}
                </span>
              </div>
              <div class="xl:flex-self-start">
                <span class="text-[1rem] font-normal text-[#74797A] ">
                  {{ getWorkingLocationDisplayText(item.workingLocation, t) }}
                </span>
              </div>
              <!-- Content section  -->
              <div class="flex flex-col xl:w-full xl:flex-row xl:justify-between">
                <div class="flex items-center">
                  <a-typography-text class="detail-title">
                    {{ `${t("table.checkInOut")}: ` }}
                  </a-typography-text>
                  <TimeAction
                    :key="timeActionComponentKey"
                    :employee-shift-id="item.employeeShiftId"
                    :value-one="item.checkInTime ?? '00:00:00'"
                    :value-two="item.checkOutTime ?? '00:00:00'"
                    type="checkInOut"
                    @on-update-attendance="onUpdateAttendance"
                  />
                </div>
                <div class="flex">
                  <div>
                    <a-typography-text class="detail-title">
                      {{ t("table.breakInOut") }}:
                    </a-typography-text>
                  </div>
                  <div class="flex flex-col items-center">
                    <div class="flex items-center">
                      <TimeAction
                        :key="timeActionComponentKey"
                        :employee-shift-id="item.employeeShiftId"
                        :value-one="item.breakList?.[0]?.breakInTime ?? '00:00:00'"
                        :value-two="item.breakList?.[0]?.breakOutTime ?? '00:00:00'"
                        :break-item="item.breakList?.[0]"
                        :break-list="item.breakList"
                        type="breakInOut_1"
                        @on-update-attendance="onUpdateAttendance"
                      />
                    </div>
                    <div class="flex items-center">
                      <div class="xl:hidden">
                        <TimeAction
                          :key="timeActionComponentKey"
                          :employee-shift-id="item.employeeShiftId"
                          :value-one="item.breakList?.[1]?.breakInTime ?? '00:00:00'"
                          :value-two="item.breakList?.[1]?.breakOutTime ?? '00:00:00'"
                          type="breakInOut_2"
                          :break-item="item.breakList?.[1]"
                          :break-list="item.breakList"
                          @on-update-attendance="onUpdateAttendance"
                        />
                      </div>
                    </div>
                    <div class="flex items-center">
                      <div class="xl:hidden">
                        <TimeAction
                          :key="timeActionComponentKey"
                          :employee-shift-id="item.employeeShiftId"
                          :value-one="item.breakList?.[2]?.breakInTime ?? '00:00:00'"
                          :value-two="item.breakList?.[2]?.breakOutTime ?? '00:00:00'"
                          type="breakInOut_3"
                          :break-item="item.breakList?.[2]"
                          :break-list="item.breakList"
                          @on-update-attendance="onUpdateAttendance"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex gap-x-[2px] xl:gap-x-16 xl:w-full xl:justify-between">
                <div>
                  <a-typography-text class="detail-title">
                    {{ t("totalWorkTime") }}:
                  </a-typography-text>
                  <span style="font-size: 1rem;">
                    00.00
                  </span>
                </div>
                <div class="flex">
                  <div class="hidden xl:flex">
                    <TimeAction
                      :key="timeActionComponentKey"
                      :employee-shift-id="item.employeeShiftId"
                      :value-one="item.breakList?.[1]?.breakInTime ?? '00:00:00'"
                      :value-two="item.breakList?.[1]?.breakOutTime ?? '00:00:00'"
                      type="breakInOut_2"
                      :break-item="item.breakList?.[1]"
                      :break-list="item.breakList"
                      @on-update-attendance="onUpdateAttendance"
                    />
                  </div>
                </div>
              </div>
              <div class="flex justify-center xl:w-full xl:justify-between">
                <div class="flex justify-center">
                  <a-typography-text class="detail-title">
                    {{ t("totalOvertime") }}:
                  </a-typography-text>
                  <span style="font-size: 1rem;">
                    00.00
                  </span>
                </div>
                <div class="flex">
                  <div class="hidden xl:flex">
                    <TimeAction
                      :key="timeActionComponentKey"
                      :employee-shift-id="item.employeeShiftId"
                      :value-one="item.breakList?.[2]?.breakInTime ?? '00:00:00'"
                      :value-two="item.breakList?.[2]?.breakOutTime ?? '00:00:00'"
                      type="breakInOut_3"
                      :break-item="item.breakList?.[2]"
                      :break-list="item.breakList"
                      @on-update-attendance="onUpdateAttendance"
                    />
                  </div>
                </div>
              </div>
              <div class="flex flex-col gap-y-[8px] xl:w-full xl:flex-row xl:justify-between">
                <div class="flex">
                  <a-typography-text class="detail-title w-10">
                    {{ `${t('Note')}: ` }}
                  </a-typography-text>
                  <div class="flex">
                    <div class="border-y-1 border-l-1 rounded-l">
                      <a-input
                        v-model:value="noteState[item.employeeShiftId ?? '']"
                        class="2xl:w-84 w-40"
                        :bordered="false"
                        :placeholder="t('dashboard.workplace.attendance.textArea')"
                        @press-enter="onUpdateAttendance(item.employeeShiftId ?? '', { description: noteState[item.employeeShiftId ?? ''] })"
                      />
                    </div>
                    <div
                      class="flex items-center border-y-1 border-r-1 p-2 rounded-r cursor-pointer"
                      @click="onUpdateAttendance(item.employeeShiftId ?? '', { description: noteState[item.employeeShiftId ?? ''] })"
                    >
                      <CarbonPlus />
                    </div>
                  </div>
                </div>
                <a-button
                  :disabled="item.isRequested ?? false"
                  type="primary"
                  @click="attendanceDailyRequest(item.employeeShiftId ?? '')"
                >
                  {{ t("button.requestApproval") }}
                </a-button>
              </div>
            </div>
          </div>
        </template>
      </div>
    </template>
  </div>
  <!-- Attendence detail end -->
</template>
