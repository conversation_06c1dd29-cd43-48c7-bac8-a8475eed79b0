<script lang="ts" setup>
import type { UnwrapRef } from 'vue'
import { computed, reactive, ref, watch } from 'vue'
import {
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  InboxOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue'
import type { UploadFile } from 'ant-design-vue'
import { usePagination } from 'vue-request'
import type { ColumnsType } from 'ant-design-vue/es/table'
import type { SelectValue } from 'ant-design-vue/es/select'
import { deleteInputCostImageApi, updateInputCostApi } from '~@/api/invoice'
import type { InputCost, InputCostItemPutParams } from '~@/api/invoice'
import type { ItemFilterRequest } from '~@/api/item'
import { getItemListApi } from '~@/api/item'
import { fetchInputCostImage } from '~@/api/company/input-cost'
import type { EntryTypeItem } from '~@/api/company/entry-type'
import { getEntryType } from '~@/api/company/entry-type'
import type { PaymentTypeItem } from '~@/api/company/payment-type'
import { getPaymentType } from '~@/api/company/payment-type'
import { getProjectComboApi } from '~@/api/company/project'
import { getVendor } from '~@/api/company/vendor'
import type { GetVendorParams } from '~@/api/company/vendor'
import type { QueryParams } from '~@/api/common-params'
import type { InputCostItem, InputCostItemParams, InputCostItemQuery } from '~@/api/input-cost-item'
import { deleteInputCostItemApi, getInputCostItemListApi, updateInputCostItemApi } from '~@/api/input-cost-item'
import { getConstructionByProjectIdApi } from '~@/api/construction'

// Import your project and vendor search APIs

// Props and Emits
const props = defineProps({
  visible: {
    type: Boolean,
    default: true,
  },
  invoice: {
    type: Object as () => InputCost,
    required: true,
  },
})

const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void
  (event: 'refreshInputCost'): void
}>()

const isVisible = ref(props.visible)
const saving = ref(false)

// State
const currentImageIndex = ref(0)
const orgId = useOrg()
const { t } = useI18n()
const messageNotify = useMessage()

const inputCostItems = ref<InputCostItem[]>([])
const fileList = ref<UploadFile[] | undefined>()
const invoice = useVModel(props, 'invoice', emit)

// const currentImage = computed(() => thumbnails.value[currentImageIndex.value])
const searchText = ref('')
const editingKey = ref('')
const showUploadModal = ref(false)
const inputCostItemModalVisiable = ref(false)
const isInputCostItemEdit = ref(false)

// State for images
const previewVisible = ref(false)
const previewImage = ref('')
const previewTitle = ref('')

const entryTypeOptions = ref<EntryTypeItem[]>([])
// Add these to your existing script setup
const isEditing = ref(false)
const editableInvoice = ref<Partial<InputCost>>({})

// Payment and entry types (add actual values from your system)
const paymentTypeOptions = ref<PaymentTypeItem[]>([])

// Projects and vendors state
interface ProjectType {
  label: string
  value: string
}

interface VendorType {
  label: string
  value: string
}
const projectOptions = ref<ProjectType[]>([])
const vendorOptons = ref<VendorType[]>([])
const constructionOptions = ref<ProjectType[]>([])

const currentImage = computed(() => {
  if (!props.invoice.imageUrls?.length)
    return ''
  const currentUrl = props.invoice.imageUrls[currentImageIndex.value]
  return fetchInputCostImage(currentUrl, orgId.value ?? '')
})

// Table columns
const columns = reactive<ColumnsType<InputCostItem>>([
  {
    title: '#',
    dataIndex: 'index',
    key: 'index',
    width: 50,
    align: 'center',
    customRender: ({ index }) => index + 1,
  },
  {
    title: t('item-name'),
    dataIndex: 'itemName',
    key: 'itemName',
  },
  {
    title: t('unit'),
    dataIndex: 'unit',
    key: 'unit',
    sorter: (a: any, b: any) => a.unit - b.unit,
  },
  {
    title: t('quantity'),
    dataIndex: 'quantity',
    key: 'quantity',
  },
  {
    title: t('price'),
    dataIndex: 'price',
    key: 'price',
    sorter: (a: any, b: any) => a.price - b.price,
  },
  {
    title: t('tax-rate'),
    dataIndex: 'taxRate',
    key: 'taxRate',
  },
  {
    title: t('action'),
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 100,
    align: 'center',
  },
])

// Filtered items based on search
const filteredItems = computed(() => {
  if (!searchText.value)
    return inputCostItems.value
  const searchLower = searchText.value.toLowerCase()
  return inputCostItems.value.filter(item =>
    item.itemName?.toLowerCase().includes(searchLower)
      || item.vendorName?.toLowerCase().includes(searchLower),
  )
})

// Editable data store
const editableData: UnwrapRef<Record<string, InputCostItem>> = reactive({})

// New item form
const inputCostItemFormState = reactive<InputCostItemPutParams>({
  transactionDate: '',
  item: {
    itemId: '',
  },
  unit: '',
  quantity: 0,
  price: 0,
  taxRate: 0,
  totalNonTaxed: 0,
  totalTaxed: 0,
  description: '',
  isDeleted: false,
})

function refreshInputCostItem() {
  inputCostItemFormState.item.itemId = ''
  inputCostItemFormState.price = undefined
  inputCostItemFormState.quantity = undefined
  inputCostItemFormState.taxRate = undefined
  inputCostItemFormState.totalNonTaxed = undefined
  inputCostItemFormState.totalTaxed = undefined
  inputCostItemFormState.description = undefined
  inputCostItemFormState.transactionDate = undefined
  inputCostItemFormState.unit = undefined
}
function handleCancel() {
  previewVisible.value = false
}

function selectImage(index: number) {
  currentImageIndex.value = index
}

function handleModalCancel() {
  emit('update:visible', false)
}

// function handleSave() {
//   handleCancel()

//   // Show success messageNotify
//   messageNotify.success('Invoice updated successfully')
// }

// Table row editing
function handleEditRow(record: InputCostItem) {
  isInputCostItemEdit.value = true
  inputCostItemModalVisiable.value = true
  inputCostItemFormState.description = record.description ?? ''
  inputCostItemFormState.item.itemId = record.itemId ?? ''
  inputCostItemFormState.price = record.price ?? undefined
  inputCostItemFormState.quantity = record.quantity ?? undefined
  inputCostItemFormState.taxRate = record.taxRate ?? undefined
  inputCostItemFormState.totalNonTaxed = record.totalNonTaxed ?? undefined
  inputCostItemFormState.totalTaxed = record.totalTaxed ?? undefined
  inputCostItemFormState.transactionDate = record.transactionDate ?? ''
  inputCostItemFormState.unit = record.unit ?? ''
  inputCostItemFormState.inputCostItemId = record.inputCostItemId ?? ''
}

function cancelEdit() {
  editingKey.value = ''
}

async function deleteInputCostItem(inputCostItemId: string) {
  try {
    const { status, message } = await deleteInputCostItemApi(inputCostItemId)
    if (status === 200)
      messageNotify.success(message)

    else
      messageNotify.error(message)
  }
  catch (error) {
    messageNotify.error('Failed to delete item')
  }
}

async function confirmDelete(inputCostItemId?: string) {
  if (!inputCostItemId)
    return
  await deleteInputCostItem(inputCostItemId)
  await getInputCostItemList()
}

async function uploadImage(): Promise<InputCost | undefined> {
  if (!props.invoice.inputCostId) {
    messageNotify.error('Input cost ID is missing')
    return
  }
  if (fileList.value?.length === 0 || !fileList.value) {
    messageNotify.warning('Please upload an image')
    return
  }

  const formData = new FormData()

  fileList.value.forEach((file) => {
    if (file?.originFileObj)
      formData.append('Images', file.originFileObj)
  })

  formData.append('InputCostItems', JSON.stringify([]))
  await updateInputCost(props.invoice.inputCostId, formData)
  showUploadModal.value = false
  // const { data, status, message } = await updateInputCostApi(props.invoice.inputCostId, formData)
  // if (status === 200) {
  //   messageNotify.success(message)
  //   showUploadModal.value = false
  //   return data ?? undefined
  //   // emit('update:visible', false)
  // }
  // else {
  //   messageNotify.error(message)
  // }
}

async function handleUpload() {
  const currentInvoice: InputCost | undefined = await uploadImage()
  initialFileList(currentInvoice?.imageUrls ?? [])
}

// Add new item
function calculateNewItemPrice() {
  if (!inputCostItemFormState.price || !inputCostItemFormState.quantity || !inputCostItemFormState.taxRate)
    return
  const subtotal = inputCostItemFormState.price * inputCostItemFormState.quantity
  inputCostItemFormState.totalTaxed = subtotal * (inputCostItemFormState.taxRate / 100)
  inputCostItemFormState.totalNonTaxed = subtotal
}

function beforeUpload(file: File) {
  if (file?.size / 1024 / 1024 > 1) {
    messageNotify.error('Image must be smaller than 1MB')
    return false
  }
  return false
}

// async function createInputCostItem(inputCostItem: InputCostItemPutParams) {
//   if (isLoading.value || !props.invoice.inputCostId)
//     return
//   isLoading.value = true
//   const params: InputCostPutParams = {
//     inputCostItems: [inputCostItem],
//   }
//   const { status, data, message } = await updateInputCostApi(props.invoice.inputCostId, params)
//   if (status === 200) {
//     messageNotify.success(message)
//     emit('refreshInputCost')
//     refreshInvoices(data as InputCost)
//   }
//   else {
//     messageNotify.error(message)
//   }
//   isLoading.value = false
// }

async function updateInputCost(inputCostId: string, formData: FormData) {
  try {
    const { data, status, message } = await updateInputCostApi(inputCostId, formData)
    if (status === 200) {
      messageNotify.success(message)
      return data ?? undefined
      // emit('update:visible', false)
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (error) {
    messageNotify.error('Failed to create item')
  }
}

async function updateInputCostItem(inputCostItemId: string, inputCostItem: InputCostItemParams) {
  try {
    const { status, message } = await updateInputCostItemApi(inputCostItemId, inputCostItem)
    if (status === 200)
      messageNotify.success(message)

    else
      messageNotify.error(message)
  }
  catch (error) {
    messageNotify.error('Failed to update item')
  }
}

async function handleUpdateInputCostItem() {
  const inputCostItemId = inputCostItemFormState.inputCostItemId
  if (!inputCostItemId)
    return
  const inputCostItem: InputCostItemParams = {
    transactionDate: inputCostItemFormState.transactionDate ?? '',
    itemId: inputCostItemFormState.item.itemId,
    unit: inputCostItemFormState.unit ?? '',
    quantity: inputCostItemFormState.quantity ?? 0,
    price: inputCostItemFormState.price ?? 0,
    taxRate: inputCostItemFormState.taxRate ?? 0,
    totalNonTaxed: inputCostItemFormState.totalNonTaxed ?? 0,
    totalTaxed: inputCostItemFormState.totalTaxed ?? 0,
    description: inputCostItemFormState.description ?? '',
  }
  await updateInputCostItem(inputCostItemId, inputCostItem)
  await getInputCostItemList()
}

async function handleCreateInputCostItem() {
  // Validate form
  if (!inputCostItemFormState.item?.itemId) {
    messageNotify.error('Please fill in all required fields')
    return
  }
  if (!props.invoice.inputCostId) {
    messageNotify.error('Input cost ID is missing')
    return
  }
  // // Create new item
  const newItemObject: InputCostItemPutParams = {
    item: {
      itemId: inputCostItemFormState.item.itemId,
    },
    unit: inputCostItemFormState.unit,
    quantity: inputCostItemFormState.quantity,
    taxRate: inputCostItemFormState.taxRate,
    totalNonTaxed: inputCostItemFormState.totalNonTaxed,
    totalTaxed: inputCostItemFormState.totalTaxed,
    price: inputCostItemFormState.price,
    description: inputCostItemFormState.description,
    transactionDate: inputCostItemFormState.transactionDate,
    isDeleted: false,
  }
  const formData = new FormData()
  formData.append('InputCostItems', JSON.stringify([newItemObject]))
  const inputCostId = props.invoice.inputCostId
  if (!inputCostId) {
    messageNotify.error('Input cost ID is missing')
    return
  }
  await updateInputCost(inputCostId, formData)
  await getInputCostItemList()
  refreshInputCostItem()
}

const itemFilters = ref<ItemFilterRequest>({
  pageNum: 1,
  pageSize: 100,
})

async function queryItems(itemFilters: ItemFilterRequest) {
  const response = await getItemListApi(itemFilters)
  return response.data
}

const {
  data: itemsData,
} = usePagination(
  queryItems,
  {
    defaultParams: [itemFilters.value],
    pagination: {
      currentKey: 'pageNum',
      pageSizeKey: 'pageSize',
      totalKey: 'totalRecords',
    },
  },
)

const items = computed(() => itemsData.value?.items ?? [])

async function saveEdit(inputCostItemId?: string, inputCostItem?: InputCostItem) {
  if (!inputCostItemId || !inputCostItem || !props.invoice.inputCostId)
    return
  const inputCostItemParams: InputCostItemPutParams = {
    inputCostItemId,
    item: {
      itemId: editableData[inputCostItemId].itemId ?? '',
    },
    unit: editableData[inputCostItemId]?.unit ?? '',
    quantity: editableData[inputCostItemId]?.quantity ?? 0,
    price: editableData[inputCostItemId]?.price ?? 0,
    taxRate: editableData[inputCostItemId]?.taxRate ?? 0,
    totalNonTaxed: editableData[inputCostItemId]?.totalNonTaxed ?? 0,
    totalTaxed: editableData[inputCostItemId]?.totalTaxed ?? 0,
    description: editableData[inputCostItemId]?.description ?? '',
    transactionDate: editableData[inputCostItemId]?.transactionDate ?? '',
    isDeleted: false,
  }

  const formData = new FormData()
  formData.append('InputCostItems', JSON.stringify([inputCostItemParams]))

  await updateInputCost(props.invoice.inputCostId, formData)
  await getInputCostItemList()
  cancelEdit()

  // const { data, status } = await updateInputCostApi(props.invoice.inputCostId, formData)
  // try {
  //   if (status !== ResponseStatusEnum.SUCCESS) {
  //     messageNotify.error('Update item failed')
  //   }
  //   else {
  //     messageNotify.success('Update item success')
  //     refreshInvoices(data as InputCost)
  //     cancelEdit()
  //   }
  // }
  // catch (error) {
  //   messageNotify.error('Failed to update item')
  // }
}

function initialFileList(imageUrls: string[] | undefined) {
  if (imageUrls) {
    fileList.value = imageUrls.map((url, index) => ({
      uid: index.toString(),
      name: url,
      status: 'done',
      url: fetchInputCostImage(url, orgId.value ?? ''),
    }))
  }
  else {
    fileList.value = []
  }
}

// Initialize calculation for new item
calculateNewItemPrice()

async function handleDeleteImage(imageUrl: string) {
  if (!props.invoice.inputCostId) {
    messageNotify.error('Input cost ID is missing')
    return
  }

  try {
    const { status, message } = await deleteInputCostImageApi(props.invoice.inputCostId, imageUrl)
    if (status === 200) {
      messageNotify.success(message)
      // Update fileList
      fileList.value = fileList.value?.filter(file => file.url !== fetchInputCostImage(imageUrl, orgId.value ?? ''))
      emit('refreshInputCost')
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (error) {
    messageNotify.error('Failed to delete image')
  }
}

async function fetchEntryType() {
  try {
    const { data, status, message } = await getEntryType()
    if (status === 200)
      entryTypeOptions.value = data?.entryTypes ?? []

    else
      messageNotify.error(message)
  }
  catch (error) {
    messageNotify.error('Failed to fetch entry types')
  }
}

async function fetchPaymentType() {
  try {
    const { data, status, message } = await getPaymentType()
    if (status === 200)
      paymentTypeOptions.value = data?.items ?? []

    else
      messageNotify.error(message)
  }
  catch (error) {
    messageNotify.error('Failed to fetch payment types')
  }
}

async function fetchProjects() {
  try {
    const params: QueryParams = {
      pageNum: 1,
      pageSize: 100,
    }
    const { data, status, message } = await getProjectComboApi(params)
    if (status === 200) {
      projectOptions.value = (data?.items ?? []).map(item => ({
        label: item.name,
        value: item.id,
      }))
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (error) {
    messageNotify.error('Failed to fetch projects')
  }
}

async function fetchVendors() {
  try {
    const params: GetVendorParams = {
      pageNum: 1,
      pageSize: 100,
    }
    const { data, status, message } = await getVendor(params)
    if (status === 200) {
      vendorOptons.value = (data?.items ?? []).map(item => ({
        label: item.vendorName,
        value: item.vendorId,
      }))
    }

    else { messageNotify.error(message) }
  }
  catch (error) {
    messageNotify.error('Failed to fetch vendors')
  }
}

async function getInputCostItemList() {
  try {
    const params: InputCostItemQuery = {
      inputCostId: props.invoice.inputCostId,
      pageNum: 1,
      pageSize: 100,
    }
    const { data, status, message } = await getInputCostItemListApi(params)
    if (status === 200)
      inputCostItems.value = data?.items ?? [] as InputCostItem[]

    else
      messageNotify.error(message)
  }
  catch (error) {
    messageNotify.error('Failed to fetch input cost items')
  }
}

async function getConstructionByProjectId(projectId: string) {
  try {
    const { data, status, message } = await getConstructionByProjectIdApi(projectId)
    if (status === 200) {
      constructionOptions.value = (data?.constructions ?? []).map(item => ({
        label: item.constructionName,
        value: item.constructionId,
      }))
    }
    else { messageNotify.error(message) }
  }
  catch (error) {
    messageNotify.error('Failed to fetch construction by project ID')
  }
}

async function onProjectChange(projectId?: string) {
  if (!projectId) {
    constructionOptions.value = []
    editableInvoice.value.constructionId = ''
    return
  }
  await getConstructionByProjectId(projectId)
  if (constructionOptions.value.length > 0)
    editableInvoice.value.constructionId = constructionOptions.value[0].value
}

watch(
  () => props?.invoice,
  (newVal) => {
    initialFileList(newVal?.imageUrls)
    getInputCostItemList()
  },
  { deep: true, immediate: true },
)

// Watch for changes in visible prop
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // Reset states when modal opens
      editingKey.value = ''
      searchText.value = ''
      isVisible.value = newVal
    }
  },
)

onMounted(() => {
  fetchEntryType()
  fetchPaymentType()
  fetchProjects()
  fetchVendors()
})

function startEditing() {
  editableInvoice.value = { ...props.invoice }
  isEditing.value = true
}

function cancelEditing() {
  editableInvoice.value = {}
  isEditing.value = false
}

async function saveChanges() {
  if (!props.invoice.inputCostId) {
    messageNotify.error('Input cost ID is missing')
    return
  }

  saving.value = true
  const formData = new FormData()
  formData.append('Title', editableInvoice.value.title || '')
  formData.append('IssueDate', editableInvoice.value.issueDate || '')
  formData.append('PaymentDate', editableInvoice.value.paymentDate || '')
  formData.append('OriginalNumber', editableInvoice.value.originalNumber || '')
  formData.append('EntryTypeId', editableInvoice.value.entryTypeId || '')
  formData.append('TotalAmount', editableInvoice.value.totalAmount?.toString() || '')
  formData.append('PaymentTypeId', editableInvoice.value.paymentTypeId || '')
  formData.append('Description', editableInvoice.value.description || '')
  formData.append('VendorId', editableInvoice.value.vendorId || '')
  formData.append('ConstructionId', editableInvoice.value.constructionId || '')

  // Call your API to update the invoice
  const data = await updateInputCost(props.invoice.inputCostId, formData)
  invoice.value = data ?? invoice.value
  isEditing.value = false
  saving.value = false

  // const { status, message } = await updateInputCostApi(props.invoice.inputCostId!, formData)

  // if (status === 200) {
  //   messageNotify.success(message)
  //   emit('refreshInputCost')
  //   isEditing.value = false
  // }
  // else {
  //   messageNotify.error(message)
  // }}
}
</script>

<template>
  <a-modal
    v-model:visible="isVisible"
    width="1050px"
    :footer="null"
    class="invoice-detail-modal"
    @cancel="handleModalCancel"
  >
    <template #title>
      <span class="text-lg font-semibold">
        {{ t('invoice-detail') }}
      </span>
    </template>
    <div class="grid grid-cols-1 md:grid-cols-7 gap-6">
      <!-- Left column - Image and thumbnails -->
      <div class="space-y-4 col-span-3">
        <!-- Main Image Display -->
        <div class="bg-gray-100 h-80 flex items-center justify-center border rounded overflow-hidden">
          <a-avatar
            v-if="currentImage"
            :src="currentImage"
            class="w-full h-full object-cover"
            shape="square"
          />
          <div v-else class="text-gray-500">
            {{ t('no-image-available') }}
          </div>
        </div>

        <!-- Thumbnails -->
        <div class="flex space-x-2 overflow-x-auto py-2">
          <div
            v-for="(img, index) in fileList"
            :key="img.uid"
            class="w-16 h-16 flex-shrink-0 border rounded cursor-pointer hover:border-blue-500 overflow-hidden"
            :class="{ 'border-blue-500 border-2': currentImageIndex === index }"
            @click="selectImage(index)"
          >
            <a-avatar
              :src="img.url"
              :alt="img.name"
              shape="square"
              class="w-full h-full object-cover"
            />
          </div>
          <div
            class="w-16 h-16 flex-shrink-0 border rounded flex items-center justify-center cursor-pointer hover:bg-gray-100"
            @click="showUploadModal = true"
          >
            <PlusOutlined />
          </div>
        </div>

        <!-- Preview Modal -->
        <a-modal
          :visible="previewVisible"
          :title="previewTitle"
          :footer="null"
          @cancel="handleCancel"
        >
          <img
            alt="Preview"
            style="width: 100%"
            :src="previewImage"
          >
        </a-modal>
      </div>

      <!-- Right column - Invoice details -->
      <div class="space-y-4 col-span-4">
        <div class="grid grid-cols-2 gap-4">
          <div class="grid grid-cols-3">
            <div class="text-sm col-span-1">
              {{ t('invoice-title') }}:
            </div>
            <div class="font-medium col-span-2">
              <a-input
                v-if="isEditing"
                v-model:value="editableInvoice.title"
                class="w-full"
              />
              <span v-else>{{ invoice.title }}</span>
            </div>
          </div>
          <div class="grid grid-cols-3">
            <div class="text-sm col-span-1">
              {{ t('release-date') }}:
            </div>
            <div class="font-medium col-span-2">
              <a-date-picker
                v-if="isEditing"
                v-model:value="editableInvoice.issueDate"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                class="w-full"
              />
              <span v-else>{{ invoice.issueDate }}:</span>
            </div>
          </div>
          <div class="grid grid-cols-3">
            <div class="text-sm col-span-1">
              {{ t('payment-term') }}:
            </div>
            <div class="font-medium col-span-2">
              <a-date-picker
                v-if="isEditing"
                v-model:value="editableInvoice.paymentDate"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                class="w-full"
              />
              <span v-else>{{ invoice.paymentDate }}</span>
            </div>
          </div>
          <div class="grid grid-cols-3">
            <div class="text-sm col-span-1">
              {{ t('invoice-number') }}:
            </div>
            <div class="font-medium col-span-2">
              <a-input
                v-if="isEditing"
                v-model:value="editableInvoice.originalNumber"
                class="w-full"
              />
              <span v-else>{{ invoice.originalNumber }}</span>
            </div>
          </div>
          <div class="grid grid-cols-3">
            <div class="text-sm col-span-1">
              {{ t('type') }}:
            </div>
            <div class="font-medium col-span-2">
              <a-select
                v-if="isEditing"
                v-model:value="editableInvoice.entryTypeId"
                :field-names="{ label: 'entryTypeName', value: 'entryTypeId' }"
                :options="entryTypeOptions"
                class="w-full"
              />
              <span v-else>{{ invoice.entryTypeName }}</span>
            </div>
          </div>
          <div class="grid grid-cols-3">
            <div class="text-sm col-span-1">
              {{ t('total') }}:
            </div>
            <div class="font-medium col-span-2">
              <a-input-number
                v-if="isEditing"
                v-model:value="editableInvoice.totalAmount"
                class="w-full"
                :min="0"
                :formatter="(value: any) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="(value: any) => value!.replace(/\$\s?|(,*)/g, '')"
              />
              <span v-else>{{ invoice.totalAmount }} ¥</span>
            </div>
          </div>
          <div class="grid grid-cols-3">
            <div class="text-sm col-span-1">
              {{ t('payment-method') }}:
            </div>
            <div class="font-medium col-span-2">
              <a-select
                v-if="isEditing"
                v-model:value="editableInvoice.paymentTypeId"
                class="w-full"
                :options="paymentTypeOptions"
                :field-names="{ label: 'paymentTypeName', value: 'paymentTypeId' }"
              />
              <span v-else>{{ invoice.paymentTypeName }}</span>
            </div>
          </div>
          <div class="grid grid-cols-3">
            <div class="text-sm col-span-1">
              {{ t('supplier') }}
            </div>
            <div class="font-medium col-span-2">
              <a-select
                v-if="isEditing"
                v-model:value="editableInvoice.vendorId"
                class="w-full"
                :options="vendorOptons"
              />
              <span v-else>{{ invoice.vendorName }}</span>
            </div>
          </div>
          <div class="grid grid-cols-3">
            <div class="text-sm col-span-1">
              {{ t('project') }}:
            </div>
            <div class="font-medium col-span-2">
              <a-select
                v-if="isEditing"
                v-model:value="editableInvoice.projectId"
                class="w-full"
                :options="projectOptions"
                allow-clear
                @change="(value: SelectValue) => onProjectChange(value?.toString())"
              />
              <div v-else class="flex items-center">
                <span>{{ invoice.projectName }}</span>
              </div>
            </div>
          </div>
          <div class="grid grid-cols-3">
            <div class="text-sm col-span-1">
              {{ t('construction') }}:
            </div>
            <div class="font-medium col-span-2">
              <a-select
                v-if="isEditing"
                v-model:value="editableInvoice.constructionId"
                class="w-full"
                :options="constructionOptions"
              />
              <span v-else>{{ invoice.constructionName }}</span>
            </div>
          </div>
          <div class="grid grid-cols-3">
            <div class="text-sm col-span-1">
              {{ t('note') }}:
            </div>
            <div class="font-medium col-span-2">
              <a-textarea
                v-if="isEditing"
                v-model:value="editableInvoice.description"
                class="w-full"
              />
              <span v-else>{{ invoice.description }}</span>
            </div>
          </div>
        </div>
        <!-- Edit buttons -->
        <div class="flex items-center justify-end">
          <a-button v-if="!isEditing" type="primary" @click="startEditing">
            {{ t('button.edit') }}
          </a-button>
          <div v-else class="space-x-2">
            <a-button @click="cancelEditing">
              {{ t('button.cancel') }}
            </a-button>
            <a-button type="primary" @click="saveChanges">
              {{ t('button.save-change') }}
            </a-button>
          </div>
        </div>
      </div>
      <!-- <InlineEditing :invoice="invoice" /> -->
    </div>

    <!-- Item list section -->
    <div class="mt-2">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-md">
          {{ t('input-cost-item-list') }}
        </h3>
        <div class="flex items-center">
          <a-input-search
            v-model:value="searchText"
            :placeholder="t('placeholder-search')"
            style="width: 250px"
            class="mr-2"
          />
          <button class="flex items-center justify-center bg-green-600 rounded-md p-2 text-white hover:bg-green-500" @click="inputCostItemModalVisiable = true">
            <PlusOutlined />
          </button>
        </div>
      </div>

      <a-table
        :data-source="filteredItems"
        :columns="columns"
        :pagination="{ pageSize: 10, showSizeChanger: true, pageSizeOptions: ['5', '10', '20'] }"
        bordered
        size="middle"
        :scroll="{ x: 'max-content' }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'itemName'">
            <div class="grid grid-cols-1">
              <div>{{ record.itemName }}</div>
              <div class="text-xs text-gray-500">
                {{ t('vendor') }}: {{ record.vendorName }}
              </div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <div class="flex item-center">
              <template v-if="editingKey === record.inputCostItemId">
                <a-button type="link" @click="cancelEdit">
                  <template #icon>
                    <CloseOutlined />
                  </template>
                </a-button>
                <a-button type="link" @click="saveEdit(record.inputCostItemId, record.inputCostItemId)">
                  <template #icon>
                    <CheckOutlined />
                  </template>
                </a-button>
              </template>
              <template v-else>
                <a-button type="link" @click="handleEditRow(record as InputCostItem)">
                  <template #icon>
                    <EditOutlined />
                  </template>
                </a-button>
                <a-popconfirm
                  :title="t('message.delete-confirmation')"
                  ok-text="Yes"
                  cancel-text="No"
                  @confirm="confirmDelete(record.inputCostItemId)"
                >
                  <a-button type="link">
                    <template #icon>
                      <DeleteOutlined class="text-red-500" />
                    </template>
                  </a-button>
                </a-popconfirm>
              </template>
            </div>
          </template>
        </template>
      </a-table>
    </div>

    <!-- <div class="flex justify-end mt-6 space-x-2">
      <a-button @click="handleCancel">
        {{ t('button.cancel') }}
      </a-button>
      <a-button type="primary" @click="handleSave">
        {{ t('button.save-change') }}
      </a-button>
    </div> -->

    <!-- Upload Image Modal -->
    <a-modal
      v-model:visible="showUploadModal"
      :title="t('upload-image')"
      :footer="null"
      @cancel="showUploadModal = false"
    >
      <a-upload-dragger
        v-model:file-list="fileList"
        name="file"
        :multiple="false"
        :before-upload="beforeUpload"
      >
        <template #itemRender="{ file }">
          <div class="flex justify-between">
            <span>{{ file.name }}</span>
            <a-button
              type="link"
              danger
              @click.stop="handleDeleteImage(file.name)"
            >
              <DeleteOutlined />
            </a-button>
          </div>
        </template>
        <p class="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p class="ant-upload-text">
          {{ t('click-or-drag-file-to-this-area-to-upload') }}
        </p>
        <p class="ant-upload-hint">
          {{ t('support-for-a-single-upload') }}
        </p>
      </a-upload-dragger>

      <div class="flex justify-end mt-4">
        <a-button @click="showUploadModal = false">
          {{ t('button.cancel') }}
        </a-button>
        <a-button type="primary" class="ml-2" @click="handleUpload">
          {{ t('button.upload') }}
        </a-button>
      </div>
    </a-modal>

    <!-- Add Item Modal -->
    <a-modal
      v-model:visible="inputCostItemModalVisiable"
      :title="isInputCostItemEdit ? t('button.edit') : t('button.add')"
      :footer="null"
      @cancel="inputCostItemModalVisiable = false"
    >
      <a-form
        :model="inputCostItemFormState"
        layout="vertical"
      >
        <a-form-item :label="t('item-name')" name="itemId">
          <a-select
            v-model:value="inputCostItemFormState.item.itemId"
            :options="items"
            :field-names="{ label: 'itemName', value: 'itemId' }"
            :placeholder="t('enter-item-name')"
          />
        </a-form-item>

        <a-form-item :label="t('transaction-date')" name="transactionDate">
          <a-date-picker
            v-model:value="inputCostItemFormState.transactionDate"
            style="width: 100%"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
          />
        </a-form-item>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <a-form-item :label="t('unit-price')" name="unitPrice">
            <a-input
              v-model:value="inputCostItemFormState.unit"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item :label="t('price')" name="price">
            <a-input-number
              v-model:value="inputCostItemFormState.price"
              :min="0"
              style="width: 100%"
              @change="calculateNewItemPrice"
            />
          </a-form-item>

          <a-form-item :label="t('quantity')" name="quantity">
            <a-input-number
              v-model:value="inputCostItemFormState.quantity"
              :min="1"
              style="width: 100%"
              @change="calculateNewItemPrice"
            />
          </a-form-item>

          <a-form-item :label="t('tax-rate')" name="taxRate">
            <a-select
              v-model:value="inputCostItemFormState.taxRate"
              style="width: 100%"
              @change="calculateNewItemPrice"
            >
              <a-select-option :value="5">
                5%
              </a-select-option>
              <a-select-option :value="10">
                10%
              </a-select-option>
              <a-select-option :value="20">
                20%
              </a-select-option>
            </a-select>
          </a-form-item>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <a-form-item :label="t('total-nontaxed')" name="totalNontaxed">
            <a-input-number
              v-model:value="inputCostItemFormState.totalNonTaxed"
              :disabled="true"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item :label="t('total-taxed')" name="totalTaxed">
            <a-input-number
              v-model:value="inputCostItemFormState.totalTaxed"
              :disabled="true"
              style="width: 100%"
            />
          </a-form-item>
        </div>
      </a-form>

      <div class="flex justify-end mt-4">
        <a-button @click="inputCostItemModalVisiable = false">
          {{ t("button.cancel") }}
        </a-button>
        <a-button v-if="!isInputCostItemEdit" type="primary" class="ml-2" @click="handleCreateInputCostItem">
          {{ t("button.add") }}
        </a-button>
        <a-button v-else type="primary" class="ml-2" @click="handleUpdateInputCostItem">
          {{ t("button.update") }}
        </a-button>
      </div>
    </a-modal>
  </a-modal>
</template>

  <style scoped>
  .invoice-detail-modal :deep(.ant-table-thead > tr > th) {
    background-color: #f9fafb;
    font-weight: 500;
  }

  .invoice-detail-modal :deep(.ant-upload-drag) {
    height: 200px;
  }
  </style>
