import type { FileType } from 'ant-design-vue/es/upload/interface'
import { ContentTypeEnum } from '~@/enums/http-enum'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'

export interface OutsourceQueryParams {
  keyword?: string
  expertise?: string
  pageNum: number
  pageSize: number
}

export interface OutSourceItem {
  createTime: string | null
  updateTime: string | null
  outSourceId: string
  outSourceCode: string | null
  outSourceName: string | null
  description: string | null
  expertise: string[] | null
  corporateNumber: string | null
  address: string | null
  phoneNumber: string | null
  email: string | null
  contactPerson: ContactPerson | null
  logoUrl?: string
}

export interface Price {
  pricePerDay: number
  pricePerWeek: number
  pricePerMonth: number
  pricePerHour: number
}

export interface ContactPerson {
  name?: string
  email?: string
  phoneNumber?: string
}

export interface OutSourceData {
  outSourceCode: string
  outSourceName: string
  description?: string
  expertise?: string[]
  corporateNumber?: string
  address?: string
  phoneNumber?: string
  email?: string
  contactPerson?: ContactPerson
  price?: Price
  logo?: FileType
}

interface ApiResponse {
  items: OutSourceItem[]
  pageNum: number
  pageSize: number
  totalRecords: number
}

export interface OutsourceResponse {
  items: OutSourceItem[]
  pageNum: number
  pageSize: number
  totalRecords: number
}

export interface OutSourcePriceItem {
  outSourceId?: string
  outSourcePriceId: string
  outSourceCode?: string
  outSourceName?: string
  pricePerDay?: number
  description?: string
}

export interface OutSourcePriceResponse {
  items: OutSourcePriceItem[]
  pageNum?: number
  pageSize?: number
  totalRecords?: number
}

const messageNotification = useMessage()

enum ApiEndpoints {
  GET_OUTSOURCE_LIST = 'v1/outsource',
  // eslint-disable-next-line ts/no-duplicate-enum-values
  CREATE_OUTSOURCE = 'v1/outsource',
}

export async function fetchOutsourceListApi(params?: OutsourceQueryParams) {
  const { data, status, message } = await useGet<ApiResponse>(
    ApiEndpoints.GET_OUTSOURCE_LIST,
    params,
  )
  if (status !== ResponseStatusEnum.SUCCESS) {
    messageNotification.error(message)
    return null
  }
  return data
}

export async function updateOutsourceApi(
  outsourceId: string,
  params: OutSourceData,
) {
  return usePut<OutSourceItem>(`v1/outsource/${outsourceId}`, params, {
    headers: { 'Content-Type': ContentTypeEnum.FORM_DATA },
  })
}

export async function deleteOutsourceApi(outsourceId: string) {
  const { data, status, message } = await useDelete(
    `v1/outsource/${outsourceId}`,
  )
  if (status !== ResponseStatusEnum.SUCCESS) {
    messageNotification.error(message)
    return null
  }
  messageNotification.success(message)
  return data
}

export async function getOutsourcePrices(params?: Partial<OutsourceQueryParams>) {
  return useGet<OutSourcePriceResponse>(`v1/outsource/prices`, params)
}

export async function createOutsourcePrice(
  id: string,
  data: Partial<OutSourcePriceItem>,
) {
  return usePost(`v1/outsource/${id}/prices`, data)
}

export async function updateOutsourcePrice(
  id: string,
  data: Partial<OutSourcePriceItem>,
) {
  return usePut(`v1/outsource/prices/${id}`, data)
}

export async function createOutsourceApi(data: OutSourceData) {
  return usePost<OutSourceItem>(ApiEndpoints.CREATE_OUTSOURCE, data, {
    headers: { 'Content-Type': ContentTypeEnum.FORM_DATA },
  })
}

export function getOutsourceLogo(outsourceId: string, orgId: string) {
  const host = import.meta.env.VITE_APP_BASE_API ?? ''
  return `${host}/v1/outsource/${outsourceId}/logo?orgId=${orgId}`
}

export function getOutsourceApi(params?: OutsourceQueryParams) {
  return useGet<OutsourceResponse>('v1/outsource', params)
}
