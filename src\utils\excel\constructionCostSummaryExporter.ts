import * as XLSX from 'xlsx'
import type { CostCategoryItem, GetCostCategoryParams } from '~@/api/company/cost-category'
import type { ConstructionCostItem } from '~@/api/construction-cost'
import type { InputCostItem, InputCostItemQuery } from '~@/api/input-cost-item'
import { useCategory } from '~@/composables/category/useCategory'
import { useInputCostItem } from '~@/composables/cost/useInputCostItem'

interface ExportSummaryOptions {
  constructionCostItem: ConstructionCostItem
  isPrimary: boolean
}

function formatNumber(value: number | null): string {
  if (!value)
    return '0'
  return value.toLocaleString('ja-JP')
}

const inputCostItemData = ref<InputCostItem[]>([])
const categoryData = ref<CostCategoryItem[]>([])
const { fetchCategory } = useCategory()
const { fetchInputCostItemList } = useInputCostItem()

const maxLength = ref(0)
const maxLengthTableOne = ref(0)
const maxLengthTableTwo = ref(0)

function getCategoryItems(categoryCode: string, position: number, type: 'TABLE_ONE' | 'TABLE_TWO'): InputCostItem[] {
  const itemsInCategory = inputCostItemData.value.filter(
    item => item.categoryCode === categoryCode,
  )
  if (type === 'TABLE_ONE') {
    maxLengthTableOne.value = Math.max(maxLengthTableOne.value, itemsInCategory.length)
    maxLength.value = maxLengthTableOne.value
  }

  else {
    maxLengthTableTwo.value = Math.max(maxLengthTableTwo.value, itemsInCategory.length)
    maxLength.value = maxLengthTableTwo.value
  }

  return position < itemsInCategory.length ? [itemsInCategory[position]] : []
}

function getCategoryTotal(categoryCode: string): number {
  return inputCostItemData.value
    .filter(item => item.categoryCode === categoryCode)
    .reduce((sum, item) => sum + (item.totalNonTaxed || 0), 0)
}

function createWSData(firstFiveCategories: CostCategoryItem[], wsData: (string | number)[][], type: 'TABLE_ONE' | 'TABLE_TWO'): (string | number)[][] {
  const headers: string[] = []
  firstFiveCategories.forEach((item: CostCategoryItem) => {
    if (item.categoryCode === 'EMPLOYEE' || item.categoryCode === 'OUTSOURCE')
      headers.push(item.categoryName, '', '', '')

    else
      headers.push(item.categoryName, '', '', '')
  })
  wsData.push(headers)

  // Create data rows (15 rows as shown in the template)
  for (let rowIndex = 0; rowIndex < maxLength.value + 3; rowIndex++) {
    const row: (string | number)[] = []

    firstFiveCategories.forEach((category) => {
      const items = getCategoryItems(category.categoryCode, rowIndex, type)
      if (items.length > 0) {
        const item = items[0]
        if (category.categoryCode === 'EMPLOYEE' || category.categoryCode === 'OUTSOURCE_DAILY') {
          row.push(
            item.itemName,
                    `単価 ¥${formatNumber(item.price)}`,
                    `${item.quantity} 人工`,
                    `金額 ¥${formatNumber(item.totalNonTaxed)}`,
          )
        }
        else {
          row.push(
            item.itemName || '',
                `単価 ¥${formatNumber(item.price)}`,
                `${item.quantity} ${item.unit}`,
                `金額 ¥${formatNumber(item.totalNonTaxed)}`,
          )
        }
      }
      else {
        row.push('', '', '', '') // Empty cells if no item
      }
    })
    wsData.push(row)
  }

  // Add totals row
  const totalsRow: (string | number)[] = []
  firstFiveCategories.forEach((category) => {
    const total = getCategoryTotal(category.categoryCode)
    totalsRow.push(`小計: ¥${formatNumber(total)}`, '', '', '')
  })
  wsData.push(totalsRow)
  return wsData
}

async function initData(options: ExportSummaryOptions) {
  maxLength.value = 0
  maxLengthTableOne.value = 0
  maxLengthTableTwo.value = 0
  const params: GetCostCategoryParams = {
    keyword: undefined,
    parentId: undefined,
    pageNum: 1,
    pageSize: 100,
  }
  categoryData.value = await fetchCategory(params)

  const inputCostItemQuery: InputCostItemQuery = {
    constructionId: options.constructionCostItem.constructionId,
    pageNum: 1,
    pageSize: 100,
  }
  inputCostItemData.value = await fetchInputCostItemList(inputCostItemQuery)
}

function styleWS(wsData: (string | number)[][], firstFiveCategories: CostCategoryItem[], otherCategories: CostCategoryItem[]): XLSX.WorkSheet {
  // Create worksheet
  const ws = XLSX.utils.aoa_to_sheet(wsData)
  // Set column widths
  ws['!cols'] = Array(12).fill({ wch: 10 }) // 4 categories × 5 columns each

  // Set row heights
  const wsHeight = wsData.length
  ws['!rows'] = Array(wsHeight).fill({ hpt: 25 })
  ws['!rows'][0] = { hpt: 30 } // Header height

  // Add styles
  ws['!merges'] = [
    // Merge title cell across all columns
    { s: { r: 0, c: 0 }, e: { r: 0, c: 16 } },
  ]

  // Add category header merges
  firstFiveCategories.forEach((_, index) => {
    (ws['!merges'] = ws['!merges'] || []).push({
      s: { r: 1, c: index * 4 },
      e: { r: 1, c: index * 4 + 3 },
    })
  })

  otherCategories.forEach((_, index) => {
    (ws['!merges'] = ws['!merges'] || []).push({
      s: { r: maxLengthTableOne.value + 6, c: index * 4 },
      e: { r: maxLengthTableOne.value + 6, c: index * 4 + 3 },
    })
  })

  return ws
}

export async function createSummarySheet(options: ExportSummaryOptions): Promise<XLSX.WorkSheet> {
  await initData(options)

  // Create header
  const title = options.isPrimary ? '本工事費' : '別途工事費'
  let wsData: (string | number)[][] = [[title]]

  // Create category headers (first 4 categories)
  const firstFiveCategories = categoryData.value.slice(0, 5)
  wsData = createWSData(firstFiveCategories, wsData, 'TABLE_ONE')

  const otherCategories = categoryData.value.slice(5)
  wsData = createWSData(otherCategories, wsData, 'TABLE_TWO')

  const ws = styleWS(wsData, firstFiveCategories, otherCategories)
  return ws
}
