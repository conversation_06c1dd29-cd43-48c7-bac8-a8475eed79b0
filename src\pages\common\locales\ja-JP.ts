export default {
  // LoginPage
  'pages.layouts.userLayout.title':
    'Antdv ProはAnt Design Vueをベースにした普遍的な中間管理システムです',
  'pages.login.accountLogin.tab': 'アカウントログイン',
  'pages.login.accountLogin.username.errorMessage':
    'ユーザー名が正しくありません',
  'pages.login.accountLogin.orgAndPassword.errorMessage':
    '所属またパスワードが正しくありません',
  'pages.login.failure': 'ログインに失敗しました。もう一度試してください。',
  'pages.login.success': 'ログインに成功しました！',
  'pages.login.username.placeholder': 'ユーザー名',
  'pages.login.username.required': 'ユーザー名を入力してください！',
  'pages.login.orgcode.placeholder': '組織コード',
  'pages.login.orgcode.required': '組織コードを入力してください！',
  'pages.login.password.placeholder': 'パスワード',
  'pages.login.change.password.placeholder': 'パスワードを変更',
  'pages.login.confirm.password.placeholder': 'パスワードを確認',
  'button.change': '変更',
  'pages.login.password.required': 'パスワードを入力してください！',
  'pages.login.phoneLogin.tab': '電話でログイン',
  'pages.login.phoneLogin.errorMessage': '検証コードが正しくありません',
  'pages.login.phoneNumber.placeholder': '電話番号',
  'pages.login.phoneNumber.required': '電話番号を入力してください！',
  'pages.login.phoneNumber.invalid': '電話番号が無効です！',
  'pages.login.captcha.placeholder': '検証コード',
  'pages.login.captcha.required': '検証コードを入力してください！',
  'pages.login.phoneLogin.getVerificationCode': 'コードを取得',
  'pages.getCaptchaSecondText': '秒',
  'pages.login.rememberMe': '私を覚えてますか',
  'pages.login.forgotPassword': 'パスワードをお忘れですか？',
  'pages.login.submit': 'ログイン',
  'pages.login.back': '戻り',
  'pages.login.next': '次',
  'pages.login.forgotAccount': 'パスワードを忘れた方',
  'pages.login.loginWith': '以下でログイン：',
  'pages.login.registerAccount': 'アカウントを登録',
  'pages.login.tips': 'システムへようこそ',
  'pages.login.title': 'ログイン',
}
