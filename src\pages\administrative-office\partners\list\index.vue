<script lang="ts" setup>
import Vendor from './tabs/vendor.vue';
import Manufacturer from './tabs/manufacturer.vue';
import Contractor from './tabs/contractor.vue';
import Customer from './tabs/customer.vue';
import Outsource from './tabs/outsource.vue';

const { t } = useI18n();
</script>

<template>
  <a-tabs destroy-inactive-tab-pane>
    <a-tab-pane key="1" :tab="t('form.customer')">
      <Customer />
    </a-tab-pane>
    <a-tab-pane key="2" :tab="t('form.contractor')">
      <Contractor />
    </a-tab-pane>
    <a-tab-pane key="3" :tab="t('form.vendor')">
      <Vendor />
    </a-tab-pane>
    <a-tab-pane key="4" :tab="t('form.outsource')">
      <Outsource />
    </a-tab-pane>
    <a-tab-pane key="5" :tab="t('form.manufacturer')">
      <Manufacturer />
    </a-tab-pane>
  </a-tabs>
</template>
