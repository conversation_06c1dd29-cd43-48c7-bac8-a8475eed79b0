import { UploadFile } from 'ant-design-vue';
import { useOrg } from '~@/composables/org';

export interface ManufacturerItemResponse {
  items: ManufacturerItem[];
  pageIndex: number;
  pageSize: number;
}

export interface EquipmentItem {
  equipmentId?: string;
  equipmentCode?: string;
  equipmentName?: string;
  equipmentSubName?: string;
  categoryName?: string;
  size?: string;
  serialNumber?: string;
  model?: string;
  fuelConsumption?: string;
  equipmentStatus?: string;
  lastMaintenanceDate?: string;
  description?: string;
}

export interface ManufacturerItem {
  logo?: UploadFile;
  logoUrl?: string;
  manufacturerId: string;
  manufacturerCode: string;
  manufacturerName: string;
  manufacturerSubName?: string;
  description?: string;
  corporateNumber?: string;
  address?: string;
  phoneNumber?: string;
  email?: string;
  contactPerson: {
    name?: string;
    phoneNumber?: string;
    email?: string;
  };
  items: EquipmentItem[];
}

interface ManufacturerLogsResponse {
  entityChanges: ManufacturerLogItem[];
  pageNum: number;
  pageSize: number;
  totalRecords: number;
}

export interface ManufacturerChangedListItem {
  fieldName: string;
  valueAfter: string | number | boolean | number[] | string[];
  valueBefore: string | number | boolean | number[] | string[];
}

export interface ManufacturerLogItem {
  action: string;
  auditLogId: string;
  changedList: ManufacturerChangedListItem[];
  entityId: string;
  description: string;
  modifiedTime: string;
  modifiedUserId: string;
  modifiedUserName: string;
}

interface GetManufacturerParams {
  keyword?: string;
  pageNum?: number;
  pageSize?: number;
}

interface GetManufacturerLogsParams {
  dateFrom?: string;
  dateTo?: string;
  action?: string;
  pageNum?: number;
  pageSize?: number;
}

export async function getManufacturer(params?: GetManufacturerParams) {
  return useGet<ManufacturerItemResponse>('v1/cost/manufacturer', params);
}

export async function getManufacturerItem(params?: GetManufacturerParams) {
  return useGet<ManufacturerItemResponse>('v1/cost/manufacturer', params);
}

export function getManufacturerLogo(id: string): string {
  const host = import.meta.env.VITE_APP_BASE_API ?? '';
  return `${host}/v1/cost/manufacturer/${id}/logo?orgId=${useOrg().value}`;
}

export async function getOneManufacturer(id: string) {
  return useGet<ManufacturerItem>(`v1/cost/manufacturer/${id}`);
}

export async function createManufacturer(data: Partial<ManufacturerItem>) {
  return usePost('v1/cost/manufacturer', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

export async function updateManufacturer(
  id: string,
  data: Partial<ManufacturerItem>
) {
  return usePut(`v1/cost/manufacturer/${id}`, data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

export async function deleteManufacturer(id: string) {
  return useDelete(`v1/cost/manufacturer/${id}`);
}

export async function getManufacturerLogs(
  id: string,
  params?: GetManufacturerLogsParams
) {
  return useGet<ManufacturerLogsResponse>(
    `v1/cost/manufacturer/${id}/logs`,
    params
  );
}
