<script lang="ts" setup>
import { PlusOutlined } from '@ant-design/icons-vue'
import { Empty, message } from 'ant-design-vue'
import dayjs from 'dayjs'
import type {
  AttendanceItem,
  AttendanceItemParams,
} from '~@/api/attendance'
import {
  createAttendance<PERSON>pi,
  deleteAttendance<PERSON>pi,
  getAttendanceApi,
  getAttendanceByDateApi,
  getAttendanceEmployeeSummaryApi,
  getWorkingLocationDisplayText,
  updateAttendanceApi,
} from '~@/api/attendance'
import type {
  RequestItem,
} from '~@/api/dashboard/date-off-request'
import {
  getRequestByAuNoPaginationApi,
} from '~@/api/dashboard/date-off-request'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'
import type { SystemStatusEnumKey } from '~@/enums/system-status-enum'
import {
  ModalType,
  RequestStatus,
  RequestStatusEnum,
} from '~@/enums/system-status-enum'
import logger from '~@/utils/logger'

type AttendanceItemCustom = AttendanceItem & {
  isAttendance?: boolean
  isAbsent?: boolean
  isSchedule?: boolean
}

interface AttendanceSummary {
  totalAbsentDays?: number
  totalPaidLeaveUsed?: number
  totalUnpaidLeaveUsed?: number
  totalWorkingDays?: number
  totalOverTime?: number
  totalWorkTime?: number
}

const props = defineProps({
  selectedDate: {
    type: dayjs.Dayjs,
    required: true,
  },
  calendarLeftHeight: {
    type: Number,
    required: true,
  },
})

const emit = defineEmits<{
  (event: 'refresh'): void
}>()

const { t } = useI18n()
const modalType = ref<ModalType>(ModalType.ADD)
const isOpenModal = ref<boolean>(false)
const summaryRight = ref()
const requestData = ref<RequestItem[]>([])
const attendanceItem = ref<AttendanceItem | null>(null)
const attendanceData = ref<AttendanceItemCustom[]>([])
const attendanceDataSummary = ref<AttendanceSummary>({})
const detailContainer = ref()
const { height: summaryRightHeight } = useElementSize(summaryRight)

async function getRequestByDate() {
  try {
    const { data } = await getRequestByAuNoPaginationApi({
      fromDate: props.selectedDate.startOf('month').format('YYYY-MM-DD'),
      toDate: props.selectedDate.endOf('month').format('YYYY-MM-DD'),
    })
    requestData.value = data?.items ?? []
  }
  catch (e) {
    logger.error(e)
  }
}

async function getAttendanceEmployeeSummary() {
  try {
    const { data, status } = await getAttendanceEmployeeSummaryApi({
      fromDate: props.selectedDate.startOf('month').format('YYYY-MM-DD'),
      toDate: props.selectedDate.endOf('month').format('YYYY-MM-DD'),
    })
    if (status === 200) {
      attendanceDataSummary.value = {
        totalWorkingDays: data?.totalWorkingDays ?? 0,
        totalAbsentDays: data?.totalAbsentDays ?? 0,
        totalWorkTime: data?.totalWorkTime ?? 0,
        totalOverTime: data?.totalOverTime ?? 0,
        totalPaidLeaveUsed: data?.totalPaidLeaveUsed ?? 0,
        totalUnpaidLeaveUsed: data?.totalUnpaidLeaveUsed ?? 0,
      }
    }
    else {
      attendanceDataSummary.value = {
        totalWorkingDays: 0,
        totalAbsentDays: 0,
        totalWorkTime: 0,
        totalOverTime: 0,
        totalPaidLeaveUsed: 0,
        totalUnpaidLeaveUsed: 0,
      }
    }
  }
  catch (e) {
    logger.error(e)
  }
}

async function getAttendanceByDate() {
  try {
    const { data } = await getAttendanceByDateApi({
      fromDate: props.selectedDate.startOf('month').format('YYYY-MM-DD'),
      toDate: props.selectedDate.endOf('month').format('YYYY-MM-DD'),
    })

    attendanceData.value = data?.items ?? []
  }
  catch (e) {
    logger.error(e)
    throw e
  }
}

async function openModal(type: ModalType, id?: string) {
  switch (type) {
    case ModalType.ADD:
      modalType.value = type
      attendanceItem.value = null
      isOpenModal.value = true
      break
    case ModalType.EDIT: {
      if (!id)
        return
      modalType.value = type
      const update = await getAttendanceApi(id)
      const item = update.data
      attendanceItem.value = item
      isOpenModal.value = true
      break
    }
    default:
      break
  }
}

async function refreshSummary() {
  await Promise.all([getAttendanceByDate(), getRequestByDate(), getAttendanceEmployeeSummary()])
}

async function onFinish(employeeShiftId: string, params: AttendanceItemParams) {
  try {
    switch (modalType.value) {
      case ModalType.ADD: {
        const create = await createAttendanceApi(params)
        if (create.status !== ResponseStatusEnum.SUCCESS)
          return
        message.success(create.message)
        break
      }
      case ModalType.EDIT: {
        const update = await updateAttendanceApi(employeeShiftId, params)
        if (update.status !== ResponseStatusEnum.SUCCESS)
          return
        message.success(update.message)
        break
      }
      default:
        break
    }

    isOpenModal.value = false
    emit('refresh')
    await refreshSummary()
  }
  catch (error) {
    console.error(error)
  }
}

const selectedAttendances = computed(() => {
  return attendanceData.value.filter((item: AttendanceItem) => {
    const workingDate = dayjs(item.workingDate).format('YYYY-MM-DD')
    return workingDate === props.selectedDate.format('YYYY-MM-DD')
  })
})

const selectedRequests = computed(() => {
  return requestData.value.filter((item: RequestItem) => {
    const dateFrom = dayjs(item.requestFrom)
    const dateTo = dayjs(item.requestTo)
    const selected = dayjs(props.selectedDate).format('YYYY-MM-DD')
    return (
      selected === dateFrom.format('YYYY-MM-DD')
      || selected === dateTo.format('YYYY-MM-DD')
      || (dateFrom.isBefore(selected, 'day') && dateTo.isAfter(selected, 'day'))
    )
  })
})

const maxHeight = computed(() => {
  return (num: number = 0) => {
    return `${
      props.calendarLeftHeight - summaryRightHeight.value - 58 - num
    }px`
  }
})

const formatTime = computed(() => {
  return (date?: string | null, defaultValue: string = '--:--') => {
    if (!date)
      return defaultValue
    return dayjs(date, 'HH:mm:ss').format('HH:mm')
  }
})

const formatDateTime = computed(() => {
  return (date?: string | null, defaultValue: string = '--/--/-- --:--') => {
    if (!date)
      return defaultValue
    return dayjs(date).format('YYYY/MM/DD HH:mm')
  }
})

const showButton = computed(() => {
  return (item?: AttendanceItemCustom) => {
    const isSameOrAfter = dayjs(props.selectedDate).isSameOrAfter(dayjs())
    if (isSameOrAfter)
      return false
    if (item?.isApproved)
      return false
    return true
  }
})

async function handleDelete(id?: string) {
  if (!id)
    return
  try {
    const del = await deleteAttendanceApi(id)
    if (del.status === ResponseStatusEnum.SUCCESS)
      message.success(del.message)
    else message.error(del.message)
  }
  catch (error) {
  }
  finally {
    emit('refresh')
    await refreshSummary()
  }
}

const getAttendanceStatus = computed(() => {
  return (is_requested?: boolean, is_approved?: boolean) => {
    if (!is_requested && !is_approved) {
      const code = 'NOT_REQUESTED'
      return { status: t(code), code, color: 'gray' }
    }

    if (!is_requested) {
      const code = 'NOT_REQUESTED'
      return { status: t(code), code, color: 'gray' }
    }

    if (is_requested && !is_approved) {
      const code = 'PENDING'
      return { status: t(code), code, color: 'orange' }
    }

    if (is_requested && is_approved) {
      const code = 'APPROVED'
      return { status: t(code), code, color: 'green' }
    }

    if (is_requested && !is_approved) {
      const code = 'REJECTED'
      return { status: t(code), code, color: 'red' }
    }
  }
})

const formattedSelectedDate = computed(() => {
  const currentLocale = t('locale')
  const date = props.selectedDate.locale(currentLocale)
  const month = date.month() + 1
  const day = date.date()
  const weekday = date.format('ddd')

  if (currentLocale === 'ja')
    return `${month}月${day}日（${weekday}）`

  return `${month}/${day} (${weekday})`
})

onMounted(async () => {
  await refreshSummary()
})

watch(
  () => props.selectedDate,
  async (newDate, oldDate) => {
    if (newDate.month() === oldDate.month())
      return
    await refreshSummary()
  },
)

watch(
  () => props.selectedDate,
  async (newDate, oldDate) => {
    if (newDate.date() === oldDate.date())
      return
    if (detailContainer.value)
      detailContainer.value.scrollTo({ top: 0, behavior: 'smooth' })
  },
)

watch(
  () => t('locale'),
  async () => await refreshSummary(),
)

defineExpose({ refreshSummary })
</script>

<template>
  <div>
    <a-row>
      <a-col ref="summaryRight" span="24">
        <div class="mb-[24px]">
          <a-row
            :gutter="[
              { xxl: 8, xl: 4 },
              { xxl: 8, xl: 4 },
            ]"
          >
            <a-col span="12">
              <a-card class="box-shadow">
                <a-row :wrap="false" justify="center" align="middle">
                  <a-col flex="none">
                    <div class="w-8 flex justify-start items-center">
                      <img src="/icon/total_work_days.svg">
                    </div>
                  </a-col>
                  <a-col flex="auto">
                    <div class="flex flex-col">
                      <span class="text-gray-500 truncate">
                        <a-tooltip
                          placement="topLeft"
                          :title="$t('calendar.working-days')"
                        >
                          {{ $t('calendar.working-days') }}
                        </a-tooltip>
                      </span>
                      <span class="font-bold text-base" color="blue">
                        {{ attendanceDataSummary?.totalWorkingDays ?? 0 }}
                      </span>
                    </div>
                  </a-col>
                </a-row>
              </a-card>
            </a-col>
            <a-col span="12">
              <a-card class="box-shadow">
                <a-row :wrap="false" justify="center" align="middle">
                  <a-col flex="none">
                    <div class="w-8 flex justify-start items-center">
                      <img src="/icon/total_day_off.svg">
                    </div>
                  </a-col>
                  <a-col flex="auto">
                    <div class="flex flex-col">
                      <span class="text-gray-500 truncate">
                        <a-tooltip
                          placement="topLeft"
                          :title="$t('calendar.absent-days')"
                        >
                          {{ $t('calendar.absent-days') }}
                        </a-tooltip>
                      </span>
                      <span class="font-bold text-base" color="blue">
                        {{ attendanceDataSummary?.totalAbsentDays ?? 0 }}
                      </span>
                    </div>
                  </a-col>
                </a-row>
              </a-card>
            </a-col>
            <a-col span="12">
              <a-card class="box-shadow">
                <a-row :wrap="false" justify="center" align="middle">
                  <a-col flex="none">
                    <div class="w-8 flex justify-start items-center">
                      <img src="/icon/total_work_time.svg">
                    </div>
                  </a-col>
                  <a-col flex="auto">
                    <div class="flex flex-col">
                      <span class="text-gray-500 truncate">
                        <a-tooltip
                          placement="topLeft"
                          :title="$t('calendar.working-time')"
                        >
                          {{ $t('calendar.working-time') }}
                        </a-tooltip>
                      </span>
                      <span class="font-bold text-base" color="blue">
                        {{ attendanceDataSummary?.totalWorkTime ?? 0 }}
                      </span>
                    </div>
                  </a-col>
                </a-row>
              </a-card>
            </a-col>
            <a-col span="12">
              <a-card class="box-shadow">
                <a-row :wrap="false" justify="center" align="middle">
                  <a-col flex="none">
                    <div class="w-8 flex justify-start items-center">
                      <img src="/icon/total_over_time.svg">
                    </div>
                  </a-col>
                  <a-col flex="auto">
                    <div class="flex flex-col">
                      <span class="text-gray-500 truncate">
                        <a-tooltip
                          placement="topLeft"
                          :title="$t('calendar.working-time')"
                        >
                          {{ $t('calendar.overtime') }}
                        </a-tooltip>
                      </span>
                      <span class="font-bold text-base" color="blue">
                        {{ attendanceDataSummary?.totalOverTime ?? 0 }}
                      </span>
                    </div>
                  </a-col>
                </a-row>
              </a-card>
            </a-col>
            <a-col span="24">
              <a-card class="box-shadow">
                <a-row :wrap="false" justify="center" align="middle">
                  <a-col flex="none">
                    <div class="w-8 flex justify-start items-center">
                      <img src="/icon/paid_leave_remain.svg">
                    </div>
                  </a-col>
                  <a-col flex="auto">
                    <div class="flex flex-col">
                      <span class="text-gray-500 truncate">
                        <a-tooltip
                          placement="topLeft"
                          :title="$t('calendar.working-time')"
                        >
                          {{ $t('calendar.paid-leave') }}
                        </a-tooltip>
                      </span>
                      <span class="font-bold text-base" color="blue">
                        {{ attendanceDataSummary?.totalPaidLeaveUsed ?? 0 }}
                      </span>
                    </div>
                  </a-col>
                </a-row>
              </a-card>
            </a-col>
            <!-- <a-col span="">
              <a-card class="box-shadow">
                <a-row :wrap="false" justify="center" align="middle">
                  <a-col flex="none" :span="24">
                    <div class="w-8 flex justify-start items-center">
                      <img src="/icon/unpaid_leave_remain.svg">
                    </div>
                  </a-col>
                  <a-col flex="auto">
                    <div class="flex flex-col">
                      <span class="text-gray-500 truncate">
                        <a-tooltip
                          placement="topLeft"
                          :title="$t('calendar.working-time')"
                        >
                          {{ $t('calendar.unpaid-leave') }}
                        </a-tooltip>
                      </span>
                      <span class="font-bold text-base" color="blue">
                        {{ attendanceDataSummary?.totalUnpaidLeaveUsed ?? 0 }}
                      </span>
                    </div>
                  </a-col>
                </a-row>
              </a-card>
            </a-col> -->
          </a-row>
        </div>
      </a-col>
      <a-col span="24">
        <a-card
          :body-style="{ paddingLeft: '0px', paddingRight: '0px' }"
          class="box-shadow"
        >
          <template #title>
            <div class="flex justify-between px-[16px]">
              <span class="text-date">
                {{ formattedSelectedDate }}
              </span>
              <div>
                <a-button
                  v-if="showButton()"
                  class="flex items-center"
                  type="primary"
                  size="small"
                  @click="openModal(ModalType.ADD)"
                >
                  <PlusOutlined />
                </a-button>
              </div>
            </div>
          </template>
          <div
            v-if="selectedRequests.length > 0 || selectedAttendances.length > 0"
            ref="detailContainer"
            class="flex flex-col px-[24px] gap-[16px] overflow-y-auto"
            :style="{ height: maxHeight() }"
          >
            <template
              v-for="attendance in selectedAttendances"
              :key="attendance"
            >
              <div>
                <div
                  class="border border-2 border-gray-300 border-solid p-4 rounded-t-md"
                  :class="{ 'rounded-b-md': !showButton(attendance) }"
                >
                  <div
                    v-if="attendance.isAttendance"
                    class="flex justify-between mb-2"
                  >
                    <div class="flex gap-2">
                      <img src="/icon/usercheck.svg">
                      <span color="#101F23" class="text-base font-medium">
                        {{ $t('calendar.attendance') }}
                      </span>
                    </div>
                  </div>
                  <div
                    v-if="attendance.isSchedule"
                    class="flex justify-between mb-2"
                  >
                    <div class="flex gap-2">
                      <img src="/icon/calendar_project_icon.svg">
                      <span color="#101F23" class="text-base font-medium">
                        {{ $t('calendar.scheduled') }}
                      </span>
                    </div>
                  </div>
                  <div
                    v-if="attendance.isAbsent"
                    class="flex justify-between mb-2"
                  >
                    <div class="flex gap-2">
                      <img src="/icon/reject.svg">
                      <span color="#101F23" class="text-base font-medium">
                        {{ $t('calendar.absent') }}
                      </span>
                    </div>
                  </div>
                  <div class="truncate">
                    <span color="#74797A">{{ $t('project') }}: </span>
                    <span color="#101F23" class="font-medium">
                      <a-tooltip
                        placement="topLeft"
                        :title="attendance.projectName"
                      >
                        {{ attendance.projectName || '---' }}
                      </a-tooltip>
                    </span>
                  </div>
                  <div class="truncate">
                    <span color="#74797A">
                      {{ $t('form.working-location') }}:
                    </span>
                    <span color="#101F23" class="font-medium">
                      <a-tooltip
                        placement="topLeft"
                        :title="
                          getWorkingLocationDisplayText(
                            attendance.workingLocation,
                            t,
                          )
                        "
                      >
                        {{
                          getWorkingLocationDisplayText(
                            attendance.workingLocation,
                            t,
                          )
                        }}
                      </a-tooltip>
                    </span>
                  </div>
                  <div class="truncate">
                    <span color="#74797A">{{ $t('check_in_gps') }}: </span>
                    <span color="#101F23" class="font-medium">
                      <a-tooltip
                        placement="topLeft"
                        :title="attendance.checkInLocation"
                      >
                        {{ attendance.checkInLocation || '---' }}
                      </a-tooltip>
                    </span>
                  </div>
                  <div class="truncate">
                    <span color="#74797A"> {{ $t('check_out_gps') }}: </span>
                    <span color="#101F23" class="font-medium">
                      <a-tooltip
                        placement="topLeft"
                        :title="attendance.checkOutLocation"
                      >
                        {{ attendance.checkOutLocation || '---' }}
                      </a-tooltip>
                    </span>
                  </div>
                  <div v-if="attendance.isSchedule">
                    <span color="#74797A">{{ $t('working_time') }}: </span>
                    <span color="#101F23" class="font-medium">
                      {{ formatTime(attendance?.scheduledStartTime, '00:00') }}
                      ~
                      {{ formatTime(attendance?.scheduledEndTime, '00:00') }}
                    </span>
                  </div>
                  <div v-else>
                    <span color="#74797A">{{ $t('working_time') }}: </span>
                    <span color="#101F23" class="font-medium">
                      {{ formatTime(attendance?.checkInTime, '00:00') }}
                      ~
                      {{ formatTime(attendance?.checkOutTime, '00:00') }}
                    </span>
                  </div>
                  <div
                    v-for="(timeBreak, index) in attendance?.breakList ?? []"
                    :key="index"
                  >
                    <span color="#74797A">{{ $t(`break_time_${index + 1}`) }}:
                    </span>
                    <span color="#101F23" class="font-medium">
                      {{ formatTime(timeBreak?.breakInTime, '00:00') }}
                      ~
                      {{ formatTime(timeBreak?.breakOutTime, '00:00') }}
                    </span>
                  </div>
                  <div v-if="attendance?.breakList?.length === 0">
                    <span color="#74797A">{{ $t('break_time') }}: </span>
                    <span color="#101F23" class="font-medium">
                      {{ t('no_break_time') }}
                    </span>
                  </div>
                  <div>
                    <span color="#74797A">{{ $t('total_work_time') }}: </span>
                    <span color="#101F23" class="font-medium">
                      {{ attendance?.totalWorkTime || '0' }}
                    </span>
                  </div>
                  <div>
                    <span color="#74797A">{{ $t('total_overtime') }}: </span>
                    <span color="#101F23" class="font-medium">
                      {{ attendance?.totalOverTime ?? '0' }}
                    </span>
                  </div>
                  <div class="flex gap-1">
                    <div color="#74797A">
                      {{ $t('form.approval') }}:
                    </div>
                    <div
                      :color="
                        getAttendanceStatus(
                          attendance?.isRequested ?? false,
                          attendance?.isApproved ?? false,
                        )?.color
                      "
                      class="flex gap-1 font-medium"
                    >
                      <img
                        v-if="
                          getAttendanceStatus(
                            attendance?.isRequested ?? false,
                            attendance?.isApproved ?? false,
                          )?.code === RequestStatusEnum.PENDING
                        "
                        src="/icon/in_review.svg"
                      >
                      <img
                        v-if="
                          getAttendanceStatus(
                            attendance?.isRequested ?? false,
                            attendance?.isApproved ?? false,
                          )?.code === RequestStatusEnum.APPROVED
                        "
                        src="/icon/correct.svg"
                      >
                      <img
                        v-if="
                          getAttendanceStatus(
                            attendance?.isRequested ?? false,
                            attendance?.isApproved ?? false,
                          )?.code === RequestStatusEnum.REJECTED
                            || getAttendanceStatus(
                              attendance?.isRequested ?? false,
                              attendance?.isApproved ?? false,
                            )?.code === RequestStatusEnum.CANCELLED
                        "
                        src="/icon/close.svg"
                      >
                      {{
                        getAttendanceStatus(
                          attendance?.isRequested ?? false,
                          attendance?.isApproved ?? false,
                        )?.status
                      }}
                    </div>
                  </div>
                </div>
                <div
                  v-if="showButton(attendance)"
                  class="flex border-b-2 border-t-0 border-r-2 border-l-2 border-gray-300 border-solid rounded-b-md justify-between"
                >
                  <div class="w-full border-r-2 border-t-0 border-l-0 border-b-0 border-gray-300 border-solid">
                    <a-button
                      class="flex justify-center items-center w-full !p-4 gap-2"
                      size="small"
                      type="text"
                      @click="
                        openModal(ModalType.EDIT, attendance?.employeeShiftId)
                      "
                    >
                      <img src="/icon/edit.svg" class="w-[20px]">
                      {{ $t('button.edit') }}
                    </a-button>
                  </div>
                  <div class="w-full">
                    <a-popconfirm
                      :title="t('message.delete-confirmation')"
                      @confirm="() => handleDelete(attendance?.employeeShiftId)"
                    >
                      <a-button
                        class="flex justify-center items-center w-full !p-4 gap-2"
                        size="small"
                        type="text"
                      >
                        <img src="/icon/delete.svg" class="w-[20px]">
                        {{ $t('button.delete') }}
                      </a-button>
                    </a-popconfirm>
                  </div>
                </div>
              </div>
            </template>
            <template v-for="request in selectedRequests" :key="request">
              <div class="border-2 border-gray-300 border-solid p-4 rounded-md">
                <div class="flex gap-2 mb-2">
                  <img src="/icon/request.svg">
                  <span color="#101F23" class="text-base font-medium">
                    {{ $t('calendar.request') }}
                  </span>
                </div>
                <p>
                  <span color="#74797A">{{ $t('request-type') }}: </span>
                  <span color="#101F23" class="font-medium">
                    {{ request.requestTypeName ?? '---' }}
                  </span>
                </p>
                <p>
                  <span color="#74797A">{{ $t('time') }}: </span>
                  <span color="#101F23" class="font-medium">
                    {{ formatDateTime(request.requestFrom) }}
                    ~
                    {{ formatDateTime(request.requestTo) }}
                  </span>
                </p>
                <p>
                  <span color="#74797A">{{ $t('reason') }}: </span>
                  <span color="#101F23" class="font-medium">
                    {{ request.description ?? '---' }}
                  </span>
                </p>
                <p>
                  <span color="#74797A">{{ `${$t('approver')} 1` }}: </span>
                  <span color="#101F23" class="font-medium">
                    {{ request.approver1Name ?? '---' }}
                  </span>
                </p>
                <p>
                  <span color="#74797A">{{ `${$t('approver')} 2` }}: </span>
                  <span color="#101F23" class="font-medium">
                    {{ request.approver2Name ?? '---' }}
                  </span>
                </p>
                <div class="flex gap-1">
                  <div color="#74797A">
                    {{ $t('form.approval') }}:
                  </div>
                  <div
                    :color="
                      RequestStatus[request.statusCode as SystemStatusEnumKey]
                        .color
                    "
                    class="flex gap-1 font-medium"
                  >
                    <img
                      v-if="request.statusCode === RequestStatusEnum.PENDING"
                      src="/icon/in_review.svg"
                    >
                    <img
                      v-if="request.statusCode === RequestStatusEnum.APPROVED"
                      src="/icon/correct.svg"
                    >
                    <img
                      v-if="
                        request.statusCode === RequestStatusEnum.REJECTED
                          || request.statusCode === RequestStatusEnum.CANCELLED
                      "
                      src="/icon/close.svg"
                    >
                    {{ request.statusName }}
                  </div>
                </div>
              </div>
            </template>
          </div>
          <a-empty
            v-else
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
            :style="{ height: maxHeight(62) }"
          />
        </a-card>
      </a-col>
    </a-row>
  </div>

  <WorkTimeModal
    :show="isOpenModal"
    :type="modalType"
    :selected-date="selectedDate"
    :attendance-item="attendanceItem"
    @update:show="isOpenModal = $event"
    @save-attendance="onFinish"
  />
</template>

<style lang="less" scoped>
.text-date {
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0%;
  vertical-align: middle;
  text-transform: uppercase;
}
</style>
