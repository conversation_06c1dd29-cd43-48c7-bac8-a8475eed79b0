<!-- eslint-disable antfu/top-level-function -->
// src/components/common/PulseFloatButton.vue
<script lang="ts" setup>
import type { PropType } from 'vue'
import { QuestionCircleOutlined } from '@ant-design/icons-vue'

// Define props
defineProps({
  tooltip: {
    type: String,
    default: '',
  },
  type: {
    type: String as PropType<'primary' | 'default' | 'dashed' | 'text' | 'link'>,
    default: 'primary',
  },
  shape: {
    type: String as PropType<'circle' | 'square'>,
    default: 'circle',
  },
})

// Define emits
const emit = defineEmits<{
  (e: 'click'): void
}>()

const handleClick = () => {
  emit('click')
}
</script>

<template>
  <a-float-button
    :shape="shape"
    :badge="{ dot: true }"
    type="primary"
    class="pulse-float-button ripple-effect hover:scale-110 transition-transform"
    @click="handleClick"
  >
    <template #icon>
      <component :is="QuestionCircleOutlined" />
    </template>
    <template #tooltip>
      {{ tooltip }}
    </template>
  </a-float-button>
</template>

<style scoped>
.pulse-float-button {
  --pulse-color: 24, 144, 255; /* Default primary color RGB */
  --pulse-opacity: 0.4;
  --pulse-size: 15px;
  animation: pulse 2s infinite;
  box-shadow: 0 0 0 rgba(var(--pulse-color), var(--pulse-opacity));
  z-index: 100;
  position: relative;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--pulse-color), var(--pulse-opacity));
  }
  70% {
    box-shadow: 0 0 0 var(--pulse-size) rgba(var(--pulse-color), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--pulse-color), 0);
  }
}

.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%) scale(0);
  animation: ripple 2s infinite;
}

@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  80% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

.pulse-float-button:hover {
  animation: none;
  transform: scale(1.2);
  transition: all 0.3s ease;
  box-shadow: 0 0 15px rgba(var(--pulse-color), 0.6);
}

/* Custom color for different types */
.pulse-float-button[type="primary"] {
  --pulse-color: 24, 144, 255;
}

.pulse-float-button[type="default"] {
  --pulse-color: 217, 217, 217;
}

.pulse-float-button[type="dashed"] {
  --pulse-color: 217, 217, 217;
}

.pulse-float-button[type="text"] {
  --pulse-color: 0, 0, 0;
}

.pulse-float-button[type="link"] {
  --pulse-color: 24, 144, 255;
}
</style>
