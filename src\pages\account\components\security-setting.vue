<script setup lang="ts">
import { LockOutlined } from '@ant-design/icons-vue'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'
import { changePasswordApi } from '~/api/common/login'
import type { ChangePasswordParams } from '~/api/common/login'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'

interface DataItem {
  title: string
  desc: string
}

const { t } = useI18n()
const messageNotify = useMessage()
const router = useRouter()

// Modal related refs
const changePasswordModal = ref(false)
const formRef = ref<FormInstance>()
const submitLoading = ref(false)

// Form state
const formState = reactive<ChangePasswordParams>({
  oldPassword: '',
  newPassword: '',
  confirmNewPassword: '',
})

const data = computed<DataItem[]>(() => {
  return [
    {
      title: t('account.settings.security.account-password'),
      desc: t('account.settings.security.account-password-desc'),
    },
    {
      title: t('account.settings.security.phone'),
      desc: t('account.settings.security.phone-desc'),
    },
    {
      title: t('account.settings.security-problem'),
      desc: t('account.settings.security-problem-desc'),
    },
    {
      title: t('account.settings.security.email'),
      desc: t('account.settings.security.email-desc'),
    },
    {
      title: t('account.settings.security.MFA'),
      desc: t('account.settings.security.MFA-desc'),
    },
  ]
})

// Password validation regex
const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{6,}$/

// Validation rules
async function validatePass(_rule: Rule, value: string) {
  if (value === '')
    return Promise.reject(new Error(t('validation.password.required')))

  if (!regex.test(value))
    return Promise.reject(new Error(t('validation.password.pattern')))

  return Promise.resolve()
}

async function validateConfirmPassword(_rule: Rule, value: string) {
  if (value === '')
    return Promise.reject(new Error(t('validation.confirmPassword.required')))

  if (value !== formState.newPassword)
    return Promise.reject(new Error(t('validation.confirmPassword.notMatch')))

  return Promise.resolve()
}

const rules: Record<string, Rule[]> = {
  oldPassword: [{ required: true, message: t('validation.oldPassword.required') }],
  newPassword: [{ required: true, validator: validatePass, trigger: 'blur' }],
  confirmNewPassword: [{ validator: validateConfirmPassword, trigger: 'blur' }],
}

// Handle password change
async function handleChangePassword() {
  try {
    await formRef.value?.validate()
    submitLoading.value = true

    const { status, code } = await changePasswordApi(formState)

    if (status === ResponseStatusEnum.SUCCESS) {
      messageNotify.success(t('account.settings.password.changeSuccess'))
      changePasswordModal.value = false
      router.push('/login')
    }
    else {
      messageNotify.error(t(code))
    }
  }
  catch (error) {
  }
  finally {
    submitLoading.value = false
  }
}

// Reset form when modal closes
function handleModalClose() {
  formRef.value?.resetFields()
}

function showChangePasswordModal() {
  changePasswordModal.value = true
}
</script>

<template>
  <a-card :title="t('account.settings.security-setting')" :bordered="false">
    <a-list item-layout="horizontal" :data-source="data">
      <template #renderItem="{ item }">
        <a-list-item>
          <a-list-item-meta :description="item.desc">
            <template #title>
              <span>{{ item.title }}</span>
            </template>
          </a-list-item-meta>
          <template #actions>
            <a-button
              v-if="item.title === t('account.settings.security.account-password')"
              type="link"
              @click="showChangePasswordModal"
            >
              {{ t('account.settings.modify') }}
            </a-button>
            <a-button v-else type="link" disabled>
              {{ t('account.settings.modify') }}
            </a-button>
          </template>
        </a-list-item>
      </template>
    </a-list>

    <!-- Change Password Modal -->
    <a-modal
      v-model:visible="changePasswordModal"
      :title="t('account.settings.password.change')"
      @cancel="handleModalClose"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        layout="vertical"
      >
        <a-form-item name="oldPassword" :label="t('oldPassword')">
          <a-input-password
            v-model:value="formState.oldPassword"
            :placeholder="t('placeholder.oldPassword')"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>

        <a-form-item name="newPassword" :label="t('newPassword')">
          <a-input-password
            v-model:value="formState.newPassword"
            :placeholder="t('placeholder.newPassword')"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>

        <a-form-item name="confirmNewPassword" :label="t('confirmNewPassword')">
          <a-input-password
            v-model:value="formState.confirmNewPassword"
            :placeholder="t('placeholder.confirmNewPassword')"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>
      </a-form>

      <template #footer>
        <a-button @click="changePasswordModal = false">
          {{ t('button.cancel') }}
        </a-button>
        <a-button
          type="primary"
          :loading="submitLoading"
          @click="handleChangePassword"
        >
          {{ t('button.submit') }}
        </a-button>
      </template>
    </a-modal>
  </a-card>
</template>

<style scoped lang="less">
:deep(.ant-card-body) {
  padding-left: 0 !important;
}
</style>
