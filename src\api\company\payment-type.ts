export interface PaymentTypeResponse {
  items: PaymentTypeItem[];
  pageIndex: number;
  pageSize: number;
  totalRecords: number;
}

export interface PaymentTypeItem {
  description: string;
  paymentTypeId: string;
  paymentTypeName: string;
}

interface PaymentTypeLogsResponse {
  entityChanges: PaymentTypeLogItem[];
  pageNum: number;
  pageSize: number;
  totalRecords: number;
}

export interface PaymentTypeLogItem {
  action: string;
  auditLogId: string;
  changedList: PaymentTypeChangedListItem[];
  entityId: string;
  description: string;
  modifiedTime: string;
  modifiedUserId: string;
  modifiedUserName: string;
}

export interface PaymentTypeChangedListItem {
  fieldName: string;
  valueAfter: string | number | boolean | number[] | string[];
  valueBefore: string | number | boolean | number[] | string[];
}

export async function getPaymentType(params?: any) {
  return useGet<PaymentTypeResponse>('v1/cost/paymenttype', params);
}

export async function getOnePaymentType(id: string, params?: any) {
  return useGet<PaymentTypeItem>(`v1/cost/paymenttype/${id}`, params);
}

export async function getPaymentTypeLogs(id: string, params?: any) {
  return useGet<PaymentTypeLogsResponse>(`v1/cost/paymenttype/${id}/logs`, params);
}

export async function deletePaymentType(id: string) {
  return useDelete(`v1/cost/paymenttype/${id}`);
}

export function createPaymentType(data: PaymentTypeItem) {
  return usePost('v1/cost/paymenttype', data);
}

export function updatePaymentType(id: string, data: PaymentTypeItem) {
  return usePut(`v1/cost/paymenttype/${id}`, data);
}
