// import { AxiosError } from 'axios'
// import router from '~/router'
// import { useMetaTitle } from '~/composables/meta-title'
// import { setRouteEmitter } from '~@/utils/route-listener'
// import logger from '~@/utils/logger'

// const allowList = ['/login', '/error', '/401', '/404', '/403', '/forgot-account', '/change-password', '/account/sign-up']
// const loginPath = '/login'

// router.beforeEach(async (to, _, next) => {
//   setRouteEmitter(to)
//   const userStore = useUserStore()
//   const token = useAuthorization()
//   logger.log('to', to)
//   if (!token.value) {
//     if (to.query?.token)
//       token.value = to.query.token.toString()

//     if (!allowList.includes(to.path) && !to.path.startsWith('/redirect')) {
//       next({
//         path: loginPath,
//         query: {
//           redirect: encodeURIComponent(to.path),
//         },
//       })
//       return
//     }
//   }
//   // If token is existed
//   else {
//     if (!userStore.userInfo
//       && !allowList.includes(to.path)
//       && !to.path.startsWith('/redirect')) {
//       try {
//         await userStore.getEmployeeInfo()
//         const currentRoute = await userStore.generateDynamicRoutes()
//         router.addRoute(currentRoute)
//         next({
//           ...to,
//           replace: true,
//         })
//         return
//       }
//       catch (e) {
//         logger.error(e)
//         if (e instanceof AxiosError && e?.response?.status === 401) {
//           next({
//             path: loginPath,
//           })
//           logger.error('401 is hear')
//         }
//         if (e instanceof AxiosError && e?.response?.status === 404)
//           logger.error('404 is hear')
//       }
//     }
//     else {
//       if (to.path === loginPath) {
//         next({
//           path: '/',
//         })
//         return
//       }
//     }
//   }
//   next()
// })

// router.afterEach((to) => {
//   useMetaTitle(to)
//   useLoadingCheck()
//   useScrollToTop()
// })
