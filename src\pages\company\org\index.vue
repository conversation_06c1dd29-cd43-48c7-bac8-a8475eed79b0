<template>
  <page-container>
    <div />
  </page-container>
</template>

<!-- <script lang="ts" setup>
import type { OrganizationItem, OrganizationParams } from '~@/api/company/org'
import { getOwnedOrgsApi, updateOrganizationApi } from '~@/api/company/org'
import logger from '~@/utils/logger'

const message = useMessage()
const formRef = ref()
const { t } = useI18n()
const orgData = ref<SimpleOrgItem[]>()
const currentOrg = ref<SimpleOrgItem>()

interface FormState {
  orgId?: string
  orgCode: string
  orgName: string
  orgPostalCode: string
  orgAddress: string
  status: boolean
  createTime: string
  updateTime: string
  taxNumber: string
}

const formState = reactive<FormState>({
  orgId: undefined,
  orgCode: '',
  orgName: '',
  orgPostalCode: '',
  orgAddress: '',
  status: true,
  createTime: '',
  updateTime: '',
  taxNumber: '',
})

async function getOwedOrgs() {
  const { data, status, code } = await getOwnedOrgsApi()
  if (status === 200) {
    orgData.value = data?.items
    message.success('获取成功')
  }
  else {
    message.error(t(code))
  }
}

function setOrgInfo(item?: any) {
  if (!item)
    return
  formState.orgId = item.orgId
  formState.orgCode = item.orgCode
  formState.orgName = item.orgName
  formState.orgPostalCode = item.orgPostalCode
  formState.orgAddress = item.orgAddress
  formState.status = item.status
  formState.createTime = item.createTime
  formState.updateTime = item.updateTime
  formState.taxNumber = item.taxNumber
}

async function updateOrganization(orgId: string, item: OrganizationParams) {
  try {
    const { data, status, code } = await updateOrganizationApi(orgId, item)
    if (status === 200) {
      currentOrg.value = data ?? undefined
      message.success('更新成功')
    }
    else {
      logger.error('status: ', status)
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

async function handleOk() {
  formRef.value.validate().then(async () => {
    await updateOrganization(formState.orgId as string, formState)
    setOrgInfo(currentOrg.value)
  })
}

onMounted(async () => {
  await getOwedOrgs()
  if (orgData.value?.length)
    setOrgInfo(orgData.value[0])
})
</script>

<template>
  <page-container>
    <a-row :gutter="24">
      <a-col :xxl="24" :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
        <a-card :style="{ marginBottom: '24px' }" :bordered="false">
          <template #title>
            {{ t('company.org.orgConfig') }}
          </template>
          <a-form
            ref="formRef"
            :model="formState"
            name="basic"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
            autocomplete="off"
          >
            <a-row :gutter="16">
              <a-col :span="24" :md="8">
                <a-form-item
                  :label="t('company.org.orgCode')"
                  name="orgCode"
                  :rules="[
                    {
                      required: true,
                      message: `${t('placeholder.enter-data', { msg: `${t('org-code')}` })}`,
                    },
                  ]"
                >
                  <a-input v-model:value="formState.orgCode" />
                </a-form-item>
              </a-col>
              <a-col :span="24" :md="8">
                <a-form-item
                  :label="t('company.org.companyName')"
                  name="orgName"
                  :rules="[
                    {
                      required: true,
                      message: `${t('placeholder.enter-data', { msg: `${t('org-name')}` })}`,
                    },
                  ]"
                >
                  <a-input v-model:value="formState.orgName" />
                </a-form-item>
              </a-col>

              <a-col :span="24" :md="8">
                <a-form-item
                  :label="t('company.org.CompanyZipcode')"
                  name="orgPostalCode"
                  :rules="[
                    {
                      required: true,
                      message: `${t('placeholder.enter-data', { msg: `${t('org-postal-code')}` })}`,
                    },
                  ]"
                >
                  <a-input v-model:value="formState.orgPostalCode" />
                </a-form-item>
              </a-col>
              <a-col :span="24" :md="8">
                <a-form-item
                  :label="t('company.org.CompanyTaxNumber')"
                  name="taxNumber"
                  :rules="[
                    {
                      required: true,
                      message: `${t('placeholder.enter-data', { msg: `${t('tax-number')}` })}`,
                      trigger: 'blur',
                    },
                  ]"
                >
                  <a-input v-model:value="formState.taxNumber" />
                </a-form-item>
              </a-col>

              <a-col :span="24" :md="8">
                <a-form-item
                  :label="t('company.org.companyAddress')"
                  name="orgAddress"
                >
                  <a-input v-model:value="formState.orgAddress" />
                </a-form-item>
              </a-col>
              <a-col :span="24" :md="8">
                <a-form-item :label="t('company.org.Status')" name="status">
                  <a-switch v-model:checked="formState.status" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item class="text-right">
                  <a-button type="primary" @click="handleOk">
                    {{ $t("company.org.Save") }}
                  </a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>
      </a-col>
    </a-row>
  </page-container>
</template> -->
