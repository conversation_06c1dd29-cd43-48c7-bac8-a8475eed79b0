<!-- eslint-disable antfu/top-level-function -->
<!-- eslint-disable curly -->
<script lang="ts" setup>
import { usePagination } from 'vue-request'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'
import type { CalendarInfoItem, EmployeeAttendanceItem, MonthlyAttendanceParams, TimePeriodParams } from '~@/api/company/office-request'
import { getMonthlyAttendanceApi, getMonthlyAttendanceByUserApi } from '~@/api/company/office-request'
import logger from '~@/utils/logger'
import { parseDate } from '~@/utils/apiTimer'

const { t } = useI18n()

const currentEmployeeInfo = ref<EmployeeAttendanceItem>()
const selectedMonth = ref<Dayjs>(dayjs())
const calendarData = ref<CalendarInfoItem[]>([])
const selectedCalendarInfoItem = ref<CalendarInfoItem>()
const selectedDate = ref(0)

// interface Params {
//   pageNum?: number
//   pageSize?: number
//   [key: string]: any
// }
// const searchForm = ref<Params>({
//   pageSize: 100,
//   pageNum: 1,
//   status: true,
// })

async function queryData() {
  const params: MonthlyAttendanceParams = {
    dateFrom: selectedMonth.value.startOf('month').format('YYYY-MM-DD'),
    dateTo: selectedMonth.value.endOf('month').format('YYYY-MM-DD'),
    pageNum: 1,
    pageSize: 100,
  }
  try {
    const { data, status, code } = await getMonthlyAttendanceApi(params)
    if (status === ResponseStatusEnum.SUCCESS) {
      return data
    }
    else {
      logger.error(code)
      return undefined
    }
  }
  catch (e) {
    logger.error(e)
  }
}

async function getMonthlyAttendanceByUser(userId: string) {
  const params: TimePeriodParams = {
    dateFrom: selectedMonth.value.startOf('month').format('YYYY-MM-DD'),
    dateTo: selectedMonth.value.endOf('month').format('YYYY-MM-DD'),
  }
  try {
    const { data, status, code } = await getMonthlyAttendanceByUserApi(userId, params)
    if (status === ResponseStatusEnum.SUCCESS) {
      calendarData.value = data?.calendar ?? []
      logger.log('calendarData', calendarData)
      return data
    }
    else {
      logger.error(code)
      return undefined
    }
  }
  catch (e) {
    logger.error(e)
  }
}

const {
  data: dataSource,
  loading,
  total,
  current,
  pageSize,
  changeCurrent,
} = usePagination(queryData, {
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const columns: any = computed(() => {
  return [
    {
      title: t('employeeName'),
      dataIndex: 'employeeName',
      key: 'employeeName',
      width: 200,
      fixed: 'left',
      // sorter: (a: UserItem, b: UserItem) => a.name.length - b.name.length,
    },
    {
      title: t('workTime'),
      dataIndex: 'workTime',
      key: 'workTime',
      width: 100,
    },
    {
      title: t('overtime'),
      dataIndex: 'overtime',
      key: 'overtime',
      sorter: true,
      width: 100,
    },
    {
      title: t('workDays'),
      dataIndex: 'workDays',
      key: 'workDays',
      width: 100,
      // customRender: ({ record }: any) => {
      //   if (!record.birthday)
      //     return t('no-data')
      //   return dayjs(record.birthday, 'YYYY-MM-DD').format('YYYY-MM-DD')
      // },
    },
    {
      title: t('offdays'),
      dataIndex: 'offdays',
      key: 'offdays',
      align: 'center',
      width: 100,
    },

    {
      title: t('usedLeaves'),
      dataIndex: 'usedLeaves',
      key: 'usedLeaves',
      align: 'center',
      width: 100,
    },
    {
      title: t('remainLeaves'),
      dataIndex: 'remainLeaves',
      key: 'remainLeaves',
      align: 'center',
      width: 100,
    },
    {
      title: t('comment'),
      dataIndex: 'comment',
      key: 'comment',
      align: 'center',
      width: 100,
    },
    {
      title: t('isRequested'),
      dataIndex: 'isRequested',
      key: 'isRequested',
      align: 'center',
      width: 100,
    },
    {
      title: t('status'),
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: 100,
    },
    {
      title: t('action'),
      dataIndex: 'actions',
      key: 'actions',
      align: 'center',
      width: 100,
      fixed: 'right',
    },
  ]
})

// const onSearch = () => {
//   const filter: any = {
//     ...searchForm.value,
//   }

//   if (filter.gender != null)
//     filter.gender = filter.gender === 1

//   if (!filter.status)
//     delete filter.status

//   // if (searchForm.value.type != null)
//   //   filter.type = searchForm.value.type == 0 ? false : true;
//   logger.log(filter)
//   handleTableChange(
//     {
//       pageSize: 10,
//       current: 1,
//     },
//     filter,
//   )
// }

const weekdays = ['Sun', 'Mon', 'Tue', 'Web', 'Thu', 'Fri', 'Sat']

const generateCalendar = computed(() => {
  logger.log('selectedMonth', selectedMonth.value)
  const firstDayOfMonth = selectedMonth.value.startOf('month')
  const endDayOfMonth = selectedMonth.value.endOf('month')
  const startOfCalendar = firstDayOfMonth.startOf('week') // Bắt đầu từ CN của tuần
  const endOfCalendar = endDayOfMonth.endOf('week') // Kết thúc tại T7 của tuần cuối

  const days = []
  let currentDay = startOfCalendar
  let valueKey = 0

  while (currentDay.isBefore(endOfCalendar) || currentDay.isSame(endOfCalendar, 'day')) {
    days.push({
      date: currentDay.date(),
      isCurrentMonth: currentDay.month() === firstDayOfMonth.month(),
      key: valueKey++,
    })
    currentDay = currentDay.add(1, 'day')
  }

  return days
})

const pageSizeOptions = [
  {
    label: '10',
    value: 10,
  },
  {
    label: '20',
    value: 20,
  },
  {
    label: '50',
    value: 50,
  },
  {
    label: '100',
    value: 100,
  },
]

const onChangeWeek = (changeType: 'prev' | 'next') => {
  currentEmployeeInfo.value = undefined
  if (changeType === 'prev') {
    selectedMonth.value = selectedMonth.value.subtract(1, 'month')
  }
  else {
    selectedMonth.value = selectedMonth.value.add(1, 'month')
  }
  changeCurrent(1)
}

const handlePageSizeChange = () => {

}

const onViewDetail = async (record: EmployeeAttendanceItem) => {
  currentEmployeeInfo.value = {
    ...record,
  }
  logger.log('currentEmployeeInfo: ', currentEmployeeInfo.value)
  if (!record.employeeId)
    return
  calendarData.value = []
  selectedCalendarInfoItem.value = undefined
  selectedDate.value = 0
  await getMonthlyAttendanceByUser(record.employeeId)
}

const onSelectDate = (date: number) => {
  selectedDate.value = date
  logger.log('date: ', date)
  selectedCalendarInfoItem.value = calendarData.value[date - 1]
  logger.log('selectedCalendarInfoItem: ', selectedCalendarInfoItem.value)
}

const customDateCellClass = (date: number) => {
  let style = 'bg-[#CDCECD] text-white'

  if (calendarData.value.length > 0) {
    if (calendarData.value[date - 1].leaveInfos.length || calendarData.value[date - 1].shiftInfos.length) {
      style = 'bg-[#B7D7F2] text-black'
    }
  }
  if (date === selectedDate.value) {
    style = 'bg-blue text-white'
  }
  if (date === selectedMonth.value.date()) {
    style = 'bg-orange text-white'
  }
  return style
}

onMounted(async () => {
})
</script>

<!-- class="flex lg:flex-row gap-x-[20px]" -->
<!--    -->
<template>
  <page-container>
    <div class="flex flex-col xl:flex-row gap-x-[20px] gap-y-[20px] xl-h-[calc(100vh-8rem)]">
      <div class="flex flex-col justify-between min-w-[400px] bg-white shadow-lg rounded-lg p-4">
        <div class="flex flex-col gap-y-4">
          <div class="flex justify-between items-center">
            <a-space>
              <a-input-search
                :placeholder="t('input.placeholder')"
                style="width: 270px"
              />
            </a-space>
            <div class="flex">
              <a-space class="gap-x-2">
                <div class="flex justify-between items-center gap-x-4">
                  <CarbonArrowLeft class="hover:bg-gray-200 cursor-pointer" @click="onChangeWeek('prev')" />
                  <div class="w-[90px] flex justify-center">
                    <p class="text-lg">
                      {{ dayjs(selectedMonth).format("MMM YYYY") }}
                    </p>
                  </div>
                  <CarbonArrowRight class="hover:bg-gray-200 cursor-pointer" @click="onChangeWeek('next')" />
                </div>
              </a-space>
            </div>
          </div>
          <a-table
            :columns="columns"
            :data-source="dataSource?.items"
            :loading="loading"
            class="office-request-table"
            :bordered="true"
            :scroll="{ x: 1500, y: 'calc(100vh - 350px)' }"
            row-key="employeeId"
            :custom-row="(record: EmployeeAttendanceItem) => {
              return record.employeeId === currentEmployeeInfo?.employeeId
                ? {
                  tag: 'tr',
                  class: 'bg-[#e6f4ff]',
                }
                : {};
            }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'employeeName'">
                <div class="flex flex-items-center">
                  <a-avatar :size="48" />
                  <div class="ml-2">
                    <strong>{{ record.employeeName }}</strong>
                    <br>
                    <a-typography-text type="secondary">
                      {{ `${t('code')}: ${record.code}` }}
                    </a-typography-text>
                  </div>
                </div>
              </template>
              <template v-if="column.dataIndex === 'actions'">
                <CarbonView class="cursor-pointer hover:text-blue" @click="onViewDetail(record as EmployeeAttendanceItem)" />
                <!-- <a-dropdown :trigger="['click']">
                <a class="ant-dropdown-link" @click.prevent>
                  <MoreOutlined />
                </a>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="edit" @click="openModal(record)">
                      <EditOutlined /> {{ t("button.edit") }}
                    </a-menu-item>
                    <a-menu-item
                      key="reset"
                      @click="resetPass(record.userId)"
                    >
                      <RedoOutlined /> {{ t("button.resetPass") }}
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item
                      key="delete"
                      style="color: red"
                      @click="showConfirm(record.userId)"
                    >
                      <DeleteOutlined /> {{ t("button.delete") }}
                    </a-menu-item>
                    <a-menu-item
                      key="viewLog"
                      style="color: green"
                      @click="openUserLogModal(record.userId)"
                    >
                      <DeleteOutlined /> {{ t("button.view") }}
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown> -->
              </template>
            </template>
          </a-table>
        </div>

        <!-- Pagination Section -->
        <div class="flex justify-between">
          <a-pagination
            v-model:current="current"
            v-model:page-size="pageSize"
            :total="total"
            class="text-[0.875rem] project-pagination"
          />
          <div
            class="flex items-center gap-x-[8px] text-[#74797A] project-select-page-size"
          >
            <span>{{ t("button.show") }}</span>
            <a-select
              v-model:value="pageSize"
              :options="pageSizeOptions"
              @change="handlePageSizeChange"
            >
              <template #suffixIcon>
                <CarbonPagninationArrowDown />
              </template>
            </a-select>
            <span>{{ t("button.entries") }}</span>
          </div>
        </div>
      </div>

      <!-- Right Section  -->
      <div class="flex min-w-[400px]">
        <template v-if="!currentEmployeeInfo">
          <div class="flex items-center justify-center bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <Empty />
          </div>
        </template>
        <template v-else>
          <div class="flex flex-col bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <div class="flex items-center  mb-4 border-b pb-4">
              <div class="w-12 h-12 rounded-full bg-gray-300 mr-4" />
              <div>
                <h2 class="text-lg font-medium">
                  {{ currentEmployeeInfo.employeeName }}
                </h2>
                <p class="text-gray-500">
                  {{ t('code') }}: {{ currentEmployeeInfo.code }}
                </p>
              </div>
            </div>
            <div class="flex justify-center mb-4">
              <p>{{ selectedMonth.format("MMM YYYY") }}</p>
            </div>
            <div class="grid grid-cols-7 gap-1">
              <div v-for="weekday in weekdays" :key="weekday" class="text-center">
                <p>{{ t(weekday) }}</p>
              </div>
            </div>
            <div class="grid grid-cols-7 gap-1 mb-3 border-b pb-4">
              <div v-for="(date) in generateCalendar" :key="date.key" class="text-center">
                <div
                  v-if="date.isCurrentMonth"
                  class="cursor-pointer  px-3 py-2 rounded-sm text-xs hover:bg-blue hover:text-white"
                  :class="customDateCellClass(date.date)"
                  @click="onSelectDate(date.date)"
                >
                  {{ date.date }}
                </div>
                <div
                  v-else
                  class="bg-white text-gray px-3 py-2 rounded-sm text-xs"
                >
                  {{ date.date }}
                </div>
              </div>
            </div>
            <div
              v-if="selectedCalendarInfoItem"
              class="space-y-3"
            >
              <div class="flex items-center justify-center">
                <p>{{ selectedMonth.date(selectedDate).format("ddd, YYYY年 MMM DD日") }}</p>
              </div>
              <div class="space-y-3 max-h-[300px] overflow-y-scroll snap-y snap-mandatory scroll-smooth">
                <div
                  v-for="(shiftItem, index) in selectedCalendarInfoItem?.shiftInfos"
                  :key="index"
                  class="bg-[#1570EF] shadow-lg pl-2 rounded-lg snap-start"
                >
                  <div class="bg-white p-2">
                    <div class="flex flex-col">
                      <p class="font-medium mb-2">
                        <span class="text-[#1570EF]">{{ dayjs(shiftItem.checkInTime, 'HH:mm:ss').format('HH:mm') }}
                          &nbsp;~&nbsp;
                          {{ dayjs(shiftItem.checkOutTime, 'HH:mm:ss').format('HH:mm') }}</span>
                          &nbsp;&nbsp;
                        <span class="font-bold">{{ shiftItem.projectName }}</span>
                      </p>
                      <div class="flex gap-x-4">
                        <p>
                          <span class="text-gray">{{ t('workTime') }}:</span>&nbsp;<span>{{ shiftItem.workHours }}</span>
                        </p>
                        <p>
                          <span class="text-gray">{{ t('overtime') }}:</span>&nbsp;<span>{{ shiftItem.overtime }}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  v-for="(leaveItem, index) in selectedCalendarInfoItem?.leaveInfos"
                  :key="index"
                  class="bg-[#F99649] pl-2 shadow-md rounded-lg snap-start"
                >
                  <div class="bg-white p-2">
                    <div class="flex flex-col">
                      <p class="font-medium mb-2">
                        <span class="text-[#1570EF]">{{ parseDate(leaveItem.requestFrom) }}&nbsp;~&nbsp;{{ parseDate(leaveItem.requestTo) }}</span>
                        &nbsp;&nbsp;
                        <span class="font-bold">{{ t('request') }}</span>
                      </p>
                      <div class="flex gap-x-4">
                        <p>
                          <span class="text-gray">{{ t('requestType') }}:</span>&nbsp;<span>{{ leaveItem.requestType }}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              v-else
              class="flex items-center justify-center h-full"
            >
              <Empty />
            </div>
          </div>
        </template>
      </div>
    </div>
  </page-container>
</template>

<style lang="less">
.custom-layout {
  > div {
    gap: 12px;
    .ant-col {
      flex: 0 0 auto;
      padding-bottom: 0;
    }
  }
}
.user-modal {
  .ant-modal-header {
    text-align: center;
    .ant-modal-title {
      font-size: 20px;
      color: #1c4771;
    }
  }
}
// .filter-popover {
//   .ant-popover-inner {
//     padding: 0;
//   }
// }

.office-request-table .ant-table-pagination.ant-pagination {
  display: none;
}

.custom-height {
  height: calc(100vh - 140px);
}
</style>
